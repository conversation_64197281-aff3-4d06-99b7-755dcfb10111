package br.com.alice.appointment.client

import br.com.alice.appointment.models.AppointmentWithStaff
import br.com.alice.appointment.models.AppointmentWithValidation
import br.com.alice.common.Disease
import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel
import br.com.alice.data.layer.models.AppointmentComponent
import br.com.alice.data.layer.models.AppointmentContractualRisk
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentEventDetail
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentFinishType
import br.com.alice.data.layer.models.AppointmentSpecialist
import br.com.alice.data.layer.models.AppointmentSpecialty
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.ExcuseNote
import br.com.alice.data.layer.models.ExternalFileType
import br.com.alice.data.layer.models.Outcome
import br.com.alice.data.layer.models.RelatedStaff
import br.com.alice.data.layer.models.TemplateType
import br.com.alice.data.layer.models.TreatedBy
import br.com.alice.data.layer.services.UpdateRequest
import br.com.alice.healthlogic.models.ETATriggerEvent
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AppointmentService : Service {

    override val namespace get() = "appointment"
    override val serviceName get() = "appointments"

    suspend fun update(
        loggedStaffId: UUID,
        appointmentId: UUID,
        request: Appointment
    ): Result<Appointment, Throwable>

    suspend fun add(model: Appointment): Result<Appointment, Throwable>

    suspend fun get(id: UUID): Result<Appointment, Throwable>

    suspend fun getAppointmentsByPerson(
        personId: PersonId,
        formatted: Boolean,
        includeInternalTypes: Boolean = false,
        staffId: UUID? = null
    ): Result<List<AppointmentWithStaff>, Throwable>

    suspend fun publishExcuseNotes(request: PublishExcuseNotesRequest): Result<List<ExcuseNote>, Throwable>

    suspend fun getExcuseNoteById(excuseNotedId: UUID): Result<String, Throwable>

    suspend fun finishNew(
        id: UUID,
        staffId: UUID,
        finishType: AppointmentFinishType,
        token: String? = null
    ): Result<Boolean, Throwable>

    suspend fun getAndValidate(
        id: UUID,
        staffId: UUID,
    ): Result<AppointmentWithValidation, Throwable>

    suspend fun hasDraft(
        personId: PersonId,
        staffId: UUID,
    ): Result<Boolean, Throwable>

    suspend fun currentDraft(
        personId: PersonId,
        staffId: UUID,
    ): Result<Appointment, Throwable>

    suspend fun delete(
        id: UUID,
        discardedType: AppointmentDiscardedType,
        discardedReason: String? = null,
    ): Result<Appointment, Throwable>

    suspend fun existsValidByReferencedLink(
        id: UUID,
        personId: PersonId,
        model: ReferenceLinkModel,
        date: LocalDate
    ): Result<Boolean, Throwable>

    suspend fun buildAutomaticTaskEngineTriggerById(id: UUID): Result<ETATriggerEvent, Throwable>

    suspend fun personHasAppointments(personId: PersonId): Result<Boolean, Throwable>

    suspend fun getDraftByPersonAndRelatedStaff(
        personId: PersonId,
        staff: UUID
    ): Result<List<Appointment>, Throwable>

    suspend fun find(filter: AppointmentFilter): Result<List<Appointment>, Throwable>

    suspend fun countByPersonId(personId: PersonId): Result<Int, Throwable>

    suspend fun getByIds(ids: List<UUID>): Result<List<Appointment>, Throwable>

    suspend fun recentByStaff(staffId: UUID, limit: Int): Result<List<Appointment>, Throwable>

}

data class UpdateAppointmentRequest(
    val personId: String,
    val description: String? = null,
    val guidance: String? = null,
    val excuseNotes: List<ExcuseNote>? = emptyList(),
    val completed: Boolean? = null,
    val type: AppointmentType? = null,
    val serviceScriptId: UUID? = null,
    val subjectiveCodes: List<Disease>? = emptyList(),
    val objectiveCodes: List<Disease>? = emptyList(),
    val objective: String? = null,
    val plan: String? = null,
    val startedAt: LocalDateTime? = null,
    val endedAt: LocalDateTime? = null,
    val attachments: List<Attachment>? = null,
    val specialty: AppointmentSpecialty? = null,
    val specialist: AppointmentSpecialist? = null,
    val referencedLinks: List<Appointment.ReferencedLink>? = emptyList(),
    val clinicalEvaluation: String? = null,
    val treatedBy: TreatedBy? = null,
    val outcome: Outcome? = null,
    val channelId: String? = null,
    val discardedType: UpdateAppointmentDiscardedType? = null,
    val caseRecordDetails: List<CaseRecordDetails>? = emptyList(),
    val draftGroup: List<RelatedStaff>? = emptyList(),
    val subjective: String? = null,
    val event: AppointmentEventDetail? = null,
    val name: String? = null,
    val components: List<AppointmentComponent>? = emptyList(),
    val templateType: TemplateType? = null,
    val emptyEventReason: String? = null,
    val externalFiles: List<ExternalFileTransport>? = emptyList(),
    val contractualRisks: List<AppointmentContractualRisk>? = emptyList(),
    val medicalDischarge: Boolean? = null,
    val primaryAttentionRequest: String? = null,
    val appointmentDate: LocalDate? = null,
    val anesthetist: AnesthetistAppointmentRequest? = null,
    override val version: Int
) : UpdateRequest

enum class UpdateAppointmentDiscardedType {
    NO_SHOW,
}

data class EvolutionCommenter(
    val staffName: String,
    val staffId: UUID,
    val profileImageUrl: String? = "",
    val councilSignature: String? = ""
)

data class EvolutionCommenterV2(
    val id: UUID,
    val fullName: String,
    val profileImageUrl: String? = "",
    val councilSignature: String? = ""
)

data class PublishExcuseNotesRequest(
    val appointmentId: UUID,
    val staffId: UUID,
    val token: String? = null,
    val excuseNote: ExcuseNote
)

data class ChannelReference(
    val name: String? = null,
    val id: String
)

data class AppointmentFilter(
    val personId: PersonId,
    val status: AppointmentStatus? = null,
    val type: AppointmentType? = null,
    val createdAtGreater: LocalDateTime? = null,
    val createdAtLess: LocalDateTime? = null,
    val event: AppointmentEventFilter? = null,
    val paginate: IntRange? = null,
    val orderByCreatedAt: Boolean = false,
    val sortOrder: SortOrder? = null,
    val staffId: UUID? = null
)

data class AppointmentEventFilter(
    val referenceModelId: String? = null,
    val referenceModel: AppointmentEventReferenceModel
)

data class ExternalFileTransport(
    val id: String,
    val name: String? = null,
    val store: Boolean = false,
    val type: ExternalFileType
)

data class AnesthetistAppointmentRequest(
    val type: AnesthetistType,
    val staff: AnesthetistStaffRequest?,
)

data class AnesthetistStaffRequest(
    val id: UUID,
)
