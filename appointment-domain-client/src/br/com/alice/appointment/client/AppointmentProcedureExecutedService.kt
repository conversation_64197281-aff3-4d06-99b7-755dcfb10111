package br.com.alice.appointment.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentProcedureExecutedService : Service {

    override val namespace get() = "appointment"
    override val serviceName get() = "procedure_executed"

    suspend fun add(model: AppointmentProcedureExecuted): Result<AppointmentProcedureExecuted, Throwable>

    suspend fun addDefaultProcedureByAppointment(model: AppointmentProcedureExecuted): Result<AppointmentProcedureExecuted, Throwable>

    suspend fun get(id: UUID): Result<AppointmentProcedureExecuted, Throwable>

    suspend fun deleteDraftProcedure(id: UUID): Result<Boolean, Throwable>

    suspend fun getByAppointmentId(id: UUID): Result<List<AppointmentProcedureExecuted>, Throwable>

    suspend fun existByAppointmentId(id: UUID): Result<Boolean, Throwable>

    suspend fun changeProceduresToDone(appointmentId: UUID): Result<List<AppointmentProcedureExecuted>, Throwable>

    suspend fun changeProceduresToDoneAndPublish(appointment: Appointment): Result<List<AppointmentProcedureExecuted>, Throwable>

    suspend fun deleteProcedures(appointmentId: UUID): Result<Boolean, Throwable>

}
