package br.com.alice.appointment.event

import br.com.alice.appointment.SERVICE_NAME
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.Anesthetist
import br.com.alice.data.layer.models.AppointmentProcedureExecuted

data class AppointmentExecutedProcedureGroupEvent(
    private val procedures: List<AppointmentProcedureExecuted>,
    private val anesthetist: Anesthetist? = null
) : NotificationEvent<AppointmentExecutedProcedureGroupPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = AppointmentExecutedProcedureGroupPayload(procedures, anesthetist)
) {
    companion object {
        const val name = "appointment-executed-procedure-group-published"
    }
}

data class AppointmentExecutedProcedureGroupPayload(
    val procedures: List<AppointmentProcedureExecuted>,
    val anesthetist: Anesthetist?
)
