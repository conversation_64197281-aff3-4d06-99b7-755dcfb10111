ktor {
    deployment {
        port = 8080
        port = ${?PORT}
        connectionGroupSize = 3
        workerGroupSize = 3
        callGroupSize = 16
    }
    application {
        modules = [br.com.alice.staff.ApplicationKt.module]
    }
    httpclient {
        timeout = 300
        timeout = ${?HTTP_CLIENT_TIMEOUT}
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

test {
    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
        anesthetistFormLink = "https://www.cognitoforms.com/Alice7/CadastroDeAnestesistasParaProcedimentosCir%C3%BArgicos"
        anesthetistTemplateName = "anesthetist_form_link_template"
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
    }
}

development {
    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
        anesthetistFormLink = "https://www.cognitoforms.com/Alice7/CadastroDeAnestesistasParaProcedimentosCir%C3%BArgicos"
        anesthetistTemplateName = "anesthetist_form_link_template"
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
    }
}

production {
    mailer {
        senderName = "Alice"
        senderName = ${?DEFAULT_EMAIL_SENDER_NAME}

        senderEmail = "<EMAIL>"
        senderEmail = ${?DEFAULT_EMAIL_SENDER_ADDRESS}

        anesthetistFormLink = "https://www.cognitoforms.com/Alice7/CadastroDeAnestesistasParaProcedimentosCir%C3%BArgicos"
        anesthetistFormLink = ${?ANESTHETIST_FORM_LINK}

        anesthetistTemplateName = "anesthetist_form_link_template"
        anesthetistTemplateName = ${?ANESTHETIST_TEMPLATE_NAME}
    }

    pinPoint {
        campaignId = "2c06b675e5e7457cb239591fc2edf3be"
        campaignId = ${?PINPOINT_CAMPAIGN_ID}
    }
}
