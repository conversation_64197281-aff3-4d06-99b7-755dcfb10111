import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig: BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {

    object Mailer {
        val defaultSenderName = config("mailer.senderName")
        val defaultSenderEmail = config("mailer.senderEmail")
        val anesthetistTemplateName = config("mailer.anesthetistTemplateName")
        val anesthetistFormLink = config("mailer.anesthetistFormLink")
    }
    object PinPoint {
        val campaignId = config("pinPoint.campaignId")
    }
}
