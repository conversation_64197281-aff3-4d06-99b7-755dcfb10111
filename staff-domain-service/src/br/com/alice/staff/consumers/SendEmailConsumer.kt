package br.com.alice.staff.consumers

import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.SendEmailRequest
import br.com.alice.staff.event.AnesthetistCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.time.LocalDateTime

class SendEmailConsumer(
    private val mailer: EmailSender,
) : Consumer() {
    companion object {
        private val defaultSender = EmailAddress(
            name = ServiceConfig.Mailer.defaultSenderName,
            email = ServiceConfig.Mailer.defaultSenderEmail
        )
        private val ANESTHETIST_TEMPLATE = ServiceConfig.Mailer.anesthetistTemplateName
        private val CAMPAIGN_ID = ServiceConfig.PinPoint.campaignId

        private val FORM_LINK = ServiceConfig.Mailer.anesthetistFormLink
    }

    suspend fun sendEmailToAnesthetist(event: AnesthetistCreatedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            catchResult {
                span("sendEmailToAnesthetist") { span ->
                    val staff = event.payload.staff
                    span.setAttribute("staff_id", staff.id)

                    val linkSendDate = LocalDateTime.now().toSaoPauloTimeZone().toBrazilianDateTimeFormat()

                    val replaceVariables = mapOf(
                        "first_name" to staff.firstName,
                        "recipient_email" to staff.email,
                        "form_link" to FORM_LINK,
                        "send_date" to linkSendDate
                    )

                    val emailAddress = listOf(
                        EmailAddress(
                            name = staff.fullName,
                            email = staff.email
                        )
                    )

                    val request = SendEmailRequest(
                        from = defaultSender,
                        to = emailAddress,
                        templateName = ANESTHETIST_TEMPLATE,
                        campaignId = CAMPAIGN_ID,
                        replaceVariables = replaceVariables
                    )
                    mailer.send(request).id.success()
                }
            }
        }

}
