package br.com.alice.staff.routes

import br.com.alice.clinicalaccount.event.PersonClinicalAccountDeleteEvent
import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.provider.model.ProviderUnitCreatedEvent
import br.com.alice.provider.model.ProviderUnitUpdatedEvent
import br.com.alice.staff.consumers.ChangeScheduleDaysConsumer
import br.com.alice.staff.consumers.ChangeShowOnSpecialistConsumer
import br.com.alice.staff.consumers.HealthcareTeamBackfillConsumer
import br.com.alice.staff.consumers.PersonClinicalAccountConsumer
import br.com.alice.staff.consumers.SendEmailConsumer
import br.com.alice.staff.consumers.StaffUpdatedConsumer
import br.com.alice.staff.event.ChangeScheduleDaysEvent
import br.com.alice.staff.event.HealthcareTeamDemographicCountBackfillEvent
import br.com.alice.staff.event.StaffUpdatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {

    val changeShowOnSpecialistConsumer by inject<ChangeShowOnSpecialistConsumer>()
    consume(
        "change-show-on-app-by-created",
        ProviderUnitCreatedEvent.name,
        changeShowOnSpecialistConsumer::changeShowOnAppByCreated
    )
    consume(
        "change-show-on-app-by-updated",
        ProviderUnitUpdatedEvent.name,
        changeShowOnSpecialistConsumer::changeShowOnAppByUpdated
    )

    val staffUpdatedConsumer by inject<StaffUpdatedConsumer>()
    consume(
        "logout-staff",
        StaffUpdatedEvent.name,
        staffUpdatedConsumer::logoutStaff
    )
    consume(
        "invalidate-staff-cache-keys",
        StaffUpdatedEvent.name,
        staffUpdatedConsumer::invalidateCacheKeys
    )
    consume(
        "update-healthcare-team-physician-staff-gender",
        StaffUpdatedEvent.name,
        staffUpdatedConsumer::updateHealthcareTeamPhysicianStaffGender
    )

    val changeScheduleDaysConsumer by inject<ChangeScheduleDaysConsumer>()
    consume(
        "change-schedule-days",
        ChangeScheduleDaysEvent.name,
        changeScheduleDaysConsumer::changeScheduleDays
    )

    val personClinicalAccountConsumer by inject<PersonClinicalAccountConsumer>()
    consume(
        "handler-person-clinical-account-to-member-count-in-healthcare-team",
        PersonHealthcareTeamAssociationUpdatedEvent.name,
        personClinicalAccountConsumer::addMemberCount
    )
    consume(
        "handler-person-clinical-account-deleted-to-decrement-member-count-in-healthcare-team",
        PersonClinicalAccountDeleteEvent.name,
        personClinicalAccountConsumer::decrementMemberCount
    )

    val healthcareTeamBackfillConsumer by inject<HealthcareTeamBackfillConsumer>()
    consume(
        "handler-healthcare-team-demographic-count-backfill",
        HealthcareTeamDemographicCountBackfillEvent.name,
        healthcareTeamBackfillConsumer::updateDemographicCount
    )

    val sendEmailConsumer by inject<SendEmailConsumer>()
    consume(
        "send-email-to-anesthetist",
        br.com.alice.staff.event.AnesthetistCreatedEvent.name,
        sendEmailConsumer::sendEmailToAnesthetist
    )

}
