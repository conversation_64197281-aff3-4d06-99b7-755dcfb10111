package br.com.alice.staff

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.coverage.ioc.CoverageDomainClientModule
import br.com.alice.data.layer.STAFF_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.CassiSpecialistModelDataService
import br.com.alice.data.layer.services.CassiSpecialistModelDataServiceClient
import br.com.alice.data.layer.services.ContactModelDataService
import br.com.alice.data.layer.services.ContactModelDataServiceClient
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.data.layer.services.HealthProfessionalModelDataServiceClient
import br.com.alice.data.layer.services.HealthcareAdditionalTeamDataService
import br.com.alice.data.layer.services.HealthcareAdditionalTeamDataServiceClient
import br.com.alice.data.layer.services.HealthcareTeamModelDataService
import br.com.alice.data.layer.services.HealthcareTeamModelDataServiceClient
import br.com.alice.data.layer.services.PersonClinicalAccountDataService
import br.com.alice.data.layer.services.PersonClinicalAccountDataServiceClient
import br.com.alice.data.layer.services.StaffModelDataService
import br.com.alice.data.layer.services.StaffModelDataServiceClient
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.staff.client.CassiSpecialistService
import br.com.alice.staff.client.ContactService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthcareAdditionalTeamService
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.client.v2.CommunitySpecialistService
import br.com.alice.staff.consumers.ChangeScheduleDaysConsumer
import br.com.alice.staff.consumers.ChangeShowOnSpecialistConsumer
import br.com.alice.staff.consumers.HealthcareTeamBackfillConsumer
import br.com.alice.staff.consumers.PersonClinicalAccountConsumer
import br.com.alice.staff.consumers.SendEmailConsumer
import br.com.alice.staff.consumers.StaffUpdatedConsumer
import br.com.alice.staff.controllers.HealthProfessionalBackfillController
import br.com.alice.staff.controllers.HealthcareTeamBackfillController
import br.com.alice.staff.controllers.StaffBackfillController
import br.com.alice.staff.ioc.STAFF_CACHE_PREFIX
import br.com.alice.staff.routes.backfillRouting
import br.com.alice.staff.routes.kafkaRoutes
import br.com.alice.staff.services.CassiSpecialistServiceImpl
import br.com.alice.staff.services.ContactServiceImpl
import br.com.alice.staff.services.HealthProfessionalServiceImpl
import br.com.alice.staff.services.HealthcareAdditionalTeamServiceImpl
import br.com.alice.staff.services.HealthcareTeamServiceImpl
import br.com.alice.staff.services.StaffServiceImpl
import br.com.alice.staff.v2.CommunitySpecialistServiceImpl
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

private const val HTTP_CLIENT_DEFAULT_TIMEOUT = 300L

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))
    private val timeoutInMillis = config
        .propertyOrNull("ktor.httpclient.timeout")?.getString()?.toLong() ?: HTTP_CLIENT_DEFAULT_TIMEOUT

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        CoverageDomainClientModule,
        GoogleMapsModule,
        ClinicalAccountDomainClientModule,
        PersonDomainClientModule,
        CommunicationModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single<Invoker> {
                DataLayerClientConfiguration.build(
                    DefaultHttpClient(timeoutInMillis = timeoutInMillis)
                )
            }

            // Health Controller
            single { HealthController(SERVICE_NAME) }

            // Load services
            loadServiceServers("br.com.alice.staff.services")

            // Data Services
            single<CassiSpecialistModelDataService> { CassiSpecialistModelDataServiceClient(get()) }
            single<HealthcareTeamModelDataService> { HealthcareTeamModelDataServiceClient(get()) }
            single<HealthcareAdditionalTeamDataService> {
                HealthcareAdditionalTeamDataServiceClient(get())
            }
            single<HealthProfessionalModelDataService> { HealthProfessionalModelDataServiceClient(get()) }
            single<PersonClinicalAccountDataService> { PersonClinicalAccountDataServiceClient(get()) }
            single<StaffModelDataService> { StaffModelDataServiceClient(get()) }
            single<ContactModelDataService> { ContactModelDataServiceClient(get()) }

            // Exposed Services
            single<CassiSpecialistService> { CassiSpecialistServiceImpl(get(), get(), get(), get()) }
            single<HealthcareTeamService> { HealthcareTeamServiceImpl(get(), get(), get(), get(), get()) }
            single<HealthcareAdditionalTeamService> { HealthcareAdditionalTeamServiceImpl(get()) }
            single<HealthProfessionalService> { HealthProfessionalServiceImpl(get(), get(), get(), get()) }
            single<ContactService> { ContactServiceImpl(get(), get()) }
            single<StaffService> { StaffServiceImpl(get(), get(), get(), get()) }
            single<CommunitySpecialistService> {
                CommunitySpecialistServiceImpl(get(), get())
            }

            // Controllers
            single { HealthProfessionalBackfillController(get(), get()) }
            single { StaffBackfillController(get(), get(), get()) }
            single { HealthcareTeamBackfillController(get(), get()) }

            // Consumers
            single { ChangeShowOnSpecialistConsumer(get()) }
            single { ChangeScheduleDaysConsumer(get(), get()) }
            single { PersonClinicalAccountConsumer(get(), get()) }
            single { HealthcareTeamBackfillConsumer(get(), get(), get(), get()) }
            val cache = CacheFactory.newInstance(STAFF_CACHE_PREFIX)
            single { StaffUpdatedConsumer(cache, get()) }
            single { SendEmailConsumer(get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules
) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, STAFF_DOMAIN_ROOT_SERVICE_NAME)
            backfillRouting()
        }

        kafkaConsumer {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.EHR)
    }
}
