package br.com.alice.staff.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.model.SendEmailRequest
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.staff.event.AnesthetistCreatedEvent
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.Test


class SendEmailConsumerTest : ConsumerTest() {
    private val mailer: EmailSender = mockk()

    val consumer = SendEmailConsumer(mailer)


    val template = ServiceConfig.Mailer.anesthetistTemplateName
    val formLink = ServiceConfig.Mailer.anesthetistFormLink
    private val defaultSender = EmailAddress(
        name = ServiceConfig.Mailer.defaultSenderName,
        email = ServiceConfig.Mailer.defaultSenderEmail
    )
    val campaignId = ServiceConfig.PinPoint.campaignId

    val anesthetist = TestModelFactory.buildStaff(
        role = Role.ANESTHETIST
    )

    @Test
    fun `$sendEmailToAnesthetist should send email to anesthetist`() = mockLocalDateTime { now ->
        val event = AnesthetistCreatedEvent(anesthetist)
        val linkSendDate = now.toSaoPauloTimeZone().toBrazilianDateTimeFormat()
        val emailReturn = EmailReceipt(
            id = RangeUUID.generate().toString(),
        )

        val replaceVariables = mapOf(
            "first_name" to anesthetist.firstName,
            "recipient_email" to anesthetist.email,
            "form_link" to formLink,
            "send_date" to linkSendDate
        )

        val emailAddress = listOf(
            EmailAddress(
                name = anesthetist.fullName,
                email = anesthetist.email
            )
        )

        val request = SendEmailRequest(
            from = defaultSender,
            to = emailAddress,
            templateName = template,
            replaceVariables = replaceVariables,
            campaignId = campaignId
        )

        coEvery {
            mailer.send(request)
        } returns emailReturn

        val result = consumer.sendEmailToAnesthetist(event)
        ResultAssert.Companion.assertThat(result).isSuccessWithData(emailReturn.id)

        coVerifyOnce {
            mailer.send(request)
        }
    }

}
