plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.staff-domain-service"
version = aliceStaffDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:staff-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-redis"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
    implementation(project(":data-packages:staff-domain-service-model-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":data-packages:coverage-domain-service-data-package"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":coverage-domain-client"))
    implementation(project(":provider-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":clinical-account-domain-client"))
    implementation(project(":communication"))

    ktor2Dependencies()

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
}
