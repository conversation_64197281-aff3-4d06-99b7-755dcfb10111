package br.com.alice.moneyin.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.RunningMode
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.communication.crm.hubspot.b2b.BusinessHubspotSalesCrmPipeline
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClient
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClientImpl
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClientLocal
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.data.layer.services.ResourceSignTokenModelDataService
import br.com.alice.data.layer.services.ResourceSignTokenModelDataServiceClient
import br.com.alice.data.layer.services.impl.ResourceSignTokenServiceImpl
import br.com.alice.moneyin.SERVICE_NAME
import br.com.alice.moneyin.ServiceConfig
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.CancelPaymentOnAcquirerScheduleService
import br.com.alice.moneyin.client.CompanyInvoiceDetailService
import br.com.alice.moneyin.client.CompanyInvoiceService
import br.com.alice.moneyin.client.FinancialDataService
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupQueryService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.MoneyInResourceSignTokenService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.client.PreActivationCompanyInvoiceService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.client.iugu.IuguBankSlipClient
import br.com.alice.moneyin.client.iugu.IuguPaymentClient
import br.com.alice.moneyin.client.iugu.iuguBankSlipClientHttpConfig
import br.com.alice.moneyin.client.iugu.iuguHttpConfig
import br.com.alice.moneyin.communication.BillingAccountablePartyMailer
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.consumers.AcquirerPaymentNotificationConsumer
import br.com.alice.moneyin.consumers.BillingAccountablePartyAssignedConsumer
import br.com.alice.moneyin.consumers.CreateExternalPaymentConsumer
import br.com.alice.moneyin.consumers.CreateInvoicePaymentConsumer
import br.com.alice.moneyin.consumers.ExternalInvoiceCreatedConsumer
import br.com.alice.moneyin.consumers.FirstPaymentScheduleConsumer
import br.com.alice.moneyin.consumers.InvoiceConsumer
import br.com.alice.moneyin.consumers.InvoicePaymentConsumer
import br.com.alice.moneyin.consumers.InvoicesNearOverdueJobConsumer
import br.com.alice.moneyin.consumers.MemberCreatedConsumer
import br.com.alice.moneyin.consumers.MemberInvoiceGroupConsumer
import br.com.alice.moneyin.consumers.MemberInvoiceGroupProcessedConsumer
import br.com.alice.moneyin.consumers.NullvsFirstPaymentConsumer
import br.com.alice.moneyin.consumers.NullvsMemberInvoiceGroupCreatedConsumer
import br.com.alice.moneyin.consumers.PaymentNotificationConsumer
import br.com.alice.moneyin.consumers.PersonUpdatedConsumer
import br.com.alice.moneyin.consumers.PreActivationPaymentConsumer
import br.com.alice.moneyin.controllers.BackfillController
import br.com.alice.moneyin.controllers.InvoiceDetailsController
import br.com.alice.moneyin.controllers.InvoicePaymentSecondCopyController
import br.com.alice.moneyin.controllers.RecurrentController
import br.com.alice.moneyin.notification.InvoiceNotifier
import br.com.alice.moneyin.notification.PaymentNotifier
import br.com.alice.moneyin.services.*
import br.com.alice.moneyin.services.internal.AcquirerOrchestratorService
import br.com.alice.moneyin.services.internal.BillingAccountablePartyIdService
import br.com.alice.moneyin.services.internal.ItauPaymentServiceProxy
import br.com.alice.moneyin.services.internal.IuguPaymentService
import br.com.alice.moneyin.services.internal.MemberInvoiceGroupFirstPaymentServiceImpl
import br.com.alice.moneyin.services.internal.preactivation.PreActivationCompanyInvoiceMemberInvoiceGroupMethod
import br.com.alice.moneyin.services.internal.preactivation.PreActivationCompanyInvoiceMethod
import br.com.alice.moneyin.services.internal.preactivation.PreActivationCompanyInvoicePreActivationPaymentMethod
import br.com.alice.moneyin.subscribers.IuguNotificationsSubscriber
import io.ktor.client.HttpClient
import io.ktor.client.engine.apache.Apache
import io.ktor.client.engine.apache5.Apache5
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module


val ServiceModule = module(createdAtStart = true) {

    // Clients
    single { HttpClient() }
    single {
        IuguPaymentClient(
            ServiceConfig.Payment.Iugu.baseUrl,
            ServiceConfig.Payment.Iugu.apiKey,
            iuguHttpConfig()
        )
    }
    single { IuguBankSlipClient(iuguBankSlipClientHttpConfig()) }

    // Communication
    single { InvoiceMailer(get(), get(), get(), get(), get(), get()) }
    singleOf(::BillingAccountablePartyMailer)

    // Notification
    singleOf(::InvoiceNotifier)
    singleOf(::PaymentNotifier)

    // Internal Services
    singleOf(::PaymentService)
    singleOf(::AcquirerOrchestratorService)
    singleOf(::IuguPaymentService)
    singleOf(::InvoicePaymentProcessor)
    singleOf(::PaymentDetailService)
    singleOf(::BillingAccountablePartyIdService)
    singleOf(::PreActivationCompanyInvoicePreActivationPaymentMethod) bind PreActivationCompanyInvoiceMethod::class
    singleOf(::PreActivationCompanyInvoiceMemberInvoiceGroupMethod) bind PreActivationCompanyInvoiceMethod::class
    singleOf(::ItauPaymentServiceProxy) bind ItauPaymentServiceProxy::class
    singleOf(::MemberInvoiceGroupFirstPaymentServiceImpl)

    // Exposed Services
    singleOf(::InvoicePaymentServiceImpl) bind InvoicePaymentService::class
    singleOf(::InvoicesServiceImpl) bind InvoicesService::class
    singleOf(::MoneyInResourceSignTokenServiceImpl) bind MoneyInResourceSignTokenService::class
    singleOf(::InvoiceItemServiceImpl) bind InvoiceItemService::class
    singleOf(::BillingAccountablePartyServiceImpl) bind BillingAccountablePartyService::class
    singleOf(::InvoiceLiquidationServiceImpl) bind InvoiceLiquidationService::class
    singleOf(::MemberInvoiceGroupServiceImpl) bind MemberInvoiceGroupService::class
    singleOf(::MemberInvoiceGroupQueryServiceImpl) bind MemberInvoiceGroupQueryService::class
    singleOf(::FinancialDataServiceImpl) bind FinancialDataService::class
    singleOf(::CancelPaymentOnAcquirerScheduleServiceImpl) bind CancelPaymentOnAcquirerScheduleService::class
    singleOf(::InvoicePdfServiceImpl) bind InvoicePdfService::class
    singleOf(::PreActivationPaymentServiceImpl) bind PreActivationPaymentService::class
    singleOf(::CompanyInvoiceServiceImpl) bind CompanyInvoiceService::class
    singleOf(::FirstPaymentScheduleServiceImpl) bind FirstPaymentScheduleService::class
    single<PreActivationCompanyInvoiceService> { PreActivationCompanyInvoiceServiceImpl(getAll()) }
    singleOf(::CompanyInvoiceDetailServiceImpl) bind CompanyInvoiceDetailService::class
    singleOf(::PortalUrlGeneratorServiceImpl) bind PortalUrlGeneratorService::class

    // Servers
    loadServiceServers("br.com.alice.moneyin.services")

    // TODO: Must delete after the migration to the new service
    single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000)) }
    single<ResourceSignTokenModelDataService> { ResourceSignTokenModelDataServiceClient(get()) }
    single { ResourceSignTokenServiceImpl(get()) }

    // Controllers
    single { HealthController(SERVICE_NAME) }
    singleOf(::IuguNotificationsSubscriber)
    singleOf(::InvoiceDetailsController)
    singleOf(::BackfillController)
    singleOf(::RecurrentController)
    singleOf(::InvoicePaymentSecondCopyController)

    // Consumers
    singleOf(::MemberCreatedConsumer)
    singleOf(::NullvsMemberInvoiceGroupCreatedConsumer)
    singleOf(::PersonUpdatedConsumer)
    singleOf(::AcquirerPaymentNotificationConsumer)
    singleOf(::BillingAccountablePartyAssignedConsumer)
    singleOf(::CreateInvoicePaymentConsumer)
    singleOf(::CreateExternalPaymentConsumer)
    singleOf(::ExternalInvoiceCreatedConsumer)
    singleOf(::InvoiceConsumer)
    singleOf(::PaymentNotificationConsumer)
    singleOf(::InvoicePaymentConsumer)
    singleOf(::InvoicesNearOverdueJobConsumer)
    singleOf(::MemberInvoiceGroupConsumer)
    singleOf(::MemberInvoiceGroupProcessedConsumer)
    singleOf(::PreActivationPaymentConsumer)
    singleOf(::NullvsFirstPaymentConsumer)
    singleOf(::FirstPaymentScheduleConsumer)

    // Hubspot configuration
    single<BusinessSalesCrmPipeline> {
        val client = if (ServiceConfig.isProduction && ServiceConfig.environment() != RunningMode.TEST)
            HubspotClientImpl(
                ServiceConfig.Crm.hubspotConfig(),
                Apache.create {
                    customizeClient {
                        setMaxConnTotal(2000)
                        setMaxConnPerRoute(200)
                    }
                }) else HubspotClientLocal()

        BusinessHubspotSalesCrmPipeline(client)
    }

    single<HubspotClient> {
        if (ServiceConfig.isProduction && ServiceConfig.environment() != RunningMode.TEST)
            HubspotClientImpl(
                ServiceConfig.Crm.hubspotConfig(),
                Apache5.create()
            ) else HubspotClientLocal()
    }
}
