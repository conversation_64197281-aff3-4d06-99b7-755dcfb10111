package br.com.alice.moneyin.consumers

import br.com.alice.business.client.CompanyService
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.foldError
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClient
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotCompanyDeal
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.communication.crm.sales.b2b.DealResult
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.moneyin.client.InvoicePaymentDetailsNotFoundException
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.event.ExternalInvoiceCreatedEvent
import br.com.alice.moneyin.model.InvalidPaymentMethodAndReasonException
import br.com.alice.moneyin.services.PaymentDetailService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import java.util.UUID

class ExternalInvoiceCreatedConsumer(
    private val paymentDetailService: PaymentDetailService,
    private val invoiceMailer: InvoiceMailer,
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline,
    private val companyService: CompanyService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePaymentService: InvoicePaymentService,
    private val hubspotClient: HubspotClient,
    private val portalUrlGeneratorService: PortalUrlGeneratorService
) : Consumer() {

    suspend fun createPaymentDetailAndSendEmail(event: ExternalInvoiceCreatedEvent) = withSubscribersEnvironment {
        logger.info(
            "ExternalInvoiceCreatedEvent received - create paymentDetail and send email",
            "event" to event
        )

        val invoicePayment = event.payload.invoicePayment

        invoicePayment.createPaymentDetail()
            .flatMap {
                if (invoicePayment.sendEmail == null || invoicePayment.sendEmail == true) {
                    sendEmail(invoicePayment.withPaymentDetail(it)).then {
                        logger.info(
                            "ExternalInvoiceCreatedEvent processed successfully!!",
                            "invoice_payment" to event.payload.invoicePayment,
                        )
                    }.thenError {
                        logger.info(
                            "ExternalInvoiceCreatedEvent processed with problems :(",
                            "invoice_payment" to event.payload.invoicePayment,
                            it
                        )
                    }.coFoldError(
                        InvoicePaymentNotProcessedToSendEmailException::class to br.com.alice.common.core.suspend() { it ->
                            logger.info("Ignoring consume event due a reason", "reason" to it)
                            true.success()
                        }
                    )
                } else {
                    logger.info(
                        "Send email was skipped", "invoice_payment_id" to invoicePayment.id,
                    )
                    it.success()
                }
            }.foldError(InvalidPaymentMethodAndReasonException::class to {
                logger.info("Ignoring consume event due a reason", "reason" to it)
                true.success()
            })
    }

    suspend fun updateHubspotWithPaymentUrl(event: ExternalInvoiceCreatedEvent) = withSubscribersEnvironment {
        val invoicePayment = event.payload.invoicePayment

        if (invoicePayment.reason != PaymentReason.B2B_FIRST_PAYMENT) return@withSubscribersEnvironment emptyList<DealResult>().success()

        logger.info(
            "ExternalInvoiceCreatedEvent received - update Hubspot with payment URL",
            "invoice_payment" to event.payload.invoicePayment
        )

        val invoiceGroupId = when (invoicePayment.invoiceGroupId) {
            null -> {
                invoicePaymentService.get(invoicePayment.id).get().invoiceGroupId
            }

            else -> {
                invoicePayment.invoiceGroupId
            }
        }

        val memberInvoiceGroup = memberInvoiceGroupService.get(invoiceGroupId!!).get()

        val company = companyService.get(memberInvoiceGroup.companyId!!).get()

        val companyHubspot = businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)

        val linkBoleto = portalUrlGeneratorService.mountPortalUrl(invoicePayment.id).get()

        if (companyHubspot.results.isNotEmpty()) {
            logger.info(
                "Deals to update with payment URL found in Hubspot",
                "deals" to companyHubspot.results,
                "link_boleto" to linkBoleto,
            )
            val updatedDeals = companyHubspot.results
                .map {
                    hubspotClient.updateCompanyDeal(
                        it.id,
                        HubspotCompanyDeal(link_do_boleto_empresarial = linkBoleto)
                    )
                }

            logger.info("Deals updated successfully", "deals" to updatedDeals)
            updatedDeals.success()
        } else emptyList<DealResult>().success()
    }

    private suspend fun InvoicePayment.createPaymentDetail() = this.validateToCreateDetail()
        .flatMap { paymentDetailService.createPaymentDetail(it.paymentDetail!!) }
        .coFoldDuplicated {
            logger.info("Payment detail is already stored in database", "invoice_payment" to this)
            paymentDetailService.getPaymentDetail(this)
        }

    private suspend fun sendEmail(invoicePayment: InvoicePayment): Result<EmailReceipt, Throwable> =
        invoicePayment.validateToSendEmail()
            .flatMap { it.validateOverdue() }
            .flatMap { it.validatePaymentMethod() }
            .flatMap { invoiceMailer.send(it, it.reason!!) }

    private fun InvoicePayment.validateToCreateDetail(): Result<InvoicePayment, Throwable> {
        return if (this.paymentDetail == null) InvoicePaymentDetailsNotFoundException(this.id).failure()
        else this.success()
    }

    private fun InvoicePayment.validateToSendEmail(): Result<InvoicePayment, Throwable> = when {
        this.paymentDetail == null -> InvoicePaymentDetailsNotFoundException(this.id).failure()
        this.reason == null -> InvoicePaymentNotProcessedToSendEmailException(this.id, "null reason").failure()
        else -> this.success()
    }

    private fun InvoicePayment.validateOverdue(): Result<InvoicePayment, Throwable> {
        val reason = this.reason
        logger.info(
            "Checking flag ignore_overdue_payment_email for payment reason", "payment_reason" to reason
        )
        val ignoreOverdue = FeatureService.get(FeatureNamespace.PAYMENTS, "ignore_overdue_payment_email", false)

        val shouldIgnore = ignoreOverdue && reason == PaymentReason.OVERDUE_PAYMENT

        return if (shouldIgnore) InvoicePaymentNotProcessedToSendEmailException(this.id, "ignoreOverdue").failure()
        else this.success()
    }

    private fun InvoicePayment.validatePaymentMethod(): Result<InvoicePayment, Throwable> {
        val allowedPaymentMethods = listOf(PaymentMethod.PIX, PaymentMethod.BOLETO, PaymentMethod.BOLEPIX)
        return if (allowedPaymentMethods.contains(this.method)) this.success()
        else InvoicePaymentNotProcessedToSendEmailException(this.id, "paymentMethod").failure()
    }

    class InvoicePaymentNotProcessedToSendEmailException(
        message: String,
        code: String = "invoice_payment_not_processed_to_send_email",
        cause: Throwable? = null
    ) : BadRequestException(message, code, cause) {
        constructor(invoicePaymentId: UUID, reason: String) : this(
            message = "InvoicePayment $invoicePaymentId was not processed to send email due internal logic. Reason: $reason"
        )
    }
}
