package br.com.alice.moneyin.consumers

import br.com.alice.common.logging.logger
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.nullvs.events.NullvsScheduleFirstPaymentResponseEvent
import com.github.kittinunf.result.flatMap

class FirstPaymentScheduleConsumer(
    private val firstPaymentScheduleService: FirstPaymentScheduleService,
) : Consumer() {

    suspend fun updateFromNullvs(event: NullvsScheduleFirstPaymentResponseEvent) = withSubscribersEnvironment {
        val firstPaymentScheduleId = event.payload.firstPaymentScheduleId
        val externalId = event.payload.externalId
        val error = event.payload.error

        logger.info(
            "FirstPaymentScheduleConsumer::updateFromNullvs received - NullvsScheduleFirstPaymentResponseEvent",
            "first_payment_schedule_id" to firstPaymentScheduleId,
            "external_id" to externalId,
            "error" to error,
        )

        firstPaymentScheduleService.get(firstPaymentScheduleId)
            .flatMap { firstPaymentSchedule ->
                val firstPaymentScheduleUpdated = externalId?.let {
                    firstPaymentSchedule.markAsScheduled(it)
                } ?: error?.let { firstPaymentSchedule.markAsFailure(it) }
                ?: throw FirstPaymentScheduleUnknownStateException("This schedule does not have externalId or error")

                firstPaymentScheduleService.update(firstPaymentScheduleUpdated)
            }
    }
}
