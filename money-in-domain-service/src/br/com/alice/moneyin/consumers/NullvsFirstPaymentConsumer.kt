package br.com.alice.moneyin.consumers

import br.com.alice.common.logging.logger
import br.com.alice.moneyin.services.internal.MemberInvoiceGroupFirstPaymentServiceImpl
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentFailedEvent

class NullvsFirstPaymentConsumer(
    private val memberInvoiceGroupFirstPaymentServiceImpl: MemberInvoiceGroupFirstPaymentServiceImpl
) : Consumer() {
    suspend fun createMemberInvoiceGroupForFirstPayment(event: NullvsFirstPaymentCreatedEvent) =
        withSubscribersEnvironment {
            logger.info("NullvsFirstPaymentCreatedEvent received")
            memberInvoiceGroupFirstPaymentServiceImpl.createFromEvent(event)
        }

    suspend fun markFirstPaymentScheduleAsFailureFromEvent(event: NullvsFirstPaymentFailedEvent) =
        withSubscribersEnvironment {
            logger.info("NullvsFirstPaymentFailedEvent received")
            memberInvoiceGroupFirstPaymentServiceImpl.markFirstPaymentScheduleAsFailureFromEvent(event)
        }
}
