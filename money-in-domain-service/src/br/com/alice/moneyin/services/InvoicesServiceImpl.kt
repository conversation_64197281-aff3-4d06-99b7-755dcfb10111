package br.com.alice.moneyin.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.money
import br.com.alice.common.core.extensions.plusSafe
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberInvoiceModel
import br.com.alice.data.layer.models.MemberInvoicePriceType
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.withInvoicePayments
import br.com.alice.data.layer.services.MemberInvoiceGroupModelDataService
import br.com.alice.data.layer.services.MemberInvoiceModelDataService
import br.com.alice.membership.client.PersonPreferencesService
import br.com.alice.moneyin.builder.MemberInvoiceBuilder
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceAlreadyPaidException
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.OverdueInvoiceException
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import br.com.alice.moneyin.notification.InvoiceNotifier
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class InvoicesServiceImpl(
    private val memberInvoiceDataService: MemberInvoiceModelDataService,
    private val invoiceNotifier: InvoiceNotifier,
    private val invoicePaymentService: InvoicePaymentService,
    private val invoiceItemService: InvoiceItemService,
    private val memberService: MemberService,
    private val personService: PersonService,
    private val personPreferencesService: PersonPreferencesService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val paymentDetailService: PaymentDetailService,
    private val memberInvoiceGroupDataService: MemberInvoiceGroupModelDataService,
) : InvoicesService {

    companion object {
        const val CHUNK_SIZE = 50
    }

    override suspend fun get(id: UUID, withPayments: Boolean): Result<MemberInvoice, Throwable> =
        memberInvoiceDataService.get(id)
            .flatMap { memberInvoice ->
                listOf(memberInvoice).withPayments(withPayments).map { it.first() }
            }.map { it.toTransport() }

    private suspend fun List<MemberInvoiceModel>.withPayments(withPayments: Boolean) = if (withPayments) {
        fetchPayments(this)
    } else {
        this.success()
    }

    override suspend fun listInvoices(memberId: UUID, withPayments: Boolean) = memberInvoiceDataService.find {
        where { this.memberId.eq(memberId) }.orderBy { this.referenceDate }.sortOrder { desc }
    }.flatMap { it.withPayments(withPayments) }
        .mapEach { it.toTransport() }

    override suspend fun listInvoiceByPerson(personId: PersonId, withPayments: Boolean) =
        memberInvoiceDataService.find {
            where { this.personId.eq(personId) }.orderBy { this.referenceDate }.sortOrder { desc }
        }.flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun getFromLastFewMonthsByPersonId(personId: PersonId, limit: Int, withPayments: Boolean) =
        listInvoiceByPerson(personId, withPayments).map { filterMemberInvoicesByStatus(it, limit) }

    private fun filterMemberInvoicesByStatus(memberInvoices: List<MemberInvoice>, limit: Int): List<MemberInvoice> =
        memberInvoices
            .filter { it.memberInvoiceGroupId != null }
            .groupBy { it.referenceDate }
            .entries.take(limit)
            .map { entry ->
                entry.value.sortedWith(
                    compareBy<MemberInvoice> {
                        it.status.getPriorityStatus()
                    }.thenByDescending { it.createdAt }
                ).first()
            }

    private fun InvoiceStatus.getPriorityStatus() =
        when (this) {
            InvoiceStatus.PAID -> 1
            InvoiceStatus.OPEN -> 2
            InvoiceStatus.CANCELED -> 3
            InvoiceStatus.FAILED -> 4
            else -> Int.MAX_VALUE
        }

    override suspend fun listInvoiceByPersons(personIds: List<PersonId>): Result<List<MemberInvoice>, Throwable> =
        memberInvoiceDataService.find { where { this.personId.inList(personIds) } }
            .mapEach { it.toTransport() }

    override suspend fun listByPersonAndStatuses(
        personId: PersonId,
        statuses: List<InvoiceStatus>,
        withPayments: Boolean
    ): Result<List<MemberInvoice>, Throwable> =
        memberInvoiceDataService.find {
            where {
                this.personId.eq(personId)
                    .and(this.status.inList(statuses))
            }.orderBy { this.referenceDate }.sortOrder { desc }
        }.flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listByMemberInvoiceGroupId(memberInvoiceGroupId: UUID, withPayments: Boolean) =
        memberInvoiceDataService.find { where { this.memberInvoiceGroupId.eq(memberInvoiceGroupId) } }
            .flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listByMemberInvoiceGroupIds(memberInvoiceGroupIds: List<UUID>, withPayments: Boolean) =
        memberInvoiceDataService.find { where { this.memberInvoiceGroupId.inList(memberInvoiceGroupIds) } }
            .flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listByMemberInvoiceGroupIdsPaginated(
        memberInvoiceGroupIds: List<UUID>,
        withPayments: Boolean,
        offset: Int,
        limit: Int,
    ) =
        memberInvoiceDataService.find {
            where { this.memberInvoiceGroupId.inList(memberInvoiceGroupIds) }
                .offset { offset }
                .limit { limit }
        }
            .flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listByPreActivationPaymentId(preActivationPaymentId: UUID, withPayments: Boolean) =
        memberInvoiceDataService.find { where { this.preActivationPaymentId.eq(preActivationPaymentId) } }
            .flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listByPreActivationPaymentIds(preActivationPaymentIds: List<UUID>, withPayments: Boolean) =
        memberInvoiceDataService.find { where { this.preActivationPaymentId.inList(preActivationPaymentIds) } }
            .flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listByPreActivationPaymentIdsPaginated(
        preActivationPaymentIds: List<UUID>,
        withPayments: Boolean, offset: Int,
        limit: Int,
    ) =
        memberInvoiceDataService.find {
            where { this.preActivationPaymentId.inList(preActivationPaymentIds) }
                .offset { offset }
                .limit { limit }
        }
            .flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun listNearOverdueInvoices(
        nearOverdueDate: LocalDateTime,
        initialDate: LocalDateTime,
        memberInvoiceType: MemberInvoiceType?
    ): Result<List<MemberInvoice>, Throwable> {
        return memberInvoiceDataService.find {
            where {
                this.status.eq(InvoiceStatus.OPEN)
                    .and(memberInvoiceType?.let { this.type.eq(it) })
                    .and(this.dueDate.greater(initialDate))
                    .and(this.dueDate.lessEq(nearOverdueDate))

            }
        }.mapEach { it.toTransport() }
    }

    private fun validateInvoice(memberInvoice: MemberInvoice): Result<MemberInvoice, Throwable> {
        logger.info("Validating new invoice", "invoice" to memberInvoice)

        if (memberInvoice.isOverdue) {
            val exception = OverdueInvoiceException("Invoice is already overdue")
            logger.info("Could not create an invoice", "reason" to exception.message, "invoice" to memberInvoice)
            return exception.failure()
        }

        return memberInvoice.success()
    }

    override suspend fun validateInvoices(memberInvoices: List<MemberInvoice>) =
        resultOf<List<MemberInvoice>, Throwable> {
            memberInvoices.firstOrNull { it.isOverdue }
                ?.let { throw OverdueInvoiceException("Invoice is already overdue") } ?: memberInvoices
        }


    override suspend fun findInvoicesByIds(
        memberInvoiceIds: List<UUID>,
        withPayments: Boolean
    ): Result<List<MemberInvoice>, Throwable> =
        memberInvoiceDataService.find {
            where { this.id.inList(memberInvoiceIds) }.orderBy { this.referenceDate }.sortOrder { desc }
        }.flatMap { it.withPayments(withPayments) }
            .mapEach { it.toTransport() }

    override suspend fun createInvoice(
        memberInvoice: MemberInvoice,
        skipValidation: Boolean
    ): Result<MemberInvoice, Throwable> =
        if (skipValidation) {
            memberInvoice.success()
        } else {
            validateInvoice(memberInvoice)
        }
            .flatMap { memberInvoiceDataService.add(it.toModel()) }
            .map { it.toTransport() }
            .then { invoiceNotifier.publishNewInvoice(it) }

    override suspend fun createInvoices(
        memberInvoices: List<MemberInvoice>,
    ) =
        validateInvoices(memberInvoices).map {
            it
                .chunked(CHUNK_SIZE)
                .pmap { invoices ->
                    memberInvoiceDataService.addList(invoices.map { it.toModel() })
                        .andThen {
                            it.map { invoice -> invoiceNotifier.publishNewInvoice(invoice.toTransport()) }
                            it.success()
                        }
                        .thenError {
                            logger.error("Error saving member invoice expenses", "error" to it)
                        }.get()
                }.flatten()
        }.mapEach { it.toTransport() }

    override suspend fun addList(memberInvoices: List<MemberInvoice>) = memberInvoices.chunked(CHUNK_SIZE)
        .pmap { invoices ->
            memberInvoiceDataService.addList(invoices.map { it.toModel() })
                .andThen {
                    it.map { invoice -> invoiceNotifier.publishNewInvoice(invoice.toTransport()) }
                    it.success()
                }
        }
        .lift()
        .map { it.flatten() }
        .mapEach { it.toTransport() }

    override suspend fun update(memberInvoice: MemberInvoice) =
        memberInvoiceDataService.update(memberInvoice.toModel()).map { it.toTransport() }

    override suspend fun issueInvoice(
        memberId: UUID,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paymentMethod: PaymentMethod,
        withPayment: Boolean,
        type: MemberInvoiceType,
    ): Result<MemberInvoice, Throwable> = memberService.get(memberId).flatMap { member ->
        logger.info(
            "Start issuing invoice for member",
            "member_id" to memberId,
            "reference_date" to referenceDate,
            "due_date" to dueDate,
            "payment_method" to paymentMethod,
            "with_payment" to withPayment,
            "type" to type,
        )

        val sameReferenceDateInvoice = findBy(memberId, referenceDate, listOf(InvoiceStatus.OPEN, InvoiceStatus.PAID))

        if (sameReferenceDateInvoice != null)
            return sameReferenceDateInvoice.toTransport().success()

        generate(member, referenceDate, dueDate, paymentMethod, type, MemberInvoicePriceType.PRO_RATA)
            .flatMap {
                logger.info("MemberInvoice generated for member", "invoice" to it)
                processInvoice(it, withPayment, member, paymentMethod)
            }
    }

    private suspend fun processInvoice(
        invoice: MemberInvoice,
        withPayment: Boolean,
        member: Member,
        paymentMethod: PaymentMethod,
    ): Result<MemberInvoice, Throwable> {
        logger.info(
            "Processing b2c invoice",
            "invoice" to invoice,
            "with_payment" to withPayment,
            "member" to member,
            "payment_method" to paymentMethod,
        )
        val billingAccountableParty = billingAccountablePartyService.getCurrentOrCreateForPerson(invoice.personId).get()
        val isFirstInvoice = invoice.type == MemberInvoiceType.FIRST_PAYMENT
        val paymentReason = if (isFirstInvoice) PaymentReason.FIRST_PAYMENT else PaymentReason.REGULAR_PAYMENT

        return createInvoice(invoice)
            .andThen {
                createMemberInvoiceGroup(it, billingAccountableParty)
            }.andThen {
                createPayment(withPayment, it, paymentMethod, paymentReason, billingAccountableParty)
            }
    }

    private suspend fun createMemberInvoiceGroup(
        invoiceCreated: MemberInvoice,
        billingAccountableParty: BillingAccountableParty
    ): Result<Any, Throwable> {
        val memberInvoiceGroup = MemberInvoiceGroup(
            externalId = null, //will be provided by Totvs later
            memberInvoiceIds = listOf(invoiceCreated.id),
            billingAccountablePartyId = billingAccountableParty.id,
            referenceDate = invoiceCreated.referenceDate,
            dueDate = invoiceCreated.dueDate.toLocalDate(),
            status = MemberInvoiceGroupStatus.PROCESSING,
            type = invoiceCreated.type,
            totalAmount = invoiceCreated.totalAmount.money,
        )

        return memberInvoiceGroupDataService.add(memberInvoiceGroup.toModel())
            .then {
                logger.info("MemberInvoiceGroup created", "member_invoice_group_id" to it.id)
                memberInvoiceDataService.update(invoiceCreated.toModel().copy(memberInvoiceGroupId = it.id))
            }
    }

    private suspend fun getPrePaymentProrationItem(
        member: Member,
        isProRata: Boolean,
        productPriceInvoiceItem: InvoiceItem,
        referenceDate: LocalDate,
        paymentMethod: PaymentMethod
    ): Result<List<InvoiceItem>, Throwable> = coResultOf {
        if (isProRata)
            listOf(
                invoiceItemService.createFirstProrationInvoiceItem(
                    member.personId,
                    productPriceInvoiceItem.value,
                    referenceDate,
                    paymentMethod
                ).get()
            )
        else
            emptyList()
    }

    private suspend fun createPayment(
        createPayment: Boolean,
        invoice: MemberInvoice,
        paymentMethod: PaymentMethod,
        paymentReason: PaymentReason,
        billingAccountableParty: BillingAccountableParty
    ): Result<Any, Throwable> = if (createPayment) {
        logger.info(
            "MemberInvoice should be created with payment info",
            "member_info" to invoice,
            "payment_method" to paymentMethod,
            "payment_reason" to paymentReason,
            "billing_accountable_party" to billingAccountableParty
        )

        val reason = invoice.type?.toPaymentReason() ?: paymentReason

        invoiceNotifier.publishCreateInvoicePaymentRequestEvent(
            invoice,
            paymentMethod,
            reason,
            invoice.dueDate,
            billingAccountableParty,
        )
    } else {
        logger.info(
            "No need to create payment for memberInvoice",
            "member_info" to invoice,
            "payment_method" to paymentMethod,
            "payment_reason" to paymentReason,
            "billing_accountable_party" to billingAccountableParty
        )
        false.success()
    }

    override suspend fun issueFirstInvoice(member: Member): Result<Member, Throwable> {
        val preferredPaymentMethod = personPreferredFirstPaymentMethod(member.personId)
        val referenceDate =
            LocalDate
                .now()
                .atBeginningOfTheMonth()

        return shouldIssueFirstInvoiceForMember(member, preferredPaymentMethod)
            .map { should ->
                if (should) {
                    issueInvoice(
                        memberId = member.id,
                        referenceDate = referenceDate,
                        dueDate = firstInvoiceDueDate(preferredPaymentMethod),
                        paymentMethod = preferredPaymentMethod,
                        type = MemberInvoiceType.FIRST_PAYMENT,
                    )
                }

                member
            }
    }

    private fun firstInvoiceDueDate(preferredPaymentMethod: PaymentMethod): LocalDateTime {
        val now = LocalDate.now()

        return if (preferredPaymentMethod == PaymentMethod.PIX) now.atEndOfTheDay()
        else now.atEndOfTheMonth().atEndOfTheDay()
    }

    private suspend fun findBy(memberId: UUID, referenceDate: LocalDate?, status: List<InvoiceStatus>) =
        memberInvoiceDataService.findOneOrNull {
            where {
                this.memberId.eq(memberId)
                    .and(this.status.inList(status.map { InvoiceStatus.valueOf(it.name) }))
                    .and(referenceDate?.let { this.referenceDate.eq(it) })
            }
        }

    override suspend fun markAsPaid(memberInvoiceId: UUID, paidAt: LocalDateTime?): Result<MemberInvoice, Throwable> =
        memberInvoiceDataService.get(memberInvoiceId).flatMap { memberInvoice ->
            if (memberInvoice.alreadyPaid) return memberInvoice.toTransport().success()

            logger.info(
                "InvoicesService::markAsPaid",
                "id" to memberInvoiceId,
                "paid_at" to paidAt,
            )

            val paidMemberInvoice = memberInvoice
                .markAsPaid(paidAt)
                .changeReferenceDate(paidAt)

            memberInvoiceDataService.update(paidMemberInvoice)
                .then { logger.info("invoice paid", "invoice" to it) }
                .map { it.toTransport() }
                .then { invoiceNotifier.publishPaidInvoice(it) }
        }

    override suspend fun markInvoicesAsPaidByLiquidation(memberInvoiceIds: List<UUID>): Result<List<MemberInvoice>, Throwable> =
        memberInvoiceDataService.find {
            where { this.id.inList(memberInvoiceIds) }
        }.pmapEach {
            if (it.isPaidByLiquidation)
                it
            else
                memberInvoiceDataService.update(it.markAsPaidByLiquidation()).get()
        }.mapEach { it.toTransport() }

    override suspend fun cancel(
        invoiceId: UUID,
        cancellationReason: CancellationReason,
        forceCancellation: Boolean
    ): Result<MemberInvoice, Throwable> {
        return memberInvoiceDataService.get(invoiceId).flatMap { invoice ->
            if (invoice.wasCanceled) return Result.success(invoice.toTransport())
            if (invoice.alreadyPaid && !forceCancellation) return Result.failure(InvoiceAlreadyPaidException("Invoice '$invoiceId' is already paid"))

            val canceledInvoice = invoice.cancel(cancellationReason)

            memberInvoiceDataService.update(canceledInvoice)
                .then { logger.info("invoice canceled", "invoice" to it) }
                .map { it.toTransport() }
        }
    }

    private suspend fun fetchPayments(memberInvoices: List<MemberInvoiceModel>): Result<List<MemberInvoiceModel>, Throwable> {
        val memberInvoicesIds = memberInvoices.map { it.id }
        val payments =
            if (memberInvoicesIds.isNotEmpty()) {
                logger.info("Fetch payments for member invoices", "member_invoices" to memberInvoicesIds)
                invoicePaymentService.listInvoicePaymentsByInvoicesIds(memberInvoicesIds, withPaymentDetails = true)
                    .get()
            } else
                emptyList()

        return memberInvoices
            .map { memberInvoice -> Pair(memberInvoice, payments) }
            .map { (memberInvoice, payments) ->
                memberInvoice.withInvoicePayments(payments.map { it.toModel() }.filter {
                    it.memberInvoiceIds.contains(
                        memberInvoice.id
                    )
                })
            }
            .success()
    }

    private suspend fun personPreferredFirstPaymentMethod(personId: PersonId): PaymentMethod {
        val paymentPreferences = personPreferencesService.findByPersonId(personId).getOrNullIfNotFound()

        return paymentPreferences?.firstPaymentMethod ?: PaymentMethod.PIX
    }

    private suspend fun shouldIssueFirstInvoiceForMember(
        member: Member,
        preferredPaymentMethod: PaymentMethod,
    ): Result<Boolean, Throwable> = coResultOf {
        coroutineScope {
            val personDeferred = async { personService.get(member.personId).get() }

            val person = personDeferred.await()

            when {
                person.isInternal -> {
                    logger.info(
                        "Member invoice was not issued, because person ${person.id} is INTERNAL",
                        "person_id" to person.id,
                    )
                    false
                }

                person.hasManualPaymentsTag -> {
                    logger.info(
                        "Member invoice was not issued, because person ${person.id} has manual_payments tag",
                        "person_id" to person.id,
                    )
                    false
                }

                preferredPaymentMethod != PaymentMethod.BOLETO && preferredPaymentMethod != PaymentMethod.PIX -> {
                    logger.info(
                        "Skipping first invoice issue since the preferred payment method isn't boleto or pix",
                        "person_id" to person.id,
                    )
                    false
                }

                member.isB2BOrAdesao -> {
                    logger.info(
                        "Skipping first invoice issue since member's product isn't B2C",
                        "person_id" to person.id,
                        "member_id" to member.id,
                        "type" to member.productType,
                    )
                    false
                }

                else -> true
            }
        }
    }

    override suspend fun findInvoiceAndPaymentsByPersonId(
        personId: PersonId,
        billingAccountablePartyId: UUID
    ): Result<Pair<List<InvoicePayment?>, MemberInvoice?>, Throwable> {
        val memberInvoice = memberInvoiceDataService.findOneOrNull {
            where {
                this.personId.eq(personId)
            }
        }
        val payments =
            invoicePaymentService.listInvoicePaymentsByBillingAccountablePartyId(billingAccountablePartyId).get()

        val paymentWithDetail = if (payments.isNotEmpty()) {
            val payment = payments[0]
            listOf(
                payment.withPaymentDetail(
                    when {
                        payment.isPix ||
                                payment.isBoleto ||
                                payment.isCreditCard -> paymentDetailService.getPaymentDetail(payment)
                            .getOrNullIfNotFound()

                        else -> null
                    }
                )
            )
        } else {
            payments
        }

        return (paymentWithDetail to memberInvoice?.toTransport()).success()
    }

    override suspend fun generateForB2B(
        subcontract: CompanySubContract,
        members: List<Member>,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paymentMethod: PaymentMethod,
        type: MemberInvoiceType,
        priceType: MemberInvoicePriceType
    ) = coroutineScope {
        coResultOf<List<MemberInvoice>, Throwable> {
            val invoiceItemsGroupedByPersonId =
                invoiceItemService.generateProductInvoiceItemForB2B(
                    members,
                    subcontract.companyId,
                    subcontract.id,
                    referenceDate,
                ).map { items -> items.associateBy { it.personId } }
                    .get()

            val activeInvoiceItemsDeferred = async {
                invoiceItemService.listActiveInvoiceItemsByPersonIds(
                    members.map { it.personId }, referenceDate
                ).map { items ->
                    items.groupBy { it.personId }
                }.getOrElse { emptyMap() }
            }

            val isProRata = priceType == MemberInvoicePriceType.PRO_RATA

            val prorationItemsDeferred = async {
                members.map { member ->
                    val productPrice = invoiceItemsGroupedByPersonId.getValue(member.personId).value

                    member.personId to productPrice
                }.run {
                    getPrePaymentProrationBatch(this, isProRata, referenceDate, paymentMethod)
                        .map { items -> items.associateBy { it.personId!! } }
                        .get()
                }
            }

            val activeInvoiceItems = activeInvoiceItemsDeferred.await()
            val prorationItems = prorationItemsDeferred.await()

            members.map { member ->
                val productItem = invoiceItemsGroupedByPersonId.getValue(member.personId)
                val prorationItem = prorationItems[member.personId]
                val activeItems = activeInvoiceItems.get(member.personId)

                val invoiceBreakdown =
                    listOfNotNull(productItem, prorationItem)
                        .plusSafe(activeItems)
                        .toInvoiceBreakdown()
                        .get()

                MemberInvoiceBuilder.build(
                    member = member,
                    referenceDate = referenceDate,
                    dueDate = dueDate,
                    invoiceBreakdown = invoiceBreakdown,
                    invoiceItems = invoiceBreakdown.invoiceItems,
                    type = type,
                )
            }
        }
    }

    private suspend fun getPrePaymentProrationBatch(
        items: List<Pair<PersonId, BigDecimal>>,
        isProRata: Boolean,
        referenceDate: LocalDate,
        paymentMethod: PaymentMethod
    ): Result<List<InvoiceItem>, Throwable> = coResultOf {
        if (isProRata)
            invoiceItemService.createProrationInBatch(
                items,
                referenceDate,
                paymentMethod
            ).get()
        else
            emptyList()
    }

    override suspend fun generate(
        member: Member,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paymentMethod: PaymentMethod,
        type: MemberInvoiceType,
        priceType: MemberInvoicePriceType
    ): Result<MemberInvoice, Throwable> = coroutineScope {
        coResultOf {
            val activeInvoiceItemsDeferred = async {
                invoiceItemService.listActiveInvoiceItems(member.personId, referenceDate).getOrElse { emptyList() }
            }
            val productPriceInvoiceItemDeferred =
                async { invoiceItemService.generateProductInvoiceItem(member.id, referenceDate).get() }

            val activeInvoiceItems = activeInvoiceItemsDeferred.await()
            val productPriceInvoiceItem = productPriceInvoiceItemDeferred.await()

            val isProRata = priceType == MemberInvoicePriceType.PRO_RATA

            val prorationInvoiceItem = getPrePaymentProrationItem(
                member,
                isProRata,
                productPriceInvoiceItem,
                referenceDate,
                paymentMethod,
            ).get()

            val invoiceItems = activeInvoiceItems + productPriceInvoiceItem + prorationInvoiceItem
            val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().get()

            MemberInvoiceBuilder.build(
                member = member,
                referenceDate = referenceDate,
                dueDate = dueDate,
                invoiceBreakdown = invoiceBreakdown,
                invoiceItems = invoiceBreakdown.invoiceItems,
                type = type,
            )
        }
    }
}
