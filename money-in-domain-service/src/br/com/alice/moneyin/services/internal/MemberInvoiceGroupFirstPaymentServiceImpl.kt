package br.com.alice.moneyin.services.internal

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FirstPaymentSchedule
import br.com.alice.data.layer.models.FirstPaymentScheduleStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.services.internal.converters.MemberInvoiceGroupFirstPaymentConverter.toMemberInvoiceGroup
import br.com.alice.moneyin.services.internal.converters.MemberInvoiceGroupFirstPaymentConverter.toMemberInvoices
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentFailedEvent
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class MemberInvoiceGroupFirstPaymentServiceImpl(
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val companyContractService: CompanyContractService,
    private val companySubContractService: CompanySubContractService,
    private val firstPaymentScheduleService: FirstPaymentScheduleService,
    private val preActivationPaymentService: PreActivationPaymentService,
    private val invoicesService: InvoicesService,
    private val memberService: MemberService,
) {

    suspend fun createFromEvent(event: NullvsFirstPaymentCreatedEvent): Result<MemberInvoiceGroup, Throwable> =
        coroutineScope {
            val payload = event.payload
            val preActivationPaymentDef = async { getFirstPaymentScheduleAndPreActivationPayment(payload).get() }
            val subContractDef = async { getSubContract(payload).get() }
            val memberInvoicesDef = async { generateMemberInvoice(payload) }

            val (preActivationPayment, firstPaymentSchedule) = preActivationPaymentDef.await()
            val subContract = subContractDef.await()
            val memberInvoices = memberInvoicesDef.await()

            val memberInvoiceGroup = payload.toMemberInvoiceGroup(subContract)
                .copy(
                    quantityMemberInvoices = memberInvoices.size,
                    preActivationPaymentId = preActivationPayment.id,
                )

            createMemberInvoiceGroup(memberInvoiceGroup)
                .flatMapPair { createMemberInvoices(it, memberInvoices) }
                .flatMap { (memberInvoices, memberInvoiceGroup) ->
                    val memberInvoiceGroupUpdated = memberInvoiceGroup.copy(
                        memberInvoiceIds = memberInvoiceGroup.memberInvoiceIds.plus(memberInvoices.map { it.id })
                    )

                    memberInvoiceGroupService.update(memberInvoiceGroupUpdated)
                }.andThen {
                    val preActivationPaymentUpdated = preActivationPayment.copy(memberInvoiceGroupId = it.id)
                    preActivationPaymentService.update(preActivationPaymentUpdated)
                }.andThen {
                    firstPaymentScheduleService.update(firstPaymentSchedule.markAsFinished())
                }
        }

    private suspend fun createMemberInvoices(
        memberInvoiceGroup: MemberInvoiceGroup,
        memberInvoices: List<MemberInvoice>
    ) =
        filterOutExistingMemberInvoices(memberInvoiceGroup, memberInvoices)
            .flatMap { invoicesService.addList(it) }
            .then {
                logger.info(
                    "MemberInvoices created",
                    "member_invoice_ids" to it.map { it.id },
                    "member_invoice_group_id" to memberInvoiceGroup.id
                )
            }

    // Avoiding recreating the same member invoice
    private suspend fun filterOutExistingMemberInvoices(
        memberInvoiceGroup: MemberInvoiceGroup,
        newMemberInvoices: List<MemberInvoice>
    ): Result<List<MemberInvoice>, Throwable> {
        return invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroup.id)
            .map { existingInvoices ->
                newMemberInvoices.filterNot { newInvoice ->
                    existingInvoices.any { existingInvoice ->
                        existingInvoice.totalAmount == newInvoice.totalAmount &&
                                existingInvoice.personId == newInvoice.personId &&
                                existingInvoice.memberId == newInvoice.memberId &&
                                existingInvoice.referenceDate == newInvoice.referenceDate
                    }
                }
            }.map { it.ifEmpty { newMemberInvoices } }
    }

    private suspend fun generateMemberInvoice(payload: NullvsFirstPaymentCreatedEvent.Payload) =
        findMembers(payload)
            .let { payload.memberDetails.toMemberInvoices(it, payload.paidAt) }

    private suspend fun getFirstPaymentScheduleAndPreActivationPayment(payload: NullvsFirstPaymentCreatedEvent.Payload) =
        firstPaymentScheduleService.getByExternalId(payload.firstPaymentScheduleId)
            .then {
                logger.info(
                    "FirstPaymentSchedule found",
                    "first_payment_schedule_id" to it.id,
                    "pre_activation_payment_id" to it.preActivationPaymentId,
                    "company_id" to it.companyId,
                    "company_subcontract_id" to it.companySubcontractId,
                    "scheduled_date" to it.scheduledDate,
                )
            }
            .flatMapPair {
                preActivationPaymentService.get(it.preActivationPaymentId)
                    .then { logger.info("PreActivationPayment found", "pre_activation_payment_id" to it.id) }
            }


    private suspend fun getSubContract(
        payload: NullvsFirstPaymentCreatedEvent.Payload
    ): Result<CompanySubContract, Throwable> {
        val contract =
            companyContractService.getByExternalIdAndGroupCompany(payload.companyContractNumber, payload.groupCompany)
                .then { logger.info("CompanyContract found", "company_contract_id" to it.id) }
                .get()

        return companySubContractService.findByContractIdAndExternalId(contract.id, payload.companySubContractNumber)
            .then {
                logger.info("CompanySubContract found", "company_subcontract_id" to it.id)
            }
    }

    private suspend fun findMembers(payload: NullvsFirstPaymentCreatedEvent.Payload) =
        payload.memberDetails
            .map { it.memberId }
            .chunked(500)
            .pmap { memberService.findByIds(it).get() }
            .flatten()
            .associateBy { it.id }
            .also { logger.info("Members found", "size" to it.size, "members" to it.values.map { it.id }) }

    private suspend fun createMemberInvoiceGroup(memberInvoiceGroup: MemberInvoiceGroup) =
        memberInvoiceGroupService.add(memberInvoiceGroup)
            .then { logger.info("MemberInvoiceGroup created", "member_invoice_group_id" to it.id) }
            .coFoldDuplicated {
                logger.info(
                    "MemberInvoiceGroup already created",
                    "external_id" to memberInvoiceGroup.externalId
                )
                memberInvoiceGroupService.findByExternalIds(listOf(memberInvoiceGroup.externalId!!)).mapFirst()
            }


    suspend fun markFirstPaymentScheduleAsFailureFromEvent(event: NullvsFirstPaymentFailedEvent): Result<FirstPaymentSchedule, Throwable> =
        firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId)
            .then { logger.info("FirstPaymentSchedule found", "first_payment_schedule_id" to it.id) }
            .flatMap {
                firstPaymentScheduleService.update(it.markAsFailure(event.payload.errorMessage))
            }
            .then { logger.info("FirstPaymentSchedule marked as failed", "first_payment_schedule_id" to it.id) }
}
