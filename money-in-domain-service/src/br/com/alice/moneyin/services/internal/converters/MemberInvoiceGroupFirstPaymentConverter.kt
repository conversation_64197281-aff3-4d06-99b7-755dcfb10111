package br.com.alice.moneyin.services.internal.converters

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.money
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import java.time.LocalDate
import java.util.UUID

object MemberInvoiceGroupFirstPaymentConverter {
    fun List<NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail>.toMemberInvoices(
        members: Map<UUID, Member>,
        paidAt: LocalDate
    ): List<MemberInvoice> =
        map {
            val member = members[it.memberId]!!

            val invoiceItems = it.items.map { item ->
                item
                    .toInvoiceItem(personId = member.personId)
            }

            val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().get()

            MemberInvoice(
                memberId = it.memberId,
                personId = member.personId,
                totalAmount = it.totalAmount,
                status = InvoiceStatus.PAID,
                referenceDate = it.referenceDate,
                dueDate = it.dueDate.atEndOfTheDay(),
                invoiceItems = invoiceBreakdown.invoiceItems,
                invoiceBreakdown = invoiceBreakdown,
                paidAt = paidAt.atEndOfTheDay(),
                type = MemberInvoiceType.B2B_FIRST_PAYMENT,
            )
        }

    fun NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem.toInvoiceItem(
        personId: PersonId? = null,
        companyId: UUID? = null,
        companySubContractId: UUID? = null,
    ) =
        InvoiceItem(
            personId = personId,
            companyId = companyId,
            companySubcontractId = companySubContractId,
            referenceDate = this.referenceDate,
            operation = this.operation,
            type = this.type,
            notes = this.notes,
            status = InvoiceItemStatus.ACTIVE,
            absoluteValue = this.value,
        )

    fun NullvsFirstPaymentCreatedEvent.Payload.toMemberInvoiceGroup(subContract: CompanySubContract) =
        MemberInvoiceGroup(
            billingAccountablePartyId = this.billingAccountablePartyId,
            referenceDate = this.referenceDate,
            dueDate = this.dueDate,
            status = MemberInvoiceGroupStatus.PAID,
            externalId = this.externalId,
            type = MemberInvoiceType.B2B_FIRST_PAYMENT,
            totalAmount = this.value.money,
            globalItems = this.gloablItems.map {
                it.toInvoiceItem(
                    companyId = subContract.companyId,
                    companySubContractId = subContract.id,
                )
            },
            companyId = subContract.companyId,
            companySubcontractId = subContract.id,
        )
}
