package br.com.alice.moneyin.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.moneyin.controllers.RecurrentController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.recurringRoutes() {
    route("/recurring_subscribers") {
        val recurrentController by inject<RecurrentController>()

        post("/send_invoices_near_overdue_events") {
            asyncLayer { coHandler(recurrentController::triggerInvoicesNearOverdueEvents) }
        }

        post("/send_liquidation_invoices_near_overdue") {
            asyncLayer { coHandler(recurrentController::sendInvoiceLiquidationEmailNearToOverdue) }
        }

        post("/cancel_requested_invoice_payments") {
            asyncLayer { coHandler(recurrentController::cancelRequestedInvoicePayments) }
        }
        post("/cancel_expired_proposals_for_migs_first_payment") {
            asyncLayer { coHandler(recurrentController::cancelExpiredProposalsForMigsFirstPayment) }
        }
        post("/cancel_expired_proposals_for_paps_first_payment") {
            asyncLayer { coHandler(recurrentController::cancelExpiredProposalsForPapsFirstPayment) }
        }

        post("/cancel_older_first_payment_schedules") {
            asyncLayer { coHandler(recurrentController::cancelOlderFirstPaymentSchedules) }
        }
    }
}
