package br.com.alice.moneyin.services.internal.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.extensions.money
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemStatus
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.services.internal.converters.MemberInvoiceGroupFirstPaymentConverter.toInvoiceItem
import br.com.alice.moneyin.services.internal.converters.MemberInvoiceGroupFirstPaymentConverter.toMemberInvoiceGroup
import br.com.alice.moneyin.services.internal.converters.MemberInvoiceGroupFirstPaymentConverter.toMemberInvoices
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import kotlin.test.Test

class MemberInvoiceGroupFirstPaymentConverterTest {
    private val billingAccountablePartyId = RangeUUID.generate()
    private val personId = PersonId()
    private val memberId = RangeUUID.generate()
    private val dueDate = LocalDate.of(2025, 6, 2)
    private val referenceDate = LocalDate.of(2025, 4, 2)
    private val paidAt = LocalDate.of(2025, 6, 2)
    private val member = TestModelFactory.buildMember(id = memberId, personId = personId)

    private val invoiceDetailItem = InvoiceDetailItem(
        operation = InvoiceItemOperation.CHARGE,
        type = InvoiceItemType.PRODUCT_CHANGE,
        referenceDate = referenceDate,
        value = 50.money,
        notes = "",
    )

    private val globalItem = InvoiceDetailItem(
        operation = InvoiceItemOperation.CHARGE,
        type = InvoiceItemType.PRODUCT_CHANGE,
        referenceDate = referenceDate,
        value = 25.money,
        notes = "",
    )

    private val memberInvoiceDetail = MemberInvoiceDetail(
        totalAmount = 25.money,
        memberId = memberId,
        referenceDate = referenceDate,
        dueDate = dueDate,
        items = listOf(invoiceDetailItem)
    )

    private val event = NullvsFirstPaymentCreatedEvent(
        payload = NullvsFirstPaymentCreatedEvent.Payload(
            firstPaymentScheduleId = "0001",
            discount = 9.0.money,
            externalId = "PLS|0000001|01|DP",
            billingAccountablePartyId = billingAccountablePartyId,
            dueDate = dueDate,
            value = 50.money,
            paidAt = paidAt,
            gloablItems = listOf(globalItem),
            memberDetails = listOf(memberInvoiceDetail),
            referenceDate = referenceDate,
            groupCompany = "0018",
            companyContractNumber = "0000001",
            companySubContractNumber = "000001",
        )
    )

    private val subContract = TestModelFactory.buildCompanySubContract(
        externalId = "000001"
    )

    @Test
    fun `#toMemberInvoiceGroup convert correctly`() {
        val expected = MemberInvoiceGroup(
            billingAccountablePartyId = billingAccountablePartyId,
            referenceDate = referenceDate,
            dueDate = dueDate,
            status = MemberInvoiceGroupStatus.PAID,
            externalId = "PLS|0000001|01|DP",
            type = MemberInvoiceType.B2B_FIRST_PAYMENT,
            totalAmount = 50.money,
            globalItems = listOf(
                InvoiceItem(
                    personId = null,
                    companyId = subContract.companyId,
                    companySubcontractId = subContract.id,
                    referenceDate = this.referenceDate,
                    operation = InvoiceItemOperation.CHARGE,
                    type = InvoiceItemType.PRODUCT_CHANGE,
                    notes = "",
                    status = InvoiceItemStatus.ACTIVE,
                    absoluteValue = 25.money,
                )
            ),
            companyId = subContract.companyId,
            companySubcontractId = subContract.id,
        )
        val result = event.payload.toMemberInvoiceGroup(subContract)

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields(
                "id",
                "createdAt",
                "updatedAt",
                "globalItems.id",
                "globalItems.createdAt",
                "globalItems.updatedAt",
            )
            .isEqualTo(expected)
    }

    @Test
    fun `#toInvoiceItem convert correctly`() {
        val expected = InvoiceItem(
            personId = null,
            companyId = subContract.companyId,
            companySubcontractId = subContract.id,
            referenceDate = this.referenceDate,
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.PRODUCT_CHANGE,
            notes = "",
            status = InvoiceItemStatus.ACTIVE,
            absoluteValue = 25.money,
        )
        val result = globalItem.toInvoiceItem(
            companyId = subContract.companyId,
            companySubContractId = subContract.id,
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields(
                "id",
                "createdAt",
                "updatedAt",
            )
            .isEqualTo(expected)
    }

    @Test
    fun `#toMemberInvoice convert correctly`() {
        val invoiceItem = InvoiceItem(
            personId = personId,
            referenceDate = referenceDate,
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.PRODUCT_CHANGE,
            notes = "",
            status = InvoiceItemStatus.ACTIVE,
            absoluteValue = 50.money,
        )

        val expected = MemberInvoice(
            memberId = memberId,
            personId = personId,
            totalAmount = 25.money,
            status = InvoiceStatus.PAID,
            referenceDate = referenceDate,
            dueDate = dueDate.atEndOfTheDay(),
            invoiceItems = listOf(invoiceItem),
            invoiceBreakdown = listOf(invoiceItem).toInvoiceBreakdown().get(),
            paidAt = paidAt.atEndOfTheDay(),
            type = MemberInvoiceType.B2B_FIRST_PAYMENT,
        )
        val result = listOf(memberInvoiceDetail).toMemberInvoices(mapOf(memberId to member), paidAt)

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields(
                "id",
                "createdAt",
                "updatedAt",
                "invoiceItems.id",
                "invoiceItems.createdAt",
                "invoiceItems.updatedAt",
                "invoiceBreakdown.invoiceItems.id",
                "invoiceBreakdown.invoiceItems.createdAt",
                "invoiceBreakdown.invoiceItems.updatedAt",
            )
            .isEqualTo(listOf(expected))
    }
}
