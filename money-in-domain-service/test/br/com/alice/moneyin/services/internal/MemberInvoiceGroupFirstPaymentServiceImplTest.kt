package br.com.alice.moneyin.services.internal

import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FirstPaymentScheduleStatus
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentFailedEvent
import br.com.alice.person.client.MemberService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import kotlin.test.Test

class MemberInvoiceGroupFirstPaymentServiceImplTest {
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val firstPaymentScheduleService: FirstPaymentScheduleService = mockk()
    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val memberService: MemberService = mockk()

    private val service = MemberInvoiceGroupFirstPaymentServiceImpl(
        memberInvoiceGroupService,
        companyContractService,
        companySubContractService,
        firstPaymentScheduleService,
        preActivationPaymentService,
        invoicesService,
        memberService,
    )

    @Nested
    inner class CreateFromEvent {

        private val billingAccountablePartyId = RangeUUID.generate()
        private val contract = TestModelFactory.buildCompanyContract()
        private val subContract = TestModelFactory.buildCompanySubContract(
            contractId = contract.id,
            billingAccountablePartyId = billingAccountablePartyId,
        )
        private val memberInvoiceGroupId = RangeUUID.generate()

        private val preActivationPayment = TestModelFactory.buildPreActivationPayment(
            billingAccountablePartyId = billingAccountablePartyId,
        )

        private val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            id = memberInvoiceGroupId,
            billingAccountablePartyId = billingAccountablePartyId,
            externalId = "PLS|0000001|01|DP",
            companyId = subContract.companyId,
            companySubcontractId = subContract.id,
            preActivationPaymentId = preActivationPayment.id,
            memberInvoiceIds = emptyList(),
        )

        private val memberInvoices = listOf(TestModelFactory.buildMemberInvoice())
        private val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        private val member = TestModelFactory.buildMember()

        private val invoiceItemDetailItem = NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem(
            referenceDate = LocalDate.now(),
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.PRODUCT_CHANGE,
            notes = "",
            value = 50.money,
        )

        private val memberInvoiceDetail = NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail(
            memberId = member.id,
            totalAmount = 50.money,
            referenceDate = LocalDate.now(),
            dueDate = LocalDate.now(),
            items = listOf(invoiceItemDetailItem),
        )

        private val event = NullvsFirstPaymentCreatedEvent(
            payload = NullvsFirstPaymentCreatedEvent.Payload(
                firstPaymentScheduleId = "0001",
                discount = 9.0.money,
                externalId = "PLS|0000001|01|DP",
                billingAccountablePartyId = billingAccountablePartyId,
                dueDate = LocalDate.now(),
                value = 50.money,
                paidAt = LocalDate.now(),
                gloablItems = emptyList(),
                memberDetails = listOf(memberInvoiceDetail),
                referenceDate = LocalDate.now(),
                groupCompany = "0018",
                companyContractNumber = "0000001",
                companySubContractNumber = "000001",
            )
        )

        @Test
        fun `#should create the member invoice group`() = runBlocking<Unit> {
            val memberInvoiceGroupUpdated = memberInvoiceGroup.copy(memberInvoiceIds = memberInvoices.map { it.id })
            val preActivationPaymentUpdated = preActivationPayment.copy(memberInvoiceGroupId = memberInvoiceGroup.id)

            coEvery { firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId) } returns firstPaymentSchedule
            coEvery { preActivationPaymentService.get(firstPaymentSchedule.preActivationPaymentId) } returns preActivationPayment
            coEvery {
                companySubContractService.findByContractIdAndExternalId(
                    contract.id,
                    event.payload.companySubContractNumber,
                )
            } returns subContract
            coEvery {
                companyContractService.getByExternalIdAndGroupCompany(
                    event.payload.companyContractNumber,
                    event.payload.groupCompany
                )
            } returns contract
            coEvery { memberService.findByIds(listOf(member.id)) } returns listOf(member)
            coEvery { invoicesService.addList(match { it.first().memberId == member.id }) } returns memberInvoices
            coEvery { memberInvoiceGroupService.add(match { it.externalId == event.payload.externalId }) } returns memberInvoiceGroup
            coEvery { memberInvoiceGroupService.update(memberInvoiceGroupUpdated) } returns memberInvoiceGroupUpdated
            coEvery { preActivationPaymentService.update(preActivationPaymentUpdated) } returns preActivationPayment
            coEvery { invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroup.id) } returns emptyList()
            coEvery {
                firstPaymentScheduleService.update(match {
                    it.id == firstPaymentSchedule.id &&
                            it.status == FirstPaymentScheduleStatus.FINISHED
                })
            } returns firstPaymentSchedule

            val result = service.createFromEvent(event)
            ResultAssert.assertThat(result).isSuccessWithData(memberInvoiceGroupUpdated)

            coVerifyOnce {
                firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId)
                preActivationPaymentService.get(firstPaymentSchedule.preActivationPaymentId)
                companySubContractService.findByContractIdAndExternalId(
                    contract.id,
                    event.payload.companySubContractNumber,
                )
                companyContractService.getByExternalIdAndGroupCompany(
                    event.payload.companyContractNumber,
                    event.payload.groupCompany
                )
                memberService.findByIds(listOf(member.id))
                invoicesService.addList(match { it.first().memberId == member.id })
                memberInvoiceGroupService.add(match { it.externalId == event.payload.externalId })
                memberInvoiceGroupService.update(memberInvoiceGroupUpdated)
                preActivationPaymentService.update(preActivationPaymentUpdated)
                invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroup.id)
                firstPaymentScheduleService.update(match { it.id == firstPaymentSchedule.id && it.status == FirstPaymentScheduleStatus.FINISHED })
            }
        }

        @Test
        fun `#should get member invoice group by external id when add returns DuplicatedItemException`() =
            runBlocking<Unit> {
                val memberInvoiceGroupUpdated = memberInvoiceGroup.copy(memberInvoiceIds = memberInvoices.map { it.id })
                val preActivationPaymentUpdated =
                    preActivationPayment.copy(memberInvoiceGroupId = memberInvoiceGroup.id)

                coEvery { firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId) } returns firstPaymentSchedule
                coEvery { preActivationPaymentService.get(firstPaymentSchedule.preActivationPaymentId) } returns preActivationPayment
                coEvery {
                    companySubContractService.findByContractIdAndExternalId(
                        contract.id,
                        event.payload.companySubContractNumber,
                    )
                } returns subContract
                coEvery {
                    companyContractService.getByExternalIdAndGroupCompany(
                        event.payload.companyContractNumber,
                        event.payload.groupCompany
                    )
                } returns contract
                coEvery { invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroup.id) } returns emptyList()
                coEvery { memberService.findByIds(listOf(member.id)) } returns listOf(member)
                coEvery { invoicesService.addList(match { it.first().memberId == member.id }) } returns memberInvoices
                coEvery {
                    memberInvoiceGroupService.add(match { it.externalId == event.payload.externalId })
                } returns DuplicatedItemException("")

                coEvery { memberInvoiceGroupService.findByExternalIds(listOf(event.payload.externalId)) } returns listOf(
                    memberInvoiceGroup
                )
                coEvery { memberInvoiceGroupService.update(memberInvoiceGroupUpdated) } returns memberInvoiceGroupUpdated
                coEvery { preActivationPaymentService.update(preActivationPaymentUpdated) } returns preActivationPayment
                coEvery {
                    firstPaymentScheduleService.update(match {
                        it.id == firstPaymentSchedule.id &&
                                it.status == FirstPaymentScheduleStatus.FINISHED
                    })
                } returns firstPaymentSchedule

                val result = service.createFromEvent(event)
                ResultAssert.assertThat(result).isSuccessWithData(memberInvoiceGroupUpdated)

                coVerifyOnce {
                    firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId)
                    preActivationPaymentService.get(firstPaymentSchedule.preActivationPaymentId)
                    companySubContractService.findByContractIdAndExternalId(
                        contract.id,
                        event.payload.companySubContractNumber,
                    )
                    companyContractService.getByExternalIdAndGroupCompany(
                        event.payload.companyContractNumber,
                        event.payload.groupCompany
                    )
                    memberService.findByIds(listOf(member.id))
                    invoicesService.addList(match { it.first().memberId == member.id })
                    memberInvoiceGroupService.add(match { it.externalId == event.payload.externalId })
                    memberInvoiceGroupService.findByExternalIds(listOf(event.payload.externalId))
                    memberInvoiceGroupService.update(memberInvoiceGroupUpdated)
                    preActivationPaymentService.update(preActivationPaymentUpdated)
                    invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroup.id)
                    firstPaymentScheduleService.update(match { it.id == firstPaymentSchedule.id && it.status == FirstPaymentScheduleStatus.FINISHED })
                }
            }

        @Test
        fun `#should add only new member invoices when some already exist in the group`() = runBlocking<Unit> {
            // Create two member invoices with different properties
            val existingMember = TestModelFactory.buildMember(id = RangeUUID.generate())
            val existingMemberInvoice = TestModelFactory.buildMemberInvoice(
                memberId = existingMember.id,
                personId = existingMember.personId,
                totalAmount = 50.money,
                referenceDate = LocalDate.now()
            )

            val newMember = TestModelFactory.buildMember(id = RangeUUID.generate())
            val newMemberInvoice = TestModelFactory.buildMemberInvoice(
                memberId = newMember.id,
                personId = newMember.personId,
                totalAmount = 75.money,
                referenceDate = LocalDate.now()
            )

            // Set up event with both members
            val existingMemberInvoiceDetail = NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail(
                memberId = existingMember.id,
                totalAmount = 50.money,
                referenceDate = LocalDate.now(),
                dueDate = LocalDate.now(),
                items = listOf(invoiceItemDetailItem)
            )

            val newMemberInvoiceDetail = NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail(
                memberId = newMember.id,
                totalAmount = 75.money,
                referenceDate = LocalDate.now(),
                dueDate = LocalDate.now(),
                items = listOf(invoiceItemDetailItem)
            )

            val eventWithTwoMembers = NullvsFirstPaymentCreatedEvent(
                payload = event.payload.copy(
                    memberDetails = listOf(existingMemberInvoiceDetail, newMemberInvoiceDetail)
                )
            )

            // Set up the memberInvoiceGroup with the existing invoice already in it
            val memberInvoiceGroupWithExistingInvoice = memberInvoiceGroup.copy(
                memberInvoiceIds = listOf(existingMemberInvoice.id)
            )

            val memberInvoiceGroupUpdated = memberInvoiceGroupWithExistingInvoice.copy(
                memberInvoiceIds = memberInvoiceGroupWithExistingInvoice.memberInvoiceIds + newMemberInvoice.id
            )

            val preActivationPaymentUpdated = preActivationPayment.copy(memberInvoiceGroupId = memberInvoiceGroup.id)

            // Setup mocks
            coEvery { firstPaymentScheduleService.getByExternalId(eventWithTwoMembers.payload.firstPaymentScheduleId) } returns firstPaymentSchedule
            coEvery { preActivationPaymentService.get(firstPaymentSchedule.preActivationPaymentId) } returns preActivationPayment
            coEvery {
                companySubContractService.findByContractIdAndExternalId(
                    contract.id,
                    eventWithTwoMembers.payload.companySubContractNumber
                )
            } returns subContract
            coEvery {
                companyContractService.getByExternalIdAndGroupCompany(
                    eventWithTwoMembers.payload.companyContractNumber,
                    eventWithTwoMembers.payload.groupCompany
                )
            } returns contract

            // Return both members when searching
            coEvery { memberService.findByIds(listOf(existingMember.id, newMember.id)) } returns listOf(
                existingMember,
                newMember
            )

            // Return the existing invoice when listing by group ID
            coEvery { invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroupWithExistingInvoice.id) } returns listOf(
                existingMemberInvoice
            )

            // Only add the new invoice
            coEvery {
                invoicesService.addList(match { invoices ->
                    invoices.size == 1 && invoices.first().memberId == newMember.id
                })
            } returns listOf(newMemberInvoice)

            coEvery { memberInvoiceGroupService.add(match { it.externalId == event.payload.externalId }) } returns memberInvoiceGroupWithExistingInvoice
            coEvery { memberInvoiceGroupService.update(memberInvoiceGroupUpdated) } returns memberInvoiceGroupUpdated
            coEvery { preActivationPaymentService.update(preActivationPaymentUpdated) } returns preActivationPayment
            coEvery {
                firstPaymentScheduleService.update(match {
                    it.id == firstPaymentSchedule.id &&
                            it.status == FirstPaymentScheduleStatus.FINISHED
                })
            } returns firstPaymentSchedule

            // Execute test
            val result = service.createFromEvent(eventWithTwoMembers)
            ResultAssert.assertThat(result).isSuccessWithData(memberInvoiceGroupUpdated)

            // Verify correct methods were called
            coVerifyOnce {
                firstPaymentScheduleService.getByExternalId(eventWithTwoMembers.payload.firstPaymentScheduleId)
                preActivationPaymentService.get(firstPaymentSchedule.preActivationPaymentId)
                companySubContractService.findByContractIdAndExternalId(
                    contract.id,
                    eventWithTwoMembers.payload.companySubContractNumber
                )
                companyContractService.getByExternalIdAndGroupCompany(
                    eventWithTwoMembers.payload.companyContractNumber,
                    eventWithTwoMembers.payload.groupCompany
                )
                memberService.findByIds(listOf(existingMember.id, newMember.id))
                invoicesService.listByMemberInvoiceGroupId(memberInvoiceGroupWithExistingInvoice.id)
                // Verify that addList is called with only the new invoice
                invoicesService.addList(match { invoices ->
                    invoices.size == 1 && invoices.first().memberId == newMember.id
                })
                memberInvoiceGroupService.add(match { it.externalId == event.payload.externalId })
                memberInvoiceGroupService.update(memberInvoiceGroupUpdated)
                preActivationPaymentService.update(preActivationPaymentUpdated)
                firstPaymentScheduleService.update(match { it.id == firstPaymentSchedule.id && it.status == FirstPaymentScheduleStatus.FINISHED })
            }
        }
    }

    @Nested
    inner class MarkFirstPaymentScheduleAsFailureFromEvent {
        private val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()

        @Test
        fun `#should mark first payment schedule as failure from event`() = runBlocking<Unit> {
            val event = NullvsFirstPaymentFailedEvent(
                payload = NullvsFirstPaymentFailedEvent.Payload(
                    firstPaymentScheduleId = "0001",
                    errorMessage = "Error"
                )
            )

            val firstPaymentScheduleUpdated = firstPaymentSchedule.copy(
                status = FirstPaymentScheduleStatus.FAILURE,
                error = event.payload.errorMessage
            )

            coEvery { firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId) } returns firstPaymentSchedule
            coEvery {
                firstPaymentScheduleService.update(match { it.id == firstPaymentScheduleUpdated.id && it.status == FirstPaymentScheduleStatus.FAILURE })
            } returns firstPaymentScheduleUpdated

            val result = service.markFirstPaymentScheduleAsFailureFromEvent(event)
            ResultAssert.assertThat(result).isSuccessWithData(firstPaymentScheduleUpdated)

            coVerifyOnce {
                firstPaymentScheduleService.getByExternalId(event.payload.firstPaymentScheduleId)
                firstPaymentScheduleService.update(match { it.id == firstPaymentScheduleUpdated.id && it.status == FirstPaymentScheduleStatus.FAILURE })
            }
        }
    }
}
