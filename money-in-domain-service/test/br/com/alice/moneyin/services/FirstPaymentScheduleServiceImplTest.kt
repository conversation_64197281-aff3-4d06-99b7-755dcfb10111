package br.com.alice.moneyin.services

import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FirstPaymentScheduleStatus
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.services.FirstPaymentScheduleModelDataService
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.PreActivationPaymentExternalIdNullException
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import br.com.alice.person.client.PersonService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import org.junit.jupiter.api.Nested
import kotlin.test.Test

class FirstPaymentScheduleServiceImplTest : MockedTestHelper() {
    private val dataService: FirstPaymentScheduleModelDataService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val personService: PersonService = mockk()
    private val service =
        FirstPaymentScheduleServiceImpl(dataService, invoicesService, kafkaProducerService, personService)

    @Nested
    inner class Create {
        @Test
        fun `#add should add new FirstPaymentSchedule`() = runBlocking {
            val company = TestModelFactory.buildCompany()
            val companyContract = TestModelFactory.buildCompanyContract()
            val companySubContract = TestModelFactory.buildCompanySubContract()

            val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
            val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

            val preActivationPaymentId = RangeUUID.generate()

            val person = TestModelFactory.buildPerson()
            val productInvoiceItem =
                TestModelFactory.buildInvoiceItem(personId = person.id, type = InvoiceItemType.PRODUCT_PRICE)

            val salesInvoiceItem =
                TestModelFactory.buildInvoiceItem(
                    personId = person.id,
                    type = InvoiceItemType.SALES,
                    operation = InvoiceItemOperation.DISCOUNT
                )
            val invoiceItems = listOf(productInvoiceItem, salesInvoiceItem)
            val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().get()

            val memberInvoice = TestModelFactory.buildMemberInvoice(
                invoiceItems = invoiceItems,
                invoiceBreakdown = invoiceBreakdown,
                memberInvoiceGroupId = null,
                preActivationPaymentId = preActivationPaymentId,
                personId = person.id,
            )

            val preActivationPayment =
                TestModelFactory.buildPreActivationPayment(memberInvoiceIds = listOf(memberInvoice.id))

            coEvery { dataService.add(match { it.companyId == company.id && it.companySubcontractId == companySubContract.id && it.preActivationPaymentId == preActivationPayment.id }) } returns firstPaymentScheduleModel
            coEvery { invoicesService.listByPreActivationPaymentId(preActivationPayment.id) } returns listOf(
                memberInvoice
            )
            coEvery { personService.findByIds(listOf(person.id.toString())) } returns listOf(person)

            coEvery {
                kafkaProducerService.produce(match<FirstPaymentScheduleCreatedEvent> {
                    it.payload.firstPaymentScheduleCreated.preActivationPayment == preActivationPayment
                            && it.payload.firstPaymentScheduleCreated.company == company
                            && it.payload.firstPaymentScheduleCreated.companyContract == companyContract
                            && it.payload.firstPaymentScheduleCreated.companySubContract == companySubContract
                })
            } returns mockk()

            val result = service.create(
                FirstPaymentScheduleService.CreatePayload(
                    company,
                    companyContract,
                    companySubContract,
                    preActivationPayment,
                )
            )

            ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

            coVerifyOnce {
                dataService.add(any())
                kafkaProducerService.produce(any<FirstPaymentScheduleCreatedEvent>())
                invoicesService.listByPreActivationPaymentId(preActivationPayment.id)
                personService.findByIds(listOf(person.id.toString()))
            }
        }

        @Test
        fun `should return PreActivationPaymentExternalIdNullException when preActivationPayment does not have an externalId`() =
            runBlocking {
                val preActivationPayment = TestModelFactory.buildPreActivationPayment(externalId = null)

                val result = service.create(
                    FirstPaymentScheduleService.CreatePayload(
                        company = TestModelFactory.buildCompany(),
                        companyContract = TestModelFactory.buildCompanyContract(),
                        companySubContract = TestModelFactory.buildCompanySubContract(),
                        preActivationPayment = preActivationPayment,
                    )
                )

                ResultAssert.assertThat(result).isFailureOfType(PreActivationPaymentExternalIdNullException::class)

                coVerifyNone {
                    dataService.add(any())
                    kafkaProducerService.produce(any())
                    invoicesService.listByPreActivationPaymentId(any())
                    personService.findByIds(any())
                }
            }
    }

    @Test
    fun `#get should retrieve FirstPaymentSchedule`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

        coEvery { dataService.get(firstPaymentScheduleModel.id) } returns firstPaymentScheduleModel

        val result = service.get(firstPaymentScheduleModel.id)

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

        coVerifyOnce { dataService.get(any()) }
    }

    @Test
    fun `#update should retrieve FirstPaymentSchedule`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()
        val firstPaymentScheduleModelUpdated =
            firstPaymentScheduleModel.copy(memberInvoiceGroupId = RangeUUID.generate())
        val firstPaymentScheduleUpdated = firstPaymentScheduleModelUpdated.toTransport()

        coEvery { dataService.update(firstPaymentScheduleModelUpdated) } returns firstPaymentScheduleModelUpdated

        val result = service.update(firstPaymentScheduleUpdated)

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentScheduleUpdated)

        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#getByExternalId should retrieve FirstPaymentSchedule`() = runBlocking<Unit> {
        val externalId = "0001"
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule(externalId = externalId)
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

        coEvery { dataService.getByExternalId(externalId) } returns firstPaymentScheduleModel

        val result = service.getByExternalId(externalId)

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

        coVerifyOnce { dataService.getByExternalId(externalId) }
    }

    @Test
    fun `#findByCompanyIdSubContractIdAndPreActivationPaymentId should retrieve FirstPaymentSchedule`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val companySubcontract = TestModelFactory.buildCompanySubContract()
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule(
            companyId = company.id,
            companySubcontractId = companySubcontract.id,
            preActivationPaymentId = preActivationPayment.id
        )

        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

        coEvery {
            dataService.findOne(
                queryEq {
                    where {
                        this.companyId.eq(company.id) and
                                this.companySubContractId.eq(companySubcontract.id) and
                                this.preActivationPaymentId.eq(preActivationPayment.id)
                    }
                }
            )
        } returns firstPaymentScheduleModel

        val result = service.findByCompanyIdSubContractIdAndPreActivationPaymentId(
            companyId = company.id,
            companySubcontractId = companySubcontract.id,
            preActivationPaymentId = preActivationPayment.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

        coVerifyOnce { dataService.findOne(any()) }
    }

    @Test
    fun `#findOlderThanOneMonthFromNow should retrieve FirstPaymentSchedule`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()
        val oneMonthAgo = LocalDate.now().minusMonths(1).atEndOfTheDay()

        coEvery {
            dataService.findOlderThanDateTime(
                oneMonthAgo,
                range = IntRange(0, 10)
            )
        } returns listOf(firstPaymentScheduleModel)

        val result = service.findOlderThanOneMonthFromNow(10)

        ResultAssert.assertThat(result).isSuccessWithData(listOf(firstPaymentSchedule))

        coVerifyOnce {
            dataService.findOlderThanDateTime(
                oneMonthAgo,
                range = IntRange(0, 10)
            )
        }
    }

    @Test
    fun `#cancelList should cancel FirstPaymentSchedule`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()
        val firstPaymentScheduleModelUpdated =
            firstPaymentScheduleModel.copy(status = FirstPaymentScheduleStatus.CANCELED)
        val firstPaymentScheduleUpdated = firstPaymentScheduleModelUpdated.toTransport()

        coEvery {
            dataService.updateList(
                listOf(firstPaymentScheduleModelUpdated),
                returnOnFailure = false
            )
        } returns listOf(firstPaymentScheduleModelUpdated)

        val result = service.cancelList(listOf(firstPaymentScheduleUpdated))

        ResultAssert.assertThat(result).isSuccessWithData(listOf(firstPaymentScheduleUpdated))

        coVerifyOnce {
            dataService.updateList(
                listOf(firstPaymentScheduleModelUpdated),
                returnOnFailure = false
            )
        }
    }
}
