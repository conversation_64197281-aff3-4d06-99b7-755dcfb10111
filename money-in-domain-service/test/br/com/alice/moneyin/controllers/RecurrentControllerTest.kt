package br.com.alice.moneyin.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CancelPaymentOnAcquirerSchedule
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.client.CancelPaymentOnAcquirerScheduleService
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PreActivationPaymentService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.event.ProcessInvoiceNearOverdueEvent
import br.com.alice.moneyin.module
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.server.application.Application
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.koin.core.context.loadKoinModules
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class RecurrentControllerTest : RoutesTestHelper() {

    private val invoicesService: InvoicesService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val invoiceLiquidationService: InvoiceLiquidationService = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val invoiceMailer: InvoiceMailer = mockk()
    private val cancelPaymentOnAcquirerScheduleService: CancelPaymentOnAcquirerScheduleService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val firstPaymentScheduleService: FirstPaymentScheduleService = mockk()

    private val recurrentController = RecurrentController(
        invoicesService,
        kafkaProducerService,
        invoiceLiquidationService,
        invoicePaymentService,
        invoiceMailer,
        cancelPaymentOnAcquirerScheduleService,
        memberInvoiceGroupService,
        preActivationPaymentService,
        firstPaymentScheduleService
    )

    private val invoice = TestModelFactory.buildMemberInvoice()
    private val memberInvoiceGroupId = UUID.randomUUID()
    private val invoice1 = TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = memberInvoiceGroupId)
    private val invoice2 = TestModelFactory.buildMemberInvoice(memberInvoiceGroupId = memberInvoiceGroupId)
    private val invoice3 = TestModelFactory.buildMemberInvoice()
    private val processInvoiceEvent = ProcessInvoiceNearOverdueEvent(invoice)
    private val processInvoiceEvent1 = ProcessInvoiceNearOverdueEvent(invoice1)
    private val processInvoiceEvent2 = ProcessInvoiceNearOverdueEvent(invoice2)
    private val processInvoiceEvent3 = ProcessInvoiceNearOverdueEvent(invoice3)

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }
    override val moduleFunction: Application.() -> Unit = { loadKoinModules(module) }

    @BeforeTest
    override fun setup() {
        super.setup()

        this.module.single { recurrentController }
    }

    @Test
    fun `#triggerInvoicesNearOverdueEvents should trigger ProcessInvoiceNearOverdueEvent`() = runBlocking {
        withFeatureFlag(FeatureNamespace.PAYMENTS, ALLOW_BRAZE_NOTIFICATIONS_OF_INVOICES_DUE_DATE, true) {
            coEvery {
                invoicesService.listNearOverdueInvoices(any(), any(), MemberInvoiceType.REGULAR_PAYMENT)
            } returns listOf(invoice, invoice1, invoice2, invoice3)

            coEvery { kafkaProducerService.produce(match { it.payload == processInvoiceEvent.payload }) } returns mockk()
            coEvery { kafkaProducerService.produce(match { it.payload == processInvoiceEvent1.payload }) } returns mockk()
            coEvery { kafkaProducerService.produce(match { it.payload == processInvoiceEvent3.payload }) } returns mockk()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/send_invoices_near_overdue_events") { response ->
                    assertThat(response).isOK()
                    coVerifyOnce {
                        invoicesService.listNearOverdueInvoices(
                            any(),
                            any(),
                            MemberInvoiceType.REGULAR_PAYMENT
                        )
                    }
                    coVerifyOnce { kafkaProducerService.produce(match { it.payload == processInvoiceEvent.payload }) }
                    coVerifyOnce { kafkaProducerService.produce(match { it.payload == processInvoiceEvent1.payload }) }
                    coVerifyNone { kafkaProducerService.produce(match { it.payload == processInvoiceEvent2.payload }) }
                    coVerifyOnce { kafkaProducerService.produce(match { it.payload == processInvoiceEvent3.payload }) }
                }
            }
        }
    }

    @Test
    fun `#triggerInvoicesNearOverdueEvents should not trigger ProcessInvoiceNearOverdueEvent when empty`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.PAYMENTS, ALLOW_BRAZE_NOTIFICATIONS_OF_INVOICES_DUE_DATE, true) {
                coEvery {
                    invoicesService.listNearOverdueInvoices(any(), any(), MemberInvoiceType.REGULAR_PAYMENT)
                } returns listOf()

                internalAuthentication {
                    postAsPlainText("/recurring_subscribers/send_invoices_near_overdue_events") { response ->
                        assertThat(response).isOK()
                        coVerifyOnce {
                            invoicesService.listNearOverdueInvoices(
                                any(),
                                any(),
                                MemberInvoiceType.REGULAR_PAYMENT
                            )
                        }
                        coVerify { kafkaProducerService wasNot called }
                    }
                }
            }
        }

    @Test
    fun `#triggerInvoicesNearOverdueEvents should not trigger ProcessInvoiceNearOverdueEvent when error`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.PAYMENTS, ALLOW_BRAZE_NOTIFICATIONS_OF_INVOICES_DUE_DATE, true) {
                coEvery {
                    invoicesService.listNearOverdueInvoices(any(), any(), MemberInvoiceType.REGULAR_PAYMENT)
                } returns BadRequestException().failure()

                internalAuthentication {
                    postAsPlainText("/recurring_subscribers/send_invoices_near_overdue_events") { response ->
                        assertThat(response).isOK()
                        coVerifyOnce {
                            invoicesService.listNearOverdueInvoices(
                                any(),
                                any(),
                                MemberInvoiceType.REGULAR_PAYMENT
                            )
                        }
                        coVerify { kafkaProducerService wasNot called }
                    }
                }
            }
        }

    @Test
    fun `#sendInvoiceLiquidationEmailNearToOverdue should send emails near to overdue`() = runBlocking {
        withFeatureFlag(FeatureNamespace.PAYMENTS, SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE, true) {
            val initialDate = LocalDate.now()
            val nearOverdueDate = initialDate.plusDays(10)
            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
            val invoicePayment = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = invoiceLiquidation.id)

            coEvery { invoiceLiquidationService.listInvoicesNearOverdue(nearOverdueDate) } returns listOf(
                invoiceLiquidation
            )
            coEvery {
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    withPaymentDetails = true,
                )
            } returns listOf(
                invoicePayment
            )
            coEvery {
                invoiceMailer.send(
                    invoicePayment,
                    PaymentReason.B2B_LIQUIDATION,
                    invoicePayment.billingAccountablePartyId
                )
            } returns EmailReceipt("test-id").success()
            coEvery { invoicePaymentService.update(invoicePayment.copy(sendEmail = true)) } returns mockk()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/send_liquidation_invoices_near_overdue") { response ->
                    assertThat(response).isOK()
                    coVerifyOnce {
                        invoiceLiquidationService.listInvoicesNearOverdue(
                            any(),
                        )
                    }
                    coVerifyOnce { invoicePaymentService.getByInvoiceLiquidationIds(any(), true) }
                    coVerifyOnce { invoiceMailer.send(any(), any(), any()) }
                    coVerifyOnce { invoicePaymentService.update(any()) }
                }
            }
        }
    }

    @Test
    fun `#sendInvoiceLiquidationEmailNearToOverdue should not update sendEmail`() = runBlocking {
        withFeatureFlag(FeatureNamespace.PAYMENTS, SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE, true) {
            val initialDate = LocalDate.now()
            val nearOverdueDate = initialDate.plusDays(10)
            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
            val invoicePayment = TestModelFactory.buildInvoicePayment(invoiceLiquidationId = invoiceLiquidation.id)

            coEvery { invoiceLiquidationService.listInvoicesNearOverdue(nearOverdueDate) } returns listOf(
                invoiceLiquidation
            )
            coEvery {
                invoicePaymentService.getByInvoiceLiquidationIds(
                    listOf(invoiceLiquidation.id),
                    withPaymentDetails = true,
                )
            } returns listOf(
                invoicePayment
            )
            coEvery {
                invoiceMailer.send(
                    invoicePayment,
                    PaymentReason.B2B_LIQUIDATION,
                    invoicePayment.billingAccountablePartyId
                )
            } returns Exception().failure()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/send_liquidation_invoices_near_overdue") { response ->
                    assertThat(response).isOK()
                    coVerifyOnce {
                        invoiceLiquidationService.listInvoicesNearOverdue(
                            any(),
                        )
                    }
                    coVerifyOnce { invoicePaymentService.getByInvoiceLiquidationIds(any(), true) }
                    coVerifyOnce { invoiceMailer.send(any(), any(), any()) }
                    coVerifyNone { invoicePaymentService.update(any()) }
                }
            }
        }
    }

    @Test
    fun `#sendInvoiceLiquidationEmailNearToOverdue should not send email when sendEmail is already true`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.PAYMENTS, SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE, true) {
                val initialDate = LocalDate.now()
                val nearOverdueDate = initialDate.plusDays(10)
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()
                val invoicePayment =
                    TestModelFactory.buildInvoicePayment(invoiceLiquidationId = invoiceLiquidation.id, sendEmail = true)

                coEvery { invoiceLiquidationService.listInvoicesNearOverdue(nearOverdueDate) } returns listOf(
                    invoiceLiquidation
                )
                coEvery { invoicePaymentService.getByInvoiceLiquidationIds(listOf(invoiceLiquidation.id)) } returns listOf(
                    invoicePayment
                )

                internalAuthentication {
                    postAsPlainText("/recurring_subscribers/send_liquidation_invoices_near_overdue") { response ->
                        assertThat(response).isOK()
                        coVerifyOnce {
                            invoiceLiquidationService.listInvoicesNearOverdue(
                                any(),
                            )
                        }
                        coVerifyOnce { invoicePaymentService.getByInvoiceLiquidationIds(any(), true) }
                        coVerifyNone { invoiceMailer.send(any(), any(), any()) }
                        coVerifyNone { invoicePaymentService.update(any()) }
                    }
                }
            }
        }

    @Test
    fun `#sendInvoiceLiquidationEmailNearToOverdue should not send email when there is no invoice payment`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.PAYMENTS, SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE, true) {
                val initialDate = LocalDate.now()
                val nearOverdueDate = initialDate.plusDays(10)
                val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation()

                coEvery { invoiceLiquidationService.listInvoicesNearOverdue(nearOverdueDate) } returns listOf(
                    invoiceLiquidation
                )
                coEvery {
                    invoicePaymentService.getByInvoiceLiquidationIds(
                        listOf(invoiceLiquidation.id),
                        withPaymentDetails = true
                    )
                } returns emptyList<InvoicePayment>().success()

                internalAuthentication {
                    postAsPlainText("/recurring_subscribers/send_liquidation_invoices_near_overdue") { response ->
                        assertThat(response).isOK()
                        coVerifyOnce {
                            invoiceLiquidationService.listInvoicesNearOverdue(
                                any(),
                            )
                        }
                        coVerifyOnce { invoicePaymentService.getByInvoiceLiquidationIds(any(), true) }
                        coVerifyNone { invoiceMailer.send(any(), any(), any()) }
                        coVerifyNone { invoicePaymentService.update(any()) }
                    }
                }
            }
        }

    @Test
    fun `#sendInvoiceLiquidationEmailNearToOverdue should handle empty invoice liquidation list`() = runBlocking {
        withFeatureFlag(FeatureNamespace.PAYMENTS, SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE, true) {
            coEvery { invoiceLiquidationService.listInvoicesNearOverdue(any()) } returns emptyList()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/send_liquidation_invoices_near_overdue") { response ->
                    assertThat(response).isOK()
                    coVerifyOnce {
                        invoiceLiquidationService.listInvoicesNearOverdue(
                            any(),
                        )
                    }
                    coVerifyOnce { invoicePaymentService.getByInvoiceLiquidationIds(any(), true) }
                    coVerifyNone { invoiceMailer.send(any(), any(), any()) }
                    coVerifyNone { invoicePaymentService.update(any()) }
                }
            }
        }
    }

    @Test
    fun `#cancelRequestedInvoicePayments should cancel the requested payments`() = mockLocalDateTime { now ->
        val cancelSchedule = CancelPaymentOnAcquirerSchedule(
            id = RangeUUID.generate(),
            requestedAt = now,
            invoicePaymentId = RangeUUID.generate()
        )
        val invoicePayment = TestModelFactory.buildInvoicePayment(id = cancelSchedule.invoicePaymentId)

        coEvery {
            cancelPaymentOnAcquirerScheduleService.findInvoicePaymentsRequestedToCancel(
                date = now.minusDays(1).atEndOfTheDay(),
                limit = 100
            )
        } returns listOf(cancelSchedule)
        coEvery {
            invoicePaymentService.get(
                cancelSchedule.invoicePaymentId,
                withPaymentDetails = false
            )
        } returns invoicePayment
        coEvery {
            cancelPaymentOnAcquirerScheduleService.cancelPaymentOnAcquirer(
                cancelSchedule,
                invoicePayment
            )
        } returns cancelSchedule.copy(canceledAt = now)

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/cancel_requested_invoice_payments") { response ->
                assertThat(response).isOK()
            }
        }
        coVerifyOnce { invoicePaymentService.get(any(), any()) }
        coVerifyOnce { cancelPaymentOnAcquirerScheduleService.findInvoicePaymentsRequestedToCancel(any(), any()) }
        coVerifyOnce { cancelPaymentOnAcquirerScheduleService.cancelPaymentOnAcquirer(any(), any()) }
    }

    @Test
    fun `#cancelExpiredProposalsForPapsFirstPayment should cancel expired first payments`() = mockLocalDateTime { now ->
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        coEvery {
            preActivationPaymentService.findPendingFirstPaymentsOlderThanDate(
                date = now.minusDays(30).atEndOfTheDay(),
                limit = 100
            )
        } returns listOf(preActivationPayment)
        coEvery {
            preActivationPaymentService.cancelById(
                preActivationPayment.id
            )
        } returns preActivationPayment

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/cancel_expired_proposals_for_paps_first_payment") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { preActivationPaymentService.findPendingFirstPaymentsOlderThanDate(any(), any()) }
        coVerifyOnce { preActivationPaymentService.cancelById(any()) }
    }

    @Test
    fun `#cancelExpiredProposalsForMigsFirstPayment should cancel expired first payments`() = mockLocalDateTime { now ->
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

        coEvery {
            memberInvoiceGroupService.findPendingFirstPaymentsOlderThanDate(
                date = now.minusDays(30).atEndOfTheDay(),
                limit = 100
            )
        } returns listOf(memberInvoiceGroup)
        coEvery {
            memberInvoiceGroupService.cancelById(
                memberInvoiceGroup.id
            )
        } returns memberInvoiceGroup

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/cancel_expired_proposals_for_migs_first_payment") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { memberInvoiceGroupService.findPendingFirstPaymentsOlderThanDate(any(), any()) }
        coVerifyOnce { memberInvoiceGroupService.cancelById(any()) }
    }

    @Test
    fun `#cancelExpiredProposalsForMigsFirstPayment should cancel expired first payments and ignore error`() =
        mockLocalDateTime { now ->
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val memberInvoiceGroupError = TestModelFactory.buildMemberInvoiceGroup()

            coEvery {
                memberInvoiceGroupService.findPendingFirstPaymentsOlderThanDate(
                    date = now.minusDays(30).atEndOfTheDay(),
                    limit = 100
                )
            } returns listOf(memberInvoiceGroupError, memberInvoiceGroup)
            coEvery {
                memberInvoiceGroupService.cancelById(
                    memberInvoiceGroup.id
                )
            } returns memberInvoiceGroup
            coEvery {
                memberInvoiceGroupService.cancelById(
                    memberInvoiceGroupError.id
                )
            } returns NotFoundException("").failure()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/cancel_expired_proposals_for_migs_first_payment") { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce { memberInvoiceGroupService.findPendingFirstPaymentsOlderThanDate(any(), any()) }
            coVerify(exactly = 2) { memberInvoiceGroupService.cancelById(any()) }
        }

    @Test
    fun `#sendInvoiceLiquidationEmailNearToOverdue should handle exception and return OK`() = runBlocking {
        withFeatureFlag(FeatureNamespace.PAYMENTS, SEND_INVOICE_LIQUIDATION_EMAIL_NEAR_TO_OVERDUE, true) {
            coEvery { invoiceLiquidationService.listInvoicesNearOverdue(any()) } returns Exception().failure()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/send_liquidation_invoices_near_overdue") { response ->
                    assertThat(response).isOK()
                    coVerifyOnce {
                        invoiceLiquidationService.listInvoicesNearOverdue(
                            any(),
                        )
                    }
                    coVerify { invoicePaymentService wasNot called }
                    coVerify { invoiceMailer wasNot called }
                    coVerify { invoicePaymentService wasNot called }
                }
            }
        }
    }

    @Test
    fun `#cancelOlderFirstPaymentSchedules should cancel older first payment schedules`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()

        coEvery {
            firstPaymentScheduleService.findOlderThanOneMonthFromNow(100)
        } returns listOf(firstPaymentSchedule)
        coEvery {
            firstPaymentScheduleService.cancelList(
                listOf(firstPaymentSchedule)
            )
        } returns listOf(firstPaymentSchedule)

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/cancel_older_first_payment_schedules") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            firstPaymentScheduleService.findOlderThanOneMonthFromNow(100)
            firstPaymentScheduleService.cancelList(listOf(firstPaymentSchedule))
        }
    }
}
