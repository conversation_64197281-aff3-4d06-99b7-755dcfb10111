package br.com.alice.moneyin.consumers

import br.com.alice.business.client.CompanyService
import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.communication.crm.hubspot.b2b.HubspotSalesFunnelPipelineImpl
import br.com.alice.communication.crm.hubspot.b2b.client.DealResponse
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClient
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotCompanyDeal
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotDealSearchResponse
import br.com.alice.communication.crm.sales.b2b.BusinessCompanyDeal
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.client.InvoicePaymentDetailsNotFoundException
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.event.ExternalInvoiceCreatedEvent
import br.com.alice.moneyin.services.PaymentDetailService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import kotlin.test.Test

class ExternalInvoiceCreatedConsumerTest : ConsumerTest() {

    private val paymentDetailService: PaymentDetailService = mockk()
    private val invoiceMailer: InvoiceMailer = mockk()
    private val businessSalesCrmPipeline: BusinessSalesCrmPipeline = mockk()
    private val companyService: CompanyService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val hubspotClient: HubspotClient = mockk()
    private val invoicePaymentService: InvoicePaymentService = mockk()
    private val portalUrlGeneratorService: PortalUrlGeneratorService = mockk()

    private val consumer = ExternalInvoiceCreatedConsumer(
        paymentDetailService,
        invoiceMailer,
        businessSalesCrmPipeline,
        companyService,
        memberInvoiceGroupService,
        invoicePaymentService,
        hubspotClient,
        portalUrlGeneratorService
    )

    @Nested
    @DisplayName("createPaymentDetail")
    inner class CreatePaymentDetail {

        @Test
        fun `#Create paymentDetail with invoicePayment payment detail`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val paymentDetailCreated = TestModelFactory.buildPixPaymentDetail()
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetailCreated.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)

            assertThat(result).isSuccess()
            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerifyNone { invoiceMailer.send(any(), any()) }
        }

        @Test
        fun `#Create return paymentDetail when paymentDetail is already stored`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val otherPaymentDetail = TestModelFactory.buildPixPaymentDetail()
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns DuplicatedItemException("").failure()
            coEvery {
                paymentDetailService.getPaymentDetail(invoicePayment)
            } returns otherPaymentDetail.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)

            assertThat(result).isSuccess()
            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerifyOnce { paymentDetailService.getPaymentDetail(invoicePayment) }
            coVerifyNone { invoiceMailer.send(any(), any()) }
        }

        @Test
        fun `#Return failure when there's no paymentDetail info`() = runBlocking {
            val invoicePayment = TestModelFactory.buildInvoicePayment(paymentDetail = null)
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            val result = consumer.createPaymentDetailAndSendEmail(event)

            assertThat(result).isFailureOfType(InvoicePaymentDetailsNotFoundException::class)
            coVerifyNone { paymentDetailService.createPaymentDetail(any()) }
            coVerifyNone { invoiceMailer.send(any(), any()) }
        }

        @Test
        fun `#Should ignore invoice with no reason`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = null
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)

            assertThat(result).isSuccess()

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerifyNone { invoiceMailer.send(any(), any()) }
        }

        @Test
        fun `#Should return success if email was successfully sent`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val reason = PaymentReason.REGULAR_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)
            val emailReceipt = EmailReceipt("some_id")

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            coEvery { invoiceMailer.send(invoicePayment, reason) } returns emailReceipt.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerifyOnce { invoiceMailer.send(invoicePayment, reason) }
        }

        @Test
        fun `#Should return success if email was successfully sent with duplicated event`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val reason = PaymentReason.REGULAR_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)
            val emailReceipt = EmailReceipt("some_id")
            val otherPaymentDetail = TestModelFactory.buildPixPaymentDetail()

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns DuplicatedItemException("").failure()
            coEvery {
                paymentDetailService.getPaymentDetail(invoicePayment)
            } returns otherPaymentDetail.success()

            coEvery { invoiceMailer.send(invoicePayment.withPaymentDetail(otherPaymentDetail), reason) } returns emailReceipt.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerifyOnce { invoiceMailer.send(invoicePayment.withPaymentDetail(otherPaymentDetail), reason) }
        }

        @Test
        fun `#Should return success if email was successfully sent for Bolepix`() = runBlocking {
            val paymentDetail = TestModelFactory.buildBolepixPaymentDetail()
            val reason = PaymentReason.REGULAR_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)
            val emailReceipt = EmailReceipt("some_id")

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            coEvery { invoiceMailer.send(invoicePayment, reason) } returns emailReceipt.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerifyOnce { invoiceMailer.send(invoicePayment, reason) }
        }

        @Test
        fun `#Should return success if OVERDUE email was successfully sent`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val reason = PaymentReason.OVERDUE_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)
            val emailReceipt = EmailReceipt("some_id")

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            coEvery { invoiceMailer.send(invoicePayment, reason) } returns emailReceipt.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerify(exactly = 1) { invoiceMailer.send(invoicePayment, reason) }
        }

        @Test
        fun `#Should ignore OVERDUE when feature flag is enabled`() = runBlocking  {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val reason = PaymentReason.OVERDUE_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            withFeatureFlag(FeatureNamespace.PAYMENTS, "ignore_overdue_payment_email", true) {
                val result = consumer.createPaymentDetailAndSendEmail(event)
                assertThat(result).isSuccess()
            }

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerify(exactly = 0) { invoiceMailer.send(any(), any()) }
        }

        @Test
        fun `#Should send PIX email if feature flag is enabled`() = runBlocking {
            val paymentDetail = TestModelFactory.buildPixPaymentDetail()
            val reason = PaymentReason.OVERDUE_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)
            val emailReceipt = EmailReceipt("some_id")

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            coEvery { invoiceMailer.send(invoicePayment, reason) } returns emailReceipt.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerify(exactly = 1) { invoiceMailer.send(invoicePayment, reason) }
        }

        @Test
        fun `#Should ignore SIMPLE_CREDIT_CARD payment method`() = runBlocking  {
            val paymentDetail = TestModelFactory.buildSimpleCreditCardPaymentDetail()
            val reason = PaymentReason.OVERDUE_PAYMENT
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentDetail.method,
                paymentDetail = paymentDetail,
                reason = reason,
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            coEvery {
                paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id))
            } returns paymentDetail.success()

            val result = consumer.createPaymentDetailAndSendEmail(event)
            assertThat(result).isSuccess()

            coVerifyOnce { paymentDetailService.createPaymentDetail(paymentDetail.copy(paymentId = invoicePayment.id)) }
            coVerify(exactly = 0) { invoiceMailer.send(any(), any()) }
        }
    }

    @Nested
    inner class UpdateHubspotWithPaymentUrl {

        @Test
        fun `#Should return success if there's company in Hubspot`() = runBlocking {
            val paymentUrl = "http://payment.url"
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                reason = PaymentReason.B2B_FIRST_PAYMENT,
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail()
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val company = TestModelFactory.buildCompany()
            val companyDealToUpdate = BusinessCompanyDeal(
                companyId = company.id.toString(),
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealStage = HubspotSalesFunnelPipelineImpl.backgroundCheckStageId,
                cnpj = company.cnpj,
            )
            val hubspotCompanyDeal = HubspotCompanyDeal(
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealstage = "*********",
                b2b_company_id_aos = companyDealToUpdate.companyId,
                empresa_cnpj = companyDealToUpdate.cnpj,
                dealname = companyDealToUpdate.dealName,
                deal_email = companyDealToUpdate.dealEmail,
                deal_phone = companyDealToUpdate.dealPhone,
                zip_code = companyDealToUpdate.addressPostalCode,
                link_do_boleto_empresarial = null,
                n3p_corretora = companyDealToUpdate.broker,
                n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
            )
            val deals = listOf(
                DealResponse(
                    id = "id",
                    properties = hubspotCompanyDeal
                )
            )

            val dealResult = DealResponse(id = "deal_response_id", properties = hubspotCompanyDeal.copy(link_do_boleto_empresarial = paymentUrl))
            val expectedResponse = listOf(dealResult)

            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup.success()

            coEvery { companyService.get(memberInvoiceGroup.companyId!!) } returns company.success()

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl.success()

            coEvery {
                businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
            } returns HubspotDealSearchResponse(total = 1, results = deals)

            coEvery {
                hubspotClient.updateCompanyDeal(
                    any(),
                    HubspotCompanyDeal(link_do_boleto_empresarial = paymentUrl)
                )
            } returns dealResult

            val result = consumer.updateHubspotWithPaymentUrl(event)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { hubspotClient.updateCompanyDeal(any(), any()) }
        }

        @Test
        fun `#Should return success if there's company in Hubspot (No invoiceGroupId Id in Event)`() = runBlocking {
            val paymentUrl = "http://payment.url"
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                reason = PaymentReason.B2B_FIRST_PAYMENT,
                paymentDetail = TestModelFactory.buildBoletoPaymentDetail(paymentUrl = paymentUrl)
            )

            val nullPayment = invoicePayment.copy(invoiceGroupId = null, id = invoicePayment.id)

            val nullEvent = ExternalInvoiceCreatedEvent(nullPayment)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val company = TestModelFactory.buildCompany()
            val companyDealToUpdate = BusinessCompanyDeal(
                companyId = company.id.toString(),
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealStage = HubspotSalesFunnelPipelineImpl.backgroundCheckStageId,
                cnpj = company.cnpj,
            )
            val hubspotCompanyDeal = HubspotCompanyDeal(
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealstage = "*********",
                b2b_company_id_aos = companyDealToUpdate.companyId,
                empresa_cnpj = companyDealToUpdate.cnpj,
                dealname = companyDealToUpdate.dealName,
                deal_email = companyDealToUpdate.dealEmail,
                deal_phone = companyDealToUpdate.dealPhone,
                zip_code = companyDealToUpdate.addressPostalCode,
                link_do_boleto_empresarial = null,
                n3p_corretora = companyDealToUpdate.broker,
                n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
            )
            val deals = listOf(
                DealResponse(
                    id = "id",
                    properties = hubspotCompanyDeal
                )
            )

            val dealResult = DealResponse(id = "deal_response_id", properties = hubspotCompanyDeal.copy(link_do_boleto_empresarial = paymentUrl))
            val expectedResponse = listOf(dealResult)

            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup.success()

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl.success()

            coEvery { invoicePaymentService.get(invoicePayment.id) } returns invoicePayment.success()

            coEvery { companyService.get(memberInvoiceGroup.companyId!!) } returns company.success()

            coEvery {
                businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
            } returns HubspotDealSearchResponse(total = 1, results = deals)

            coEvery {
                hubspotClient.updateCompanyDeal(
                    any(),
                    HubspotCompanyDeal(link_do_boleto_empresarial = paymentUrl)
                )
            } returns dealResult

            val result = consumer.updateHubspotWithPaymentUrl(nullEvent)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { hubspotClient.updateCompanyDeal(any(), any()) }
        }

        @Test
        fun `#Should return success if there's company in Hubspot (PixPayment)`() = runBlocking {
            val paymentUrl = "http://payment.url"

            val invoicePayment = TestModelFactory.buildInvoicePayment(
                reason = PaymentReason.B2B_FIRST_PAYMENT,
                paymentDetail = TestModelFactory.buildPixPaymentDetail(paymentUrl = paymentUrl),
                method = PaymentMethod.PIX
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val company = TestModelFactory.buildCompany()
            val companyDealToUpdate = BusinessCompanyDeal(
                companyId = company.id.toString(),
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealStage = HubspotSalesFunnelPipelineImpl.backgroundCheckStageId,
                cnpj = company.cnpj,
            )
            val hubspotCompanyDeal = HubspotCompanyDeal(
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealstage = "*********",
                b2b_company_id_aos = companyDealToUpdate.companyId,
                empresa_cnpj = companyDealToUpdate.cnpj,
                dealname = companyDealToUpdate.dealName,
                deal_email = companyDealToUpdate.dealEmail,
                deal_phone = companyDealToUpdate.dealPhone,
                zip_code = companyDealToUpdate.addressPostalCode,
                    link_do_boleto_empresarial = null,
                n3p_corretora = companyDealToUpdate.broker,
                n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
            )
            val deals = listOf(
                DealResponse(
                    id = "id",
                    properties = hubspotCompanyDeal
                )
            )

            val dealResult = DealResponse(id = "deal_response_id", properties = hubspotCompanyDeal.copy(link_do_boleto_empresarial = paymentUrl))
            val expectedResponse = listOf(dealResult)

            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup.success()

            coEvery { companyService.get(memberInvoiceGroup.companyId!!) } returns company.success()

            coEvery {
                businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
            } returns HubspotDealSearchResponse(total = 1, results = deals)

            coEvery {
                hubspotClient.updateCompanyDeal(
                    any(),
                    HubspotCompanyDeal(link_do_boleto_empresarial = paymentUrl)
                )
            } returns dealResult

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl.success()

            val result = consumer.updateHubspotWithPaymentUrl(event)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { hubspotClient.updateCompanyDeal(any(), any()) }
        }

        @Test
        fun `#Should return success if there's company in Hubspot (BolePixPayment)`() = runBlocking {
            val paymentUrl = "http://payment.url"

            val invoicePayment = TestModelFactory.buildInvoicePayment(
                reason = PaymentReason.B2B_FIRST_PAYMENT,
                paymentDetail = TestModelFactory.buildBolepixPaymentDetail(paymentUrl = paymentUrl),
                method = PaymentMethod.BOLEPIX
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val company = TestModelFactory.buildCompany()
            val companyDealToUpdate = BusinessCompanyDeal(
                companyId = company.id.toString(),
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealStage = HubspotSalesFunnelPipelineImpl.backgroundCheckStageId,
                cnpj = company.cnpj,
            )
            val hubspotCompanyDeal = HubspotCompanyDeal(
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealstage = "*********",
                b2b_company_id_aos = companyDealToUpdate.companyId,
                empresa_cnpj = companyDealToUpdate.cnpj,
                dealname = companyDealToUpdate.dealName,
                deal_email = companyDealToUpdate.dealEmail,
                deal_phone = companyDealToUpdate.dealPhone,
                zip_code = companyDealToUpdate.addressPostalCode,
                link_do_boleto_empresarial = null,
                n3p_corretora = companyDealToUpdate.broker,
                n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
            )
            val deals = listOf(
                DealResponse(
                    id = "id",
                    properties = hubspotCompanyDeal
                )
            )

            val dealResult = DealResponse(id = "deal_response_id", properties = hubspotCompanyDeal.copy(link_do_boleto_empresarial = paymentUrl))
            val expectedResponse = listOf(dealResult)

            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup.success()

            coEvery { companyService.get(memberInvoiceGroup.companyId!!) } returns company.success()

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl.success()

            coEvery {
                businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
            } returns HubspotDealSearchResponse(total = 1, results = deals)

            coEvery {
                hubspotClient.updateCompanyDeal(
                    any(),
                    HubspotCompanyDeal(link_do_boleto_empresarial = paymentUrl)
                )
            } returns dealResult

            val result = consumer.updateHubspotWithPaymentUrl(event)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { hubspotClient.updateCompanyDeal(any(), any()) }
        }

        @Test
        fun `#Should return success if there's company in Hubspot (CreditCardPayment)`() = runBlocking {
            val paymentUrl = "SIMPLE_CREDIT_CARD não possui codigo de barras/código pix para pagamento"

            val invoicePayment = TestModelFactory.buildInvoicePayment(
                reason = PaymentReason.B2B_FIRST_PAYMENT,
                paymentDetail = TestModelFactory.buildSimpleCreditCardPaymentDetail(paymentUrl = paymentUrl),
                method = PaymentMethod.SIMPLE_CREDIT_CARD
            )
            val event = ExternalInvoiceCreatedEvent(invoicePayment)

            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()
            val company = TestModelFactory.buildCompany()
            val companyDealToUpdate = BusinessCompanyDeal(
                companyId = company.id.toString(),
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealStage = HubspotSalesFunnelPipelineImpl.backgroundCheckStageId,
                cnpj = company.cnpj,
            )
            val hubspotCompanyDeal = HubspotCompanyDeal(
                pipeline = HubspotSalesFunnelPipelineImpl.pipelineIdBrokerInternal,
                dealstage = "*********",
                b2b_company_id_aos = companyDealToUpdate.companyId,
                empresa_cnpj = companyDealToUpdate.cnpj,
                dealname = companyDealToUpdate.dealName,
                deal_email = companyDealToUpdate.dealEmail,
                deal_phone = companyDealToUpdate.dealPhone,
                zip_code = companyDealToUpdate.addressPostalCode,
                link_do_boleto_empresarial = null,
                n3p_corretora = companyDealToUpdate.broker,
                n3p__cpf_do_corretor = companyDealToUpdate.salesAgentDocument
            )
            val deals = listOf(
                DealResponse(
                    id = "id",
                    properties = hubspotCompanyDeal
                )
            )

            val dealResult = DealResponse(id = "deal_response_id", properties = hubspotCompanyDeal.copy(link_do_boleto_empresarial = paymentUrl))
            val expectedResponse = listOf(dealResult)

            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup.success()

            coEvery { companyService.get(memberInvoiceGroup.companyId!!) } returns company.success()

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl.success()

            coEvery {
                businessSalesCrmPipeline.searchDealByCnpj(company.cnpj)
            } returns HubspotDealSearchResponse(total = 1, results = deals)

            coEvery {
                hubspotClient.updateCompanyDeal(
                    any(),
                    HubspotCompanyDeal(link_do_boleto_empresarial = paymentUrl)
                )
            } returns dealResult

            val result = consumer.updateHubspotWithPaymentUrl(event)
            assertThat(result).isSuccessWithData(expectedResponse)

            coVerifyOnce { hubspotClient.updateCompanyDeal(any(), any()) }
        }

    }
}
