package br.com.alice.moneyin.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.moneyin.services.internal.MemberInvoiceGroupFirstPaymentServiceImpl
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentFailedEvent
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID
import kotlin.test.AfterTest

class NullvsFirstPaymentConsumerTest : ConsumerTest() {

    private val memberInvoiceGroupFirstPaymentService: MemberInvoiceGroupFirstPaymentServiceImpl = mockk()
    private val consumer = NullvsFirstPaymentConsumer(memberInvoiceGroupFirstPaymentService)

    @AfterTest
    fun setup() = clearAllMocks()

    @Nested
    inner class CreateMemberInvoiceGroupForFirstPayment {

        private val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

        private val invoiceItemDetailItem = NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem(
            referenceDate = LocalDate.now(),
            operation = InvoiceItemOperation.CHARGE,
            type = InvoiceItemType.PRODUCT_CHANGE,
            notes = "",
            value = 50.money,
        )

        private val memberInvoiceDetail = NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail(
            memberId = RangeUUID.generate(),
            totalAmount = 50.money,
            referenceDate = LocalDate.now(),
            dueDate = LocalDate.now(),
            items = listOf(invoiceItemDetailItem),
        )

        private val event = NullvsFirstPaymentCreatedEvent(
            payload = NullvsFirstPaymentCreatedEvent.Payload(
                firstPaymentScheduleId = UUID.randomUUID().toString(),
                externalId = "external-id-123",
                companyContractNumber = "contract-123",
                groupCompany = "company-abc",
                memberDetails = listOf(memberInvoiceDetail),
                companySubContractNumber = "subcontract-123",
                billingAccountablePartyId = RangeUUID.generate(),
                dueDate = LocalDate.now(),
                value = 50.money,
                paidAt = LocalDate.now(),
                gloablItems = emptyList(),
                referenceDate = LocalDate.now(),
                discount = 0.0.money,
            )
        )

        @Test
        fun `#should call service and return result`() = runBlocking<Unit> {
            coEvery {
                memberInvoiceGroupFirstPaymentService.createFromEvent(event)
            } returns memberInvoiceGroup

            val result = consumer.createMemberInvoiceGroupForFirstPayment(event)

            assertThat(result).isSuccessWithData(memberInvoiceGroup)

            coVerifyOnce {
                memberInvoiceGroupFirstPaymentService.createFromEvent(event)
            }
        }

        @Test
        fun `#should propagate errors from service`() = runBlocking<Unit> {
            coEvery {
                memberInvoiceGroupFirstPaymentService.createFromEvent(event)
            } returns Exception("error")

            val result = consumer.createMemberInvoiceGroupForFirstPayment(event)
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce {
                memberInvoiceGroupFirstPaymentService.createFromEvent(event)
            }
        }
    }

    @Nested
    inner class MarkFirstPaymentScheduleAsFailureFromEvent {
        private val event = NullvsFirstPaymentFailedEvent(
            payload = NullvsFirstPaymentFailedEvent.Payload(
                firstPaymentScheduleId = UUID.randomUUID().toString(),
                errorMessage = "error"
            )
        )

        private val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()

        @Test
        fun `#should call service and return result`() = runBlocking<Unit> {
            coEvery {
                memberInvoiceGroupFirstPaymentService.markFirstPaymentScheduleAsFailureFromEvent(event)
            } returns firstPaymentSchedule

            val result = consumer.markFirstPaymentScheduleAsFailureFromEvent(event)

            assertThat(result).isSuccessWithData(firstPaymentSchedule)

            coVerifyOnce {
                memberInvoiceGroupFirstPaymentService.markFirstPaymentScheduleAsFailureFromEvent(event)
            }
        }
    }
}
