package br.com.alice.moneyin.consumers

import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.nullvs.events.NullvsScheduleFirstPaymentResponseEvent
import io.mockk.coEvery
import io.mockk.mockk
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.util.UUID
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FirstPaymentScheduleConsumerTest : ConsumerTest() {
    private val firstPaymentScheduleService: FirstPaymentScheduleService = mockk()

    private val consumer = FirstPaymentScheduleConsumer(firstPaymentScheduleService)

    @Nested
    inner class UpdateFromNullvs {

        private val firstPaymentScheduleId = UUID.randomUUID()
        private val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule(id = firstPaymentScheduleId)

        private val successfulEvent = NullvsScheduleFirstPaymentResponseEvent(
            firstPaymentScheduleId,
            "0001",
            null
        )

        private val failureEvent =
            NullvsScheduleFirstPaymentResponseEvent(
                firstPaymentScheduleId,
                null,
                "Something was wrong",
            )

        @Test
        fun `#should update as finished when the event is bearing the external id`() = mockLocalDateTime {
            val firstPaymentScheduleUpdated = firstPaymentSchedule.markAsScheduled("0001")

            coEvery { firstPaymentScheduleService.get(firstPaymentScheduleId) } returns firstPaymentSchedule
            coEvery { firstPaymentScheduleService.update(firstPaymentScheduleUpdated) } returns firstPaymentScheduleUpdated

            val result = consumer.updateFromNullvs(successfulEvent)

            ResultAssert.assertThat(result).isSuccessWithData(firstPaymentScheduleUpdated)

            coVerifyOnce {
                firstPaymentScheduleService.get(firstPaymentScheduleId)
                firstPaymentScheduleService.update(firstPaymentScheduleUpdated)
            }
        }

        @Test
        fun `#should update as failure when the event is bearing an error message`() = mockLocalDateTime {
            val firstPaymentScheduleUpdated = firstPaymentSchedule.markAsFailure("Something was wrong")

            coEvery { firstPaymentScheduleService.get(firstPaymentScheduleId) } returns firstPaymentSchedule
            coEvery { firstPaymentScheduleService.update(firstPaymentScheduleUpdated) } returns firstPaymentScheduleUpdated

            val result = consumer.updateFromNullvs(failureEvent)

            ResultAssert.assertThat(result).isSuccessWithData(firstPaymentScheduleUpdated)

            coVerifyOnce {
                firstPaymentScheduleService.get(firstPaymentScheduleId)
                firstPaymentScheduleService.update(firstPaymentScheduleUpdated)
            }
        }

        @Test
        fun `#should throw an exception when the event is not bearing neither external id nor error message`() =
            mockLocalDateTime {
                coEvery { firstPaymentScheduleService.get(firstPaymentScheduleId) } returns firstPaymentSchedule

                val event = NullvsScheduleFirstPaymentResponseEvent(
                    firstPaymentScheduleId,
                    null,
                    null
                )

                val result = consumer.updateFromNullvs(event)

                ResultAssert.assertThat(result).isFailureOfType(FirstPaymentScheduleUnknownStateException::class)

                coVerifyOnce {
                    firstPaymentScheduleService.get(firstPaymentScheduleId)
                }

                coVerifyNone {
                    firstPaymentScheduleService.update(any())
                }
            }

    }

}
