package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingModel
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDate
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertTrue

class ResourceBundleSpecialtyPricingServiceTest {

    private val dataService: ResourceBundleSpecialtyPricingModelDataService = mockk()
    private val service = ResourceBundleSpecialtyPricingService(dataService)

    private val prices = listOf(
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_3,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_2,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_1,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_2,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_1,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_0,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_1,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_0,100.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.ULTRA_EXPERT, TierType.TIER_0,100.0.toBigDecimal()),
    )

    @AfterTest
    fun clear() = clearAllMocks()

    @BeforeTest
    fun setup() {
        mockkStatic(LocalDate::class)
    }

    @Test
    fun `#getEffectiveDaysCheckingWorkDays should return current month and next month`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        coEvery { LocalDate.now() } returns nowDate

        val result = service.getEffectiveDaysCheckingWorkDays()

        assertEquals(result.size, 2)
        assertTrue(result.contains(LocalDate.of(2025, 6, 1)))
        assertTrue(result.contains(LocalDate.of(2025, 7, 1)))
    }

    @Test
    fun `#getEffectiveDaysCheckingWorkDays should return current month and previous month`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 3)

        coEvery { LocalDate.now() } returns nowDate

        val result = service.getEffectiveDaysCheckingWorkDays()

        assertEquals(result.size, 2)
        assertTrue(result.contains(LocalDate.of(2025, 6, 1)))
        assertTrue(result.contains(LocalDate.of(2025, 5, 1)))
    }

    @Test
    fun `#onSpecialtyInactivation should expire old`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val currentSpecialty = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusMonths(2),
            endAt = null,
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(currentSpecialty.toModel()).success()
        coEvery { dataService.update(currentSpecialty.expire(nowDate).toModel()) } returns currentSpecialty.toModel().success()

        val result = service.onSpecialtyInactivation(resourceBundleSpecialtyId)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyNone { dataService.add(any()) }
        coVerifyNone { dataService.delete(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#onSpecialtyInactivation should expire old and Delete Future`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()

        val current = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = nowDate.minusMonths(2),
            endAt = nowDate.atEndOfTheMonth(),
            prices = prices
        )

        val future = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = nowDate.plusMonths(1).atBeginningOfTheMonth(),
            endAt = null,
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(current.toModel(), future.toModel()).success()
        coEvery { dataService.update(current.expire(nowDate).toModel()) } returns current.toModel().success()
        coEvery { dataService.delete(future.toModel()) } returns true.success()

        val result = service.onSpecialtyInactivation(resourceBundleSpecialtyId)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyNone { dataService.add(any()) }
        coVerifyOnce { dataService.delete(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#onSpecialtyActivation should reactivate same month`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()

        val current = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = nowDate.minusMonths(2),
            endAt = nowDate.minusDays(3),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(current.toModel()).success()
        coEvery { dataService.update(current.reactivate().toModel()) } returns current.toModel().success()

        service.onSpecialtyActivation(resourceBundleSpecialtyId)

        coVerifyNone { dataService.add(any()) }
        coVerifyNone { dataService.delete(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should create when there is no price`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val cretedExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns emptyList<ResourceBundleSpecialtyPricingModel>().success()
        mockAdd(cretedExpected)

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyNone { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should create when there is only expired past prices`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val createdExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        val expiredPrices = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusMonths(2),
            endAt = effectiveDate.minusMonths(1),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(expiredPrices.toModel()).success()
        mockAdd(createdExpected)

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyNone { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should create and expire past prices`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val createdExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        val expiredPrices = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusMonths(2),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(expiredPrices.toModel()).success()
        mockAdd(createdExpected)
        mockUpdate(expiredPrices.expire(effectiveDate.minusDays(1)))

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should create and expire past deactivated prices`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val createdExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        val expiredPrices = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusMonths(2),
            endAt = effectiveDate.plusDays(5),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(expiredPrices.toModel()).success()
        mockAdd(createdExpected)
        mockUpdate(expiredPrices.expire(effectiveDate.minusDays(1)))

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }


    @Test
    fun `#addNewPricing should create future and expire past prices`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 7, 1)

        val createdExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        val expiredPrices = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusMonths(1),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(expiredPrices.toModel()).success()
        mockAdd(createdExpected)
        mockUpdate(expiredPrices.expire(effectiveDate.minusDays(1)))

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should create expired price`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val futurePricing = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.plusMonths(1),
            prices = prices
        )

        val createdExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = futurePricing.beginAt.minusDays(1),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(futurePricing.toModel()).success()
        mockAdd(createdExpected)

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyNone { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should update existing active price`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val active = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(active.toModel()).success()
        mockUpdate(active)

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyNone { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should update existing active reactivate`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val active = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = effectiveDate.plusDays(2),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(active.toModel()).success()
        mockUpdate(active.copy(endAt = null))

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyNone { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should update existing active price even if exists future price`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val active = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = prices
        )

        val futurePricing = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.plusMonths(1),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(active.toModel(), futurePricing.toModel()).success()
        mockUpdate(active)

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyNone { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#addNewPricing should create expired and expire past prices`() = runBlocking {
        val nowDate = LocalDate.of(2025, 6, 15)

        val resourceBundleSpecialtyId = UUID.randomUUID()
        val effectiveDate = LocalDate.of(2025, 6, 1)

        val expiredPrices = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusMonths(2),
            endAt = effectiveDate.plusDays(5),
            prices = prices
        )

        val futurePricing = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.plusMonths(1),
            prices = prices
        )

        val createdExpected = ResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = futurePricing.beginAt.minusDays(1),
            prices = prices
        )

        coEvery { LocalDate.now() } returns nowDate
        coEvery { dataService.find(any()) } returns listOf(
            expiredPrices.toModel(),
            futurePricing.toModel()
        ).success()
        mockAdd(createdExpected)
        mockUpdate(expiredPrices.expire(effectiveDate.minusDays(1)))

        val result = service.addNewPricing(resourceBundleSpecialtyId, effectiveDate, prices)
        ResultAssert.assertThat(result).isSuccess()

        coVerifyOnce { dataService.add(any()) }
        coVerifyOnce { dataService.update(any()) }
    }

    private fun mockAdd(pricing: ResourceBundleSpecialtyPricing) {
        coEvery { dataService.add(match {
            it.resourceBundleSpecialtyId == pricing.resourceBundleSpecialtyId &&
                    it.beginAt == pricing.beginAt &&
                    it.endAt == pricing.endAt
        }) } returns pricing.toModel().success()
    }

    private fun mockUpdate(pricing: ResourceBundleSpecialtyPricing) {
        coEvery { dataService.update(match {
            it.resourceBundleSpecialtyId == pricing.resourceBundleSpecialtyId &&
                    it.beginAt == pricing.beginAt &&
                    it.endAt == pricing.endAt
        }) } returns pricing.toModel().success()
    }


    @Test
    fun `getCurrentlyActiveByResourceBundleSpecialtyId should return only active pricing`() = runBlocking {
        val resourceBundleSpecialtyId = UUID.randomUUID()
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialtyId)

        val pricing1 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(10),
            endAt = null // Active pricing
        )
        val pricing2 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(20),
            endAt = LocalDate.now().minusDays(5) // Inactive pricing
        )

        coEvery { dataService.find(any()) } returns listOf(pricing1, pricing2).map { it.toModel() }.success()

        val result = service.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)

        assertEquals(Result.success(listOf(pricing1)), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }
}
