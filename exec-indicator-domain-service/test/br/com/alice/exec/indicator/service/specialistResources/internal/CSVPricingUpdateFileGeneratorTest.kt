package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.logging.logger
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.readFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CSVPricingUpdateError
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.GenericVaultUploadByteArray
import br.com.alice.filevault.models.VaultResponse
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.assertj.core.api.Assertions
import java.io.ByteArrayInputStream
import java.io.File
import java.io.InputStreamReader
import java.math.BigDecimal
import java.nio.charset.Charset
import java.time.LocalDate
import kotlin.math.log
import kotlin.test.Test

class CSVPricingUpdateFileGeneratorTest {

    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()

    private val service = CSVPricingUpdateFileGenerator(
        resourceBundleSpecialtyService,
        resourceBundleSpecialtyPricingService,
        medicalSpecialtyService,
        healthSpecialistResourceBundleService,
        resourceBundleSpecialtyPricingUpdateService,
        fileVaultActionService,
    )

    private val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle()
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()

    private val resourceBundleSpecialty = TestModelFactory.buildResourceBundleSpecialty(
        resourceBundleId = healthSpecialistResourceBundle.id,
        specialtyId = medicalSpecialty.id,
    )

    private val pricing = TestModelFactory.buildResourceBundleSpecialtyPricing(
        resourceBundleSpecialtyId = resourceBundleSpecialty.id,
        prices = listOf(
            ResourceBundleSpecialtyPrice(
                tier = SpecialistTier.TALENTED,
                price = BigDecimal(100.15),
                productTier = TierType.TIER_0
            ),
            ResourceBundleSpecialtyPrice(
                tier = SpecialistTier.SUPER_EXPERT,
                price = BigDecimal(200.39),
                productTier = TierType.TIER_1
            )
        )
    )

    private val resourcePricingCSV = ResourceSpecialtyPricingCSV(
        resourceBundleSpecialtyId = resourceBundleSpecialty.id,
        medicalSpecialtyName = medicalSpecialty.name,
        aliceCode = healthSpecialistResourceBundle.code,
        primaryTuss = healthSpecialistResourceBundle.primaryTuss,
        description = healthSpecialistResourceBundle.description,
        executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
        talentedT3 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_3),
        talentedT2 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_2),
        talentedT1 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_1),
        talentedT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_0),
        expertT2 = pricing.getPriceByTierAndProductTier(SpecialistTier.EXPERT, TierType.TIER_2),
        expertT1 = pricing.getPriceByTierAndProductTier(SpecialistTier.EXPERT, TierType.TIER_1),
        expertT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.EXPERT, TierType.TIER_0),
        superExpertT1 = pricing.getPriceByTierAndProductTier(SpecialistTier.SUPER_EXPERT, TierType.TIER_1),
        superExpertT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.SUPER_EXPERT, TierType.TIER_0),
        ultraExpertT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.ULTRA_EXPERT, TierType.TIER_0)
    )

    private val csvFile = File(javaClass.classLoader.getResource("generated-csv-example.csv")!!.path)
    private val fileVaultId = RangeUUID.generate()
    private val fileName = "generated-csv-example.csv"

    private val genericVaultByteArray = GenericVaultUploadByteArray(
        domain = CSVPricingUpdateFileGenerator.EITA_FILE_VAULT_STORAGE_DOMAIN,
        namespace = CSVPricingUpdateFileGenerator.EITA_FILE_VAULT_STORAGE_NAMESPACE,
        originalFileName = csvFile.name,
        fileContent = ByteArray(0),
        fileType = FileType.fromExtension("csv")!!,
        fileSize = 0L
    )

    private val genericFileVault = TestModelFactory.buildGenericFileVault(
        id = fileVaultId,
        domain = CSVPricingUpdateFileGenerator.EITA_FILE_VAULT_STORAGE_DOMAIN,
        namespace = CSVPricingUpdateFileGenerator.EITA_FILE_VAULT_STORAGE_NAMESPACE,
        originalFileName = fileName,
        fileSize = 0L
    )

    @Test
    fun `generate all`(): Unit = runBlocking {
        coEvery {
            resourceBundleSpecialtyService.findAllActive()
        } returns listOf(resourceBundleSpecialty)

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id))
        } returns listOf(pricing)

        coEvery {
            medicalSpecialtyService.getByIds(listOf(medicalSpecialty.id))
        } returns listOf(medicalSpecialty)

        coEvery {
            healthSpecialistResourceBundleService.findByIds(listOf(healthSpecialistResourceBundle.id))
        } returns listOf(healthSpecialistResourceBundle)

        val result = service.generate(emptyList())

        assertThat(result).isSuccess()

        val csvBytes = result.get().bytes
        val reader = InputStreamReader(ByteArrayInputStream(csvBytes))
        val csvFormat = CSVFormat.DEFAULT
            .withFirstRecordAsHeader()
            .withDelimiter(CSVPricingUpdateFileGenerator.CSV_DELIMITER.single())
            .withIgnoreSurroundingSpaces()
            .withRecordSeparator('\n')

        val parser = CSVParser(reader, csvFormat)

        val records = parser.records
        Assertions.assertThat(records).hasSize(1)

        val record = records[0]

        Assertions.assertThat(record.get("RESOURCE_BUNDLE_SPECIALTY_ID"))
            .isEqualTo(resourceBundleSpecialty.id.toString())
        Assertions.assertThat(record.get("Especialidade")).isEqualTo(medicalSpecialty.name)
        Assertions.assertThat(record.get("Alice Code")).isEqualTo(healthSpecialistResourceBundle.code)
        Assertions.assertThat(record.get("TALENTED T0")).isEqualTo(resourcePricingCSV.talentedT0.toString())
        Assertions.assertThat(record.get("SUPER EXPERT T1")).isEqualTo(resourcePricingCSV.superExpertT1.toString())

        parser.close()

        coVerifyOnce {
            resourceBundleSpecialtyService.findAllActive()
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id))
            medicalSpecialtyService.getByIds(listOf(medicalSpecialty.id))
            healthSpecialistResourceBundleService.findByIds(listOf(healthSpecialistResourceBundle.id))
        }

    }

    @Test
    fun `generate passing id if resource bundle specialties is not found`() = runBlocking {
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialty.id)
        coEvery {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
        } returns emptyList()

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        } returns emptyList()

        val result = service.generate(resourceBundleSpecialtyIds)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        }

    }

    @Test
    fun `generate CSV response correctly`() = runBlocking {
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialty.id)
        coEvery {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
        } returns listOf(resourceBundleSpecialty)

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        } returns listOf(pricing)

        val medicalSpecialtyIds = listOf(medicalSpecialty.id)
        coEvery {
            medicalSpecialtyService.getByIds(medicalSpecialtyIds)
        } returns listOf(medicalSpecialty)

        val resourceBundleIds = listOf(healthSpecialistResourceBundle.id)
        coEvery {
            healthSpecialistResourceBundleService.findByIds(resourceBundleIds)
        } returns listOf(healthSpecialistResourceBundle)

        val result = service.generate(resourceBundleSpecialtyIds)
        assertThat(result).isSuccess()

        val csvBytes = result.get().bytes
        val reader = InputStreamReader(ByteArrayInputStream(csvBytes))

        val csvFormat = CSVFormat.DEFAULT
            .withFirstRecordAsHeader()
            .withDelimiter(CSVPricingUpdateFileGenerator.CSV_DELIMITER.single())
            .withIgnoreSurroundingSpaces()
            .withRecordSeparator('\n')

        val parser = CSVParser(reader, csvFormat)
        val records = parser.records

        Assertions.assertThat(records).hasSize(1)

        val record = records[0]

        Assertions.assertThat(record.get("RESOURCE_BUNDLE_SPECIALTY_ID"))
            .isEqualTo(resourceBundleSpecialty.id.toString())
        Assertions.assertThat(record.get("Especialidade")).isEqualTo(medicalSpecialty.name)
        Assertions.assertThat(record.get("Alice Code")).isEqualTo(healthSpecialistResourceBundle.code)

        Assertions.assertThat(record.get("TALENTED T0")).isEqualTo(resourcePricingCSV.talentedT0.toString())
        Assertions.assertThat(record.get("SUPER EXPERT T1")).isEqualTo(resourcePricingCSV.superExpertT1.toString())

        parser.close()

        coVerifyOnce {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
            medicalSpecialtyService.getByIds(medicalSpecialtyIds)
            healthSpecialistResourceBundleService.findByIds(resourceBundleIds)
        }

    }


    @Test
    fun `generateFailedLinesFile should return csv file`() = runBlocking {
        val resourceBundleSpecialtyPricingUpdateId = RangeUUID.generate()
        val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate(
            failedRowsErrors = listOf(
                CSVPricingUpdateError(
                    row = 1,
                    error = "Error 1",
                )
            )
        )

        val vaultResponse = VaultResponse(
            id = resourceBundleSpecialtyPricingUpdate.fileVaultId,
            fileName = "failed_lines.csv",
            type = "text/csv",
            vaultUrl = "example.com",
            fileSize = 1234,
            url = "file://${System.getProperty("user.dir")}/testResources/specialistResources/blank_price.csv"
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdateId)
        } returns resourceBundleSpecialtyPricingUpdate.success()

        coEvery {
            fileVaultActionService.securedGenericLink(resourceBundleSpecialtyPricingUpdate.fileVaultId)
        } returns vaultResponse.success()

        val result = service.generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId)

        assertThat(result).isSuccess()

        val csvBytes = result.get().bytes.toString(Charset.defaultCharset())
        val blankPriceFile = readFile("testResources/specialistResources/blank_price_with_obs.csv")
        Assertions.assertThat(csvBytes).isEqualTo(blankPriceFile)

        coVerifyOnce { fileVaultActionService.securedGenericLink(resourceBundleSpecialtyPricingUpdate.fileVaultId) }
    }

    @Test
    fun `uploadPriceChanges should add pricing update to the database`() = runBlocking {
        val effectiveDate = LocalDate.of(2023, 10, 8)
        val staffId = RangeUUID.generate()

        val requestBody = UploadPriceChangesRequest(
            fileName = csvFile.name,
            content = csvFile.readBytes(),
            pricesBeginAt = effectiveDate,
            fileType = FileType.fromExtension(csvFile.extension)!!,
            staffId = staffId
        )

        val pricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate(
            fileVaultId = fileVaultId,
            fileName = fileName,
            createdByStaffId = staffId,
            failedRowsErrors = emptyList(),
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns NotFoundException("").failure()

        coEvery {
            fileVaultActionService.uploadGenericFile(
                match {
                    it.domain == genericVaultByteArray.domain &&
                            it.namespace == genericVaultByteArray.namespace &&
                            it.originalFileName == genericVaultByteArray.originalFileName &&
                            it.fileType == genericVaultByteArray.fileType
                }
            )
        } returns genericFileVault.success()

        coEvery {
            fileVaultActionService.uploadGenericFile(
                match {
                    it.domain == genericVaultByteArray.domain &&
                            it.namespace == genericVaultByteArray.namespace &&
                            it.originalFileName == genericVaultByteArray.originalFileName &&
                            it.fileType == genericVaultByteArray.fileType
                }
            )
        } returns genericFileVault.success()

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.add(match {
                it.fileVaultId == fileVaultId &&
                        it.fileName == fileName &&
                        it.createdByStaffId == staffId &&
                        it.pricesBeginAt == effectiveDate
            })
        } returns pricingUpdate.success()

        val result = service.uploadPriceChanges(requestBody)

        assertThat(result).isSuccess()

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.add(any())
        }

        coVerifyOnce {
            fileVaultActionService.uploadGenericFile(any())
        }
    }
}
