package br.com.alice.exec.indicator.service.specialistResources

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.exec.indicator.client.CSVGenerationResponse
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.models.ProcessingResourceBundleSpecialtyPricingUpdateResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import br.com.alice.exec.indicator.service.specialistResources.internal.CSVPricingUpdateFileGenerator
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingUpdateService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.VaultResponse
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test

class ResourceSpecialtyPricingUpdateServiceImplTest {

    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val staffService: StaffService = mockk()
    private val csvPricingUpdateFileGenerator: CSVPricingUpdateFileGenerator = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()

    private val service = ResourceSpecialtyPricingUpdateServiceImpl(
        resourceBundleSpecialtyPricingUpdateService,
        fileVaultActionService,
        staffService,
        csvPricingUpdateFileGenerator,
        resourceBundleSpecialtyPricingService,
    )

    private val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle()
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()

    private val resourceBundleSpecialty = TestModelFactory.buildResourceBundleSpecialty(
        resourceBundleId = healthSpecialistResourceBundle.id,
        specialtyId = medicalSpecialty.id,
    )

    @Test
    fun `generate all`(): Unit = runBlocking {
        coEvery { csvPricingUpdateFileGenerator.generate(emptyList()) } returns CSVGenerationResponse(
            bytes = "eee".toByteArray(),
            fileName = "file.csv"
        )

        val result = service.generate(emptyList())

        assertThat(result).isSuccess()

        coVerifyOnce { csvPricingUpdateFileGenerator.generate(emptyList()) }

    }

    @Test
    fun `getProcessingResourceBundleSpecialtyPricingUpdate should return processing response when update exists`() = runBlocking {
        val update = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        coEvery { resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate() } returns update.success()

        val result = service.getProcessingResourceBundleSpecialtyPricingUpdate()

        val expectedResponse = ProcessingResourceBundleSpecialtyPricingUpdateResponse(
            isProcessing = true,
            resourceBundleSpecialtyPricingUpdate = update
        )

        assertEquals(com.github.kittinunf.result.Result.success(expectedResponse), result)
    }

    @Test
    fun `getProcessingResourceBundleSpecialtyPricingUpdate should return not processing response when no update exists`() = runBlocking {
        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns com.github.kittinunf.result.Result.failure(NotFoundException())

        val result = service.getProcessingResourceBundleSpecialtyPricingUpdate()

        val expectedResponse = ProcessingResourceBundleSpecialtyPricingUpdateResponse(
            isProcessing = false
        )

        assertEquals(Result.success(expectedResponse), result)
    }

    @Test
    fun `generateFailedLinesFile should return csv file`() = runBlocking {
        val resourceBundleSpecialtyPricingUpdateId = RangeUUID.generate()

        coEvery { csvPricingUpdateFileGenerator.generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId) } returns CSVGenerationResponse(
            fileName = "failed_lines.csv",
            bytes = "row,error\n1,Error 1".toByteArray()
        ).success()

        val result = service.generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId)

        assertThat(result).isSuccess()

        coVerifyOnce { csvPricingUpdateFileGenerator.generateFailedLinesFile(any()) }
    }

    @Test
    fun `uploadPriceChanges should add pricing update to the database`() = runBlocking {
        val effectiveDate = LocalDate.of(2023, 10, 8)
        val staffId = RangeUUID.generate()

        val requestBody = UploadPriceChangesRequest(
            fileName = "csvFile.name",
            content = "csvFile.readBytes()".toByteArray(),
            pricesBeginAt = effectiveDate,
            fileType = FileType.CSV,
            staffId = staffId
        )

        val pricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate(
            fileVaultId = UUID.randomUUID(),
            fileName = "filename",
            createdByStaffId = staffId,
            failedRowsErrors = emptyList(),
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns NotFoundException("").failure()

        coEvery { csvPricingUpdateFileGenerator.uploadPriceChanges(requestBody) } returns pricingUpdate.success()

        val result = service.uploadPriceChanges(requestBody)

        assertThat(result).isSuccess()



        coVerifyOnce { resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate() }
        coVerifyOnce { csvPricingUpdateFileGenerator.uploadPriceChanges(requestBody) }
    }

    @Test
    fun `uploadPriceChanges should do nothing if there is a file already being processed`() = runBlocking {
        val effectiveDate = LocalDate.of(2023, 10, 8)
        val staffId = RangeUUID.generate()

        val requestBody = UploadPriceChangesRequest(
            fileName = "csvFile.name",
            content = "csvFile.readBytes()".toByteArray(),
            pricesBeginAt = effectiveDate,
            fileType = FileType.CSV,
            staffId = staffId
        )

        coEvery { resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate() } returns TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        val result = service.uploadPriceChanges(requestBody)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate() }
        coVerifyNone { csvPricingUpdateFileGenerator.uploadPriceChanges(requestBody) }
    }

    @Test
    fun `getResourceBundleSpecialtyPricingUpdateHistory should return history with count`() = runBlocking {
        val filters = PricingUpdateHistoryFilters(
            startDate = LocalDate.now().minusDays(1),
            endDate = LocalDate.now(),
            status = listOf(ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING)
        )
        val range = IntRange(0, 10)

        val staff = TestModelFactory.buildStaff()

        val resource = ResourceBundleSpecialtyPricingUpdate(
            id = RangeUUID.generate(),
            fileName = "history_update.csv",
            fileVaultId = RangeUUID.generate(),
            createdByStaffId = staff.id,
            processingAt = LocalDateTime.now(),
            completedAt = LocalDateTime.now(),
            rowsCount = 100,
            failedRowsCount = 0,
            failedRowsErrors = emptyList(),
            parsingError = null,
            pricesBeginAt = LocalDate.now(),
        )

        val vaultResponse = VaultResponse(
            id = resource.fileVaultId,
            fileName = "failed_lines.csv",
            type = "text/csv",
            vaultUrl = "example.com",
            fileSize = 1234,
            url = "file://${System.getProperty("user.dir")}/testResources/failed_lines.csv"
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getResourceBundleSpecialtyPricingUpdateHistory(
                filters,
                range
            )
        } returns listOf(resource)

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.countResourceBundleSpecialtyPricingUpdateHistory(filters)
        } returns 10

        coEvery {
            staffService.findByList(listOf(staff.id))
        } returns listOf(staff)

        coEvery {
            fileVaultActionService.securedGenericLinks(listOf(resource.fileVaultId))
        } returns listOf(vaultResponse)

        val result = service.getPricingUpdateHistory(filters, range)

        val expected = ResourceBundleSpecialtyPricingUpdateHistoryItem(
            id = resource.id,
            fileName = resource.fileName,
            fileUrl = vaultResponse.url,
            createdByStaff = staff,
            processingAt = resource.processingAt,
            completedAt = resource.completedAt,
            rowsCount = resource.rowsCount,
            failedRowsCount = resource.failedRowsCount,
            parsingError = resource.parsingError,
            pricesBeginAt = resource.pricesBeginAt,
            createdAt = resource.createdAt
        )

        assertThat(result).isSuccessWithData(
            ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
                count = 10,
                items = listOf(expected)
            )
        )

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.getResourceBundleSpecialtyPricingUpdateHistory(any(), any())
            resourceBundleSpecialtyPricingUpdateService.countResourceBundleSpecialtyPricingUpdateHistory(any())
            staffService.findByList(any())
            fileVaultActionService.securedGenericLinks(any())
        }
    }
}
