package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.services.ResourceBundleSpecialtyModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import com.github.kittinunf.result.Result
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import java.util.UUID
import kotlin.test.Test

class ResourceBundleSpecialtyServiceTest {

    private val resourceBundleSpecialtyModelDataService: ResourceBundleSpecialtyModelDataService = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = ResourceBundleSpecialtyService(
        resourceBundleSpecialtyModelDataService,
        resourceBundleSpecialtyPricingService
    )

    @Test
    fun `test get`() = runBlocking {
        val id = RangeUUID.generate()
        val expected = ResourceBundleSpecialty(
            id,
            RangeUUID.generate(),
            RangeUUID.generate(),
            Status.ACTIVE,
            PricingStatus.PRICED
        )

        coEvery { resourceBundleSpecialtyModelDataService.get(id) } returns Result.success(expected.toModel())

        val result = resourceBundleSpecialtyService.get(id)

        assertEquals(Result.success(expected), result)
        coVerify { resourceBundleSpecialtyModelDataService.get(id) }
    }

    @Test
    fun `test findByIds`() = runBlocking {
        val ids = listOf(UUID.randomUUID())
        val expected = listOf(
            ResourceBundleSpecialty(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                Status.ACTIVE,
                PricingStatus.PRICED
            )
        )

        coEvery { resourceBundleSpecialtyModelDataService.find(any()) } returns Result.success(expected.map { it.toModel() })

        val result = resourceBundleSpecialtyService.findByIds(ids)

        assertEquals(Result.success(expected), result)
        coVerify { resourceBundleSpecialtyModelDataService.find(any()) }
    }

    @Test
    fun `test findAllActive`() = runBlocking {
        val expected = listOf(
            ResourceBundleSpecialty(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                Status.ACTIVE,
                PricingStatus.PRICED
            )
        )

        coEvery { resourceBundleSpecialtyModelDataService.find(any()) } returns Result.success(expected.map { it.toModel() })

        val result = resourceBundleSpecialtyService.findAllActive()

        assertEquals(Result.success(expected), result)
        coVerify { resourceBundleSpecialtyModelDataService.find(any()) }
    }

    @Test
    fun `test getByResourceBundleId`() = runBlocking {
        val id = UUID.randomUUID()
        val expected = listOf(
            ResourceBundleSpecialty(
                id,
                UUID.randomUUID(),
                UUID.randomUUID(),
                Status.ACTIVE,
                PricingStatus.PRICED
            )
        )

        coEvery { resourceBundleSpecialtyModelDataService.find(any()) } returns Result.success(expected.map { it.toModel() })

        val result = resourceBundleSpecialtyService.getByResourceBundleId(id)

        assertEquals(Result.success(expected), result)
        coVerify { resourceBundleSpecialtyModelDataService.find(any()) }
    }

    @Test
    fun `test updateList`() = runBlocking {
        val specialties = listOf(
            ResourceBundleSpecialty(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                Status.ACTIVE,
                PricingStatus.PRICED
            )
        )

        coEvery { resourceBundleSpecialtyModelDataService.updateList(any()) } returns Result.success(specialties.map { it.toModel() })

        val result = resourceBundleSpecialtyService.updateList(specialties)

        assertEquals(Result.success(specialties), result)
        coVerify { resourceBundleSpecialtyModelDataService.updateList(any()) }
    }

    @Test
    fun `#getByPricingStatus should return a list of ResourceBundleSpecialty`() = runBlocking {
        val pricingStatus = PricingStatus.PRICED
        val expected = listOf(
            ResourceBundleSpecialty(
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                Status.ACTIVE,
                pricingStatus
            )
        )

        coEvery { resourceBundleSpecialtyModelDataService.find(
            queryEq {
                where {
                    this.pricingStatus.eq(pricingStatus).and(
                        status.eq(Status.ACTIVE)
                    )
                }
            }
        ) } returns Result.success(expected.map { it.toModel() })

        val result = resourceBundleSpecialtyService.getByPricingStatus(pricingStatus)

        assertEquals(Result.success(expected), result)
        coVerify { resourceBundleSpecialtyModelDataService.find(any()) }
    }
}
