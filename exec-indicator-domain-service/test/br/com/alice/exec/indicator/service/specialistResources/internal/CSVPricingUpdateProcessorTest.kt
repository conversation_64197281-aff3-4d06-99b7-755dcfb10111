package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.readFile
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.TierType
import br.com.alice.filevault.client.FileVaultActionService
import com.github.kittinunf.result.Result
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID

class CSVPricingUpdateProcessorTest {

    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService = mockk()
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()

    private val service = CSVPricingUpdateProcessor(
        resourceBundleSpecialtyPricingUpdateService,
        resourceBundleSpecialtyService,
        fileVaultActionService
    )

    private val resourceBundleSpecialtyId = UUID.fromString("f1b94eb8-6246-45d7-9ac2-c5e4ca30bec1")
    private val fileVaultId = RangeUUID.generate()
    private val staffId = RangeUUID.generate()
    private val effectiveDate = LocalDate.now()

    private val resourceBundleSpecialtyPricingUpdate = ResourceBundleSpecialtyPricingUpdate(
        fileName = "pricing_update.csv",
        fileVaultId = fileVaultId,
        createdByStaffId = staffId,
        processingAt = null,
        completedAt = null,
        rowsCount = 0,
        failedRowsCount = 0,
        failedRowsErrors = emptyList(),
        parsingError = null,
        pricesBeginAt = effectiveDate
    )

    private val resourceBundleSpecialty = ResourceBundleSpecialty(
        id = resourceBundleSpecialtyId,
        healthSpecialistResourceBundleId = UUID.randomUUID(),
        medicalSpecialtyId = UUID.randomUUID(),
        status = Status.ACTIVE,
        pricingStatus = PricingStatus.PRICED
    )

    private val blankPriceFile = readFile("testResources/specialistResources/blank_price.csv")
    private val missingColumnsFile = readFile("testResources/specialistResources/missing_column.csv")
    private val missingRowColumnsFile = readFile("testResources/specialistResources/missing_row_column.csv")
    private val csvWithInvalidValues = readFile("testResources/specialistResources/invalid_price.csv")
    private val okCSVFile = readFile("testResources/specialistResources/ok_price_update.csv")
    private val okIntPrices = readFile("testResources/specialistResources/ok_int_number.csv")
    private val okFilePrices = listOf(
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_3, 120.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_2, 130.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_1, 140.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, 150.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_2, 160.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_1, 170.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_0, 180.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_1, 190.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_0, 200.0.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.ULTRA_EXPERT, TierType.TIER_0, 210.0.toBigDecimal()),
    )

    private val okIntFilePrices = listOf(
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_3, 120.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_2, 130.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_1, 140.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, 150.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_2, 160.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_1, 170.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_0, 180.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_1, 190.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_0, 200.toBigDecimal()),
        ResourceBundleSpecialtyPrice(SpecialistTier.ULTRA_EXPERT, TierType.TIER_0, 210.toBigDecimal()),
    )

    @AfterEach
    fun tearDown() = clearAllMocks()



    @Test
    fun `process should handle successFile`() = runBlocking {

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns okCSVFile.toByteArray()
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdate.id) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyService.get(resourceBundleSpecialtyId) } returns resourceBundleSpecialty
        coEvery { resourceBundleSpecialtyService.changeResourcePrice(any(), any(), any()) } returns resourceBundleSpecialty


        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 0
            })

            resourceBundleSpecialtyService.changeResourcePrice(
                resourceBundleSpecialtyId, effectiveDate, okFilePrices
            )
        }
    }

    @Test
    fun `process should handle int`() = runBlocking {

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns okIntPrices.toByteArray()
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdate.id) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyService.get(resourceBundleSpecialtyId) } returns resourceBundleSpecialty
        coEvery { resourceBundleSpecialtyService.changeResourcePrice(any(), any(), any()) } returns resourceBundleSpecialty


        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 0
            })

            resourceBundleSpecialtyService.changeResourcePrice(
                resourceBundleSpecialtyId, effectiveDate, okIntFilePrices
            )
        }
    }

    @Test
    fun `process should handle resource not found error`() = runBlocking {

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns okCSVFile.toByteArray()
        coEvery { resourceBundleSpecialtyService.get(any()) } returns NotFoundException("Resource not found")
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null && it.failedRowsCount == 1 && it.failedRowsErrors.isNotEmpty()
            })
        }
    }

    @Test
    fun `process should handle parsing error`() = runBlocking {
        val invalidCsvContent = "Invalid CSV Content".toByteArray()

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns Result.success(invalidCsvContent)
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null && it.parsingError != null
            })
        }
    }

    @Test
    fun `process should handle missing columns`() = runBlocking {

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns missingColumnsFile.toByteArray()
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null && it.parsingError != null && it.parsingError == "Colunas faltando: TALENTED T3;SUPER EXPERT T0"
            })
        }
    }


    @Test
    fun `process should handle missing row values`() = runBlocking {
        coEvery { fileVaultActionService.genericFileContentById(any()) } returns missingRowColumnsFile.toByteArray()
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 1
                        && it.failedRowsErrors.any { error -> error.error.contains("Número de colunas inválido") }
            })
        }
    }

    @Test
    fun `process should handle invalid price values`() = runBlocking {
        coEvery { fileVaultActionService.genericFileContentById(any()) } returns csvWithInvalidValues.toByteArray()
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 1
                        && it.failedRowsErrors.any { error -> error.error.contains("TALENTED T3") }
            })
        }
    }

    @Test
    fun `process should handle blank price values`() = runBlocking {
        coEvery { fileVaultActionService.genericFileContentById(any()) } returns blankPriceFile.toByteArray()
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyService.get(resourceBundleSpecialtyId) } returns resourceBundleSpecialty

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 1
                        && it.failedRowsErrors.any { error -> error.error.contains("TALENTED T3") }
            })
        }
    }
}
