package br.com.alice.exec.indicator.service.specialistResources

import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.models.PaginatedList
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import br.com.alice.exec.indicator.models.SpecialistResourcePricingListing
import br.com.alice.exec.indicator.models.SpecialistResourcePricingPeriod
import br.com.alice.exec.indicator.models.SpecialistResourceQuantities
import br.com.alice.exec.indicator.models.SpecialistResourceSpecialty
import br.com.alice.exec.indicator.models.SpecialistResourceTuss
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyService
import br.com.alice.provider.client.MedicalSpecialtyService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class SpecialistResourceServiceImplTest {
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()

    private val service = SpecialistResourceServiceImpl(
        healthSpecialistResourceBundleService,
        resourceBundleSpecialtyService,
        resourceBundleSpecialtyPricingService,
        medicalSpecialtyService,
        healthcareResourceService,
    )

    private val primaryTuss = TestModelFactory.buildHealthcareResource()
    private val secondaryTuss = TestModelFactory.buildHealthcareResource()
    private val resource = TestModelFactory.buildHealthSpecialistResourceBundle(
        primaryTuss = primaryTuss.code,
        secondaryResources = listOf(secondaryTuss.id)
    )
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()
    private val resourceSpecialty = TestModelFactory.buildResourceBundleSpecialty(
        resourceBundleId = resource.id,
        specialtyId = medicalSpecialty.id,
    )

    private val expected = SpecialistResource(
        id = resource.id,
        code = resource.code,
        primaryTuss = SpecialistResourceTuss(
            code = resource.primaryTuss,
            description = primaryTuss.description
        ),
        secondaryResources = listOf(SpecialistResourceTuss(
            code = secondaryTuss.code,
            description = secondaryTuss.description
        )),
        executionAmount = resource.executionAmount,
        executionEnvironment = resource.executionEnvironment,
        description = resource.description,
        serviceType = resource.serviceType,
        medicalSpecialties = listOf(
            SpecialistResourceSpecialty(
                medicalSpecialtyId = resourceSpecialty.medicalSpecialtyId,
                description = medicalSpecialty.name,
                pricingStatus = resourceSpecialty.pricingStatus,
                recommendationLevel = resourceSpecialty.appointmentRecommendationLevel
            )
        ),
        status = resource.status,
        createdAt = resource.createdAt,
        updatedAt = resource.updatedAt
    )


    @Test
    fun `#get should get specialist resource`() = runBlocking {
        coEvery { healthSpecialistResourceBundleService.get(resource.id) } returns resource
        mockResourceResponseBuild()

        val result = service.get(resource.id)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce {
            healthSpecialistResourceBundleService.get(any())
            resourceBundleSpecialtyService.getByResourceBundleIds(any())
            medicalSpecialtyService.getByIds(any())
            healthcareResourceService.findByTussCodes(any())
            healthcareResourceService.getByIds(any())
        }
    }


    @Test
    fun `#list should get specialist resources`() = runBlocking {
        val filter = SpecialistResourceFilters(
            query = "test",
            status = Status.ACTIVE,
            pricingStatus = PricingStatus.PRICED,
        )

        val expectedResponse = PaginatedList(
            items = listOf(expected),
            totalItems = 1,
            range = 0..10
        )

        coEvery { resourceBundleSpecialtyService.getByFilters(
            pricingStatus = filter?.pricingStatus,
            medicalSpecialtyIds = filter?.medicalSpecialtyIds,
            recommendationLevel = filter?.recommendationLevel,
            status = Status.ACTIVE,
        ) } returns listOf(resourceSpecialty)

        coEvery { healthSpecialistResourceBundleService.findByFilters(
            ids = listOf(resourceSpecialty.healthSpecialistResourceBundleId),
            query = filter?.query,
            status = filter?.status,
            serviceTypes = filter?.serviceType,
            range = 0..10,
        ) } returns listOf(resource)

        coEvery { healthSpecialistResourceBundleService.countByFilters(
            ids = listOf(resourceSpecialty.healthSpecialistResourceBundleId),
            query = filter?.query,
            status = filter?.status,
            serviceTypes = filter?.serviceType,
        ) } returns 1


        mockResourceResponseBuild()

        val result = service.list(filter, 0..10)
        assertThat(result).isSuccessWithData(expectedResponse)

        coVerifyOnce {
            healthSpecialistResourceBundleService.findByFilters(any(), any(), any(), any(), any())
            healthSpecialistResourceBundleService.countByFilters(any(), any(), any(), any())
            resourceBundleSpecialtyService.getByResourceBundleIds(any())
            medicalSpecialtyService.getByIds(any())
            healthcareResourceService.findByTussCodes(any())
            healthcareResourceService.getByIds(any())
        }
    }

    private fun mockResourceResponseBuild() {
        coEvery { resourceBundleSpecialtyService.getByResourceBundleIds(listOf(resource.id)) } returns listOf(resourceSpecialty)
        coEvery { medicalSpecialtyService.getByIds(listOf(resourceSpecialty.medicalSpecialtyId)) } returns listOf(medicalSpecialty)


        coEvery { healthcareResourceService.findByTussCodes(listOf(resource.primaryTuss)) } returns listOf(primaryTuss)
        coEvery { healthcareResourceService.getByIds(resource.secondaryResources) } returns listOf(secondaryTuss)
    }

    @Test
    fun `#getPricing should get price listing`() = runBlocking {
        val pricingList = TestModelFactory.buildResourceBundleSpecialtyPricing(
            resourceBundleSpecialtyId = resourceSpecialty.id,
        )

        val expectedResponse = SpecialistResourcePricingListing(
            resourceId = resourceSpecialty.healthSpecialistResourceBundleId,
            medicalSpecialtyId = resourceSpecialty.medicalSpecialtyId,
            pricingPeriods = listOf(
                SpecialistResourcePricingPeriod(
                    beginAt = pricingList.beginAt,
                    endAt = pricingList.endAt,
                    prices = pricingList.prices
                )
            )
        )

        coEvery { resourceBundleSpecialtyService.getByFilters(
            resourceBundleIds = listOf(resource.id),
            pricingStatus = PricingStatus.PRICED,
            medicalSpecialtyIds = listOf(resourceSpecialty.medicalSpecialtyId),
            status = Status.ACTIVE,
        ) } returns listOf(resourceSpecialty)

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(listOf(resourceSpecialty.id))
        } returns listOf(pricingList)

        val result = service.getPricing(listOf(resourceSpecialty.medicalSpecialtyId), listOf(resource.id))
        assertThat(result).isSuccessWithData(listOf(expectedResponse))

        coVerifyOnce {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(any())
            resourceBundleSpecialtyService.getByFilters(any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `#listQuantities should get quantities for specialiy`() = runBlocking {
        val expectedResponse = SpecialistResourceQuantities(
            serviceType = mapOf(resource.serviceType to 1),
            pricingStatus = mapOf(resourceSpecialty.pricingStatus to 1),
            recommendationLevel = mapOf(resourceSpecialty.appointmentRecommendationLevel to 1)
        )

        val countResult = listOf(
            CountByValues(
                values = listOf(resource.serviceType.name),
                count = 1
            )
        )

        coEvery { resourceBundleSpecialtyService.getByFilters(
            medicalSpecialtyIds = listOf(resourceSpecialty.medicalSpecialtyId),
            status = Status.ACTIVE,
        ) } returns listOf(resourceSpecialty)

        coEvery { healthSpecialistResourceBundleService.countServiceType(listOf(resource.id)) } returns countResult

        val result = service.listQuantities(medicalSpecialty.id)
        assertThat(result).isSuccessWithData(expectedResponse)

        coVerifyOnce {
            healthSpecialistResourceBundleService.countServiceType(any())
            resourceBundleSpecialtyService.getByFilters(any(), any(), any(), any(), any())
        }
    }
}
