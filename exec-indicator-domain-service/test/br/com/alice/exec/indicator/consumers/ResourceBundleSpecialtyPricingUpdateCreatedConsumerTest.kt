package br.com.alice.exec.indicator.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.events.ResourceBundleSpecialtyPricingUpdateCreatedEvent
import br.com.alice.exec.indicator.service.specialistResources.internal.CSVPricingUpdateProcessor
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ResourceBundleSpecialtyPricingUpdateCreatedConsumerTest : ConsumerTest() {
    private val CSVPricingUpdateProcessor: CSVPricingUpdateProcessor = mockk()
    private val resourceBundleSpecialtyPricingUpdateCreatedConsumer = ResourceBundleSpecialtyPricingUpdateCreatedConsumer(
        CSVPricingUpdateProcessor
    )

    @Test
    fun `processResourceBundleSpecialtyPricingUpdateCreated should process the event successfully`() = runBlocking<Unit> {
        val resourceBundleSpecialtyPricingUpdateId = RangeUUID.generate()
        val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        val event = ResourceBundleSpecialtyPricingUpdateCreatedEvent(
            resourceBundleSpecialtyPricingUpdateId
        )

        coEvery {
            CSVPricingUpdateProcessor.process(resourceBundleSpecialtyPricingUpdateId)
        } returns resourceBundleSpecialtyPricingUpdate.success()

        val result = resourceBundleSpecialtyPricingUpdateCreatedConsumer.processResourceBundleSpecialtyPricingUpdateCreated(event)

        assertThat(result).isSuccessWithData(resourceBundleSpecialtyPricingUpdate)

        coVerifyOnce {
            CSVPricingUpdateProcessor.process(resourceBundleSpecialtyPricingUpdateId)
        }
    }
}
