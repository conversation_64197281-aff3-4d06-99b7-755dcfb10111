package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.helpers.calculateWorkDays
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID


class ResourceBundleSpecialtyPricingService(
    private val dataService: ResourceBundleSpecialtyPricingModelDataService,
) {

    fun getEffectiveDaysCheckingWorkDays(): List<LocalDate> {
        val today = LocalDate.now()
        val workDays = calculateWorkDays(
            startDate = today.atBeginningOfTheMonth(),
            endDate = today
        )

        val dates = if (workDays > 5) {
            listOf(
                today.atBeginningOfTheMonth(),
                today.plusMonths(1).atBeginningOfTheMonth()
            )
        } else {
            listOf(
                today.minusMonths(1).atBeginningOfTheMonth(),
                today.atBeginningOfTheMonth()
            )
        }

        return dates
    }

    suspend fun onSpecialtyInactivation(resourceBundleSpecialtyId: UUID): Result<Any, Throwable> {
        val today = LocalDate.now()

        val prices = getActiveAndFuturePricingOnEffectiveDate(
            resourceBundleSpecialtyId,
            today
        ).get()

        val toDelete = prices.firstOrNull { it.beginAt.isAfterEq(today) }
        val toExpire = prices.firstOrNull { it.beginAt.isBeforeEq(today) && (it.endAt == null || it.endAt!!.isAfterEq(today)) }

        toDelete?.let { dataService.delete(it.toModel()).get() }
        toExpire?.let { update(it.expire()).get() }
        return true.success()
    }

    suspend fun onSpecialtyActivation(resourceBundleSpecialtyId: UUID): Result<ResourceBundleSpecialtyPricing?, Throwable> =
        getActiveAndFuturePricingOnEffectiveDate(
            resourceBundleSpecialtyId,
            LocalDate.now().atBeginningOfTheMonth()
        )
            .map { it.firstOrNull() }
            .flatMap {
                it?.let { update(it.reactivate()) }
                    ?: Result.success(null)
            }

    suspend fun addNewPricing(
        resourceBundleSpecialtyId: UUID,
        effectiveDate: LocalDate,
        prices: List<ResourceBundleSpecialtyPrice>
    ): Result<ResourceBundleSpecialtyPricing, Throwable> {
        if (effectiveDate !in getEffectiveDaysCheckingWorkDays()) return InvalidArgumentException("Invalid Effective date").failure()

        return getActiveAndFuturePricingOnEffectiveDate(resourceBundleSpecialtyId, effectiveDate)
            .flatMap { activesPrices ->
                when {
                    activesPrices.size > 2 -> Exception("More than one active pricing found").failure()
                    activesPrices.isEmpty() -> create(resourceBundleSpecialtyId, prices, effectiveDate)
                    activesPrices.hasOnlyPrevious(effectiveDate) -> createAndExpirePrevious(
                        resourceBundleSpecialtyId,
                        activesPrices.first(),
                        prices, effectiveDate
                    )
                    activesPrices.hasOnlyFuture(effectiveDate) -> createExpiredPricing(
                        resourceBundleSpecialtyId,
                        activesPrices.first(),
                        prices,
                        effectiveDate
                    )
                    activesPrices.effectiveDateExistsReactivation(effectiveDate) -> updatePrice(
                        activesPrices.first { it.beginAt == effectiveDate },
                        prices,
                        null
                    )
                    activesPrices.effectiveDateExists(effectiveDate) -> updatePrice(
                        activesPrices.first { it.beginAt == effectiveDate },
                        prices,
                        activesPrices.first { it.beginAt == effectiveDate }.endAt
                    )
                    activesPrices.hasPastAndFuture(effectiveDate) -> createExpiredAndExpirePrevious(
                        resourceBundleSpecialtyId,
                        activesPrices.first { it.beginAt.isBefore(effectiveDate) },
                        activesPrices.first { it.beginAt.isAfter(effectiveDate) },
                        prices,
                        effectiveDate
                    )
                    else -> Exception("Unexpected State").failure()
                }
            }
    }

    private fun List<ResourceBundleSpecialtyPricing>.hasOnlyFuture(effectiveDate: LocalDate): Boolean {
        return this.size == 1 && this.first().beginAt.isAfter(effectiveDate)
    }

    private fun List<ResourceBundleSpecialtyPricing>.hasOnlyPrevious(effectiveDate: LocalDate): Boolean {
        return this.size == 1 && this.first().beginAt.isBefore(effectiveDate)
    }

    private fun List<ResourceBundleSpecialtyPricing>.hasPastAndFuture(effectiveDate: LocalDate): Boolean {
        return this.size == 2 &&
                this.any { it.beginAt.isBefore(effectiveDate) } &&
                this.any { it.beginAt.isAfter(effectiveDate) }
    }

    private fun List<ResourceBundleSpecialtyPricing>.effectiveDateExistsReactivation(effectiveDate: LocalDate): Boolean {
        return this.size == 1
                && this.first().beginAt == effectiveDate
                && this.first().endAt != null
    }

    private fun List<ResourceBundleSpecialtyPricing>.effectiveDateExists(effectiveDate: LocalDate): Boolean {
        return this.any { it.beginAt == effectiveDate }
    }

    private suspend fun createExpiredPricing(
        resourceBundleSpecialtyId: UUID,
        futurePricing: ResourceBundleSpecialtyPricing,
        prices: List<ResourceBundleSpecialtyPrice>,
        effectiveDate: LocalDate,

    ) = create(
        resourceBundleSpecialtyId,
        prices,
        effectiveDate,
        futurePricing.beginAt.minusDays(1)
    )

    private suspend fun createAndExpirePrevious(
        resourceBundleSpecialtyId: UUID,
        previous: ResourceBundleSpecialtyPricing,
        prices: List<ResourceBundleSpecialtyPrice>,
        effectiveDate: LocalDate,

    ) = update(previous.expire(effectiveDate.minusDays(1)))
        .flatMap {
            create(
                resourceBundleSpecialtyId,
                prices,
                effectiveDate
            )
        }

    private suspend fun createExpiredAndExpirePrevious(
        resourceBundleSpecialtyId: UUID,
        previous: ResourceBundleSpecialtyPricing,
        future: ResourceBundleSpecialtyPricing,
        prices: List<ResourceBundleSpecialtyPrice>,
        effectiveDate: LocalDate,

    ) = update(previous.expire(effectiveDate.minusDays(1)))
        .flatMap {
            create(
                resourceBundleSpecialtyId,
                prices,
                effectiveDate,
                future.beginAt.minusDays(1)
            )
        }

    private suspend fun create(
        resourceBundleSpecialtyId: UUID,
        prices: List<ResourceBundleSpecialtyPrice>,
        effectiveDate: LocalDate,
        endAt : LocalDate? = null
    ) =
        dataService.add(
            ResourceBundleSpecialtyPricing(
                resourceBundleSpecialtyId = resourceBundleSpecialtyId,
                beginAt = effectiveDate,
                prices = prices,
                endAt = endAt
            ).toModel()
        ).map { it.toTransport() }

    private suspend fun updatePrice(
        currentPricingList: ResourceBundleSpecialtyPricing,
        prices: List<ResourceBundleSpecialtyPrice>,
        endAt: LocalDate?
    ) = update(
        currentPricingList.copy(
            prices = prices,
            endAt = endAt,
        )
    )

    private suspend fun getActiveAndFuturePricingOnEffectiveDate(
        resourceBundleSpecialtyIds: UUID,
        effectiveDate: LocalDate,
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        dataService.find {
            where { resourceBundleSpecialtyId.eq(resourceBundleSpecialtyIds) }
        }
            .mapEach { it.toTransport() }
            .map { pricing ->
                pricing.filter {
                    it.beginAt.isAfterEq(effectiveDate) ||
                            (it.beginAt.isBefore(effectiveDate) && it.endAt == null) ||
                            (it.beginAt.isBefore(effectiveDate) && it.endAt!!.isAfter(effectiveDate))
                }
            }

    private suspend fun update(specialty: ResourceBundleSpecialtyPricing) =
        dataService.update(specialty.toModel()).map { it.toTransport() }


    suspend fun getCurrentlyActiveByResourceBundleSpecialtyId(
        resourceBundleSpecialtyIds: List<UUID>
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        dataService.find { where { resourceBundleSpecialtyId.inList(resourceBundleSpecialtyIds) } }
            .mapEach { it.toTransport() }
            .map { prices ->  prices.filter {
                it.beginAt <= LocalDate.now() && (it.endAt == null || it.endAt!! >= LocalDate.now())
            }
        }
}
