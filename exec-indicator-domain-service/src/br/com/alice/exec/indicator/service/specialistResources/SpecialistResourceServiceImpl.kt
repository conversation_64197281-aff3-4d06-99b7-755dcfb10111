package br.com.alice.exec.indicator.service.specialistResources

import br.com.alice.common.core.Status
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.models.PaginatedList
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import br.com.alice.exec.indicator.models.SpecialistResourcePricingListing
import br.com.alice.exec.indicator.models.SpecialistResourcePricingPeriod
import br.com.alice.exec.indicator.client.specialistResources.SpecialistResourceService
import br.com.alice.exec.indicator.models.SpecialistResourceQuantities
import br.com.alice.exec.indicator.models.SpecialistResourceSpecialty
import br.com.alice.exec.indicator.models.SpecialistResourceTuss
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyService
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class SpecialistResourceServiceImpl(
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService,
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService,
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val healthcareResourceService: HealthcareResourceService
): SpecialistResourceService {

    override suspend fun get(id: UUID): Result<SpecialistResource, Throwable> =
        healthSpecialistResourceBundleService.get(id)
            .flatMap { getResourceResponse(listOf(it)) }
            .map { it.first() }

    override suspend fun list(filter: SpecialistResourceFilters?, range: IntRange): Result<PaginatedList<SpecialistResource>, Throwable> = coroutineScope  {
        coResultOf {
            val ids = if (filter?.recommendationLevel != null || filter?.medicalSpecialtyIds != null || filter?.pricingStatus != null) {
                val resourceSpecialties = resourceBundleSpecialtyService.getByFilters(
                    pricingStatus = filter?.pricingStatus,
                    medicalSpecialtyIds = filter?.medicalSpecialtyIds,
                    recommendationLevel = filter?.recommendationLevel,
                    status = Status.ACTIVE,
                ).mapEach { it.healthSpecialistResourceBundleId }.get()

                if (resourceSpecialties.isEmpty()) {
                    return@coResultOf PaginatedList(
                        totalItems = 0,
                        items = emptyList(),
                        range = range
                    )
                }

                resourceSpecialties
            } else emptyList()

            val resourcesDef = async { healthSpecialistResourceBundleService.findByFilters(
                ids = ids,
                query = filter?.query,
                status = filter?.status,
                serviceTypes = filter?.serviceType,
                range = range,
            ) }

            val countDef = async { healthSpecialistResourceBundleService.countByFilters(
                ids = ids,
                query = filter?.query,
                serviceTypes = filter?.serviceType,
                status = filter?.status,
            ) }

            val resources = resourcesDef.await().get()
            val count = countDef.await().get()


            getResourceResponse(resources).map {
                PaginatedList(
                    totalItems = count,
                    items = it,
                    range = range
                )
            }.get()
        }
    }

    override suspend fun listQuantities(spacialtyId: UUID): Result<SpecialistResourceQuantities, Throwable> = coResultOf {
        val resourceSpecialties = resourceBundleSpecialtyService.getByFilters(
            medicalSpecialtyIds = listOf(spacialtyId),
            status = Status.ACTIVE,
        ).get()

        val totalByPricingStatus = resourceSpecialties
            .groupBy { it.pricingStatus }
            .map { it.key to it.value.size }
            .toMap()

        val totalByRecommendationLevel = resourceSpecialties
            .groupBy { it.appointmentRecommendationLevel }
            .map { it.key to it.value.size }
            .toMap()

        val totalByServiceType = healthSpecialistResourceBundleService.countServiceType(resourceSpecialties.map { it.healthSpecialistResourceBundleId })
            .mapEach { HealthSpecialistResourceBundleServiceType.valueOf(it.values.first()) to it.count }
            .map { it.toMap() }
            .get()

        SpecialistResourceQuantities(
            pricingStatus = totalByPricingStatus,
            recommendationLevel = totalByRecommendationLevel,
            serviceType = totalByServiceType
        )
    }

    override suspend fun getPricing(
        medicalSpecialtiesIds: List<UUID>,
        resourceIds: List<UUID>,
        onlyCurrent: Boolean
    ): Result<List<SpecialistResourcePricingListing>, Throwable> = coroutineScope {
        val resourceSpecialties = resourceBundleSpecialtyService.getByFilters(
            resourceBundleIds = resourceIds,
            pricingStatus = PricingStatus.PRICED,
            medicalSpecialtyIds = medicalSpecialtiesIds,
            status = Status.ACTIVE,
        ).get()

        resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceSpecialties.map { it.id })
            .map { it.groupBy { it.resourceBundleSpecialtyId } }
            .map {
                it.map { (resourceBundleSpecialtyId, pricingList) ->
                    val resourceSpecialty = resourceSpecialties.firstOrNull { it.id == resourceBundleSpecialtyId }

                    if (resourceSpecialty == null) null
                    else SpecialistResourcePricingListing(
                        resourceId = resourceSpecialty.healthSpecialistResourceBundleId,
                        medicalSpecialtyId = resourceSpecialty.medicalSpecialtyId,
                        pricingPeriods = pricingList.map { pricing ->
                            SpecialistResourcePricingPeriod(
                                beginAt = pricing.beginAt,
                                endAt = pricing.endAt,
                                prices = pricing.prices

                            )
                        }
                    )
                }.filterNotNull()
            }
    }

    private suspend fun getResourceResponse(resources: List<HealthSpecialistResourceBundle>) = coroutineScope {
        coResultOf<List<SpecialistResource>, Throwable> {
            val resourceSpecialtiesDef = async {
                resourceBundleSpecialtyService
                    .getByResourceBundleIds(resources.map { it.id })
                    .flatMapPair { resourceSpecialty -> medicalSpecialtyService.getByIds(resourceSpecialty.map { it.medicalSpecialtyId }) }
                    .get()
            }

            val primaryResourceDef = async { healthcareResourceService.findByTussCodes(resources.map { it.primaryTuss } .distinct()).get() }
            val secondaryResourcesDef = async { healthcareResourceService.getByIds(resources.flatMap { it.secondaryResources }.distinct()).get() }

            val primaryResources = primaryResourceDef.await()
            val secondaryResources = secondaryResourcesDef.await()
            val (specialties, resourceSpecialties) = resourceSpecialtiesDef.await()

            resources.map { resource ->

                SpecialistResource(
                    id = resource.id,
                    code = resource.code,
                    primaryTuss = SpecialistResourceTuss(
                        code = resource.primaryTuss,
                        description = primaryResources.find { it.code == resource.primaryTuss }?.description.orEmpty()
                    ),
                    secondaryResources = secondaryResources
                        .filter { it.id in resource.secondaryResources }
                        .map { SpecialistResourceTuss(it.code, it.description) },
                    executionAmount = resource.executionAmount,
                    executionEnvironment = resource.executionEnvironment,
                    description = resource.description,
                    serviceType = resource.serviceType,
                    medicalSpecialties = resourceSpecialties
                        .filter { it.healthSpecialistResourceBundleId == resource.id }
                        .map { resourceSpecialty ->
                            SpecialistResourceSpecialty(
                                medicalSpecialtyId = resourceSpecialty.medicalSpecialtyId,
                                description = specialties.firstOrNull { it.id == resourceSpecialty.medicalSpecialtyId }?.name.orEmpty(),
                                pricingStatus = resourceSpecialty.pricingStatus,
                                recommendationLevel = resourceSpecialty.appointmentRecommendationLevel
                            )
                        },
                    status = resource.status,
                    createdAt = resource.createdAt,
                    updatedAt = resource.updatedAt,
                )
            }
        }
    }
}
