package br.com.alice.exec.indicator.service.specialistResources

import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.services.HealthSpecialistResourceBundleModelDataService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleFilter
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HealthcareResourceService.Companion.DEFAULT_PRIMARY_TUSS_TABLE
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import br.com.alice.exec.indicator.events.HealthSpecialistResourceBundleUpsertedEvent
import br.com.alice.exec.indicator.service.specialistResources.HealthSpecialistResourceBundleServiceImpl.ErrorsMessage.PRIMARY_RESOURCE_NOT_FOUND
import br.com.alice.exec.indicator.service.specialistResources.HealthSpecialistResourceBundleServiceImpl.ErrorsMessage.SECONDARY_RESOURCE_NOT_FOUND
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import java.util.UUID

class HealthSpecialistResourceBundleServiceImpl(
    private val healthSpecialistResourceBundleDataService: HealthSpecialistResourceBundleModelDataService,
    private val producerService: KafkaProducerService,
    private val healthcareResourceService: HealthcareResourceService,
) : HealthSpecialistResourceBundleService {

    private object ErrorsMessage {
        const val PRIMARY_RESOURCE_NOT_FOUND = "health_specialist_resource_bundle_primary_resource_not_found"
        const val SECONDARY_RESOURCE_NOT_FOUND = "health_specialist_resource_bundle_secondary_resource_not_found"
    }

    override suspend fun get(id: UUID): Result<HealthSpecialistResourceBundle, Throwable> = useReadDatabase {
        healthSpecialistResourceBundleDataService.get(id).map {
            it.toTransport()
        }
    }

    override suspend fun update(model: HealthSpecialistResourceBundle) = withLog("update", model) {
        healthSpecialistResourceBundleDataService.update(model.toModel())
            .then { producerService.produce(HealthSpecialistResourceBundleUpsertedEvent(it.id)) }
            .map { it.toTransport() }
    }

    override suspend fun delete(model: HealthSpecialistResourceBundle): Result<Boolean, Throwable> {
        logger.info("HealthSpecialistResourceBundleServiceImpl#delete", "model" to model)
        return healthSpecialistResourceBundleDataService.delete(model.toModel())
    }

    override suspend fun findByIds(ids: List<UUID>) = useReadDatabase {
        logger.info("HealthSpecialistResourceBundleServiceImpl#findByIds", "ids" to ids)
        healthSpecialistResourceBundleDataService.find { where { this.id.inList(ids) } }.map {
            it.toTransport()
        }
    }

    override suspend fun findByCodes(codes: List<String>): Result<List<HealthSpecialistResourceBundle>, Throwable> =
        useReadDatabase {
            logger.info("HealthSpecialistResourceBundleServiceImpl#findByCodes", "codes" to codes)
            healthSpecialistResourceBundleDataService.find { where { this.code.inList(codes) } }.map {
                it.toTransport()
            }
        }

    override suspend fun findByPrimaryTuss(primaryTuss: String): Result<List<HealthSpecialistResourceBundle>, Throwable> =
        useReadDatabase {
            logger.info("HealthSpecialistResourceBundleServiceImpl#findByPrimaryTuss", "primaryTuss" to primaryTuss)
            healthSpecialistResourceBundleDataService.find { where { this.primaryTuss.eq(primaryTuss) } }.map {
                it.toTransport()
            }
        }

    override suspend fun add(model: HealthSpecialistResourceBundle) = withLog("add", model) {
        healthSpecialistResourceBundleDataService.add(model.toModel())
            .then { producerService.produce(HealthSpecialistResourceBundleUpsertedEvent(it.id)) }
            .map { it.toTransport() }
    }

    override suspend fun findByPrimaryTussList(primaryTussList: List<String>) = useReadDatabase {
        logger.info(
            "HealthSpecialistResourceBundleServiceImpl#findByPrimaryTussList",
            "primaryTussList" to primaryTussList
        )
        healthSpecialistResourceBundleDataService.find { where { this.primaryTuss.inList(primaryTussList) } }.map {
            it.map {
                it.toTransport()
            }
        }
    }

    override suspend fun search(query: String, limit: Int): Result<List<HealthSpecialistResourceBundle>, Throwable> = useReadDatabase {
        healthSpecialistResourceBundleDataService.find {
            where { this.searchTokens.search(query).and(this.status.eq(Status.ACTIVE)) }.limit { limit }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun findByFilters(
        query: String?,
        range: IntRange?,
        ids: List<UUID>?,
        serviceTypes: List<HealthSpecialistResourceBundleServiceType>?,
        status: Status?
    ) = useReadDatabase {
        logger.info(
            "HealthSpecialistResourceBundleServiceImpl#findByFilters",
            "query" to query,
            "range" to range
        )
        if (range == null) {
            healthSpecialistResourceBundleDataService.find {
                where { this.id.isNotNull().andQueryPredicate(query)
                    .andIdsInList(ids)
                    .andStatus(status)
                    .andServiceTypesInList(serviceTypes)
                }
                    .orderBy { this.updatedAt }
                    .sortOrder { desc }
            }.map {
                it.toTransport()
            }
        } else {
            healthSpecialistResourceBundleDataService.find {
                where {
                    this.id.isNotNull().andQueryPredicate(query)
                        .andIdsInList(ids)
                        .andStatus(status)
                        .andServiceTypesInList(serviceTypes)
                }
                    .orderBy { this.updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            }.map {
                it.toTransport()
            }
        }
    }

    override suspend fun countByFilters(
        query: String?,
        ids: List<UUID>?,
        serviceTypes: List<HealthSpecialistResourceBundleServiceType>?,
        status: Status?
    ) = useReadDatabase {
        logger.info("HealthSpecialistResourceBundleServiceImpl#countByFilters", "query" to query)
        healthSpecialistResourceBundleDataService.count { where {
            this.id.isNotNull()
                .andQueryPredicate(query)
                .andIdsInList(ids)
                .andServiceTypesInList(serviceTypes)
                .andStatus(status)
        } }
    }

    override suspend fun findByResourceBundleFilters(filter: HealthSpecialistResourceBundleFilter) = useReadDatabase {
        healthSpecialistResourceBundleDataService.findOne {
            where {
                this.primaryTuss.eq(filter.primaryTuss)
                    .and(this.executionAmount.eq(filter.executionAmount))
                    .and(this.serviceType.eq(filter.serviceType))
                    .and(this.executionEnvironment.eq(filter.executionEnvironment))
                    .andSecondaryResources(filter.secondaryResources)
            }
        }.map {
            it.toTransport()
        }
    }

    override suspend fun countServiceType(ids: List<UUID>) =
       healthSpecialistResourceBundleDataService.countGrouped {
            where { this.id.inList(ids) }.groupBy { listOf(this.serviceType) }
        }


    override suspend fun upsert(
        model: HealthSpecialistResourceBundle
    ): Result<HealthSpecialistResourceBundle, Throwable> {
        logger.info(
            "HealthSpecialistResourceBundleServiceImpl::upsert",
            "model" to model
        )

        healthcareResourceService.getByCodeAndTableType(model.primaryTuss, DEFAULT_PRIMARY_TUSS_TABLE).getOrElse {
            return NotFoundException(PRIMARY_RESOURCE_NOT_FOUND).failure()
        }

        model.secondaryResources.let {
            if (it.isNotNullOrEmpty()) {
                val healthcareResource = healthcareResourceService.getByIds(it).get()
                if (healthcareResource.size != it.size) return NotFoundException(SECONDARY_RESOURCE_NOT_FOUND).failure()
            }
        }

        val healthSpecialistResourceBundle = findExistingResourceBundle(model)

        return if (healthSpecialistResourceBundle == null) {
            add(model)
        } else {
            update(
                healthSpecialistResourceBundle.copy(
                    description = model.description,
                    status = model.status,
                )
            )
        }
    }

    @OptIn(OrPredicateUsage::class)
    override suspend fun findByCodesOrPrimaryTuss(codes: List<String>) = useReadDatabase {
        logger.info("HealthSpecialistResourceBundleServiceImpl#findByCodesOrPrimaryTuss", "codes" to codes)
        healthSpecialistResourceBundleDataService.find {
            where { this.code.inList(codes).or(this.primaryTuss.inList(codes)) }
        }.map {
            it.toTransport()
        }
    }

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun findBy(
        filter: HealthSpecialistResourceBundleService.Filter,
        range: IntRange?
    ): Result<List<HealthSpecialistResourceBundle>, Throwable> =
        catchResult {
            filter.valid()
            useReadDatabase {
                healthSpecialistResourceBundleDataService.find {
                    val whereCondition = where {
                        basePredicateForFilters()
                            .withFilter(filter.ids) { this.id.inList(it) }
                            .withFilter(filter.searchToken) { this.searchTokens.search(it) }!!
                    }
                    range?.let { whereCondition.offset { it.first }.limit { it.count() } } ?: whereCondition
                }.map { it.toTransport() }
            }
        }

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun countBy(filter: HealthSpecialistResourceBundleService.Filter): Result<Int, Throwable> =
        catchResult {
            filter.valid()
            useReadDatabase {
                healthSpecialistResourceBundleDataService.count {
                    where {
                        basePredicateForFilters()
                            .withFilter(filter.ids) { this.id.inList(it) }
                            .withFilter(filter.searchToken) { this.searchTokens.search(it) }!!
                    }
                }
            }
        }

    private fun Predicate.andQueryPredicate(query: String?): Predicate {
        if (query.isNullOrBlank()) return this
        return this.and(HealthSpecialistResourceBundleModelDataService.FieldOptions().searchTokens.search(query))
    }

    private fun Predicate.andIdsInList(ids: List<UUID>?): Predicate {
        if (ids.isNullOrEmpty()) return this
        return this.and(HealthSpecialistResourceBundleModelDataService.FieldOptions().id.inList(ids))
    }

    private fun Predicate.andServiceTypesInList(
        serviceTypes: List<HealthSpecialistResourceBundleServiceType>?
    ): Predicate {
        if (serviceTypes.isNullOrEmpty()) return this
        return this.and(HealthSpecialistResourceBundleModelDataService.FieldOptions().serviceType.inList(serviceTypes))
    }

    private fun Predicate.andStatus(
        status: Status?
    ): Predicate {
        if (status == null) return this
        return this.and(HealthSpecialistResourceBundleModelDataService.FieldOptions().status.eq(status))
    }

    private suspend fun withLog(
        logMessage: String,
        model: HealthSpecialistResourceBundle,
        block: suspend () -> Result<HealthSpecialistResourceBundle, Throwable>
    ): Result<HealthSpecialistResourceBundle, Throwable> {
        logger.info("HealthSpecialistResourceBundleServiceImpl#$logMessage", "model" to model)
        return block()
    }

    @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
    private fun Predicate.andSecondaryResources(secondaryResources: List<UUID>?): Predicate {
        if (secondaryResources.isNullOrEmpty()) return this
        return this and Predicate.jsonSearch(
            HealthSpecialistResourceBundleModelDataService.FieldOptions().secondaryResources,
            "[${secondaryResources.sorted().distinct().joinToString(",") { "\"$it\"" }}]"
        )
    }

    private suspend fun findExistingResourceBundle(model: HealthSpecialistResourceBundle) =
        findByResourceBundleFilters(
            HealthSpecialistResourceBundleFilter(
                primaryTuss = model.primaryTuss,
                secondaryResources = model.secondaryResources,
                executionAmount = model.executionAmount,
                serviceType = model.serviceType,
                executionEnvironment = model.executionEnvironment,
            )
        ).getOrNullIfNotFound()

}
