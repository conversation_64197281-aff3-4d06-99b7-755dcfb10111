package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.CSVPricingUpdateError
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.TierType
import br.com.alice.filevault.client.FileVaultActionService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CSVPricingUpdateProcessor(
    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService,
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService,
    private val fileVaultActionService: FileVaultActionService
) {

    companion object {
        private const val RESOURCE_BUNDLE_SPECIALTY_ID = "RESOURCE_BUNDLE_SPECIALTY_ID"
        private const val TALENTED_T3 = "TALENTED T3"
        private const val TALENTED_T2 = "TALENTED T2"
        private const val TALENTED_T1 = "TALENTED T1"
        private const val TALENTED_T0 = "TALENTED T0"
        private const val EXPERT_T2 = "EXPERT T2"
        private const val EXPERT_T1 = "EXPERT T1"
        private const val EXPERT_T0 = "EXPERT T0"
        private const val SUPER_EXPERT_T1 = "SUPER EXPERT T1"
        private const val SUPER_EXPERT_T0 = "SUPER EXPERT T0"
        private const val ULTRA_EXPERT_T0 = "ULTRA EXPERT T0"

        private const val CSV_COLUMNS_COUNT = 16
        private val DEFAULT_ERROR_PRICE = BigDecimal(-99)
    }

    suspend fun process(resourceBundleSpecialtyPricingUpdateId: UUID) =
        resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdateId)
            .flatMap { resourceBundleSpecialtyPricingUpdateService.update(it.copy(processingAt = LocalDateTime.now())) }
            .flatMapPair { fileVaultActionService.genericFileContentById(it.fileVaultId) }
            .flatMap { (fileContent, resourceBundleSpecialtyPricingUpdate) ->
                processFileContent(fileContent, resourceBundleSpecialtyPricingUpdate)
            }

    private suspend fun processFileContent(
        fileContent: ByteArray,
        resourceBundleSpecialtyPricingUpdate: ResourceBundleSpecialtyPricingUpdate
    ): Result<ResourceBundleSpecialtyPricingUpdate, Throwable> {
        try {
            validateFile(fileContent)

            fileContent.inputStream().buffered().bufferedReader().use { reader ->
                CSVParser(reader, CSVFormat.DEFAULT.withDelimiter(',').withFirstRecordAsHeader()).use { csv ->
                    validateHeader(csv.headerNames)

                    val failedRows = mutableListOf<CSVPricingUpdateError>()
                    val bundleIdSet = mutableSetOf<UUID>()

                    for ((index, record) in csv.records.withIndex()) {
                        val currentLine = index + 1

                        processEachRow(
                            record = record,
                            currentLine = currentLine,
                            failedRows = failedRows,
                            effectiveDate = resourceBundleSpecialtyPricingUpdate.pricesBeginAt,
                            bundleIdSet = bundleIdSet
                        )
                    }

                    val completedUpdate = resourceBundleSpecialtyPricingUpdate.copy(
                        completedAt = LocalDateTime.now(),
                        failedRowsCount = failedRows.size,
                        failedRowsErrors = failedRows,
                        rowsCount = csv.recordNumber.toInt()
                    )

                    return resourceBundleSpecialtyPricingUpdateService.update(completedUpdate)
                }
            }
        } catch (e: Exception) {
            val errorMessage = when (e) {
                is IllegalArgumentException -> e.message ?: "Formato de CSV inválido"
                is java.io.IOException -> "Erro ao ler o arquivo: ${e.message}"
                else -> "Erro ao processar o arquivo: ${e.message}"
            }

            val errorUpdate = resourceBundleSpecialtyPricingUpdate.copy(
                completedAt = LocalDateTime.now(),
                parsingError = errorMessage
            )

            return resourceBundleSpecialtyPricingUpdateService.update(errorUpdate)
        }
    }

    private fun validateHeader(headerNames: List<String>) {
        val requiredHeaders = listOf(
            RESOURCE_BUNDLE_SPECIALTY_ID,
            TALENTED_T3,
            TALENTED_T2,
            TALENTED_T1,
            TALENTED_T0,
            EXPERT_T2,
            EXPERT_T1,
            EXPERT_T0,
            SUPER_EXPERT_T1,
            SUPER_EXPERT_T0,
            ULTRA_EXPERT_T0
        )

        val missingColumns = requiredHeaders.mapNotNull {
            if (it !in headerNames) it
            else null
        }

        if (missingColumns.isNotEmpty()) throw IllegalArgumentException("Colunas faltando: ${missingColumns.joinToString(";")}")
    }

    private fun validateFile(fileContent: ByteArray) =
        fileContent.inputStream().buffered().bufferedReader().use { reader ->
            if (!reader.ready()) {
                throw IllegalArgumentException("Arquivo CSV está vazio")
            }

            val firstLine = reader.readLine()
            if (firstLine == null || firstLine.isBlank() || !firstLine.contains(RESOURCE_BUNDLE_SPECIALTY_ID)) {
                throw IllegalArgumentException("Arquivo CSV não está com a formatação correta")
            }
        }

    private suspend fun processEachRow(
        record: CSVRecord,
        currentLine: Int,
        failedRows: MutableList<CSVPricingUpdateError>,
        effectiveDate: LocalDate,
        bundleIdSet: MutableSet<UUID>
    ) = try {
        val row = parseRow(record, currentLine, bundleIdSet)
        val validationErrors = validateRow(row)

        if (validationErrors.isEmpty()) {
            resourceBundleSpecialtyService.changeResourcePrice(
                resourceBundleSpecialtyId = row.resourceBundleSpecialtyId,
                prices = createPricesFromRow(row),
                effectiveDate = effectiveDate,
            ).get()

        } else failedRows.add(CSVPricingUpdateError(currentLine, validationErrors.joinToString("; ")))

    } catch (e: Exception) {
        failedRows.add(CSVPricingUpdateError(currentLine, "Erro durante o processamento: ${e.message}"))
    }

    private fun parseRow(
        record: CSVRecord,
        index: Int,
        bundleIdSet: MutableSet<UUID>
    ): ResourceBundleSpecialtyPricingCSVRow {
        val resourceBundleSpecialtyId = getUUID(record)
        if (bundleIdSet.contains(resourceBundleSpecialtyId)) {
            throw IllegalArgumentException("Codigo duplicado: $resourceBundleSpecialtyId")
        }
        if (record.size() < CSV_COLUMNS_COUNT) {
            throw IllegalArgumentException("Número de colunas inválido na linha $index: ${record.size()}")
        }

        bundleIdSet.add(resourceBundleSpecialtyId)

        return ResourceBundleSpecialtyPricingCSVRow(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            talentedT3 = getDecimalOrDefault(record, TALENTED_T3),
            talentedT2 = getDecimalOrDefault(record, TALENTED_T2),
            talentedT1 = getDecimalOrDefault(record, TALENTED_T1),
            talentedT0 = getDecimalOrDefault(record, TALENTED_T0),
            expertT2 = getDecimalOrDefault(record, EXPERT_T2),
            expertT1 = getDecimalOrDefault(record, EXPERT_T1),
            expertT0 = getDecimalOrDefault(record, EXPERT_T0),
            superExpertT1 = getDecimalOrDefault(record, SUPER_EXPERT_T1),
            superExpertT0 = getDecimalOrDefault(record, SUPER_EXPERT_T0),
            ultraExpertT0 = getDecimalOrDefault(record, ULTRA_EXPERT_T0),
            rowIndex = index
        )
    }

    private fun getUUID(record: CSVRecord) = try {
        UUID.fromString(record.get(RESOURCE_BUNDLE_SPECIALTY_ID))
    } catch (e: Exception) {
        throw IllegalArgumentException("Erro ao converter $RESOURCE_BUNDLE_SPECIALTY_ID para UUID")
    }

    private fun getDecimalOrDefault(record: CSVRecord, columnName: String) =
        try {
            val value = record.takeIf { it.isMapped(columnName) }?.get(columnName)?.takeIf { it.isNotBlank() }
            value?.let { it.toBigDecimal() } ?: DEFAULT_ERROR_PRICE
        }  catch (e: Exception) {
            tryInt(record, columnName)
        }

    private fun tryInt(record: CSVRecord, columnName: String) =
        try {
            val value = record.takeIf { it.isMapped(columnName) }?.get(columnName)?.takeIf { it.isNotBlank() }
            value?.let { it.toInt().toBigDecimal() } ?: DEFAULT_ERROR_PRICE
        }  catch (e: Exception) {
            throw IllegalArgumentException("Erro ao converter $columnName para decimal")
        }

    private suspend fun validateRow(row: ResourceBundleSpecialtyPricingCSVRow): List<String> {
        val errors = mutableListOf<String>()

        validateResourceBundleSpecialty(row, errors)
        validatePositivePrices(row, errors)
        validateRequiredPrices(row, errors)

        return errors
    }

    private suspend fun validateResourceBundleSpecialty(
        row: ResourceBundleSpecialtyPricingCSVRow,
        errors: MutableList<String>
    ) {
        val bundleSpecialty = resourceBundleSpecialtyService.get(row.resourceBundleSpecialtyId).getOrNullIfNotFound()

        if (bundleSpecialty == null || !bundleSpecialty.isActive()) {
            errors.add("Especialidade e código alice não estão vinculados")
        }
    }

    private fun validatePositivePrices(row: ResourceBundleSpecialtyPricingCSVRow, errors: MutableList<String>) {
        mapTierAndPrices(row).forEach { (_, pricePair) ->
            val (price, label) = pricePair

            if (price < BigDecimal.ZERO && price != DEFAULT_ERROR_PRICE) {
                errors.add("$label preço precisa ser maior ou igual a 0.00")
            }
        }
    }

    private fun validateRequiredPrices(row: ResourceBundleSpecialtyPricingCSVRow, errors: MutableList<String>) {
        val requiredPrices = ResourceBundleSpecialtyPricing.requiredCombinations

        mapTierAndPrices(row).forEach { (tierPair, pricePair) ->
            val (tier, productTier) = tierPair
            val (price, label) = pricePair

            if (price == DEFAULT_ERROR_PRICE && requiredPrices[tier]?.contains(productTier) == true) {
                errors.add("Preço obrigatório $label não encontrado")
            }
        }
    }

    private fun createPricesFromRow(row: ResourceBundleSpecialtyPricingCSVRow) = listOf(
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_3, row.talentedT3),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_2, row.talentedT2),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_1, row.talentedT1),
        ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, row.talentedT0),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_2, row.expertT2),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_1, row.expertT1),
        ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_0, row.expertT0),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_1, row.superExpertT1),
        ResourceBundleSpecialtyPrice(SpecialistTier.SUPER_EXPERT, TierType.TIER_0, row.superExpertT0),
        ResourceBundleSpecialtyPrice(SpecialistTier.ULTRA_EXPERT, TierType.TIER_0, row.ultraExpertT0)
    )

    private fun mapTierAndPrices(row: ResourceBundleSpecialtyPricingCSVRow) = mapOf(
        (SpecialistTier.TALENTED to TierType.TIER_3) to (row.talentedT3 to TALENTED_T3),
        (SpecialistTier.TALENTED to TierType.TIER_2) to (row.talentedT2 to TALENTED_T2),
        (SpecialistTier.TALENTED to TierType.TIER_1) to (row.talentedT1 to TALENTED_T1),
        (SpecialistTier.TALENTED to TierType.TIER_0) to (row.talentedT0 to TALENTED_T0),
        (SpecialistTier.EXPERT to TierType.TIER_2) to (row.expertT2 to EXPERT_T2),
        (SpecialistTier.EXPERT to TierType.TIER_1) to (row.expertT1 to EXPERT_T1),
        (SpecialistTier.EXPERT to TierType.TIER_0) to (row.expertT0 to EXPERT_T0),
        (SpecialistTier.SUPER_EXPERT to TierType.TIER_1) to (row.superExpertT1 to SUPER_EXPERT_T1),
        (SpecialistTier.SUPER_EXPERT to TierType.TIER_0) to (row.superExpertT0 to SUPER_EXPERT_T0),
        (SpecialistTier.ULTRA_EXPERT to TierType.TIER_0) to (row.ultraExpertT0 to ULTRA_EXPERT_T0)
    )
}

data class ResourceBundleSpecialtyPricingCSVRow(
    val resourceBundleSpecialtyId: UUID,
    val talentedT3: BigDecimal,
    val talentedT2: BigDecimal,
    val talentedT1: BigDecimal,
    val talentedT0: BigDecimal,
    val expertT2: BigDecimal,
    val expertT1: BigDecimal,
    val expertT0: BigDecimal,
    val superExpertT1: BigDecimal,
    val superExpertT0: BigDecimal,
    val ultraExpertT0: BigDecimal,
    val rowIndex: Int
)
