package br.com.alice.exec.indicator.service.specialistResources

import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.notContains
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleManagementService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.events.HealthSpecialistResourceBundleUpsertedEvent
import br.com.alice.exec.indicator.logics.SuggestedProcedureLogic
import br.com.alice.exec.indicator.models.AppointmentRecommendationBondRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePaginatedResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePricingStatus
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundleWithPricingData
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistMedicalSpecialtyResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistRequestFilters
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleResponseStatus
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleWithCountResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtiesWithCount
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregate
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregateCount
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyResponse
import br.com.alice.exec.indicator.models.ResourceSuggestedProcedure
import br.com.alice.exec.indicator.models.SecondaryResourcesTransport
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyService
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class HealthSpecialistResourceBundleManagementServiceImpl(
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService,
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService,
    private val producerService: KafkaProducerService,
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
) : HealthSpecialistResourceBundleManagementService {

    override suspend fun update(
        id: UUID,
        request: HealthSpecialistResourceBundlePatchRequest
    ): Result<HealthSpecialistResourceBundlePatchResponse, Throwable> {
        return healthSpecialistResourceBundleService.get(id)
            .flatMap {
                healthSpecialistResourceBundleService.update(
                    it.copy(
                        description = request.aliceDescription ?: it.description,
                        status = request.status ?: it.status,
                    ))
            }
            .flatMapPair { resource ->
                request.medicalSpecialtyIds?.let { resourceBundleSpecialtyService.updateResourceSpecialties(resource.id, it) }
                    ?: resourceBundleSpecialtyService.getActiveByResourceBundleId(resource.id)
            }
            .map { (resourceBundleSpecialties, healthSpecialistResourceBundle) ->
                HealthSpecialistResourceBundlePatchResponse(
                    id = healthSpecialistResourceBundle.id,
                    secondaryResources = healthSpecialistResourceBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
                    executionAmount = healthSpecialistResourceBundle.executionAmount,
                    executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
                    aliceDescription = healthSpecialistResourceBundle.description,
                    status = healthSpecialistResourceBundle.status,
                    serviceType = healthSpecialistResourceBundle.serviceType,
                    medicalSpecialtyIds = resourceBundleSpecialties.map { it.medicalSpecialtyId },
                ) }
            .then { producerService.produce(HealthSpecialistResourceBundleUpsertedEvent(it.id)) }
    }

    override suspend fun changeAppointmentLevelBond(
        specialtyId: UUID,
        request: AppointmentRecommendationBondRequest
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyService.findBySpecialtyId(specialtyId).map {
            SuggestedProcedureLogic.buildProcedureSuggestedToUpdateBond(
                it,
                request
            )
        }.flatMap { (procedureToRemove, procedureToAdd) ->
            resourceBundleSpecialtyService.updateList(
                procedureToAdd + procedureToRemove
            )
        }

    override suspend fun list(
        query: String?,
        range: IntRange?
    ): Result<HealthSpecialistResourceBundlePaginatedResponse, Throwable> = coroutineScope {
        val bundlesDeferred = async { healthSpecialistResourceBundleService.findByFilters(query, range).get() }
        val countDeferred = async { healthSpecialistResourceBundleService.countByFilters(query).get() }
        val allSpecialtiesCountDeferred = async { medicalSpecialtyService.getActivesByType(
            type = MedicalSpecialtyType.SPECIALTY,
            excludeInternal = true,
        ).get() }

        val bundles = bundlesDeferred.await()
        val count = countDeferred.await()
        val allSpecialtiesCount = allSpecialtiesCountDeferred.await()

        resourceBundleSpecialtyService.getByResourceBundleIds(bundles.map { it.id }).flatMapPair {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(
                it.map { resourceBundleSpecialty -> resourceBundleSpecialty.id }
            )
        }.map { (resourceBundleSpecialtyPricingList, resourceBundleSpecialtyList) ->
            val resourceBundleSpecialtyByResourceBundleId = resourceBundleSpecialtyList.groupBy {
                resourceBundleSpecialty -> resourceBundleSpecialty.healthSpecialistResourceBundleId
            }
            val resourceBundleSpecialtyPricingByResourceBundleSpecialtyId = resourceBundleSpecialtyPricingList.associateBy {
                resourceBundleSpecialtyPricing -> resourceBundleSpecialtyPricing.resourceBundleSpecialtyId
            }

            HealthSpecialistResourceBundlePaginatedResponse(
                total = count,
                items = bundles.map { healthSpecialistResourceBundle ->
                    val resourceBundleSpecialties = resourceBundleSpecialtyByResourceBundleId[healthSpecialistResourceBundle.id] ?: emptyList()

                    HealthSpecialistResourceBundleWithPricingData(
                        healthSpecialistResourceBundle = healthSpecialistResourceBundle,
                        specialtiesCount = resourceBundleSpecialtyByResourceBundleId[healthSpecialistResourceBundle.id]?.size ?: 0,
                        pricingStatus = calculatePricingStatus(
                            resourceBundleSpecialtyPricingByResourceBundleSpecialtyId,
                            resourceBundleSpecialties
                        ),
                        medicalSpecialtyIds = resourceBundleSpecialties.map { it.medicalSpecialtyId },
                        allSpecialtiesCount = allSpecialtiesCount.count(),
                    )
                }
            )
        }

    }

    override suspend fun getMedicalSpecialtiesRelatedToResourceBundle(
        id: UUID,
        range: IntRange,
    ): Result<ResourceBundleSpecialtiesWithCount, Throwable> = coroutineScope {
        resourceBundleSpecialtyService.getByResourceBundleId(id)
            .map {
                val resourceBundleSpecialties = it.filter { resourceBundleSpecialty ->
                    resourceBundleSpecialty.status == Status.ACTIVE
                }

                val medicalSpecialtiesDeferred = async {
                    medicalSpecialtyService.getByIds(
                        resourceBundleSpecialties.map { resourceBundleSpecialty -> resourceBundleSpecialty.medicalSpecialtyId }
                    ).get()
                }

                val resourceBundleSpecialtyPricingListDeferred = async {
                    resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(
                        resourceBundleSpecialties.map { resourceBundleSpecialty -> resourceBundleSpecialty.id }
                    ).get()
                }

                val medicalSpecialtiesById = medicalSpecialtiesDeferred.await().associateBy { it.id }
                val resourceBundleSpecialtyPricingByResourceSpecialtyId =
                    resourceBundleSpecialtyPricingListDeferred.await().associateBy { it.resourceBundleSpecialtyId }

                resourceBundleSpecialties.map { resourceBundleSpecialty ->
                    buildResourceBundleSpecialtyResponse(
                        resourceBundleSpecialty,
                        medicalSpecialtiesById,
                        resourceBundleSpecialtyPricingByResourceSpecialtyId
                    )
                }
            }.map {
                val specialties = it.sortedBy { resourceBundleSpecialtyResponse -> resourceBundleSpecialtyResponse.name }
                    .subList(range.first, (range.last + 1).coerceAtMost(it.size))

                ResourceBundleSpecialtiesWithCount(
                    count = it.size,
                    specialties = specialties,
                )
            }
    }

    override suspend fun getPendingResourceSpecialtyBundlesForPricing(): Result<List<ResourceBundleSpecialty>, Throwable> {
        return resourceBundleSpecialtyService.getByPricingStatus(pricingStatus = PricingStatus.NOT_PRICED)
    }

    override suspend fun getResourceBundleSpecialtyPricingList(
        filters: PricingForHealthSpecialistRequestFilters,
        range: IntRange
    ): Result<PricingForHealthSpecialistResourceBundleWithCountResponse, Throwable> {
        val resourceBundleSpecialtiesToFilter = if (hasResourceBundleSpecialtiesFilters(filters)) {
            getResourceBundleSpecialtiesForFilter(filters).get()
        } else {
            null
        }

        val healthSpecialistResourceBundleIdsToFilter = resourceBundleSpecialtiesToFilter?.map {
            it.healthSpecialistResourceBundleId
        }?.distinct()

        return getBundlesAndCount(
            filters = filters,
            ids = healthSpecialistResourceBundleIdsToFilter,
            range = range,
        ).flatMapPair { (healthSpecialistResourceBundles, count) ->
            getResourceBundleSpecialtiesRelatedToResourceBundle(
                resourceBundleSpecialtiesToFilter,
                healthSpecialistResourceBundles
            )
        }.mapPair { (resourceBundleSpecialties, _) ->
            getMedicalSpecialtiesAndResourceBundleSpecialtyPricingListRelatedToResourceBundleSpecialties(
                resourceBundleSpecialties
            )
        }.map { (medicalSpecialtiesAndResourceBundleSpecialtyPricingList, resourceBundleSpecialtiesAndHealthSpecialistResourceBundles) ->
            val resourceBundleSpecialties = resourceBundleSpecialtiesAndHealthSpecialistResourceBundles.first
            val healthSpecialistResourceBundlesAndCount = resourceBundleSpecialtiesAndHealthSpecialistResourceBundles.second
            val healthSpecialistResourceBundles = healthSpecialistResourceBundlesAndCount.first
            val count = healthSpecialistResourceBundlesAndCount.second
            val medicalSpecialtiesById = medicalSpecialtiesAndResourceBundleSpecialtyPricingList.first
                .associateBy { it.id }
            val resourceBundleSpecialtyPricingList = medicalSpecialtiesAndResourceBundleSpecialtyPricingList.second

            buildPricingForHealthSpecialistResourceBundleWithCountResponse(
                resourceBundleSpecialtyPricingList,
                resourceBundleSpecialties,
                medicalSpecialtiesById,
                healthSpecialistResourceBundles,
                count
            )
        }
    }

    private suspend fun getResourceBundleSpecialtiesForFilter(
        filters: PricingForHealthSpecialistRequestFilters,
    ): Result<List<ResourceBundleSpecialty>, Throwable> = coResultOf {
        if (filters.status != null || filters.medicalSpecialtyIds != null) {
            resourceBundleSpecialtyService.getByFilters(
                pricingStatus= filters.status,
                medicalSpecialtyIds = filters.medicalSpecialtyIds,
            ).flatMapError {
                logger.info("HealthSpecialistResourceBundleManagementServiceImpl::getResourceBundleSpecialtiesForFilter Error getting resource bundle specialties")
                it.failure()
            }.get()
        } else {
            throw Exception("HealthSpecialistResourceBundleManagementServiceImpl::getResourceBundleSpecialtiesForFilter Invalid filter")
        }
    }

    private suspend fun getBundlesAndCount(
        filters: PricingForHealthSpecialistRequestFilters,
        range: IntRange,
        ids: List<UUID>? = null,
    ) = coroutineScope {
        val resourceBundlesDeferred = async {
            healthSpecialistResourceBundleService.findByFilters(
                query = filters.query,
                ids = ids,
                range = range,
                serviceTypes = filters.serviceTypes,
            )
        }

        val countResourceBundlesDeferred = async {
            healthSpecialistResourceBundleService.countByFilters(
                query = filters.query,
                ids = ids,
                serviceTypes = filters.serviceTypes,
            )
        }

        coResultOf<Pair<List<HealthSpecialistResourceBundle>, Int>, Throwable> {
            resourceBundlesDeferred.await().get() to countResourceBundlesDeferred.await().get()
        }
    }

    private suspend fun getMedicalSpecialtiesAndResourceBundleSpecialtyPricingListRelatedToResourceBundleSpecialties(
        resourceBundleSpecialties: List<ResourceBundleSpecialty>
    ) = coroutineScope {
        val medicalSpecialtiesDeferred = async {
            medicalSpecialtyService.getByIds(resourceBundleSpecialties.map { it.medicalSpecialtyId })
        }

        val resourceBundleSpecialtyPricingListDeferred = async {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(
                resourceBundleSpecialties.map { it.id }
            )
        }

        val medicalSpecialties = medicalSpecialtiesDeferred.await().get()
        val resourceBundleSpecialtyPricingList = resourceBundleSpecialtyPricingListDeferred.await().get()

        return@coroutineScope medicalSpecialties to resourceBundleSpecialtyPricingList
    }

    private suspend fun getResourceBundleSpecialtiesRelatedToResourceBundle(
        resourceBundleSpecialtiesToFilter: List<ResourceBundleSpecialty>?,
        healthSpecialistResourceBundles: List<HealthSpecialistResourceBundle>
    ) = coResultOf<List<ResourceBundleSpecialty>, Throwable> {

        // If the filter is not null, we use it to get the resource bundle specialties
        val resourceBundleSpecialties = resourceBundleSpecialtiesToFilter
            ?: resourceBundleSpecialtyService.getByResourceBundleIds(
                healthSpecialistResourceBundles.map { it.id }
            ).flatMapError {
                logger.info("HealthSpecialistResourceBundleManagementServiceImpl::getResourceBundleSpecialtyPricingList Error getting resource bundle specialties")
                it.failure()
            }.get()

        resourceBundleSpecialties
    }

    private fun hasResourceBundleSpecialtiesFilters(
        filters: PricingForHealthSpecialistRequestFilters,
    ): Boolean {
        return filters.medicalSpecialtyIds != null || filters.status != null
    }

    override suspend fun getBySpecialty(
        specialtyId: UUID,
        query: String?,
        range: IntRange?
    ): Result<ResourceBundleSpecialtyAggregateCount, Throwable> = coroutineScope {
        val resources = resourceBundleSpecialtyService.findBySpecialtyId(specialtyId).get()
        if (resources.isEmpty()) return@coroutineScope ResourceBundleSpecialtyAggregateCount(
            count = 0,
            response = emptyList()
        ).success()

        val filter = HealthSpecialistResourceBundleService.Filter(
            searchToken = query,
            ids = resources.map { it.healthSpecialistResourceBundleId }
        )
        val bundles = async { healthSpecialistResourceBundleService.findBy(filter, range).get() }
        val count = async { healthSpecialistResourceBundleService.countBy(filter).get() }

        return@coroutineScope ResourceBundleSpecialtyAggregateCount(
            count = count.await(),
            response = buildResourceBundleSpecialtyAggregate(
                resourceBundleSpecialty = resources,
                healthSpecialistResourceBundle = bundles.await(),
            )
        ).success()
    }

    override suspend fun getSuggestedBySpecialty(specialtyId: UUID): Result<ResourceSuggestedProcedure, Throwable> =
        resourceBundleSpecialtyService.findBySpecialtyId(
            specialtyId = specialtyId,
            appointmentLevel = listOf(
                AppointmentRecommendationLevel.DEFAULT,
                AppointmentRecommendationLevel.RECOMMENDED
            )
        ).flatMapPair { resource ->
            if (resource.isEmpty()) {
                return@flatMapPair emptyList<HealthSpecialistResourceBundle>().success()
            }
            healthSpecialistResourceBundleService.findBy(
                HealthSpecialistResourceBundleService.Filter(ids = resource.map { it.healthSpecialistResourceBundleId }),
            )
        }.map { (bundles, resources) ->
            buildResourceBundleSpecialtyAggregate(
                resourceBundleSpecialty = resources,
                healthSpecialistResourceBundle = bundles,
            )
        }.map {
            ResourceSuggestedProcedure(
                procedureDefault = it.firstOrNull { resourceBundleSpecialtyAggregate ->
                    resourceBundleSpecialtyAggregate.appointmentRecommendationLevel == AppointmentRecommendationLevel.DEFAULT
                },
                suggestedProcedure = it.filter { resourceBundleSpecialtyAggregate ->
                    resourceBundleSpecialtyAggregate.appointmentRecommendationLevel == AppointmentRecommendationLevel.RECOMMENDED
                }
            )
        }

    private fun calculatePricingStatus(
        resourceBundleSpecialtyPricingByResourceBundleSpecialtyId: Map<UUID, ResourceBundleSpecialtyPricing>,
        resourceBundleSpecialtyList: List<ResourceBundleSpecialty>
    ): HealthSpecialistResourceBundlePricingStatus {
        for (resourceBundleSpecialty in resourceBundleSpecialtyList) {
            val pricing = resourceBundleSpecialtyPricingByResourceBundleSpecialtyId[resourceBundleSpecialty.id]
            if (pricing == null || !pricing.hasRequiredPrices()) {
                return HealthSpecialistResourceBundlePricingStatus.PENDING
            }
        }
        return HealthSpecialistResourceBundlePricingStatus.PRICED
    }


    private fun buildResourceBundleSpecialtyResponse(
        resourceBundleSpecialty: ResourceBundleSpecialty,
        medicalSpecialtiesById: Map<UUID, MedicalSpecialty>,
        resourceBundleSpecialtyPricingByResourceSpecialtyId: Map<UUID, ResourceBundleSpecialtyPricing>
    ): ResourceBundleSpecialtyResponse {
        val medicalSpecialty = medicalSpecialtiesById[resourceBundleSpecialty.medicalSpecialtyId]
        return ResourceBundleSpecialtyResponse(
            id = resourceBundleSpecialty.id,
            name = medicalSpecialty?.name ?: "",
            isTherapy = medicalSpecialty?.isTherapy == true,
            pricingStatus = calculatePricingStatus(
                resourceBundleSpecialtyPricingByResourceSpecialtyId,
                listOf(resourceBundleSpecialty)
            ),
            currentBeginAt = resourceBundleSpecialtyPricingByResourceSpecialtyId[resourceBundleSpecialty.id]?.beginAt,
            currentEndAt = resourceBundleSpecialtyPricingByResourceSpecialtyId[resourceBundleSpecialty.id]?.endAt,
            // If the end date is null, it means that the price is active and there is no scheduled change,
            // if the end date is not null and the specialty is active, it means that there is a scheduled change,
            // otherwise endAt would be before now and the specialty would be inactive
            hasScheduledPriceChange = resourceBundleSpecialtyPricingByResourceSpecialtyId[resourceBundleSpecialty.id]?.endAt != null,
            medicalSpecialtyId = resourceBundleSpecialty.medicalSpecialtyId,
        )
    }


    private fun buildPricingForHealthSpecialistResourceBundleWithCountResponse(
        resourceBundleSpecialtyPricingList: List<ResourceBundleSpecialtyPricing>,
        resourceBundleSpecialtyList: List<ResourceBundleSpecialty>,
        medicalSpecialtiesById: Map<UUID, MedicalSpecialty>,
        healthSpecialistResourceBundles: List<HealthSpecialistResourceBundle>,
        count: Int,
    ): PricingForHealthSpecialistResourceBundleWithCountResponse {
        val resourceBundleSpecialtyByResourceBundleId = resourceBundleSpecialtyList.groupBy {
            resourceBundleSpecialty -> resourceBundleSpecialty.healthSpecialistResourceBundleId
        }
        val resourceBundleSpecialtyPricingByResourceBundleSpecialtyId = resourceBundleSpecialtyPricingList.associateBy {
            resourceBundleSpecialtyPricing -> resourceBundleSpecialtyPricing.resourceBundleSpecialtyId
        }

        return PricingForHealthSpecialistResourceBundleWithCountResponse(
            count = count,
            items = healthSpecialistResourceBundles.map { healthSpecialistResourceBundle ->
                val resourceBundleSpecialties = resourceBundleSpecialtyByResourceBundleId[healthSpecialistResourceBundle.id] ?: emptyList()

                PricingForHealthSpecialistResourceBundleResponse(
                    healthSpecialistResourceBundleId = healthSpecialistResourceBundle.id,
                    primaryTuss = healthSpecialistResourceBundle.primaryTuss,
                    aliceCode = healthSpecialistResourceBundle.code,
                    description = healthSpecialistResourceBundle.description,
                    serviceType = healthSpecialistResourceBundle.serviceType.description,
                    pendingNumber = resourceBundleSpecialties.count { it.pricingStatus != PricingStatus.PRICED },
                    status = when {
                        resourceBundleSpecialties.isEmpty() -> PricingForHealthSpecialistResourceBundleResponseStatus.NO_ASSOCIATED_SPECIALTIES
                        resourceBundleSpecialties.any { it.pricingStatus != PricingStatus.PRICED } -> PricingForHealthSpecialistResourceBundleResponseStatus.PENDING
                        else -> PricingForHealthSpecialistResourceBundleResponseStatus.PRICED
                    },
                    medicalSpecialties = resourceBundleSpecialties.map { resourceBundleSpecialty ->
                        val medicalSpecialty = medicalSpecialtiesById[resourceBundleSpecialty.medicalSpecialtyId]
                        PricingForHealthSpecialistMedicalSpecialtyResponse(
                            resourceBundleSpecialtyId = resourceBundleSpecialty.id,
                            medicalSpecialtyId = resourceBundleSpecialty.medicalSpecialtyId,
                            description = medicalSpecialty?.name ?: "",
                            prices = resourceBundleSpecialtyPricingByResourceBundleSpecialtyId[resourceBundleSpecialty.id]?.prices ?: emptyList(),
                            pendingNumber =
                                resourceBundleSpecialtyPricingByResourceBundleSpecialtyId[resourceBundleSpecialty.id]?.numberOfMissingRequiredPrices() ?:
                                ResourceBundleSpecialtyPricing.numberOfRequiredPrices,
                            beginAt = resourceBundleSpecialtyPricingByResourceBundleSpecialtyId[resourceBundleSpecialty.id]?.beginAt,
                            changeBeginAt = resourceBundleSpecialtyPricingByResourceBundleSpecialtyId[resourceBundleSpecialty.id]?.endAt?.plusDays(1),
                        )
                    }.sortedBy { it.description }
                )
            }
        )
    }

    private fun buildResourceBundleSpecialtyAggregate(
        resourceBundleSpecialty: List<ResourceBundleSpecialty>,
        healthSpecialistResourceBundle: List<HealthSpecialistResourceBundle>,
    ): List<ResourceBundleSpecialtyAggregate> {
        return resourceBundleSpecialty.mapNotNull {
            val healthSpecialist = healthSpecialistResourceBundle.firstOrNull { healthSpecialist ->
                healthSpecialist.id == it.healthSpecialistResourceBundleId
            } ?: return@mapNotNull null

            ResourceBundleSpecialtyAggregate(
                id = it.id,
                appointmentRecommendationLevel = it.appointmentRecommendationLevel,
                status = it.status,
                medicalSpecialtyId = it.medicalSpecialtyId,
                primaryTuss = healthSpecialist.primaryTuss,
                code = healthSpecialist.code,
                description = healthSpecialist.description,
                healthSpecialistResourceBundleStatus = healthSpecialist.status,
                serviceType = healthSpecialist.serviceType,
            )
        }
    }

}
