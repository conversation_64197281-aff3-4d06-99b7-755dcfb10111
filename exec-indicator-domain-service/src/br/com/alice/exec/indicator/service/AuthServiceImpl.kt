package br.com.alice.exec.indicator.service

import br.com.alice.authentication.sendSignInEmailLink
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.utils.RandomIdUtils
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MagicNumbers
import br.com.alice.data.layer.models.Staff
import br.com.alice.exec.indicator.client.AuthService
import br.com.alice.exec.indicator.client.AuthorizerService
import br.com.alice.exec.indicator.client.MagicNumbersService
import br.com.alice.exec.indicator.models.ActiveClinicalUserResponse
import br.com.alice.exec.indicator.models.Authorizer
import br.com.alice.exec.indicator.models.EitaUserType
import br.com.alice.exec.indicator.models.SignInAuthorizersResponse
import br.com.alice.exec.indicator.service.internal.EmailService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.util.UUID

class AuthServiceImpl(
    private val emailService: EmailService,
    private val staffService: StaffService,
    private val authorizerService: AuthorizerService,
    private val magicNumbersService: MagicNumbersService,
    private val providerUnitService: ProviderUnitService
) : AuthService {

    private val pinCodeChars = "**********".toCharArray()
    private val pinCodeSize = 6
    private val pinExpirationInMinutes = 10L

    override suspend fun createAuthLink(email: String, url: String): Result<String, Throwable> =
        sendSignInEmailLink(
            email,
            url
        ).success()

    override suspend fun createPinAuthentication(
        email: String,
        url: String,
        sendEmail: Boolean
    ): Result<String, Throwable> {

        val secretUrl = this.createAuthLink(email, url).get()
        val magicNumbers = MagicNumbers(
            code = createPinCode(),
            link = secretUrl,
            expirationAt = LocalDateTime.now().plusMinutes(pinExpirationInMinutes),
            generateTo = email
        )

        return coroutineScope {
            launch { magicNumbersService.add(magicNumbers) }
            val recipient = email.split("@")

            logger.info(
                "EITA sign with magic number",
                "recipient_owner" to recipient.first(),
                "recipient_domain" to recipient.last(),
                "magic_number" to magicNumbers.code
            )

            if (sendEmail) {
                launch {
                    emailService.sendPinAuthenticationEmail(
                        email = email,
                        recipient = email,
                        pin = magicNumbers.code,
                        app = "EITA"
                    ).thenError {
                        logger.error("Error sending pin code email", it)
                    }
                }
            }

            "OK".success()
        }
    }

    override suspend fun verifyPinAuthentication(pin: String): Result<String, Throwable> {
        return magicNumbersService.findAvailableCode(pin)
            .flatMap { activeCode ->
                logger.info("verifyPinAuthentication: mark code has accessed by user", "pin" to pin)
                magicNumbersService.changeToAccessed(activeCode)

                activeCode.link.success()
            }
            .coFoldNotFound {
                logger.error("verifyPinAuthentication: magic number not found", "pin" to pin)
                NotFoundException(
                    code = "magic_number_active_not_found",
                    message = "Código não existente, expirado ou já acessado"
                ).failure()
            }
    }

    private fun authorizersToSignInResponse(
        authorizers: List<Authorizer>,
        domain: String?,
        userType: EitaUserType? = EitaUserType.GENERAL,
        staff: Staff? = null,
    ) =
        SignInAuthorizersResponse(
            allow = authorizers.isNotEmpty(),
            authorizers = authorizers,
            domain = domain,
            userType = userType!!,
            staff = staff,
            userId = staff?.id
        ).success()

    override suspend fun getAvailableAuthorizersForStaff(
        email: String,
        shouldReturnAllAuthorizers: Boolean
    ): Result<SignInAuthorizersResponse, Throwable> {
        val domain = email.split("@").last()

        return staffService.findActiveByEmail(email)
            .flatMap { staff ->
                val allow = isStaffAllowedToLogin(staff)
                val allowEveryProviderUnit = isStaffAllowedToLoginOnEverything(staff)
                logger.info("findByEmail", "allow" to allow, "allowEveryproviderUnit" to allowEveryProviderUnit)

                return@flatMap if (allowEveryProviderUnit && shouldReturnAllAuthorizers) {
                    return coResultOf {
                        val providerAuthorizer = mutableListOf<Authorizer>()
                        if (staff.role in canSwitchToDuquesaProviderUnitsRoles()) {
                            providerUnitService.findAllClinicalDuquesa().get().map { providerUnit ->
                                providerAuthorizer.add(
                                    Authorizer(
                                        id = null,
                                        name = providerUnit.name,
                                        providerUnitId = providerUnit.id,
                                        unitType = providerUnit.type,
                                        cnpj = providerUnit.cnpj,
                                        address = providerUnit.address?.formattedAddress().orEmpty(),
                                        brand = providerUnit.brand
                                    )
                                )
                            }
                        }

                        val authorizers = authorizerService.all().get()
                        logger.info(".all()", "authorizers" to authorizers)
                        authorizersToSignInResponse(authorizers.plus(providerAuthorizer), domain, staff = staff).get()
                    }
                } else if (allow) {
                    authorizerService.authorizersByDomain(domain).map { authorizers ->
                        return authorizersToSignInResponse(authorizers, domain, staff = staff)
                    }
                } else {
                    SignInAuthorizersResponse(domain = domain).success()
                }
            }
            .coFoldNotFound {
                authorizerService.authorizersByDomain(domain)
                    .flatMap { authorizers -> authorizersToSignInResponse(authorizers, domain) }
            }
    }

    override suspend fun getAvailableAuthorizersByEmployee(
        employeeRole: EitaUserType,
        employeeId: UUID
    ): Result<SignInAuthorizersResponse, Throwable> =
        authorizerService.authorizersByEmployee(employeeRole = employeeRole, employeeId = employeeId)
            .fold({ authorizers ->
                authorizersToSignInResponse(authorizers = authorizers, domain = null, userType = employeeRole)
            }, {
                logger.info("Authorizers get by employee failed", "error" to it)
                SignInAuthorizersResponse(domain = null, userType = employeeRole).success()
            }
            )

    override suspend fun verifyActiveClinicalUserByEmail(
        email: String
    ) = coResultOf<ActiveClinicalUserResponse, Throwable> {
        logger.info(
            "EITA verifyActiveClinicalUserByEmail: begin",
            "email" to email
        )

        val staff = staffService.findActiveByEmail(email).getOrNullIfNotFound()
        verifyClinicalUser(staff)
    }

    private fun verifyClinicalUser(
        staff: Staff?
    ): ActiveClinicalUserResponse {
        val clinicalStaffTypes = listOf(
            StaffType.COMMUNITY_SPECIALIST,
            StaffType.HEALTH_ADMINISTRATIVE,
            StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL
        )

        if (staff != null && staff.type in clinicalStaffTypes) {
            val type = staff.role.toEitaUserType()
            logger.info(
                "EITA verifyActiveClinicalUserByEmail: Staff Found",
                "staff_id" to staff.id,
                "type" to type
            )
            return ActiveClinicalUserResponse(
                id = staff.id,
                staff = staff,
                exists = true,
                type = type
            )
        }

        return ActiveClinicalUserResponse(exists = false)
    }

    override suspend fun signIn(email: String): Result<SignInAuthorizersResponse, Throwable> {
        val domain = email.split("@").last()

        val clinicalUser = verifyActiveClinicalUserByEmail(email).get()

        if (clinicalUser.exists) {
            val staffId = clinicalUser.staff?.id
            logger.info(
                "Sign in by Clinical User",
                "email" to email,
                "id" to clinicalUser.id,
                "type" to clinicalUser.type,
                "staff_id" to staffId,
            )

            val authorizers = getAuthorizersByEmployee(
                staffId = staffId,
                userType = clinicalUser.type!!
            ).getOrElse { emptyList() }

            logger.info(
                "Clinical User Authorizers",
                "allow" to authorizers.isNotEmpty(),
                "authorizers" to authorizers,
                "id" to clinicalUser.id,
                "type" to clinicalUser.type,
                "email" to email
            )

            return SignInAuthorizersResponse(
                allow = authorizers.isNotEmpty(),
                authorizers = authorizers,
                domain = domain,
                userType = clinicalUser.type!!,
                userId = clinicalUser.id,
                staff = clinicalUser.staff
            ).success()
        }

        if (domain in eitaStaffAccessDomains()) return getAvailableAuthorizersForStaff(email, false)

        return authorizerService.authorizersByDomain(domain)
            .map { authorizers ->
                logger.info("Got authorizers by domain", "authorizers" to authorizers, "email" to email)
                SignInAuthorizersResponse(
                    allow = authorizers.isNotEmpty(),
                    authorizers = authorizers,
                    domain = domain
                )
            }.thenError {
                logger.error("Authorizers get by domain failed", "email" to email, it)
            }

    }

    private suspend fun getAuthorizersByEmployee(
        staffId: UUID?,
        userType: EitaUserType
    ) = coroutineScope {
        coResultOf<List<Authorizer>, Throwable> {
            val staffAuthorizers = staffId?.let {
                authorizerService.authorizersByEmployee(userType, it)
            }?.getOrElse { emptyList() } ?: emptyList()

            staffAuthorizers.distinctBy { it.id }
        }
    }

    private fun isStaffAllowedToLoginOnEverything(staff: Staff) =
        FeatureService.anyInList(
            namespace = FeatureNamespace.EXEC_INDICATOR,
            key = "eita_access_to_units_switch",
            testValues = listOf(staff.role.name),
            defaultReturn = false
        ) || staff.role.name.let { roleName ->
            FeatureService.inList(
                namespace = FeatureNamespace.EXEC_INDICATOR,
                key = "eita_access_to_units_switch",
                testValue = roleName,
                defaultReturn = false
            )
        }

    private fun isStaffAllowedToLogin(staff: Staff) =
        staff.isEitaUser()

    private fun createPinCode(): String {
        return RandomIdUtils.randomId(alphabet = pinCodeChars, size = pinCodeSize)
    }

    private fun Role?.toEitaUserType() =
        when (this) {
            Role.COMMUNITY -> EitaUserType.HEALTH_SPECIALIST
            Role.HEALTH_COMMUNITY -> EitaUserType.HEALTH_SPECIALIST
            Role.HEALTH_ADMINISTRATIVE_ELIGIBILITY -> EitaUserType.HEALTH_ADMINISTRATIVE_ELIGIBILITY
            Role.ANESTHETIST -> EitaUserType.EXTERNAL_PAID_HEALTH_PROFESSIONAL
            else -> EitaUserType.HEALTH_ADMINISTRATIVE
        }

    private fun eitaStaffAccessDomains() = FeatureService.getList(
        namespace = FeatureNamespace.EXEC_INDICATOR,
        key = "eita_staff_access_domains",
        defaultValue = listOf("alice.com.br", "alice.timedesaude.com.br", "parceiro.alice.com.br")
    )

    private fun canSwitchToDuquesaProviderUnitsRoles() = listOf(
        Role.CHIEF_NAVIGATOR,
        Role.CHIEF_NAVIGATOR_OPS,
        Role.NAVIGATOR,
        Role.NAVIGATOR_OPS
    )

}
