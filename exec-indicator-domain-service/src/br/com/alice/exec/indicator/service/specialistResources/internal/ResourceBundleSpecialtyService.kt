package br.com.alice.exec.indicator.service.specialistResources.internal

import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.services.ResourceBundleSpecialtyModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID

class ResourceBundleSpecialtyService(
    private val dataService: ResourceBundleSpecialtyModelDataService,
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService
) {

    suspend fun updateResourceSpecialties(
        resourceId: UUID,
        specialtyIds: List<UUID>
    ): Result<List<ResourceBundleSpecialty>, Throwable> = coResultOf {
        val currentSpecialties = getActiveByResourceBundleId(resourceId).get()
        val currentSpecialtiesIds = currentSpecialties.map { it.medicalSpecialtyId }

        val medicalToInactivate = currentSpecialtiesIds.filter { it !in specialtyIds }
        val medicalToActivate = specialtyIds.filter { it !in currentSpecialtiesIds }


        medicalToInactivate.map { removeSpecialtyFromToResource(resourceId, it).get() }
        val medicalAdded = medicalToActivate.map { addSpecialtyToResource(resourceId, it).get() }

        val medicalSpecialtyToKeep = currentSpecialties.filter { it.medicalSpecialtyId in specialtyIds }
        medicalAdded + medicalSpecialtyToKeep
    }

    suspend fun changeResourcePrice(
        resourceBundleSpecialtyId: UUID,
        effectiveDate: LocalDate,
        prices: List<ResourceBundleSpecialtyPrice>
    ): Result<ResourceBundleSpecialty, Throwable> = coResultOf {
        val resourceBundleSpecialty = get(resourceBundleSpecialtyId).getOrNullIfNotFound()
            ?: throw NotFoundException("ResourceBundleSpecialty with id $resourceBundleSpecialtyId not found")

        if (resourceBundleSpecialty.status != Status.ACTIVE) {
            throw InvalidArgumentException("ResourceBundleSpecialty with id $resourceBundleSpecialtyId is not active")
        }

        resourceBundleSpecialtyPricingService.addNewPricing(
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            effectiveDate = effectiveDate,
            prices = prices
        ).flatMap {
            update(
                resourceBundleSpecialty.copy(
                    pricingStatus = PricingStatus.PRICED,
                )
            )
        }.get()
    }


    private suspend fun addSpecialtyToResource(resourceId: UUID, medicalSpecialtyId: UUID): Result<ResourceBundleSpecialty, Throwable> {
        val resourceBundleSpecialty = findByResourceAndSpecialty(resourceId, medicalSpecialtyId).getOrNullIfNotFound()
            ?: return add(ResourceBundleSpecialty(
                healthSpecialistResourceBundleId = resourceId,
                medicalSpecialtyId = medicalSpecialtyId,
                status = Status.ACTIVE,
                pricingStatus = PricingStatus.NOT_PRICED,
                appointmentRecommendationLevel = AppointmentRecommendationLevel.NONE
            ))

        if (resourceBundleSpecialty.isActive()) return resourceBundleSpecialty.success()

        return resourceBundleSpecialtyPricingService.onSpecialtyActivation(resourceBundleSpecialty.id)
            .map { currentPrice ->
                resourceBundleSpecialty.copy(
                    status = Status.ACTIVE,
                    pricingStatus = if (currentPrice != null) PricingStatus.PRICED else PricingStatus.NOT_PRICED,
                    appointmentRecommendationLevel = AppointmentRecommendationLevel.NONE
                )
            }
            .flatMap { update(it) }
    }

    private suspend fun removeSpecialtyFromToResource(resourceId: UUID, medicalSpecialtyId: UUID): Result<ResourceBundleSpecialty?, Throwable> {
        val resourceBundleSpecialty = findByResourceAndSpecialty(resourceId, medicalSpecialtyId).getOrNullIfNotFound() ?: return Result.success(null)


        return resourceBundleSpecialtyPricingService
            .onSpecialtyInactivation(resourceBundleSpecialty.id)
            .flatMap { update(resourceBundleSpecialty.inactivate()) }
    }

    private suspend fun update(resourceBundleSpecialty: ResourceBundleSpecialty) =
        dataService
            .update(resourceBundleSpecialty.toModel())
            .map { it.toTransport() }

    private suspend fun add(
        resourceBundleSpecialty: ResourceBundleSpecialty
    ): Result<ResourceBundleSpecialty, Throwable> =
        dataService.add(resourceBundleSpecialty.toModel()).map { it.toTransport() }

    private suspend fun findByResourceAndSpecialty(
        resourceId: UUID,
        specialtyId: UUID
    ): Result<ResourceBundleSpecialty, Throwable> =
        dataService.findOne {
            where {
                healthSpecialistResourceBundleId.eq(resourceId)
                    .and(medicalSpecialtyId.eq(specialtyId))
            }
        }.map { it.toTransport() }


    suspend fun get(id: UUID): Result<ResourceBundleSpecialty, Throwable> =
        dataService.get(id).map { it.toTransport() }

    suspend fun findByIds(
        ids: List<UUID>,
        filterActive: Boolean = true
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService
            .find {
                where {
                    val activePredicate = if (filterActive) status.eq(Status.ACTIVE) else null

                    id.inList(ids).and(activePredicate)
                }
            }.map { it.toTransport() }

    suspend fun findAllActive(): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService
            .find { where { status.eq(Status.ACTIVE) }.orderBy { createdAt }.sortOrder { desc } }
            .map { it.toTransport() }

    suspend fun getActiveByResourceBundleId(id: UUID): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService
            .find { where { healthSpecialistResourceBundleId.eq(id) and status.eq(Status.ACTIVE) } }
            .map { it.toTransport() }

    suspend fun getByResourceBundleId(id: UUID): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService
            .find { where { healthSpecialistResourceBundleId.eq(id) } }
            .map { it.toTransport() }

    suspend fun updateList(resourceBundleSpecialties: List<ResourceBundleSpecialty>) =
        dataService
            .updateList(resourceBundleSpecialties.map { it.toModel() })
            .map { it.toTransport() }


    suspend fun getByResourceBundleIds(
        ids: List<UUID>,
        filterActive: Boolean = true
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService.find {
            where {
                healthSpecialistResourceBundleId.inList(ids).and(
                    if (filterActive) {
                        status.eq(Status.ACTIVE)
                    } else {
                        null
                    }
                )
            }
        }.map {
            it.toTransport()
        }

    suspend fun getByPricingStatus(
        pricingStatus: PricingStatus,
        filterActive: Boolean = true
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService.find {
            where {
                this.pricingStatus.eq(pricingStatus).and(
                    if (filterActive) {
                        status.eq(Status.ACTIVE)
                    } else {
                        null
                    }
                )
            }
        }.map {
            it.toTransport()
        }

    suspend fun getByFilters(
        medicalSpecialtyIds: List<UUID>? = null,
        pricingStatus: PricingStatus? = null,
        resourceBundleIds: List<UUID>? = null,
        status: Status = Status.ACTIVE,
        recommendationLevel: List<AppointmentRecommendationLevel>? = null
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService.find {
            where {
                this.status.eq(status).and(
                    if (medicalSpecialtyIds != null) {
                        medicalSpecialtyId.inList(medicalSpecialtyIds)
                    } else {
                        null
                    }
                ).and(
                    if (pricingStatus != null) {
                        this.pricingStatus.eq(pricingStatus)
                    } else {
                        null
                    }
                ).and(
                    if (resourceBundleIds != null) {
                        healthSpecialistResourceBundleId.inList(resourceBundleIds)
                    } else {
                        null
                    }
                ).and(
                    if (recommendationLevel != null) {
                        appointmentRecommendationLevel.inList(recommendationLevel)
                    } else {
                        null
                    }
                )
            }
        }.map {
            it.toTransport()
        }

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun findBySpecialtyId(
        specialtyId: UUID,
        appointmentLevel: List<AppointmentRecommendationLevel> = emptyList()
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        dataService.find {
            where {
                medicalSpecialtyId.eq(specialtyId)
                    .withFilter(appointmentLevel) { this.appointmentRecommendationLevel.inList(appointmentLevel) }!!
            }
        }.map {
            it.toTransport()
        }
}

