package br.com.alice.exec.indicator.service

import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.exec.indicator.client.AuthorizerService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.exec.indicator.logics.Providers
import br.com.alice.exec.indicator.models.Authorizer
import br.com.alice.exec.indicator.models.EitaUserType
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class AuthorizerServiceImpl(
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService,
    private val providerUnitService: ProviderUnitService
) : AuthorizerService {

    override suspend fun authorizersByDomain(domainRequest: String): Result<List<Authorizer>, Throwable> =
        execIndicatorAuthorizerService.getByDomain(domainRequest)
            .flatMap { authorizers ->
                authorizersFromExecAuthorizer(authorizers)
            }

    private suspend fun authorizersFromExecAuthorizer(authorizers: List<ExecIndicatorAuthorizer>) =
        providerUnitService.getByIds(authorizers.map { it.providerUnitId })
            .map { providers ->
                Providers.authorizerFromProviders(providers, authorizers)
            }
            .then { convertedAuthorizers ->
                logger.info("authorizers", "authorizers" to convertedAuthorizers)
            }

    override suspend fun authorizersByEmployee(
        employeeRole: EitaUserType,
        employeeId: UUID
    ): Result<List<Authorizer>, Throwable> {
        val providerUnits = when (employeeRole) {
            EitaUserType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            EitaUserType.HEALTH_SPECIALIST -> providerUnitService.getByClinicalStaffId(employeeId)

            EitaUserType.HEALTH_ADMINISTRATIVE,
            EitaUserType.HEALTH_ADMINISTRATIVE_ELIGIBILITY -> providerUnitService.getByAdministrativeStaffId(employeeId)

            else -> return emptyList<Authorizer>().success()
        }
        return providerUnits.map { it.buildAuthorizers() }
    }

    private fun List<ProviderUnit>.buildAuthorizers() =
        this.map { providerUnit ->
            Authorizer(
                id = null,
                name = providerUnit.name,
                providerUnitId = providerUnit.id,
                unitType = providerUnit.type,
                cnpj = providerUnit.cnpj,
                address = providerUnit.address?.formattedAddress().orEmpty(),
                brand = providerUnit.brand,
            )
        }

    override suspend fun all(): Result<List<Authorizer>, Throwable> =
        execIndicatorAuthorizerService.getByRange(IntRange(0, execIndicatorAuthorizerService.countAll().get()))
            .flatMap { authorizers ->
                authorizersFromExecAuthorizer(authorizers.filter { it.domain.trim().isNotEmpty() })
            }.map { authorizers -> authorizers.distinctBy { it.providerUnitId } }
}
