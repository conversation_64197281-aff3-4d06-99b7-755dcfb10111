package br.com.alice.exec.indicator.service.specialistResources

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.exec.indicator.client.EffectiveDatesResponse
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.client.ResourceSpecialtyPricingUpdateService
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.exec.indicator.models.ProcessingResourceBundleSpecialtyPricingUpdateResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import br.com.alice.exec.indicator.service.specialistResources.internal.CSVPricingUpdateFileGenerator
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.specialistResources.internal.ResourceBundleSpecialtyPricingUpdateService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.util.UUID

class ResourceSpecialtyPricingUpdateServiceImpl(
    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService,
    private val fileVaultActionService: FileVaultActionService,
    private val staffService: StaffService,
    private val csvPricingUpdateFileGenerator: CSVPricingUpdateFileGenerator,
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService
) : ResourceSpecialtyPricingUpdateService {

    override suspend fun getEffectiveDates() =
        EffectiveDatesResponse(resourceBundleSpecialtyPricingService.getEffectiveDaysCheckingWorkDays()).success()

    override suspend fun generate(resourceBundleSpecialtyIds: List<UUID>) =
        csvPricingUpdateFileGenerator.generate(resourceBundleSpecialtyIds)

    override suspend fun generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId: UUID) =
        csvPricingUpdateFileGenerator.generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId)


    override suspend fun getProcessingResourceBundleSpecialtyPricingUpdate(): Result<ProcessingResourceBundleSpecialtyPricingUpdateResponse, Throwable> {
        return resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate().map {
            ProcessingResourceBundleSpecialtyPricingUpdateResponse(
                isProcessing = true,
                resourceBundleSpecialtyPricingUpdate = it
            )
        }.foldNotFound {
            ProcessingResourceBundleSpecialtyPricingUpdateResponse(
                isProcessing = false,
            ).success()
        }
    }

    override suspend fun getPricingUpdateHistory(
        filters: PricingUpdateHistoryFilters,
        range: IntRange
    ): Result<ResourceBundleSpecialtyPricingUpdateHistoryWithCount, Throwable> {
        logger.info(
            "ResourceSpecialtyPricingCSVServiceImpl - getPricingUpdateHistory",
            "filters" to filters,
            "range" to range
        )

        return coroutineScope {
            val itemsDef = async {
                resourceBundleSpecialtyPricingUpdateService
                    .getResourceBundleSpecialtyPricingUpdateHistory(filters, range)
            }

            val totalDef = async {
                resourceBundleSpecialtyPricingUpdateService
                    .countResourceBundleSpecialtyPricingUpdateHistory(filters)
            }

            coResultOf {
                ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
                    count = totalDef.await().get(),
                    items = itemsDef.await().toHistoryResponse().get()
                )
            }

        }
    }

    override suspend fun uploadPriceChanges(request: UploadPriceChangesRequest) =
        getProcessingResourceBundleSpecialtyPricingUpdate().flatMap {
            if (it.isProcessing) {
                logger.error(
                    "ResourceSpecialtyPricingCSVServiceImpl uploadPriceChanges",
                    "isProcessing" to true
                )

                return InvalidArgumentException("There is already a process running").failure()
            }
            csvPricingUpdateFileGenerator.uploadPriceChanges(request)
        }

    private suspend fun Result<List<ResourceBundleSpecialtyPricingUpdate>, Throwable>.toHistoryResponse() =
        this.flatMapPair { resources -> staffService.findByList(resources.map { it.createdByStaffId }) }
            .flatMap { (staffs, resources) ->
                val staffsMap = staffs.associateBy { it.id }
                fileVaultActionService.securedGenericLinks(resources.map { it.fileVaultId })
                    .map { files ->
                        val filesMap = files.associateBy { it.id }

                        resources.map { resource ->
                            ResourceBundleSpecialtyPricingUpdateHistoryItem(
                                id = resource.id,
                                fileName = resource.fileName,
                                fileUrl = filesMap[resource.fileVaultId]?.url,
                                createdByStaff = staffsMap[resource.createdByStaffId],
                                processingAt = resource.processingAt,
                                completedAt = resource.completedAt,
                                rowsCount = resource.rowsCount,
                                failedRowsCount = resource.failedRowsCount,
                                failedRowsErrors = resource.failedRowsErrors,
                                parsingError = resource.parsingError,
                                pricesBeginAt = resource.pricesBeginAt,
                                createdAt = resource.createdAt
                            )
                        }
                    }
            }
}
