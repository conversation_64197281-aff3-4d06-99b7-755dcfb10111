package br.com.alice.exec.indicator.consumers

import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.exec.indicator.events.ResourceBundleSpecialtyPricingUpdateCreatedEvent
import br.com.alice.exec.indicator.service.specialistResources.internal.CSVPricingUpdateProcessor

class ResourceBundleSpecialtyPricingUpdateCreatedConsumer(
    private val CSVPricingUpdateProcessor: CSVPricingUpdateProcessor
) : Consumer() {

    suspend fun processResourceBundleSpecialtyPricingUpdateCreated(
        event: ResourceBundleSpecialtyPricingUpdateCreatedEvent
    ) = withSubscribersEnvironment {
        val resourceBundleSpecialtyPricingUpdateId = event.payload.resourceBundleSpecialtyPricingUpdateId

        logger.info("ResourceBundleSpecialtyPricingUpdateCreatedConsumer::processResourceBundleSpecialtyPricingUpdateCreated",
            "resourceBundleSpecialtyPricingUpdateId" to resourceBundleSpecialtyPricingUpdateId,
            "status" to "started"
        )

        CSVPricingUpdateProcessor.process(resourceBundleSpecialtyPricingUpdateId).then {
            logger.info("ResourceBundleSpecialtyPricingUpdateCreatedConsumer::processResourceBundleSpecialtyPricingUpdateCreated",
                "resourceBundleSpecialtyPricingUpdateId" to resourceBundleSpecialtyPricingUpdateId,
                "status" to "finished"
            )
            it
        }.thenError {
            logger.error("ResourceBundleSpecialtyPricingUpdateCreatedConsumer::processResourceBundleSpecialtyPricingUpdateCreated",
                "resourceBundleSpecialtyPricingUpdateId" to resourceBundleSpecialtyPricingUpdateId,
                "status" to "failed"
            )
        }
    }
}
