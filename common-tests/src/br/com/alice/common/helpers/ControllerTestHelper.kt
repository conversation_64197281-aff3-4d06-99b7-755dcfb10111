package br.com.alice.common.helpers

import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.Authenticator.ROLES_KEY
import br.com.alice.authentication.Authenticator.USER_ID_KEY
import br.com.alice.authentication.Authenticator.USER_SUB_TYPE_KEY
import br.com.alice.authentication.Authenticator.USER_TYPE_KEY
import br.com.alice.authentication.UserType
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.common.core.User
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.models.Person
import br.com.alice.common.models.Staff
import br.com.alice.common.notification.LocalNotificationService
import br.com.alice.common.notification.NotificationService
import br.com.alice.common.service.serialization.isoDateGson
import br.com.alice.featureconfig.core.featureConfigBootstrap
import com.google.firebase.auth.FirebaseToken
import com.typesafe.config.ConfigFactory
import io.ktor.client.request.forms.FormPart
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.header
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentDisposition
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.headersOf
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.testing.testApplication
import io.mockk.every
import io.mockk.isMockKMock
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import java.io.File
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlinx.coroutines.runBlocking
import org.koin.core.module.Module
import org.koin.dsl.module
import org.slf4j.Logger

abstract class ControllerTestHelper {

    open val gson = isoDateGson
    protected val logger: Logger = mockk(relaxUnitFun = true)
    private val environmentToken = "environmentToken"

    protected open val module: Module = module {
        single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }
        single<NotificationService> { LocalNotificationService }
    }

    protected open val moduleFunction: Application.() -> Unit = {}

    var authenticationToken: String? = null

    @BeforeTest
    open fun setup() {
    }

    @AfterTest
    open fun after() {
    }

    open fun uid(user: User) = user.id.toString()

    open fun authenticatedAs(token: String, user: User, requestFunction: ControllerTestHelper.() -> Unit) {
        val firebaseToken: FirebaseToken = mockk()

        val userTypeClaim = when (user) {
            is Person -> mapOf(USER_SUB_TYPE_KEY to UserType.MEMBER.toString())
            is Staff -> mapOf(ROLES_KEY to listOf(user.role.name))
            else -> emptyMap()
        }

        every { firebaseToken.uid } returns uid(user)
        every { firebaseToken.email } returns user.email
        every { firebaseToken.claims } returns mapOf(
            USER_TYPE_KEY to user::class.simpleName,
            USER_ID_KEY to user.id.toString()
        ) + userTypeClaim

        every { firebaseToken.picture } returns "http://url.to.image/"

        mockkObject(Authenticator) {
            every { Authenticator.verifySystemAccessToken(token) } returns null
            every { Authenticator.verifyIdToken(token) } returns firebaseToken
            every { Authenticator.verifyIdToken(token, false) } returns firebaseToken
            every { Authenticator.generateCustomToken(any(), any<String>(), any()) } returns token
            every { Authenticator.generateCustomToken(any(), any<KClass<*>>(), any()) } returns token
            every { Authenticator.generateRootServiceToken(any()) } returns environmentToken

            authenticationToken = token
            requestFunction.invoke(this)
            authenticationToken = null
        }
    }

    fun get(url: String, assertFunction: suspend (HttpResponse) -> Unit) =
        handleRequest(HttpMethod.Get, url, null, emptyMap(), assertFunction)

    fun get(
        url: String,
        headers: Map<String, String> = mapOf(),
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Get, url, null, headers, assertFunction)

    fun post(
        to: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Post, to, body, headers, assertFunction)

    fun postAsPlainText(
        to: String,
        body: Any? = null,
        assertFunction: suspend (HttpResponse) -> Unit
    ) = mockkBootstraps {
        testApplication {
            application(moduleFunction)

            handlePlainTextRequest(HttpMethod.Post, to, body, assertFunction)
        }
    }

    fun put(
        to: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Put, to, body, headers, assertFunction)

    fun patch(
        to: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Patch, to, body, headers, assertFunction)

    fun delete(url: String, body: Any? = null, assertFunction: suspend (HttpResponse) -> Unit) =
        handleRequest(HttpMethod.Delete, url, body, emptyMap(), assertFunction)

    fun multipart(
        method: HttpMethod,
        to: String,
        fileName: String,
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleMultipart(method, to, listOf(fileName), emptyMap(), assertFunction)

    fun multipart(
        method: HttpMethod,
        to: String,
        fileName: String?,
        parameters: Map<String, String>,
        assertFunction: suspend (HttpResponse) -> Unit
    ) = handleMultipart(method, to, listOf(fileName), parameters, assertFunction)

    fun multipart(
        method: HttpMethod,
        to: String,
        fileNames: List<String?>,
        parameters: Map<String, String>,
        assertFunction: suspend (HttpResponse) -> Unit
    ) = handleMultipart(method, to, fileNames, parameters, assertFunction)

    private fun handleMultipart(
        method: HttpMethod,
        to: String,
        fileNames: List<String?>,
        parameters: Map<String, String>,
        assertFunction: suspend (HttpResponse) -> Unit
    ) = mockkBootstraps {
        testApplication {
            application(moduleFunction)

            client.request(to) {
                this.method = method

                val boundary = "***bbb***"

                authenticationToken?.let { header(HttpHeaders.Authorization, "Bearer $it") }
                header(
                    HttpHeaders.ContentType,
                    ContentType.MultiPart.FormData.withParameter("boundary", boundary).toString()
                )
                header(HttpHeaders.AcceptLanguage, "pt-BR")

                val items = parameters.map { (key, value) ->
                    FormPart(
                        key = key,
                        value = value,
                        headers = headersOf(
                            HttpHeaders.ContentDisposition,
                            ContentType.MultiPart.FormData
                                .withParameter(ContentDisposition.Parameters.Name, key)
                                .toString()
                        )
                    )
                }

                val finalItems = fileNames.mapNotNull { fileName ->
                    fileName?.let {
                        val file = File("testResources/$fileName")

                        FormPart(
                            key = fileName,
                            value = file.readBytes(),
                            headers = headersOf(
                                HttpHeaders.ContentDisposition,
                                ContentDisposition.File
                                    .withParameter(ContentDisposition.Parameters.Name, "file")
                                    .withParameter(ContentDisposition.Parameters.FileName, fileName)
                                    .toString()
                            )
                        )
                    }
                } + items

                setBody(
                    MultiPartFormDataContent(
                        formData { finalItems.forEach { append(it) } },
                        boundary,
                        ContentType.MultiPart.FormData.withParameter("boundary", boundary)
                    )
                )
            }.apply { assertFunction(this) }
        }
    }

    inline fun <reified T> getResponse(response: HttpResponse): T =
        runBlocking { gson.fromJson(response.bodyAsText()) }

    inline fun <reified T> fromJson(json: String): T = gson.fromJson(json)

    private fun handleRequest(
        method: HttpMethod,
        to: String,
        body: Any?,
        headers: Map<String, String>,
        assertFunction: suspend (HttpResponse) -> Unit
    ) = mockkBootstraps {
        testApplication {
            application(moduleFunction)

            createClient { followRedirects = false }.request(to) {

                this.method = method

                authenticationToken?.let { header(HttpHeaders.Authorization, "Bearer $it") }
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                header(HttpHeaders.AcceptLanguage, "pt-BR")
                headers.entries.forEach { header(it.key, it.value) }

                body?.let { if (it is String) setBody(it) else setBody(gson.toJson(it)) }
            }.apply { assertFunction(this) }
        }
    }

    private fun mockkBootstraps(testFunction: () -> Unit) {
        mockkStatic(
            "br.com.alice.featureconfig.core.FeatureConfigBootstrapKt",
            "br.com.alice.authentication.AuthenticationBootstrapKt",
        ) {
            every { any<Application>().featureConfigBootstrap(any(), any()) } returns Unit
            every { any<Application>().authenticationBootstrap() } returns Unit

            if (isMockKMock(Authenticator, objectMock = true)) testFunction()
            else {
                mockkObject(Authenticator) {
                    every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
                    every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
                    testFunction()
                }
            }
        }
    }
}
