package br.com.alice.common.helpers

import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.Authenticator.ROLES_KEY
import br.com.alice.authentication.Authenticator.USER_ID_KEY
import br.com.alice.authentication.Authenticator.USER_SUB_TYPE_KEY
import br.com.alice.authentication.Authenticator.USER_TYPE_KEY
import br.com.alice.authentication.UserType
import br.com.alice.common.core.User
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.models.Person
import br.com.alice.common.models.Staff
import br.com.alice.common.notification.LocalNotificationService
import br.com.alice.common.notification.NotificationService
import br.com.alice.common.service.serialization.isoDateGson
import com.google.firebase.auth.FirebaseToken
import com.typesafe.config.ConfigFactory
import io.ktor.http.ContentDisposition
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.content.PartData
import io.ktor.http.headersOf
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.testing.TestApplicationResponse
import io.ktor.server.testing.handleRequest
import io.ktor.server.testing.setBody
import io.ktor.server.testing.withTestApplication
import io.ktor.utils.io.streams.asInput
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import java.io.File
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlinx.coroutines.runBlocking
import org.koin.core.module.Module
import org.koin.dsl.module
import org.slf4j.Logger

abstract class RoutesTestHelper: MockedTestHelper() {

    open val gson = isoDateGson
    protected val logger: Logger = mockk(relaxUnitFun = true)
    private val environmentToken = "environmentToken"

    protected open val module: Module = module {
        single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }
        single<NotificationService> { LocalNotificationService }
    }

    protected open val setupFunction: Application.() -> Unit = {}
    protected open val moduleFunction: Application.() -> Unit = {}

    var authenticationToken: String? = null

    @BeforeTest
    override fun setup() {
        super.setup()
    }

    @AfterTest
    open fun after() {
    }

    open fun uid(user: User) = user.id.toString()

    open fun authenticatedAs(token: String, user: User, requestFunction: RoutesTestHelper.() -> Unit) {
        val firebaseToken: FirebaseToken = mockk()

        val userTypeClaim = when (user) {
            is Person -> mapOf(USER_SUB_TYPE_KEY to UserType.MEMBER.toString())
            is Staff -> mapOf(ROLES_KEY to listOf(user.role.name))
            else -> emptyMap()
        }

        every { firebaseToken.uid } returns uid(user)
        every { firebaseToken.email } returns user.email
        every { firebaseToken.claims } returns mapOf(
            USER_TYPE_KEY to user::class.simpleName,
            USER_ID_KEY to user.id.toString()
        ) + userTypeClaim

        every { firebaseToken.picture } returns "http://url.to.image/"

        mockkObject(Authenticator)

        every { Authenticator.verifySystemAccessToken(token) } returns null
        every { Authenticator.verifyIdToken(token) } returns firebaseToken
        every { Authenticator.verifyIdToken(token, false) } returns firebaseToken
        every { Authenticator.generateCustomToken(any(), any<String>(), any()) } returns token
        every { Authenticator.generateCustomToken(any(), any<KClass<*>>(), any()) } returns token
        every { Authenticator.generateRootServiceToken(any()) } returns environmentToken

        authenticationToken = token
        requestFunction.invoke(this)
        authenticationToken = null
    }

    fun internalAuthentication(requestFunction: RoutesTestHelper.() -> Unit) {
        mockkObject(Authenticator)
        every { Authenticator.verifyIdToken(any()) } returns mockk<FirebaseToken>()
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"

        requestFunction.invoke(this)
    }

    suspend fun coInternalAuthentication(requestFunction: suspend RoutesTestHelper.() -> Unit) {
        mockkObject(Authenticator)
        every { Authenticator.verifyIdToken(any()) } returns mockk<FirebaseToken>()
        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"

        requestFunction.invoke(this)
    }

    fun get(url: String, assertFunction: (TestApplicationResponse) -> Unit) =
        handleRequest(HttpMethod.Get, url, null, emptyMap(), assertFunction)

    fun get(
        url: String,
        headers: Map<String, String> = mapOf(),
        assertFunction: (TestApplicationResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Get, url, null, headers, assertFunction)

    fun post(
        to: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: (TestApplicationResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Post, to, body, headers, assertFunction)

    fun postAsPlainText(
        to: String,
        body: Any? = null,
        assertFunction: (TestApplicationResponse) -> Unit
    ) {
        withTestApplication(setupFunction) {
            moduleFunction(this.application)

            handlePlainTextRequest(HttpMethod.Post, to, body, assertFunction)
        }
    }

    fun put(
        to: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: (TestApplicationResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Put, to, body, headers, assertFunction)

    fun patch(
        to: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: (TestApplicationResponse) -> Unit
    ) =
        handleRequest(HttpMethod.Patch, to, body, headers, assertFunction)

    fun delete(url: String, body: Any? = null, assertFunction: (TestApplicationResponse) -> Unit) =
        handleRequest(HttpMethod.Delete, url, body, emptyMap(), assertFunction)

    fun multipart(
        method: HttpMethod,
        to: String,
        fileName: String,
        assertFunction: (TestApplicationResponse) -> Unit
    ) =
        handleMultipart(method, to, listOf(fileName), emptyMap(), assertFunction)

    fun multipart(
        method: HttpMethod,
        to: String,
        fileName: String?,
        parameters: Map<String, String>,
        assertFunction: (TestApplicationResponse) -> Unit
    ) = handleMultipart(method, to, listOf(fileName), parameters, assertFunction)

    fun multipart(
        method: HttpMethod,
        to: String,
        fileNames: List<String?>,
        parameters: Map<String, String>,
        assertFunction: (TestApplicationResponse) -> Unit
    ) = handleMultipart(method, to, fileNames, parameters, assertFunction)

    private fun handleMultipart(
        method: HttpMethod,
        to: String,
        fileNames: List<String?>,
        parameters: Map<String, String>,
        assertFunction: (TestApplicationResponse) -> Unit
    ) {
        withTestApplication(setupFunction) {
            moduleFunction(this.application)

            handleRequest(method, to) {
                val boundary = "***bbb***"

                authenticationToken?.let { addHeader(HttpHeaders.Authorization, "Bearer $it") }
                addHeader(
                    HttpHeaders.ContentType,
                    ContentType.MultiPart.FormData.withParameter("boundary", boundary).toString()
                )
                addHeader(HttpHeaders.AcceptLanguage, "pt-BR")

                val items = parameters.map { (key, value) ->
                    PartData.FormItem(
                        value, {}, headersOf(
                            HttpHeaders.ContentDisposition,
                            ContentType.MultiPart.FormData
                                .withParameter(ContentDisposition.Parameters.Name, key)
                                .toString()
                        )
                    )
                }

                val finalItems = mutableListOf<PartData>()
                if (fileNames.isNotEmpty()) {
                    fileNames.map { fileName ->
                        if (fileName == null) {
                            null
                        } else {
                            val file = File("testResources/$fileName")

                            val fileItem = PartData.FileItem(
                                { file.readBytes().inputStream().asInput() },
                                {},
                                headersOf(
                                    HttpHeaders.ContentDisposition,
                                    ContentDisposition.File
                                        .withParameter(ContentDisposition.Parameters.Name, "file")
                                        .withParameter(ContentDisposition.Parameters.FileName, fileName)
                                        .toString()
                                )
                            )
                            finalItems.add(fileItem)
                        }
                    }
                }
                finalItems.addAll(items)
                setBody(boundary, finalItems)
            }.apply { assertFunction(response) }
        }
    }

    inline fun <reified T> getResponse(response: TestApplicationResponse): T =
        runBlocking { gson.fromJson(response.content!!) }

    inline fun <reified T> fromJson(json: String): T = gson.fromJson(json)

    private fun handleRequest(
        method: HttpMethod,
        to: String,
        body: Any?,
        headers: Map<String, String>,
        assertFunction: (TestApplicationResponse) -> Unit
    ) {
        withTestApplication(setupFunction) {
            moduleFunction(this.application)

            val finalHeaders = if (headers.containsKey(HttpHeaders.UserAgent)) headers
            else headers.plus(HttpHeaders.UserAgent to "Ktor client")

            handleRequest(method = method, uri = to) {
                authenticationToken?.let { addHeader(HttpHeaders.Authorization, "Bearer $it") }
                addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                addHeader(HttpHeaders.AcceptLanguage, "pt-BR")
                finalHeaders.entries.forEach { addHeader(it.key, it.value) }
                body?.let { if (it is String) setBody(it) else setBody(gson.toJson(it)) }
            }.apply { assertFunction(response) }
        }
    }
}
