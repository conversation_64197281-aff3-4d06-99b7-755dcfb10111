package br.com.alice.common.data.dsl.matchers

import br.com.alice.common.core.exceptions.RfcException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.isSuccess
import org.assertj.core.api.AbstractAssert
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import kotlin.reflect.KClass

val localDateTimeComparator = Comparator<LocalDateTime> { d1, d2 ->
    d1.truncatedTo(ChronoUnit.MILLIS).compareTo(d2.truncatedTo(ChronoUnit.MILLIS))
}

val localTimeComparator = Comparator<LocalTime> { t1, t2 ->
    t1.truncatedTo(ChronoUnit.MILLIS).compareTo(t2.truncatedTo(ChronoUnit.MILLIS))
}

class ResultAssert<T : Any, E : Throwable>(actual: Result<T, E>) :
    AbstractAssert<ResultAssert<T, E>, Result<T, E>>(actual, ResultAssert::class.java) {

    init {
        Assertions.registerFormatterForType(LocalDateTime::class.java) { value ->
            value.truncatedTo(ChronoUnit.MILLIS).toString()
        }

        Assertions.registerFormatterForType(LocalTime::class.java) { value ->
            value.truncatedTo(ChronoUnit.MILLIS).toString()
        }
    }

    private val objectAssert = if (actual.isSuccess()) {
        Assertions.assertThat(actual.get())
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingComparatorForType(localTimeComparator, LocalTime::class.java)
    } else Assertions.assertThat(actual.component2())

    companion object {
        fun <T : Any, E : Throwable> assertThat(actual: Result<T, E>) = ResultAssert(actual)
    }

    fun <V : Any> withComparatorForType(type: Class<V>, comparator: Comparator<V>): ResultAssert<T, E> {
        objectAssert.usingComparatorForType(comparator, type)
        return this
    }

    fun isSuccess(): ResultAssert<T, E> = this.isInstanceOf(Result.Success::class.java)

    fun isSuccessOfType(kClass: KClass<out T>) {
        isSuccess()
        val value = this.actual.get()

        if (value::class != kClass) {
            val message = "Expected type ${kClass.qualifiedName} but was ${value::class.qualifiedName}"
            failWithMessage(message)
        }
    }

    fun isSuccessWithData(data: T) {
        isSuccess()

        objectAssert.let { assert ->
            // we need to validate the data type to use the recursive comparison because in Java 17 primitive types cannot be accessed by reflection
            if (data::class.isData || data is Iterable<*> || data is Map<*, *>) assert.usingRecursiveComparison()
            else assert
        }.isEqualTo(data)
    }

    fun isSuccessWithDataIgnoringGivenFields(data: T, vararg propertiesOrFieldsToIgnore: String) {
        isSuccess()

        objectAssert
            .usingRecursiveComparison()
            .ignoringFields(*propertiesOrFieldsToIgnore)
            .isEqualTo(data)
    }

    fun isSuccessWithDataFields(data: T, vararg propertiesOrFields: String) {
        isSuccess()

        objectAssert
            .usingRecursiveComparison()
            .comparingOnlyFields(*propertiesOrFields)
            .isEqualTo(data)
    }

    @Deprecated("Use isSuccessWithDataFields instead")
    fun isSuccessWithListDataComparingOnlyGivenFields(data: T, vararg propertiesOrFields: String) {
        isSuccess()

        Assertions.assertThat(actual.get() as List<*>)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingComparatorForType(localTimeComparator, LocalTime::class.java)
            .usingRecursiveComparison().comparingOnlyFields(*propertiesOrFields)
            .isEqualTo(data)
    }

    @Deprecated("Use isSuccessWithDataIgnoringGivenFields instead")
    fun isSuccessWithListDataIgnoringGivenFields(data: T, vararg propertiesOrFieldsToIgnore: String) {
        isSuccess()

        Assertions.assertThat(actual.get() as List<*>)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields(*propertiesOrFieldsToIgnore)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingComparatorForType(localTimeComparator, LocalTime::class.java)
            .isEqualTo(data)
    }

    fun isFailure(): ResultAssert<T, E> = this.isInstanceOf(Result.Failure::class.java)

    fun isFailureOfType(kClass: KClass<out E>) {
        isFailure()

        val exceptionThrown = this.actual.component2()

        if (exceptionThrown == null) {
            failWithMessage("Exception from result is empty")
            return
        }

        if (exceptionThrown::class != kClass) {
            val message =
                "Expected exception of type ${kClass.qualifiedName} but was ${exceptionThrown::class.qualifiedName}"
            failWithMessage(message)
        }
    }

    fun fails() = Fails()

    inner class Fails {

        fun withCode(code: String): Fails {
            val exceptionThrown = actual.component2()

            if (exceptionThrown != null && exceptionThrown is RfcException && exceptionThrown.code != code) {
                failWithMessage("Expected error code to be $code but was ${exceptionThrown.code}")
            }

            return this
        }

        fun withMessage(message: String): Fails {
            val exceptionThrown = actual.component2()

            if (exceptionThrown != null && exceptionThrown.message != message) {
                failWithMessage("Expected error message to be $message but was ${exceptionThrown.message}")
            }

            return this
        }

        fun ofType(kClass: KClass<out E>) {
            isFailureOfType(kClass)
        }

        fun withCause(cause: KClass<out Throwable>): Fails {
            val exceptionThrown = actual.component2()

            if (exceptionThrown != null && exceptionThrown.cause != null && exceptionThrown.cause!!::class != cause) {
                failWithMessage("Expected cause to be $cause but was ${exceptionThrown.cause}")
            }

            return this
        }
    }
}
