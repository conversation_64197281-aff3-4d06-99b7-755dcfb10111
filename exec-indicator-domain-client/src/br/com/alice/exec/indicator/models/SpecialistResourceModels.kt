package br.com.alice.exec.indicator.models

import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class PaginatedList<T>(
    val items: List<T>,
    val totalItems: Int = 0,
    val range: IntRange? = null
)

data class SpecialistResourceFilters(
    val query: String? = null,
    val status: Status? = null,
    val pricingStatus: PricingStatus? = null,
    val medicalSpecialtyIds: List<UUID>? = null,
    val serviceType: List<HealthSpecialistResourceBundleServiceType>? = null,
    val recommendationLevel: List<AppointmentRecommendationLevel>? = null
)

data class SpecialistResource(
    val id: UUID,
    val code: String,
    val primaryTuss: SpecialistResourceTuss,
    var secondaryResources: List<SpecialistResourceTuss>,
    val executionAmount: Int,
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment,
    val description: String,
    val serviceType: HealthSpecialistResourceBundleServiceType,
    val medicalSpecialties: List<SpecialistResourceSpecialty>,
    val status: Status,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

data class SpecialistResourceTuss(
    val code: String,
    val description: String,
)

data class SpecialistResourceSpecialty(
    val medicalSpecialtyId: UUID,
    val description: String,
    val pricingStatus: PricingStatus,
    val recommendationLevel: AppointmentRecommendationLevel
)

data class SpecialistResourcePricingListing(
    val resourceId: UUID,
    val medicalSpecialtyId: UUID,
    val pricingPeriods: List<SpecialistResourcePricingPeriod>
)

data class SpecialistResourcePricingPeriod(
    val beginAt: LocalDate,
    val endAt: LocalDate?,
    val prices: List<ResourceBundleSpecialtyPrice>
)

data class SpecialistResourceQuantities(
    val serviceType: Map<HealthSpecialistResourceBundleServiceType, Int>,
    val pricingStatus: Map<PricingStatus, Int>,
    val recommendationLevel: Map<AppointmentRecommendationLevel, Int>
)
