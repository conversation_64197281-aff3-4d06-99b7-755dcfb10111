package br.com.alice.exec.indicator.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.exec.indicator.models.AppointmentRecommendationBondRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePaginatedResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistRequestFilters
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleWithCountResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtiesWithCount
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregateCount
import br.com.alice.exec.indicator.models.ResourceSuggestedProcedure
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthSpecialistResourceBundleManagementService : Service {
    override val namespace get() = "eita"
    override val serviceName get() = "health_specialist_resource_bundle_management"

    suspend fun update(
        id: UUID,
        request: HealthSpecialistResourceBundlePatchRequest
    ): Result<HealthSpecialistResourceBundlePatchResponse, Throwable>

    suspend fun list(
        query: String? = null,
        range: IntRange? = null
    ): Result<HealthSpecialistResourceBundlePaginatedResponse, Throwable>

    suspend fun getMedicalSpecialtiesRelatedToResourceBundle(
        id: UUID,
        range: IntRange,
    ): Result<ResourceBundleSpecialtiesWithCount, Throwable>

    suspend fun getPendingResourceSpecialtyBundlesForPricing(): Result<List<ResourceBundleSpecialty>, Throwable>

    suspend fun getResourceBundleSpecialtyPricingList(
        filters: PricingForHealthSpecialistRequestFilters,
        range: IntRange,
    ): Result<PricingForHealthSpecialistResourceBundleWithCountResponse, Throwable>

    suspend fun getBySpecialty(
        specialtyId: UUID,
        query: String? = null,
        range: IntRange? = null,
    ): Result<ResourceBundleSpecialtyAggregateCount, Throwable>

    suspend fun getSuggestedBySpecialty(specialtyId: UUID): Result<ResourceSuggestedProcedure, Throwable>

    suspend fun changeAppointmentLevelBond(
        specialtyId: UUID,
        request: AppointmentRecommendationBondRequest,
    ): Result<List<ResourceBundleSpecialty>, Throwable>
}
