package br.com.alice.exec.indicator.client.specialistResources

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.exec.indicator.models.PaginatedList
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import br.com.alice.exec.indicator.models.SpecialistResourcePricingListing
import br.com.alice.exec.indicator.models.SpecialistResourceQuantities
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface SpecialistResourceService : Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "specialist_resource_service"

    suspend fun get(id: UUID): Result<SpecialistResource, Throwable>

    suspend fun listQuantities(specialtyId: UUID): Result<SpecialistResourceQuantities, Throwable>

    suspend fun list(
        filter: SpecialistResourceFilters? = null,
        range: IntRange = 0..10
    ): Result<PaginatedList<SpecialistResource>, Throwable>


    suspend fun getPricing(
        medicalSpecialtiesIds: List<UUID>,
        resourceIds: List<UUID>,
        onlyCurrent: Boolean = true
    ): Result<List<SpecialistResourcePricingListing>, Throwable>
}
