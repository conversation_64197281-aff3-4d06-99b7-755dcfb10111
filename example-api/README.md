# Example API
Test application used to test build, deploy, etc.

### Responsible Team
Platform
- Find us on ``#eng-platform`` on Slack ;)
- [Platform Notion](https://www.notion.so/alicehealth/Platform-Team-b789312d8f064ce0a599010450ea22d8)

### Local development

Requirements
* [docker](https://www.docker.com)
* [docker-compose](https://docs.docker.com/compose/)

Operations using ``make <operation> <parameters>``

* ``clean`` - delete build files
* ``tests`` - run all tests
* ``run`` - run project on 8090 port

### Run locally
``make run`` and then you can access http://localhost:8090

## JenkinsCI Workflow Validation: v1.9
<!-- ignore changes -->
