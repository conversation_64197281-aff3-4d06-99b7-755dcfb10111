package br.com.alice.businessrisk.routes

import br.com.alice.businessrisk.controllers.MacoBackfillController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.backFillRoutes() {
    route("/backfill") {
        val macoBackfillController by inject<MacoBackfillController>()
        post("/fix_contract_maco_risk") { coHandler(macoBackfillController::fixContractsMacoRisk) }
        post("/add_beneficiary_maco_historical") { coHandler(macoBackfillController::addBeneficiaryMacoHistorical) }
        post("/add_company_contract_historical") { coHandler(macoBackfillController::addCompanyContractMacoHistorical) }
        delete("/delete_beneficiary_maco_historical/{companyContractMacoId}") {
            coHandler(
                "companyContractMacoId",
                macoBackfillController::deleteCompanyContractMacoHistoricalDataRequest
            )
        }
    }
}

