package br.com.alice.businessrisk.controllers

import br.com.alice.businessrisk.clients.BeneficiaryMacoService
import br.com.alice.businessrisk.clients.CompanyContractMacoService
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.BeneficiaryMaco
import br.com.alice.data.layer.models.CompanyContractMaco
import br.com.alice.data.layer.models.PersonContractualRiskLevel
import br.com.alice.data.layer.models.RiskAssessmentReasonType
import br.com.alice.data.layer.models.RiskAssessmentStatus
import br.com.alice.data.layer.models.StandardCostDetails
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class MacoBackfillController(
    private val companyContractMacoService: CompanyContractMacoService,
    private val beneficiaryMacoService: BeneficiaryMacoService,
    private val personService: PersonService,
): BusinessRiskBaseController() {
    suspend fun fixContractsMacoRisk(request: ContractsMacoFixRequest) = withBackfillEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        request.fixes
            .pmap { contractMacoFix ->
                companyContractMacoService.findLastByDateAndContractId(contractMacoFix.fixDateTime, contractMacoFix.contractId.toUUID()).then { companyContractMaco ->
                    val updatedCompanyContractMaco = companyContractMaco.copy(
                        riskAssessmentStatus = contractMacoFix.riskAssessmentStatus,
                        riskAssessmentReason = "Backfill de ajuste operacional",
                        riskAssessmentReasonType = RiskAssessmentReasonType.OTHERS,
                    )
                    companyContractMacoService.update(updatedCompanyContractMaco).then { successCount.getAndIncrement() }
                        .thenError {
                            errors[companyContractMaco.id.toString()] = it.message ?: "ERROR"
                            errorsCount.getAndIncrement()
                        }
                }
            }

        Response(
            HttpStatusCode.OK,
            MacoBackFillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = mapOf("errors" to errors)
            )
        )
    }

    suspend fun addBeneficiaryMacoHistorical(request: BeneficiaryMacoHistoricalDataRequest) = withBackfillEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        request.beneficiaries
            .pmap { beneficiary ->
                personService.findByNationalId(beneficiary.nationalId).map {
                    BeneficiaryMaco(
                        personId = it.id,
                        beneficiaryId = beneficiary.beneficiaryId,
                        productId = beneficiary.productId,
                        contractId = beneficiary.contractId,
                        riskWeight = beneficiary.riskWeight,
                        riskLevel = beneficiary.riskLevel,
                        grossRevenue = beneficiary.grossRevenue,
                        standardCost = beneficiary.standardCost,
                        personalizedCost = beneficiary.personalizedCost,
                        standardCostDetails = StandardCostDetails(
                            standardCostId = null,
                            age = null,
                            sex = null,
                            standardCost = beneficiary.standardCost,
                            companySize = null,
                            companyBusinessUnit = null,
                            adhesion = null,
                            productAnsNumber = null
                        )
                    )}.flatMap { beneficiaryMaco ->
                    beneficiaryMacoService.add(beneficiaryMaco).then { successCount.getAndIncrement() }
                        .thenError {
                            errors[beneficiary.beneficiaryId.toString()] = it.message ?: "ERROR"
                            errorsCount.getAndIncrement()
                        }
                }.thenError {
                    errors[beneficiary.nationalId] = it.message ?: "ERROR"
                    errorsCount.getAndIncrement()
                }
            }
        Response(
            HttpStatusCode.OK,
            MacoBackFillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = mapOf("errors" to errors)
            )
        )
    }

    suspend fun addCompanyContractMacoHistorical(request: ContractCompanyMacoHistoricalDataRequest) = withBackfillEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        request.contracts.pmap {contract ->
            beneficiaryMacoService.findByContractId(contract.contractId).flatMap { beneficiaries ->
                val backfillBeneficiaries = beneficiaries.filter {
                    it.standardCostDetails.standardCostId == null
                }.distinctBy {
                    it.personId
                }

                val companyContractMaco = CompanyContractMaco(
                    contractId = contract.contractId,
                    beneficiaryMacoIds = backfillBeneficiaries.map { it.id },
                    targetMacoLimit = contract.targetMacoLimit,
                    beneficiaryCount = backfillBeneficiaries.size,
                    totalGrossRevenue = backfillBeneficiaries.sumOf { it.grossRevenue },
                    totalCostRisk = backfillBeneficiaries.sumOf { it.totalCostRisk },
                    totalCostWeight = backfillBeneficiaries.sumOf { it.weightedStandardCost },
                    totalRiskIncome = backfillBeneficiaries.sumOf { it.riskIncome },
                    totalStandardCost = backfillBeneficiaries.sumOf { it.standardCost },
                    totalStandardIncome = backfillBeneficiaries.sumOf { it.standardIncome },
                    totalWeightedIncome = backfillBeneficiaries.sumOf { it.weightedIncome },
                )

                companyContractMacoService.add(companyContractMaco).then { successCount.getAndIncrement() }
                    .thenError {
                        errors[contract.toString()] = it.message ?: "ERROR"
                        errorsCount.getAndIncrement()
                    }
            }
        }

        Response(
            HttpStatusCode.OK,
            MacoBackFillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = mapOf("errors" to errors)
            )
        )
    }

    suspend fun deleteCompanyContractMacoHistoricalDataRequest(companyContractMacoId: UUID) = withBackfillEnvironment {
        companyContractMacoService.get(companyContractMacoId)
            .flatMap { companyContractMacoService.delete(it) }
            .foldResponse()
    }
}

data class ContractsMacoFixRequest(val fixes: List<ContractMacoFix>)

data class ContractMacoFix(
    val contractId: String,
    val fixDateTime: LocalDateTime,
    val riskAssessmentStatus: RiskAssessmentStatus
)

data class MacoBackFillResponse(
    val successCount: Int = 0,
    val errorsCount: Int = 0,
    val additionalInfo: Map<String, Any?>? = null
)

data class BeneficiaryMacoHistoricalDataRequest(val beneficiaries: List<BeneficiaryMacoHistoricalData>)

data class BeneficiaryMacoHistoricalData(
    val id: UUID = RangeUUID.generate(),
    val nationalId: String,
    val beneficiaryId: UUID,
    val contractId: UUID,
    val productId: UUID,
    val riskLevel: PersonContractualRiskLevel,
    val riskWeight: BigDecimal,
    val grossRevenue: BigDecimal,
    val standardCost: BigDecimal,
    val personalizedCost: BigDecimal,
)

data class ContractCompanyMacoHistoricalDataRequest(val contracts: List<ContractCompanyMacoDataHistoricalData>)

data class ContractCompanyMacoDataHistoricalData(
    val contractId: UUID,
    val targetMacoLimit: BigDecimal
)
