package br.com.alice.sales_channel.br.com.alice.sales_channel.controller

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.SalesFirm
import br.com.alice.sales_channel.controller.BackfillDealController
import br.com.alice.sales_channel.controller.BackfillDealRequest
import br.com.alice.sales_channel.model.DealRequest
import br.com.alice.sales_channel.model.DealStatusRequest
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import br.com.alice.sales_channel.service.SalesFirmAgentPartnershipService
import br.com.alice.sales_channel.service.SalesFirmService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackfillDealControllerTest: ControllerTestHelper() {
    private val dealService: OngoingCompanyDealService = mockk()
    private val salesFirmService: SalesFirmService = mockk()
    private val salesFirmAgentPartnershipService: SalesFirmAgentPartnershipService = mockk()

    private val backfillDealController =  BackfillDealController(
        dealService,
        salesFirmService,
        salesFirmAgentPartnershipService,
    )

    private val partnership = TestModelFactory.buildSalesFirmAgentPartnership()

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(salesFirmService)
        module.single { backfillDealController }
    }

    @Test
    fun `#createDeal should create deal`() = runBlocking {
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()

        coEvery {
            salesFirmAgentPartnershipService.getBySalesFirmAndAgent(partnership.salesFirmId, partnership.salesAgentId)
        } returns partnership.success()
        coEvery { salesFirmService.getSalesFirmByName(any()) } returns getSalesFirm().success()
        coEvery { dealService.add(any()) } returns ongoingCompanyDeal.success()

        post(to = "/backfill/deal", body = getDealRequest()) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { dealService.add(any()) }
        coVerifyOnce { salesFirmService.getSalesFirmByName(any()) }
    }

    @Test
    fun `#createDeal should create deal and create the partnership`() = runBlocking {
        val salesFirmId = RangeUUID.generate()
        val salesFirm = getSalesFirm().copy(
            id = salesFirmId,
        )
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()
        val request = getDealRequest().copy(
            salesAgentId = partnership.salesAgentId,
            sellerName = getSalesFirm().name,
        )

        coEvery {
            salesFirmAgentPartnershipService.getBySalesFirmAndAgent(salesFirm.id, request.salesAgentId!!)
        } returns NotFoundException().failure()
        coEvery {
            salesFirmAgentPartnershipService.create(match {
                it.salesAgentId == partnership.salesAgentId &&
                it.salesFirmId == salesFirm.id
            })
        } returns partnership.success()
        coEvery { salesFirmService.getSalesFirmByName(any()) } returns salesFirm.success()
        coEvery { dealService.add(any()) } returns ongoingCompanyDeal.success()

        post(to = "/backfill/deal", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { dealService.add(any()) }
        coVerifyOnce { salesFirmService.getSalesFirmByName(any()) }
        coVerifyOnce { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
        coVerifyOnce { salesFirmAgentPartnershipService.create(any()) }
    }

    @Test
    fun `#createDeal should not create deal when sales firm not exists`() = runBlocking {
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()

        coEvery {
            salesFirmAgentPartnershipService.getBySalesFirmAndAgent(partnership.salesFirmId, partnership.salesAgentId)
        } returns partnership.success()
        coEvery { salesFirmService.getSalesFirmByName(any()) } returns NotFoundException().failure()
        coEvery { dealService.add(any()) } returns ongoingCompanyDeal.success()

        post(to = "/backfill/deal", body = getDealRequest()) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyNone { dealService.add(any()) }
        coVerifyOnce { salesFirmService.getSalesFirmByName(any()) }
        coVerifyNone { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
        coVerifyNone { salesFirmAgentPartnershipService.create(any()) }
    }

    @Test
    fun `#moveDeal should move deal to another status`() = runBlocking {
        val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal()

        coEvery { dealService.getBySourceId(any()) } returns ongoingCompanyDeal.success()
        coEvery { dealService.update(any()) } returns ongoingCompanyDeal.success()

        post(to = "/backfill/deals_status", body = getDealStatusRequest()) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { dealService.update(any()) }
    }

    @Test
    fun `should update deal successfully`() = runBlocking {
        val request = BackfillDealRequest(
            id = UUID.randomUUID().toString(),
            companyId = UUID.randomUUID(),
            companyLegalName = "Test Legal Name",
            companyDocument = "salesFirmId",
            salesFirmId = UUID.randomUUID(),
            status = DealStage.RISK_FLOW.name,
            salesAgentId = UUID.randomUUID(),
            channel = DealChannel.BROKER,
            salesAgentDocument = "11111111111"
        )

        val existingDeal = TestModelFactory.buildOngoingCompanyDeal(channel = DealChannel.INTERNAL)

        coEvery { dealService.get(any()) } returns existingDeal.success()
        coEvery { dealService.update(any()) } returns existingDeal.success()
        coEvery {
            salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any())
        } returns partnership.success()
        coEvery { salesFirmService.getSalesFirmByName(any()) } returns getSalesFirm().success()


        post(to = "/backfill/update_deal", body = request) { response ->
            ResponseAssert.assertThat(response).isOK()
        }

        coVerifyOnce { dealService.get(any()) }
        coVerifyOnce { dealService.update(any()) }
    }

    @Test
    fun `should return error when partnership not found`() = runBlocking {
        val request = BackfillDealRequest(
            id = UUID.randomUUID().toString(),
            companyId = UUID.randomUUID(),
            companyLegalName = "Test Legal Name",
            companyDocument = "salesFirmId",
            salesFirmId = UUID.randomUUID(),
            status = DealStage.RISK_FLOW.name,
            salesAgentId = UUID.randomUUID(),
            channel = DealChannel.BROKER,
            salesAgentDocument = "11111111111"
        )

        val existingDeal = TestModelFactory.buildOngoingCompanyDeal()

        coEvery { dealService.get(any()) } returns existingDeal.success()
        coEvery { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) } returns NotFoundException().failure()

        post(to = "/backfill/update_deal", body = request) { response ->
            ResponseAssert.assertThat(response).isInternalServerError()
        }

        coVerifyOnce { dealService.get(any()) }
        coVerifyNone { dealService.updateById(any(), any()) }
        coVerifyOnce { salesFirmAgentPartnershipService.getBySalesFirmAndAgent(any(), any()) }
    }

    private fun getDealRequest(): DealRequest =
        DealRequest(
            companyName = "Rogerio Ceni",
            sellerName = "SPFC",
            companyDocument = "11111111111111",
            status = DealStage.RISK_FLOW.name,
            companyLegalName = "Rogerio Ceni Trikas",
            employeeCount = 10,
            livesCount = 5,
            contractModel = "contrato abc",
            createdAt = "1693916444658",
            updatedAt = "1693916444658",
            id = "deal0001",
        )

    private fun getDealStatusRequest(): DealStatusRequest =
        DealStatusRequest(
            status = DealStage.PROCESS_FINISHED.name,
            id = "deal0001",
            shouldValidateStatus = true,
        )

    private fun getSalesFirm(): SalesFirm =
        SalesFirm(
            name = "selecao_2002_broker",
            legalName = "selecao de ataque da copa de 2002",
            cnpj = "68282398000130",
            email = "<EMAIL>",
            phoneNumber = "11988344567"
        )
}
