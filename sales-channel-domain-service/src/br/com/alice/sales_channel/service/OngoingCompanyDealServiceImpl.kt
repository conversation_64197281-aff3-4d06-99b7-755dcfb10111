package br.com.alice.sales_channel.service

import br.com.alice.business.client.CompanyService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.normalizeCnpjWithMask
import br.com.alice.common.core.extensions.normalizeCnpjWithoutMask
import br.com.alice.common.core.extensions.toCPFMask
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.CancelReason
import br.com.alice.data.layer.models.CompulsoryMembership
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.DealStage
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.models.OngoingCompanyDealDetails
import br.com.alice.data.layer.models.OngoingDealStatusHistory
import br.com.alice.data.layer.services.OngoingCompanyDealDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.sales_channel.dto.PaginatedList
import br.com.alice.sales_channel.events.DealContractRequestedEvent
import br.com.alice.sales_channel.events.OngoingCompanyDealStatusChangedEvent
import br.com.alice.sales_channel.metric.Metrics.ongoingCompanyDealCreated
import br.com.alice.sales_channel.model.dto.OngoingDealsCountByStatus
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

class OngoingCompanyDealServiceImpl(
    private val ongoingCompanyDealDataService: OngoingCompanyDealDataService,
    private val kafkaProducerService: KafkaProducerService,
    private val cachedOngoingDealService: CachedOngoingDealService,
    private val companyService: CompanyService,
) : OngoingCompanyDealService {
    companion object {
        private val OPTIONAL_COMPANY_ID_STATUS_LIST = listOf(DealStage.DEAL_CREATED, DealStage.LEAD, DealStage.SALES_CONTACT, DealStage.QUALIFICATION_SALES_EXEC, DealStage.LOST_LEAD)
    }

    override suspend fun get(id: UUID) =
        ongoingCompanyDealDataService.findOne { where { this.id.eq(id) and deletedAt.isNull() } }

    override suspend fun getByIds(ids: List<UUID>): Result<List<OngoingCompanyDeal>, Throwable> = useReadDatabase {
        ongoingCompanyDealDataService.find {
            where {
                this.id.inList(ids) and
                this.deletedAt.isNull()
            }
        }
    }

    override suspend fun getByRange(offset: Int, limit: Int): Result<List<OngoingCompanyDeal>, Throwable> =
        ongoingCompanyDealDataService.find {
            where {
                this.deletedAt.isNull()
            }
            offset {
                offset
            }.limit {
                limit
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }

    override suspend fun getBySalesFirm(
        salesFirmId: UUID,
        offset: Int,
        limit: Int,
    ): Result<List<OngoingCompanyDeal>, Throwable> = useReadDatabase {
        ongoingCompanyDealDataService.find {
            where {
                this.salesFirmId.eq(salesFirmId) and
                this.deletedAt.isNull()
            }.offset {
                offset
            }.limit {
                limit
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }
    }

    override suspend fun sendContract(dealId: UUID) =
        get(dealId)
            .flatMap {
                if (!listOf(DealStage.CONTRACT_PREPARATION, DealStage.RISK_FLOW).contains(it.status)) {
                    logger.error("Invalid deal stage for contract generation", "deal_id" to dealId, "status" to it.status)
                    InvalidDealStageChangeException(it.status, DealStage.CONTRACT_PREPARATION).failure()
                }
                else
                    it.success()
            }
            .then { if (!shouldUpdateDealForContractGeneration(it.cnpj)) return it.success() }
            .then { if (!isGracePeriodPortabilityApproved(it)) return it.success() }
            .flatMap {
                if (it.status == DealStage.CONTRACT_PREPARATION) it.success()
                else upsertOngoingCompanyDeal(it.copy(status = DealStage.CONTRACT_PREPARATION))
            }
            .then { kafkaProducerService.produce(DealContractRequestedEvent(it)) }

    override suspend fun upsertOngoingCompanyDeal(
        ongoingCompanyDeal: OngoingCompanyDeal,
        shouldValidateStatus: Boolean,
    ): Result<OngoingCompanyDeal, Throwable> {
        val oldDeal = ongoingCompanyDealDataService.findOne {
            where { this.sourceId.eq(ongoingCompanyDeal.sourceId) and this.deletedAt.isNull() }
        }.getOrNull()

        logger.info(
            "Upserting ongoing company deal",
            "source_id" to ongoingCompanyDeal.sourceId,
            "company_id" to ongoingCompanyDeal.companyId,
            "status" to ongoingCompanyDeal.status,
            "old_deal" to oldDeal,
        )

        return if (oldDeal == null) {
            createOngoingCompanyDeal(ongoingCompanyDeal)
        } else {
            if (oldDeal.status != ongoingCompanyDeal.status) {
                if (shouldUpdateStatus(oldDeal.status, ongoingCompanyDeal.status, shouldValidateStatus)) {
                    logger.info(
                        "Updating ongoing company deal status",
                        "id" to oldDeal.id,
                        "status" to ongoingCompanyDeal.status,
                        "source_id" to ongoingCompanyDeal.sourceId,
                        "company_id" to ongoingCompanyDeal.companyId,
                    )
                    updateAndProduceStatusChangedIfNecessary(
                        oldDealVersion = oldDeal,
                        newDealVersion = ongoingCompanyDeal,
                        statusChanged = true,
                    )
                } else {
                    return oldDeal.success()
                }
            } else {
                updateAndProduceStatusChangedIfNecessary(
                    oldDealVersion = oldDeal,
                    newDealVersion = ongoingCompanyDeal,
                    statusChanged = false,
                )
            }
        }.coFoldNotFound {
            val result = ongoingCompanyDealDataService.add(ongoingCompanyDeal)
            logger.info(
                "Creating new ongoingCompanyDeal",
                "company_name" to ongoingCompanyDeal.name,
                "company_legal_name" to ongoingCompanyDeal.legalName
            )
            ongoingCompanyDealCreated(result.get())
            result
        }
    }

    private fun shouldUpdateStatus(
        oldStatus: DealStage,
        newStatus: DealStage,
        shouldValidateStatus: Boolean = true,
    ) = !shouldValidateStatus || (oldStatus != newStatus && newStatus.isAfter(oldStatus))

    private suspend fun updateAndProduceStatusChangedIfNecessary(
        oldDealVersion: OngoingCompanyDeal,
        newDealVersion: OngoingCompanyDeal,
        statusChanged: Boolean,
    ) =
        update(
            newOngoingCompanyDeal = newDealVersion,
            oldVersionOngoingCompanyDeal = oldDealVersion,
        ).then {
            if (statusChanged) {
                kafkaProducerService.produce(
                    OngoingCompanyDealStatusChangedEvent(
                        id = it.id,
                        status = it.status,
                        previousStatus = oldDealVersion.status,
                        updatedAt = LocalDateTime.now(),
                    )
                )
            }
        }

    private suspend fun createOngoingCompanyDeal(ongoingCompanyDeal: OngoingCompanyDeal): Result<OngoingCompanyDeal, Throwable> {
        val result = ongoingCompanyDealDataService.add(ongoingCompanyDeal)

        ongoingCompanyDealCreated(result.get())

        logger.info(
            "Creating new ongoingCompanyDeal",
            "company_name" to ongoingCompanyDeal.name,
            "company_legal_name" to ongoingCompanyDeal.legalName,
            "ongoing_company_deal_id" to result.get().id,
        )

        return result
    }

    private suspend fun verifyAndGetCompanyIdIfChannelInternal(currentId: UUID?, deal: OngoingCompanyDeal): UUID? {
        if (deal.channel == DealChannel.INTERNAL) return currentId

        val companyId = getCompanyId(currentId, deal)
        return when {
            companyId == null && OPTIONAL_COMPANY_ID_STATUS_LIST.contains(deal.status) -> {
                logger.info("company is optional for deal", "deal_id" to deal.id)
                null
            }
            companyId == null -> {
                logger.error("company not found for deal", "deal_id" to deal.id)
                throw NotFoundException("company not found for deal")
            }
            else -> companyId
        }
    }

    private suspend fun getCompanyId(currentId: UUID?, deal: OngoingCompanyDeal): UUID? =
        if (currentId != null) {
            logger.info("upsertOngoingCompanyDeal company already exists", "company_id" to currentId, "deal_id" to deal.id)
            currentId
        } else {
            val companyId = try {
                val cnpj = deal.cnpj.normalizeCnpjWithoutMask()

                logger.info(
                    "Gonna search for company by cnpj",
                    "cnpj" to cnpj,
                    "cnpj_deal" to deal.cnpj,
                )

                companyService.findByCnpj(cnpj).getOrNull()?.id
            } catch (e: NotFoundException) {
                null
            }

            logger.info("upsertOngoingCompanyDeal company not on deal",
                "deal_id" to deal.id,
                "company_doc" to deal.cnpj,
                "company_id" to companyId,
            )

            companyId
        }

    override suspend fun delete(id: UUID): Result<OngoingCompanyDeal, Throwable> =
        get(id).flatMap { ongoingCompanyDealDataService.update(it.copy(deletedAt = LocalDateTime.now())) }

    override suspend fun cancel(
        deal: OngoingCompanyDeal,
        reason: CancelReason?,
        description: String?
    ): Result<OngoingCompanyDeal, Throwable> {
        val previousStatus = deal.status
        val ongoingDealStatusHistory = OngoingDealStatusHistory(
            previousStatus = previousStatus,
            status = DealStage.CANCELED,
            reason = reason,
            description = description
        )

        return ongoingCompanyDealDataService.update(
            deal.copy(
                status = DealStage.CANCELED,
                statusHistory = deal.statusHistory + ongoingDealStatusHistory
            )
        ).then { newDeal ->
            kafkaProducerService.produce(
                OngoingCompanyDealStatusChangedEvent(
                    id = newDeal.id,
                    status = newDeal.status,
                    previousStatus = previousStatus,
                    updatedAt = LocalDateTime.now()
                )
            )
        }
    }

    override suspend fun reactivate(deal: OngoingCompanyDeal): Result<OngoingCompanyDeal, Throwable> {
        val newStatus = deal.statusHistory.last().previousStatus
        val previousStatus = deal.status
        val ongoingDealStatusHistory = OngoingDealStatusHistory(
            previousStatus = previousStatus,
            status = newStatus,
        )

        return ongoingCompanyDealDataService.update(
            deal.copy(
                status = newStatus,
                statusHistory = deal.statusHistory + ongoingDealStatusHistory
            )
        ).then { newDeal ->
            kafkaProducerService.produce(
                OngoingCompanyDealStatusChangedEvent(
                    id = newDeal.id,
                    status = newDeal.status,
                    previousStatus = previousStatus,
                    updatedAt = LocalDateTime.now()
                )
            )
        }
    }

    override suspend fun add(ongoingCompanyDeal: OngoingCompanyDeal): Result<OngoingCompanyDeal, Throwable> {
        return getDealBySourceId(ongoingCompanyDeal.sourceId).then {
            logger.info(
                "Deal already exists",
                "deal_name" to it.name,
                "source_id" to it.sourceId
            )
            true.success()
        }.coFoldNotFound {
            val ongoingCompanyDealCreated = ongoingCompanyDealDataService.add(ongoingCompanyDeal)
            logger.info(
                "Creating new ongoingCompanyDeal",
                "company_name" to ongoingCompanyDeal.name,
                "company_legal_name" to ongoingCompanyDeal.legalName
            )
            ongoingCompanyDealCreated(ongoingCompanyDealCreated.get())
            ongoingCompanyDealCreated
        }
    }

    override suspend fun searchBySalesFirm(
        searchToken: String,
        salesFirmId: UUID,
        offset: Int,
        limit: Int,
    ): Result<List<OngoingCompanyDeal>, Throwable> = useReadDatabase {
        ongoingCompanyDealDataService.find {
            where {
                this.searchTokens.search(searchToken) and
                this.salesFirmId.eq(salesFirmId) and
                this.deletedAt.isNull()
            }.offset {
                offset
            }.limit {
                limit
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }
    }

    override suspend fun searchBySalesAgentDocument(
        searchToken: String,
        salesAgentDocument: String,
        salesFirmId: UUID,
        offset: Int,
        limit: Int
    ): Result<List<OngoingCompanyDeal>, Throwable> = useReadDatabase {
        ongoingCompanyDealDataService.find {
            where {
                this.searchTokens.search(searchToken) and
                this.salesAgentDocument.inList(listOf(salesAgentDocument.toCPFMask(), salesAgentDocument)) and
                this.salesFirmId.eq(salesFirmId) and
                this.deletedAt.isNull()
            }.offset {
                offset
            }.limit {
                limit
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }
    }

    override suspend fun getBySalesAgentDocument(
        salesAgentDocument: String,
        salesFirmId: UUID,
        offset: Int,
        limit: Int,
    ): Result<List<OngoingCompanyDeal>, Throwable> = useReadDatabase {
        ongoingCompanyDealDataService.find {
            where {
                this.salesAgentDocument.inList(listOf(salesAgentDocument.toCPFMask(), salesAgentDocument)) and
                this.salesFirmId.eq(salesFirmId) and
                this.deletedAt.isNull()
            }.offset {
                offset
            }.limit {
                limit
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }
    }

    override suspend fun getFilteredPaginated(filter: OngoingCompanyDealFilter): Result<PaginatedList<OngoingCompanyDeal>, Throwable> =
        ongoingCompanyDealDataService
            .count { where { filter.predicate() } }
            .flatMapPair { totalItems ->
                if (totalItems == 0) emptyList<OngoingCompanyDeal>().success()
                else ongoingCompanyDealDataService.find {
                    filter.buildQuery(this)
                }
            }.map { (items, totalItems) ->
                PaginatedList(
                    items = items,
                    totalItems = totalItems,
                    range = filter.range
                )
            }

    override suspend fun getCountBySalesFirmGroupedByStatus(salesFirmId: UUID): Result<OngoingDealsCountByStatus, Throwable> =
        cachedOngoingDealService.getCountBySalesFirmGroupedByStatus(salesFirmId)

    override suspend fun getCountBySalesAgentDocumentGroupedByStatus(salesAgentDocument: String): Result<OngoingDealsCountByStatus, Throwable> =
        cachedOngoingDealService.getCountBySalesAgentDocumentGroupedByStatus(salesAgentDocument)

    override suspend fun getByCompanyId(companyId: UUID): Result<List<OngoingCompanyDeal>, Throwable> =
        ongoingCompanyDealDataService.find {
            where {
                this.companyId.eq(companyId) and
                        this.deletedAt.isNull()
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }

    override suspend fun findByCnpj(cnpj: String): Result<List<OngoingCompanyDeal>, Throwable> =
        ongoingCompanyDealDataService.find {
            where {
                this.cnpj.inList(
                    listOf(
                        cnpj.normalizeCnpjWithMask(),
                        cnpj.normalizeCnpjWithoutMask()
                    )
                )
            }.orderBy { createdAt }
                .sortOrder { desc }
        }

    private suspend fun getDealBySourceId(sourceId: String): Result<OngoingCompanyDeal, Throwable> {
        return ongoingCompanyDealDataService.findOne { where { this.sourceId.eq(sourceId) and this.deletedAt.isNull() } }
    }


    @OptIn(OrPredicateUsage::class)
    override suspend fun getFirstActiveByCompanyIdOrCnpj(
        companyId: UUID,
        cnpj: String
    ): Result<OngoingCompanyDeal, Throwable> =
        ongoingCompanyDealDataService.findOne {
            where {
                scope(
                    this.companyId.eq(companyId) or
                            this.cnpj.inList(
                                listOf(
                                    cnpj.normalizeCnpjWithMask(),
                                    cnpj.normalizeCnpjWithoutMask()
                                )
                            )
                ) and this.deletedAt.isNull()
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }
        }

    override suspend fun getByCnpjAndStatus(
        cnpj: String,
        status: DealStage
    ): Result<OngoingCompanyDeal, Throwable> =
        ongoingCompanyDealDataService.findOne {
            where {
                this.cnpj.inList(
                    listOf(
                        cnpj.normalizeCnpjWithMask(),
                        cnpj.normalizeCnpjWithoutMask()
                    )
                ) and this.status.eq(status)
            }
        }

    override suspend fun getByCnpjAndStatuses(
        cnpj: String,
        statuses: List<DealStage>,
    ): Result<List<OngoingCompanyDeal>, Throwable> {
        logger.info(
            "Getting ongoing company deals by cnpj and statuses",
            "cnpj" to cnpj,
            "statuses" to statuses,
        )
        return ongoingCompanyDealDataService.find {
            where {
                this.cnpj.inList(
                    listOf(
                        cnpj.normalizeCnpjWithMask(),
                        cnpj.normalizeCnpjWithoutMask()
                    )
                ) and this.status.inList(statuses)
            }
        }
    }

    override suspend fun update(
        ongoingCompanyDeal: OngoingCompanyDeal,
        shouldValidateStatus: Boolean,
    ): Result<OngoingCompanyDeal, Throwable> {
        val oldDeal = ongoingCompanyDealDataService.findOne {
            where { this.sourceId.eq(ongoingCompanyDeal.sourceId) and this.deletedAt.isNull() }
        }.get()

        return if (oldDeal.status != ongoingCompanyDeal.status) {
            if (shouldUpdateStatus(oldDeal.status, ongoingCompanyDeal.status, shouldValidateStatus)) {
                logger.info(
                    "Updating ongoing company deal status",
                    "id" to oldDeal.id,
                    "status" to ongoingCompanyDeal.status,
                    "source_id" to ongoingCompanyDeal.sourceId,
                    "company_id" to ongoingCompanyDeal.companyId,
                )
                updateAndProduceStatusChangedIfNecessary(
                    oldDealVersion = oldDeal,
                    newDealVersion = ongoingCompanyDeal,
                    statusChanged = true,
                )
            } else {
                logger.info(
                    "Ignoring status update since the new status is not after the old status",
                    "old_status" to oldDeal.status,
                    "new_status" to ongoingCompanyDeal.status,
                    "source_id" to ongoingCompanyDeal.sourceId,
                    "company_id" to ongoingCompanyDeal.companyId,
                )
                return oldDeal.success()
            }
        } else {
            updateAndProduceStatusChangedIfNecessary(
                oldDealVersion = oldDeal,
                newDealVersion = ongoingCompanyDeal,
                statusChanged = shouldUpdateStatus(oldDeal.status, ongoingCompanyDeal.status),
            )
        }
    }

    override suspend fun updateById(id: UUID, ongoingCompanyDeal: OngoingCompanyDeal): Result<OngoingCompanyDeal, Throwable> {
        val oldDeal = ongoingCompanyDealDataService.get(id).get()

        return if (oldDeal.status != ongoingCompanyDeal.status) {
            if (shouldUpdateStatus(oldDeal.status, ongoingCompanyDeal.status)) {
                logger.info(
                    "Updating ongoing company deal status",
                    "id" to oldDeal.id,
                    "status" to ongoingCompanyDeal.status,
                    "source_id" to ongoingCompanyDeal.sourceId,
                    "company_id" to ongoingCompanyDeal.companyId,
                )
                updateAndProduceStatusChangedIfNecessary(
                    oldDealVersion = oldDeal,
                    newDealVersion = ongoingCompanyDeal,
                    statusChanged = true,
                )
            } else {
                logger.info(
                    "Ignoring status update since the new status is not after the old status",
                    "old_status" to oldDeal.status,
                    "new_status" to ongoingCompanyDeal.status,
                    "source_id" to ongoingCompanyDeal.sourceId,
                    "company_id" to ongoingCompanyDeal.companyId,
                )
                return oldDeal.success()
            }
        } else {
            updateAndProduceStatusChangedIfNecessary(
                oldDealVersion = oldDeal,
                newDealVersion = ongoingCompanyDeal,
                statusChanged = shouldUpdateStatus(oldDeal.status, ongoingCompanyDeal.status),
            )
        }
    }

    private suspend fun update(
        newOngoingCompanyDeal: OngoingCompanyDeal,
        oldVersionOngoingCompanyDeal: OngoingCompanyDeal,
    ): Result<OngoingCompanyDeal, Throwable> {
        val updated = ongoingCompanyDealDataService.update(
            oldVersionOngoingCompanyDeal.copy(
                status = newOngoingCompanyDeal.status,
                portabilityType = newOngoingCompanyDeal.portabilityType,
                portabilityResponse = newOngoingCompanyDeal.portabilityResponse,
                portabilityDeclinedProceed = newOngoingCompanyDeal.portabilityDeclinedProceed,
                sourceCreatedAt = newOngoingCompanyDeal.sourceCreatedAt,
                sourceUpdatedAt = newOngoingCompanyDeal.updatedAt,
                sourceId = newOngoingCompanyDeal.sourceId,
                statusHistory = if (newOngoingCompanyDeal.status != oldVersionOngoingCompanyDeal.status) {
                    oldVersionOngoingCompanyDeal.statusHistory + OngoingDealStatusHistory(
                        previousStatus = oldVersionOngoingCompanyDeal.status,
                        status = newOngoingCompanyDeal.status,
                    )
                } else {
                    oldVersionOngoingCompanyDeal.statusHistory
                },
                dealDetails = OngoingCompanyDealDetails(
                    employeeCount = newOngoingCompanyDeal.dealDetails.employeeCount ?: oldVersionOngoingCompanyDeal.dealDetails.employeeCount,
                    livesCount = newOngoingCompanyDeal.dealDetails.livesCount ?: oldVersionOngoingCompanyDeal.dealDetails.livesCount,
                    contractModel = newOngoingCompanyDeal.dealDetails.contractModel ?: oldVersionOngoingCompanyDeal.dealDetails.contractModel,
                    compulsoryMembership = CompulsoryMembership(
                        isCompulsoryMembership = newOngoingCompanyDeal.dealDetails.compulsoryMembership?.isCompulsoryMembership ?: oldVersionOngoingCompanyDeal.dealDetails.compulsoryMembership?.isCompulsoryMembership,
                        compulsoryMembershipType = newOngoingCompanyDeal.dealDetails.compulsoryMembership?.compulsoryMembershipType ?: oldVersionOngoingCompanyDeal.dealDetails.compulsoryMembership?.compulsoryMembershipType,
                        compulsoryMembershipResponse = newOngoingCompanyDeal.dealDetails.compulsoryMembership?.compulsoryMembershipResponse ?: oldVersionOngoingCompanyDeal.dealDetails.compulsoryMembership?.compulsoryMembershipResponse
                    )
                ),
                companyId = verifyAndGetCompanyIdIfChannelInternal(newOngoingCompanyDeal.companyId ?: oldVersionOngoingCompanyDeal.companyId, newOngoingCompanyDeal),
                salesFirmId = newOngoingCompanyDeal.salesFirmId,
                salesAgentId = newOngoingCompanyDeal.salesAgentId ?: oldVersionOngoingCompanyDeal.salesAgentId,
                salesFirmAgentPartnershipId = newOngoingCompanyDeal.salesFirmAgentPartnershipId ?: oldVersionOngoingCompanyDeal.salesFirmAgentPartnershipId,
                channel = newOngoingCompanyDeal.channel ?: oldVersionOngoingCompanyDeal.channel,
            )
        ).get()

        logger.info(
            "Updated ongoing company deal",
            "id" to updated.id,
            "status" to updated.status,
            "source_id" to updated.sourceId,
            "company_id" to updated.companyId,
        )

        return updated.success()
    }

    override suspend fun getBySourceId(sourceId: String): Result<OngoingCompanyDeal, Throwable> =
        ongoingCompanyDealDataService.findOne { where { this.sourceId.eq(sourceId) and this.deletedAt.isNull() } }

    override suspend fun listWithoutCompanyId(channel: DealChannel, limit: Int, offset: Int): Result<List<OngoingCompanyDeal>, Throwable> =
        ongoingCompanyDealDataService.find { where {
            this.companyId.isNull() and this.channel.eq(channel)
        }.limit { limit }.offset { offset } }


    private fun shouldUpdateDealForContractGeneration(cnpj: String) =
        FeatureService.anyInList(
            FeatureNamespace.BUSINESS,
            "update-company-for-contract-generation-enabled-companies",
            listOf("00000000000000", cnpj)
        )

    private fun isGracePeriodPortabilityApproved(deal: OngoingCompanyDeal) = (
            (deal.portabilityType.isNullOrBlank() || deal.portabilityType?.lowercase().equals("portabilidade")) ||
                    (deal.portabilityType?.lowercase().equals("compra de carência") &&  deal.portabilityResponse.isNotNullOrBlank() && !rejectedResponses.contains(deal.portabilityResponse?.lowercase())) ||
                    (deal.portabilityDeclinedProceed?.lowercase() == "sim")
            )

    private val rejectedResponses = listOf("documentos pendentes", "portabilidade negada", "carência total")
}
