package br.com.alice.common

import br.com.alice.authentication.AuthenticationBootstrap
import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.FirebasePrincipalContext
import br.com.alice.authentication.SystemAccessTokenPrincipal
import br.com.alice.authentication.authToken
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.RfcException
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.opentelemetry.OpenTelemetryContext
import br.com.alice.common.observability.opentelemetry.Tracer.span
import br.com.alice.common.rfc.AsyncLayer
import br.com.alice.common.rfc.ReadDb
import br.com.alice.common.rfc.RootServiceToken
import br.com.alice.common.rfc.WriteDb
import com.google.gson.Gson
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import io.ktor.http.Parameters
import io.ktor.http.content.PartData
import io.ktor.http.content.forEachPart
import io.ktor.http.content.streamProvider
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.application
import io.ktor.server.application.call
import io.ktor.server.auth.authentication
import io.ktor.server.request.header
import io.ktor.server.request.isMultipart
import io.ktor.server.request.path
import io.ktor.server.request.receive
import io.ktor.server.request.receiveMultipart
import io.ktor.server.request.receiveText
import io.ktor.server.response.header
import io.ktor.server.response.respond
import io.ktor.util.AttributeKey
import io.ktor.util.pipeline.PipelineContext
import io.opentelemetry.api.trace.SpanKind
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStream
import java.lang.reflect.InvocationTargetException
import java.math.BigInteger
import java.util.UUID
import kotlin.coroutines.AbstractCoroutineContextElement
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.coroutineContext
import kotlin.reflect.full.instanceParameter
import kotlin.reflect.full.memberFunctions


// Form params
suspend inline fun PipelineContext<*, ApplicationCall>.rawBodyHandler(
    crossinline controllerAction: suspend (String) -> Response
) =
    withContexts {
        controllerAction(call.receiveText()).apply {
            respond(this)
        }
    }

// no route params

suspend fun PipelineContext<*, ApplicationCall>.handler(controllerAction: () -> Response) =
    withContexts {
        controllerAction().apply {
            respond(this)
        }
    }

suspend fun PipelineContext<*, ApplicationCall>.coHandler(controllerAction: suspend () -> Response) =
    withContexts {
        controllerAction().apply {
            respond(this)
        }
    }

// 1 route param

suspend inline fun <reified T> PipelineContext<*, ApplicationCall>.handler(
    parameter1: String,
    crossinline controllerAction: (T) -> Response
) =
    withContexts {
        val value = call.parameters[parameter1]!!
        controllerAction(convertParameter(value)).apply {
            respond(this)
        }
    }

suspend inline fun <reified T> PipelineContext<*, ApplicationCall>.coHandler(
    parameter1: String,
    crossinline controllerAction: suspend (T) -> Response
) =
    withContexts {
        val value = call.parameters[parameter1]!!
        controllerAction(convertParameter(value)).apply {
            respond(this)
        }
    }

suspend inline fun <reified T> PipelineContext<*, ApplicationCall>.coHandlerWithHeaders(
    parameter1: String,
    crossinline controllerAction: suspend (T, headers: Headers) -> Response
) =
    withContexts {
        val value = call.parameters[parameter1]!!
        val headers = call.request.headers
        controllerAction(convertParameter(value), headers).apply {
            respond(this)
        }
    }

// 2 route params

suspend inline fun <reified T1, reified T2> PipelineContext<*, ApplicationCall>.handler(
    parameter1: String,
    parameter2: String,
    crossinline controllerAction: (T1, T2) -> Response
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val value2 = call.parameters[parameter2]!!
        controllerAction(convertParameter(value1), convertParameter(value2)).apply {
            respond(this)
        }
    }

suspend inline fun <reified T1, reified T2> PipelineContext<*, ApplicationCall>.coHandler(
    parameter1: String,
    parameter2: String,
    crossinline controllerAction: suspend (T1, T2) -> Response
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val value2 = call.parameters[parameter2]!!
        controllerAction(convertParameter(value1), convertParameter(value2)).apply {
            respond(this)
        }
    }

// 3 route params

suspend inline fun <reified T1, reified T2, reified T3> PipelineContext<*, ApplicationCall>.handler(
    parameter1: String,
    parameter2: String,
    parameter3: String,
    crossinline controllerAction: (T1, T2, T3) -> Response
) = withContexts {
    val value1 = call.parameters[parameter1]!!
    val value2 = call.parameters[parameter2]!!
    val value3 = call.parameters[parameter3]!!
    controllerAction(convertParameter(value1), convertParameter(value2), convertParameter(value3)).apply {
        respond(this)
    }
}

suspend inline fun <reified T1, reified T2, reified T3> PipelineContext<*, ApplicationCall>.coHandler(
    parameter1: String,
    parameter2: String,
    parameter3: String,
    crossinline controllerAction: suspend (T1, T2, T3) -> Response
) = withContexts {
    val value1 = call.parameters[parameter1]!!
    val value2 = call.parameters[parameter2]!!
    val value3 = call.parameters[parameter3]!!
    controllerAction(convertParameter(value1), convertParameter(value2), convertParameter(value3)).apply {
        respond(this)
    }
}

//TODO: Refactor into coHandlerWithHeaders using Headers like the other cases
suspend inline fun <reified T1, reified T2, reified T3> PipelineContext<*, ApplicationCall>.coHandlerWithHeaders(
    header1: String,
    header2: String,
    header3: String,
    crossinline controllerAction: suspend (T1, T2, T3) -> Response
) = withContexts {
    val value1 = call.request.headers[header1]!!
    val value2 = call.request.headers[header2]!!
    val value3 = call.request.headers[header3]!!
    controllerAction(convertParameter(value1), convertParameter(value2), convertParameter(value3)).apply {
        respond(this)
    }
}

// no route params but query param or body request

suspend inline fun <reified T : Any> PipelineContext<*, ApplicationCall>.handler(
    crossinline controllerAction: (T) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val genericParameter = getGenericParameter<T>(call, gson)
        controllerAction(genericParameter).apply {
            respond(this)
        }
    }

suspend inline fun <reified T : Any> PipelineContext<*, ApplicationCall>.coHandler(
    crossinline controllerAction: suspend (T) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val genericParameter = getGenericParameter<T>(call, gson)
        controllerAction(genericParameter).apply {
            respond(this)
        }
    }

suspend inline fun <reified T : Any> PipelineContext<*, ApplicationCall>.coHandlerWithHeaders(
    crossinline controllerAction: suspend (T, headers: Headers) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val genericParameter = getGenericParameter<T>(call, gson)
        val headers = call.request.headers
        controllerAction(genericParameter, headers).apply {
            respond(this)
        }
    }

// 1 route param and query param or body request

suspend inline fun <reified T1, reified T2 : Any> PipelineContext<*, ApplicationCall>.handler(
    parameter1: String,
    crossinline controllerAction: (T1, T2) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val genericParameter = getGenericParameter<T2>(call, gson)
        controllerAction(convertParameter(value1), genericParameter).apply {
            respond(this)
        }
    }

suspend inline fun <reified T1, reified T2 : Any> PipelineContext<*, ApplicationCall>.coHandler(
    parameter1: String,
    crossinline controllerAction: suspend (T1, T2) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val genericParameter = getGenericParameter<T2>(call, gson)
        controllerAction(convertParameter(value1), genericParameter).apply {
            respond(this)
        }
    }

// 2 route params and query param/body request

suspend inline fun <reified T1, reified T2, reified T3 : Any> PipelineContext<*, ApplicationCall>.handler(
    parameter1: String,
    parameter2: String,
    crossinline controllerAction: (T1, T2, T3) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val value2 = call.parameters[parameter2]!!
        val genericParameter = getGenericParameter<T3>(call, gson)
        controllerAction(convertParameter(value1), convertParameter(value2), genericParameter).apply {
            respond(this)
        }
    }

suspend inline fun <reified T1, reified T2, reified T3 : Any> PipelineContext<*, ApplicationCall>.coHandler(
    parameter1: String,
    parameter2: String,
    crossinline controllerAction: suspend (T1, T2, T3) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val value2 = call.parameters[parameter2]!!
        val genericParameter = getGenericParameter<T3>(call, gson)
        controllerAction(convertParameter(value1), convertParameter(value2), genericParameter).apply {
            respond(this)
        }
    }

suspend inline fun <reified T1, reified T2, reified T3 : Any, reified T4 : Any> PipelineContext<*, ApplicationCall>.coHandler(
    parameter1: String,
    parameter2: String,
    crossinline controllerAction: suspend (T1, T2, T3, T4) -> Response,
    gson: Gson? = null
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val value2 = call.parameters[parameter2]!!
        val params = call.request.queryParameters as T3
        val genericParameter = getGenericParameter<T4>(call, gson)
        controllerAction(convertParameter(value1), convertParameter(value2), params, genericParameter).apply {
            respond(this)
        }
    }



// multipart

suspend fun getMultipartRequest(call: ApplicationCall): MultipartRequest {
    if (!call.request.isMultipart()) throw MultipartRequestRequiredException("ContentType with Multipart is required")

    val parameters = mutableMapOf<String, String>()
    var content: InputStream? = null
    var originalFile: File? = null

    call.parameters.forEach { key, values ->
        values.firstOrNull()?.let { value ->
            parameters[key] = value
        }
    }

    span("receiveMultipart", SpanKind.SERVER) {
        val receivedMultipart = call.receiveMultipart()
        receivedMultipart.forEachPart { part ->
            when (part) {
                is PartData.FormItem -> part.name?.let { parameters[it] = part.value }
                is PartData.FileItem -> {
                    content = part.streamProvider()
                    originalFile = File(part.originalFileName)
                }
                is PartData.BinaryItem -> logger.error("#multipartHandler - PartData BinaryItem not handled")
                else -> logger.error("#multipartHandler - Unknown PartData type")
            }
        }
    }

    return MultipartRequest(parameters, content, originalFile)
}

suspend inline fun PipelineContext<*, ApplicationCall>.multipartHandler(
    crossinline controllerAction: suspend (MultipartRequest) -> Response
) =
    span("multipartHandler", SpanKind.SERVER) {
        withContexts {
            val receivedData = getMultipartRequest(call)
            controllerAction(receivedData).apply { respond(this) }
        }
    }

suspend inline fun <reified T1 : Any> PipelineContext<*, ApplicationCall>.multipartHandler(
    parameter1: String,
    crossinline controllerAction: suspend (T1, MultipartRequest) -> Response
) =
    span("multipartHandler", SpanKind.SERVER) {
        withContexts {
            val value1 = call.parameters[parameter1]!!
            val receivedData = getMultipartRequest(call)
            controllerAction(convertParameter(value1), receivedData).apply { respond(this) }
        }
    }

// multipart multiple files

suspend fun getMultipartRequestMultipleFiles(call: ApplicationCall): MultipleFilesMultipartRequest {
    if (!call.request.isMultipart()) throw MultipartRequestRequiredException("ContentType with Multipart is required")

    val parameters = mutableMapOf<String, String>()
    val filesContent = mutableListOf<InputStream>()
    val originalFiles = mutableListOf<File>()

    call.parameters.forEach { key, values ->
        values.firstOrNull()?.let { value ->
            parameters[key] = value
        }
    }

    val receivedMultipart = call.receiveMultipart()
    receivedMultipart.forEachPart { part ->
        when (part) {
            is PartData.FormItem -> part.name?.let { parameters[it] = part.value }
            is PartData.FileItem -> {
                filesContent.add(part.streamProvider())
                originalFiles.add(File(part.originalFileName))
            }
            is PartData.BinaryItem -> logger.error("#multipartHandler - PartData BinaryItem not handled")
            else -> logger.error("#multipartHandler - Unknown PartData type")
        }
    }

    return MultipleFilesMultipartRequest(parameters, filesContent, originalFiles)
}

suspend inline fun PipelineContext<*, ApplicationCall>.multipleFileMultipartHandler(
    crossinline controllerAction: suspend (MultipleFilesMultipartRequest) -> Response
) =
    withContexts {
        val receivedData = getMultipartRequestMultipleFiles(call)
        controllerAction(receivedData).apply { respond(this) }
    }

suspend inline fun <reified T1 : Any> PipelineContext<*, ApplicationCall>.multipleFileMultipartHandler(
    parameter1: String,
    crossinline controllerAction: suspend (T1, MultipleFilesMultipartRequest) -> Response
) =
    withContexts {
        val value1 = call.parameters[parameter1]!!
        val receivedData = getMultipartRequestMultipleFiles(call)
        controllerAction(convertParameter(value1), receivedData).apply { respond(this) }
    }

//
// --- Other stuff ---
//

suspend fun PipelineContext<*, ApplicationCall>.respond(response: Response) = respond(call, response)

const val headerTraceId = "X-Trace-Id"
const val headerOpenTelemetryTraceId = "X-OpenTelemetry-Trace-Id"
const val headerOpenTelemetrySpanId = "X-OpenTelemetry-Span-Id"
suspend fun respond(call: ApplicationCall, response: Response, ex: Throwable? = null) {
    response.headers.forEach { entry ->
        call.response.headers.append(entry.key, entry.value)
    }

    OpenTelemetryContext.traceId()?.let { call.response.headers.append(headerOpenTelemetryTraceId, it) }
    OpenTelemetryContext.spanId()?.let { call.response.headers.append(headerOpenTelemetrySpanId, it) }

    ex?.let {
        OpenTelemetryContext.span()?.recordException(it)
        if (it is RfcException) {
            OpenTelemetryContext.span()?.setAttribute("origin_domain", it.originDomain)
            OpenTelemetryContext.span()?.setAttribute("throwing_call", it.throwingCall)
        }
    }

    if (response.status.isRedirect()) {
        call.response.headers.append(HttpHeaders.Location, response.message.toString())
        call.respond(response.status)
    } else {
        call.respond(response.status, response.message)
    }
}

inline fun <reified T> convertParameter(value: String): T {
    return when (T::class) {
        String::class -> value as T
        Int::class -> value.toInt() as T
        BigInteger::class -> value.toBigInteger() as T
        UUID::class -> value.toUUID() as T
        PersonId::class -> value.toPersonId() as T
        else -> throw RuntimeException("Unable to parse type ${T::class}")
    }
}

suspend inline fun <reified T : Any> getGenericParameter(call: ApplicationCall, gson: Gson?) = when {
    T::class == Parameters::class -> call.request.queryParameters as T
    T::class == String::class -> call.receive<String>() as T
    else -> {
        val requestBody: T = if (gson != null) {
            gson.fromJson(call.receive())
                ?: throw InvalidArgumentException(message = "Can't convert null to type ${T::class}")
        } else {
            call.receive()
        }
        validateRequestBody(requestBody)
        requestBody
    }
}

inline fun <reified T : Any> validateRequestBody(request: T) {
    if (T::class.isData) {
        val copy = T::class.memberFunctions.first { it.name == "copy" }
        val instanceParam = copy.instanceParameter!!
        try {
            copy.callBy(
                mapOf(
                    instanceParam to request
                )
            )
        } catch (e: InvocationTargetException) {
            throw InvalidArgumentException(
                message = "Request content is not compatible with type ${T::class}, request=${request}",
                cause = e.targetException
            )
        }
    } else {
        throw InvalidArgumentException(message = "Type ${T::class} must be a data class")
    }
}

class SystemAccessTokenPrincipalContext(val systemAccessTokenPrincipal: SystemAccessTokenPrincipal?) :
    AbstractCoroutineContextElement(SystemAccessTokenPrincipalContext) {
    companion object Key : CoroutineContext.Key<SystemAccessTokenPrincipalContext>
}

class RootServiceContext(val token: String?) : AbstractCoroutineContextElement(RootServiceContext) {
    companion object Key : CoroutineContext.Key<RootServiceContext>
}

class CallerContext(val service_name: String?, val path: String?) : AbstractCoroutineContextElement(CallerContext) {
    companion object Key : CoroutineContext.Key<CallerContext>
}

suspend fun <T> withRootServicePolicy(rootServiceName: String?, someFunction: suspend () -> T): T {
    val rootServiceToken = rootServiceName?.let { Authenticator.generateRootServiceToken(it) }
    return withContext(coroutineContext + RootServiceContext(rootServiceToken)) { someFunction.invoke() }
}

suspend fun idToken() =
    coroutineContext[FirebasePrincipalContext]?.firebasePrincipal?.idToken
        ?: coroutineContext[AuthTokenContext]?.authToken

suspend fun rootServiceToken() =
    coroutineContext[RootServiceContext]?.token

// using only service name for now
suspend fun callerPath() =
    (coroutineContext[CallerContext]?.service_name ?: "?") // + (coroutineContext[CallerContext]?.path ?: "?")

val PolicyRootServiceKey = AttributeKey<String>("RootServiceKey")

suspend fun isAsyncLayerContext(): Boolean =
    coroutineContext[AsyncLayerContext]?.let { true } ?: false

class AsyncLayerContext : AbstractCoroutineContextElement(AsyncLayerContext) {
    companion object Key : CoroutineContext.Key<AsyncLayerContext>
}

suspend fun <T> asyncLayer(someFunction: suspend () -> T): T =
    withContext(coroutineContext + AsyncLayerContext()) {
        someFunction.invoke()
    }

suspend fun <T> PipelineContext<*, ApplicationCall>.asyncLayer(someFunction: suspend () -> T): T =
    withContext(coroutineContext + AsyncLayerContext()) {
        someFunction.invoke()
    }

class UseReadDatabaseContext : AbstractCoroutineContextElement(UseReadDatabaseContext) {
    companion object Key : CoroutineContext.Key<UseReadDatabaseContext>
}

enum class DatabaseInstance {
    PRIMARY, READ
}

suspend fun <T> useDatabase(databaseInstance: DatabaseInstance, someFunction: suspend () -> T): T =
    when (databaseInstance) {
        DatabaseInstance.PRIMARY -> someFunction.invoke()
        DatabaseInstance.READ -> useReadDatabase { someFunction.invoke() }
    }

suspend fun <T> useReadDatabase(someFunction: suspend () -> T): T =
    withContext(coroutineContext + UseReadDatabaseContext()) {
        someFunction.invoke()
    }

suspend fun <T> PipelineContext<*, ApplicationCall>.useReadDatabase(someFunction: suspend () -> T): T =
    withContext(coroutineContext + UseReadDatabaseContext()) {
        someFunction.invoke()
    }

suspend fun isUseReadDatabaseContext(): Boolean =
    coroutineContext[UseReadDatabaseContext]?.let { true } ?: false

class UseWriteDatabaseContext : AbstractCoroutineContextElement(UseWriteDatabaseContext) {
    companion object Key : CoroutineContext.Key<UseWriteDatabaseContext>
}

suspend fun <T> useWriteDatabase(someFunction: suspend () -> T): T =
    withContext(coroutineContext + UseWriteDatabaseContext()) {
        someFunction.invoke()
    }

suspend fun <T> PipelineContext<*, ApplicationCall>.useWriteDatabase(someFunction: suspend () -> T): T =
    withContext(coroutineContext + UseWriteDatabaseContext()) {
        someFunction.invoke()
    }

suspend fun isUseWriteDatabaseContext(): Boolean =
    coroutineContext[UseWriteDatabaseContext]?.let { true } ?: false

suspend fun <T> PipelineContext<*, ApplicationCall>.withContexts(someFunction: suspend () -> T): T {
    var context =
        coroutineContext + HttpRequest(context.request) + FirebasePrincipalContext(call.authentication.principal()) +
                SystemAccessTokenPrincipalContext(call.authentication.principal())

    if (BaseConfig.instance.runningMode.value == "test") {
        val authToken = call.request.authToken()
        authToken?.let { context += AuthTokenContext(it) }
    }

    var rootServiceToken = call.request.header(HttpHeaders.RootServiceToken)
    rootServiceToken?.let { context += RootServiceContext(it) }

    val isAsyncLayer = call.request.header(HttpHeaders.AsyncLayer) == "async"
    if (isAsyncLayer) context += AsyncLayerContext()

    val isWriteContext = call.request.header(HttpHeaders.WriteDb) == "write-db"
    if (isWriteContext) context += UseWriteDatabaseContext()

    val isReadDbContext = call.request.header(HttpHeaders.ReadDb) == "read-db"
    if (isReadDbContext) context += UseReadDatabaseContext()

    val rootServiceName =
        call.attributes.getOrNull(PolicyRootServiceKey) ?: application.attributes.getOrNull(PolicyRootServiceKey)

    if (rootServiceToken == null && AuthenticationBootstrap.isInitialized()) {
        rootServiceToken = rootServiceName?.let { Authenticator.generateRootServiceToken(it) }
        context += RootServiceContext(rootServiceToken)
    }

    context += CallerContext(rootServiceName, call.request.path())

    return withContext(context) {
        someFunction.invoke()
    }
}

data class MultipartRequest(
    val parameters: Map<String, String>,
    val fileContent: InputStream?,
    val file: File?
)

data class MultipleFilesMultipartRequest(
    val parameters: Map<String, String>,
    val filesContent: List<InputStream>?,
    val files: List<File>?
)
