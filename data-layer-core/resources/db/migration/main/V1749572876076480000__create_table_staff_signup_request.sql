CREATE TABLE IF NOT EXISTS staff_signup_request (
    id UUID PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    status TEXT NOT NULL,
    rejection_reason TEXT,
    reviewed_by UUID,
    staff_id UUID,
    integration_content JSONB NOT NULL,
    type TEXT NOT NULL,
    additional_info JSONB,
    FOREIGN KEY (reviewed_by) REFERENCES staff(id),
    FOREIGN KEY (staff_id) REFERENCES staff(id)
);

CREATE INDEX IF NOT EXISTS idx_staff_signup_status ON staff_signup_request(status);
CREATE INDEX IF NOT EXISTS idx_staff_signup_created_at ON staff_signup_request(created_at);
CREATE INDEX IF NOT EXISTS idx_staff_signup_type ON staff_signup_request(type);
CREATE INDEX IF NOT EXISTS idx_staff_signup_reviewed_by ON staff_signup_request(reviewed_by);
CREATE INDEX IF NOT EXISTS idx_staff_signup_staff_id ON staff_signup_request(staff_id);
