package app.provider_domain_service_test

import rego.v1

import data.app.provider_domain_service

test_view_count_create_update_test_code_package_allowed if {
	{1,2,3,4} == provider_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "TestCodePackageModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "TestCodePackageModel",
            },
        },
        {
            "index": 3,
            "action": "create",
            "resource": {
                "opaType": "TestCodePackageModel",
            },
        },
        {
            "index": 4,
            "action": "update",
            "resource": {
                "opaType": "TestCodePackageModel",
            },
        }]
	}
}

test_provider_operations_allowed if {
    {1, 2, 3} == provider_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ProviderModel"
            }
        },
        {
            "index": 2,
            "action": "create",
            "resource": {
                "opaType": "ProviderModel"
            }
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "ProviderModel"
            }
        }]
    }
}

test_provider_unit_operations_allowed if {
    {1, 2, 3} == provider_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ProviderUnitModel"
            }
        },
        {
            "index": 2,
            "action": "create",
            "resource": {
                "opaType": "ProviderUnitModel"
            }
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "ProviderUnitModel"
            }
        }]
    }
}

test_structure_address_operations_allowed if {
    {1, 2, 3} == provider_domain_service.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "StructuredAddress"
            }
        },
        {
            "index": 2,
            "action": "create",
            "resource": {
                "opaType": "StructuredAddress"
            }
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "StructuredAddress"
            }
        }]
    }
}


