package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.Converter
import br.com.alice.common.core.Model
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.Table
import org.jdbi.v3.core.Jdbi
import kotlin.reflect.KClass

internal class DatabasePipelineFactory(
    private val jdbi: Jdbi,
    private val jdbiReadDb: Jdbi,
    private val authorizationService: AuthorizationService,
    private val tokenVerifier: TokenVerifier,
    private val personTokenService: PersonTokenService,
    private val contextService: ContextService,
    private val replicationLagService: ReplicationLagService,
) {

    fun <M : Model, T : Table<T>> get(
        modelClass: KClass<M>,
        tableClass: KClass<T>,
        withAuthorizationPipeline: Boolean = true,
        converter: Converter<M, T>? = null
    ): DatabasePipeline<M> {
        val finalConverter = converter ?: ConverterExtension.converter(modelClass, tableClass, personTokenService)

        val repository = JdbiRepository(jdbi, tableClass, jdbiReadDb)

        val jdbiAdapter = JdbiAdapterPipeline(repository, tokenVerifier, replicationLagService)
        val textualDeIdentification = TextualDeIdentificationPipeline(jdbiAdapter, jdbi, personTokenService)
        val deIdentification = DeIdentificationPipeline(tableClass, textualDeIdentification, personTokenService)
        val conversion = ConversionPipeline(modelClass, deIdentification, finalConverter)
        val audit = AuditPipeline(contextService, conversion, personTokenService)

        return if (withAuthorizationPipeline)
            AuthorizationPipeline(
                modelClass,
                tableClass,
                audit,
                authorizationService,
                contextService,
                this
            )
        else audit
    }

}

