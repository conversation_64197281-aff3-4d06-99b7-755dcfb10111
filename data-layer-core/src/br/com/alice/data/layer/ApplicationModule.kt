package br.com.alice.data.layer

import br.com.alice.authentication.SystemAccessTokenVerifier
import br.com.alice.authentication.TokenVerifier
import br.com.alice.authentication.TokenVerifierImpl
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.common.application.setup
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.RunningMode
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.logging.Logger
import br.com.alice.common.notification.LocalNotificationService
import br.com.alice.common.notification.NotificationService
import br.com.alice.common.notification.installNotificationSubscriptionAutoConfirm
import br.com.alice.common.notification.sns.SnsNotificationService
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.authorization.OPAAuthorizer
import br.com.alice.data.layer.authorization.OPAConfiguration
import br.com.alice.data.layer.bootstrap.testPeopleBootstrap
import br.com.alice.data.layer.configuration.Database
import br.com.alice.data.layer.controllers.DataServiceController
import br.com.alice.data.layer.ioc.DataServicesModule
import br.com.alice.data.layer.ioc.HealthCheckersModule
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.policies.allPolicies
import br.com.alice.data.layer.routes.apiRoutes
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.PersonTokenServiceImpl
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.services.ReplicationLagServiceImpl
import br.com.alice.data.layer.services.TestPersonServiceImpl
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.plugins.statuspages.StatusPages
import io.ktor.server.response.respond
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))
    private val environment = config.property("systemEnv").getString()
    private val appMillisecondsMeasure = listOf(50L, 100L)

    fun dependencyInjectionModules() = listOf(
        FeatureConfigDomainClientModule,
        DataServicesModule,
        HealthCheckersModule,
        module(createdAtStart = true) {
            // Configuration
            single { config }

            // Databases
            single { Database(get()) }
            single(named(MAIN_JDBI)) { inject<Database>().value.get("main") }
            single(named(MAIN_RO_JDBI)) { inject<Database>().value.get("main_ro") }
            single(named(TOKEN_JDBI)) { inject<Database>().value.get("token") }

            single { SystemAccessTokenVerifier(config.property("$environment.firebase.clientX509CertUrl").getString()) }
            single<TokenVerifier> { TokenVerifierImpl(get()) }

            // Repositories
            single { OPAConfiguration(config) }
            single { OPAAuthorizer(get()) }
            single { AuthorizationService(allPolicies, get()) }
            single { ContextService(get()) }
            single<PersonTokenService> { PersonTokenServiceImpl(get(named(TOKEN_JDBI))) }
            single<ReplicationLagService> { ReplicationLagServiceImpl(get(named(MAIN_RO_JDBI))) }
            single {
                DatabasePipelineFactory(
                    get(named(MAIN_JDBI)),
                    get(named(MAIN_RO_JDBI)),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }

            when (ServiceConfig.runningMode) {
                RunningMode.PRODUCTION -> {
                    single<NotificationService> { SnsNotificationService }
                }
                else -> {
                    single<NotificationService> { LocalNotificationService }
                }
            }

            // Controllers
            single { DataServiceController(getKoin()) }
            single { HealthController("data-layer") }

            // Services
            single { TestPersonServiceImpl(get()) }

            loadServiceServers(
                "br.com.alice.data.layer.services",
                listOfNotNull(
                    if (ServiceConfig.runningMode == RunningMode.PRODUCTION)
                        "br.com.alice.data.layer.services.BookDataServiceServer"
                    else null
                )
            )
        }
    )

    fun module(modules: List<Module> = dependencyInjectionModules(), application: Application) {
        with(application) {
            setup(modules, appMillisecondsMeasure) {
                authenticationBootstrap()

                install(StatusPages) {
                    exception<Exception> { call, cause ->
                        Logger.error("Unexpected error", cause)
                        call.respond(HttpStatusCode.InternalServerError)
                    }
                }

                featureConfigBootstrap(
                    FeatureNamespace.DATA_LAYER,
                    FeatureNamespace.ENG_PLATFORM
                )
                installNotificationSubscriptionAutoConfirm()
                testPeopleBootstrap()

                routing {
                    apiRoutes()
                }
            }
        }
    }
}
