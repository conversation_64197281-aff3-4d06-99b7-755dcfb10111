package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.models.Gender
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.common.UpdatedBy
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class StaffTable(
    val email: String,
    val firstName: String,
    val lastName: String,
    val active: Boolean = true,
    val gender: Gender,
    val profileImageUrl: String? = null,
    val nationalId: String? = null,
    var searchTokens: TsVector? = null,
    val role: Role,
    val type: StaffType,
    val birthdate: LocalDate? = null,
    val onCall: Boolean = false,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<StaffTable> {

    init {
        val value = listOfNotNull(firstName, lastName, email, nationalId).joinToString(" ")
        searchTokens = TsVector(value.unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
