package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.MemberInvoiceType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class MemberInvoiceGroupTable(
    val externalId: String?,
    val memberInvoiceIds: List<UUID> = emptyList(),
    val billingAccountablePartyId: UUID,
    val referenceDate: LocalDate,
    val dueDate: LocalDate,
    val status: MemberInvoiceGroupStatus,
    val type: MemberInvoiceType?,
    val totalAmount: BigDecimal?,
    val globalItems: List<InvoiceItemModel>? = emptyList(),
    val companyId: UUID? = null,
    val companySubcontractId: UUID? = null,
    val quantityMemberInvoices: Int? = null,
    val invoiceLiquidationIds: List<UUID>? = null,
    val preActivationPaymentId: UUID? = null,
    val discount: BigDecimal = BigDecimal.ZERO,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<MemberInvoiceGroupTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
