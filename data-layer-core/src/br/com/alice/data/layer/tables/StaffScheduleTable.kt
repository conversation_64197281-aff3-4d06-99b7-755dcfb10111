package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.data.layer.models.StaffScheduleType
import io.ktor.util.date.WeekDay
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class StaffScheduleTable(
    val staffId: UUID,
    val healthProfessionalId: UUID? = null,
    val startHour: LocalTime,
    val untilHour: LocalTime,
    val weekDay: WeekDay,
    val status: StaffScheduleStatus = StaffScheduleStatus.ACTIVE,
    val type: StaffScheduleType = StaffScheduleType.HAD,
    val providerUnitId: UUID? = null,
    val alsoDigital: Boolean,
    val exceptionEventTypes: List<UUID> = emptyList(),
    val lastUpdatedBy: UUID? = null,
    val carveOutHours: Int? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<StaffScheduleTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}

