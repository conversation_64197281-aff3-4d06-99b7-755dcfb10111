package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus
import br.com.alice.data.layer.models.HrMemberUploadTrackingError
import br.com.alice.data.layer.tables.Table
import java.time.LocalDateTime
import java.util.UUID

internal data class HrMemberUploadTrackingTable(
    override val id: UUID = RangeUUID.generate(),
    val uploadId: UUID,
    val companyId: UUID,
    val companyStaffId: UUID,
    val status: HrMemberUploadTrackingStatus,
    val memberNationalId: String,
    val errors: List<HrMemberUploadTrackingError>? = null,
    val reportNotifiedAt: LocalDateTime? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy?,
) : Table<HrMemberUploadTrackingTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
