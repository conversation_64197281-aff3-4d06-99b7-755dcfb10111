package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.PreActivationPaymentStatusHistory
import br.com.alice.data.layer.models.PreActivationPaymentType
import br.com.alice.data.layer.services.PersonPiiToken
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class PreActivationPaymentTable (
    override val id: UUID = RangeUUID.generate(),
    val externalId: String?,
    val memberInvoiceIds: List<UUID> = emptyList(),
    val totalAmount: BigDecimal,
    val statusHistory: List<PreActivationPaymentStatusHistory> = emptyList(),
    val status: PreActivationPaymentStatus,
    val type: PreActivationPaymentType,
    val billingAccountablePartyId: UUID,
    val dueDate: LocalDate,
    val globalItems: List<InvoiceItemModel> = emptyList(),
    val personId: PersonPiiToken? = null,
    val companyId: UUID? = null,
    val companySubContractId: UUID? = null,
    val memberInvoiceGroupId: UUID? = null,
    val referenceDate: LocalDate,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PreActivationPaymentTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
