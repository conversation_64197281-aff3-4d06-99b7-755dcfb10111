package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.AppointmentEvent
import br.com.alice.data.layer.models.AppointmentMacro
import br.com.alice.data.layer.models.ContactModel
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.MedicalSpecialtyModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.Timeline

val registerTimeline = policySet {
    match(
        "can view, count, update, create and delete",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any Timeline") { resourceIs(Timeline::class) }
    }
}

val registerAppointmentMacro = policySet {
    allows(AppointmentMacro::class, View, Update, Count, Create)
}


val viewAppointmentMacro = policySet {
    allows(AppointmentMacro::class, View, Count)
}

val getSpecialist = policySet {
    match("can view", { action is View }) {
        match("any StaffModel") { resourceIs(StaffModel::class) }
        match("any HealthProfessionalModel") { resourceIs(HealthProfessionalModel::class) }
        match("any MedicalSpecialty") { resourceIs(MedicalSpecialtyModel::class) }
        match("any StructuredAddress") { resourceIs(StructuredAddress::class) }
        match("any ContactModel") { resource is ContactModel }
    }
}

val viewAndCreateAndUpdateAndDeleteAppointmentEvent = policySet {
    match(
        "can view, count, update, create and delete",
        { action is View || action is Count || action is Update || action is Create || action is Delete }) {
        match("any AppointmentEvent") { resourceIs(AppointmentEvent::class) }
    }
}
