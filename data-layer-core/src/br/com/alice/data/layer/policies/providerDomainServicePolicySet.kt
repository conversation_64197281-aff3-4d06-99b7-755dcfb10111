package br.com.alice.data.layer.policies

import br.com.alice.data.layer.PROVIDER_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.PROVIDER_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.ProviderModel
import br.com.alice.data.layer.models.ProviderUnitGroupModel
import br.com.alice.data.layer.models.ProviderUnitModel
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.TestCodePackageModel
import br.com.alice.data.layer.subjects.Unauthenticated

val providerDomainServicePolicySet = policySet {
    match("at provider-domain-service", { rootService.name == PROVIDER_DOMAIN_ROOT_SERVICE_NAME }) {
        includes(viewAndUpdateTestCodePackages)
        includes(updateAndCreateProvider)
        includes(updateAndCreateProviderUnit)
        includes(createAndViewAndUpdateUpdateProviderUnitGroup)
    }

    match("at provider-environment-backfill", { rootService.name == PROVIDER_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            includes(createAndViewAndUpdateProviderAndProviderUnit)
            includes(createAndViewAndUpdateUpdateProviderUnitGroup)
        }
    }
}

val viewAndUpdateTestCodePackages = policySet {
    match(
        "can view, update, create and delete",
        { action is View || action is Update || action is Create || action is Delete }) {
        match("any TestCodePackage") { resourceIs(TestCodePackageModel::class) }
    }
}

val updateAndCreateProvider = policySet {
    match("can view and update", { action is View || action is Update || action is Create }) {
        match("any Provider") { resource is ProviderModel }
    }
}

val updateAndCreateProviderUnit = policySet {
    match("can view and update", { action is View || action is Update || action is Create }) {
        match("any ProviderUnit") { resource is ProviderUnitModel }
        match("any StructuredAddress") { resource is StructuredAddress }
    }
}

val createAndViewAndUpdateUpdateProviderUnitGroup = policySet {
    match("can view and update", { action is View || action is Update || action is Create }) {
        match("any ProviderUnitGroup") { resource is ProviderUnitGroupModel }
    }
}

val createAndViewAndUpdateProviderAndProviderUnit = policySet {
    match("can create and can view and can update", { action is Update || action is Create || action is View }) {
        match("any ProviderUnit") { resource is ProviderUnitModel }
        match("any Provider") { resource is ProviderModel }
        match("any StructuredAddress") { resource is StructuredAddress }
    }
}
