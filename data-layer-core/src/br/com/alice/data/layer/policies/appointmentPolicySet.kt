package br.com.alice.data.layer.policies

import br.com.alice.data.layer.APPOINTMENT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.AppointmentEvolution
import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.PersonCase
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.StaffSignTokenModel
import br.com.alice.data.layer.models.PersonInternalReference
import br.com.alice.data.layer.policies.features.getSpecialist
import br.com.alice.data.layer.policies.features.registerTimeline
import br.com.alice.data.layer.policies.features.viewAndCreateAndUpdateAndDeleteAppointmentEvent
import br.com.alice.data.layer.policies.features.viewAndUpdateAnyFile
import br.com.alice.data.layer.subjects.Unauthenticated

val appointmentPolicySet = policySet {

    match("at appointment-domain-service", { rootService.name == APPOINTMENT_ROOT_SERVICE_NAME }) {
        includes(registerTimeline)
        includes(getSpecialist)
        includes(viewAndCreateAndUpdateAndDeleteAppointmentEvent)
        match("any Appointment Evolution") { resourceIs(AppointmentEvolution::class) }
        allows(StaffSignTokenModel::class, View)
        allows(PersonModel::class, View)

        // just to do backfill, please remove after
        allows(PersonHealthEvent::class, View)
        allows(Appointment::class, View, Update, Create)

        who(Unauthenticated::class) {
            includes(viewAndUpdateAnyFile)
            allows(Appointment::class, View, Update, Create)
            allows(PersonCase::class, View)
            allows(AppointmentProcedureExecuted::class, View, Update, Delete)
            allows(HealthPlanTask::class, View)
            allows(PersonInternalReference::class, View)
        }
    }
}
