package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.*


val defaultAmasAccess = policySet {
    includes(viewProductComponents)
    includes(controlInvoiceTissBatchGuiaProcedure)
    match("can view", { action is View }) {
        match("any PersonModel") { resource is PersonModel }
        match("any MemberModel") { resource is MemberModel }
        match("any Product") { resource is ProductModel }
        match("any authorizer") { resource is ExecIndicatorAuthorizerModel }
        match("any staff") { resource is StaffModel }
        match("any provider unit") { resource is ProviderUnitModel }
        match("any structure address") { resource is StructuredAddress }
        match("any ContactModel") { resource is ContactModel }
        match("any NationalReceipt") { resource is NationalReceiptModel }
        match("any provider") { resource is ProviderModel }
        match("any HealthSpecialistResourceBundle") { resource is HealthSpecialistResourceBundleModel }
        match("any ResourceBundleSpecialty") { resource is ResourceBundleSpecialtyModel }
        match("any ResourceBundleSpecialtyPricing") { resource is ResourceBundleSpecialtyPricingModel }
        match("any HealthcareResourceModel") { resource is HealthcareResourceModel}
        match("any MedicalSpecialtyModel") { resource is MedicalSpecialtyModel}

    }
    match("can count", { action is Count }) {
        match("any TissBatch") { resourceIs(TissBatchModel::class) }
        match("any Guia") { resourceIs(GuiaModel::class) }
        match("any GuiaProcedure") { resourceIs(GuiaProcedureModel::class) }
        match("any TissGuiaExpense") { resourceIs(TissGuiaExpenseModel::class) }
        match("any Invoice") { resourceIs(InvoiceModel::class) }
        match("any InvoiceCritique") { resourceIs(InvoiceCritiqueModel::class) }
        match("any ProviderUnit") { resourceIs(ProviderUnitModel::class) }
        match("any HealthSpecialistResourceBundle") { resourceIs(HealthSpecialistResourceBundleModel::class) }
        match("any ResourceBundleSpecialty") { resourceIs(ResourceBundleSpecialtyModel::class) }
        match("any ResourceBundleSpecialtyPricing") { resourceIs(ResourceBundleSpecialtyPricingModel::class) }
    }
    match("can view and create", { action is View || action is Create}) {
        match("any GenericFileVault") { resource is GenericFileVault }
    }
}

val uploadTissBatch = policySet {
    match("can view and create and update", { action is View || action is Create || action is Update }) {
        match("any tissBatch") { resource is TissBatchModel }
        match("any TissBatchError") { resource is TissBatchErrorModel }
        match("any GenericFileVault") { resource is GenericFileVault }
        match("any Guia") { resource is GuiaModel }
        match("any GuiaProcedure") { resource is GuiaProcedureModel }
        match("any TissGuiaExpense") { resource is TissGuiaExpenseModel }
    }
    match("can view", { action is View }) {
        match("any PersonInternalReference") { resource is PersonInternalReference }
        match("any authorizer") { resource is ExecIndicatorAuthorizerModel }
        match("any provider unit") { resource is ProviderUnitModel }
        match("any structure ") { resource is StructuredAddress }
        match("any ContactModel") { resource is ContactModel }
        match("any provider") { resource is ProviderModel }
    }
}

val uploadPreviewEarningSummary = policySet {
    includes(controlInvoiceTissBatchGuiaProcedure)
    match("can view and create and update and delete", { action is View || action is Create || action is Update || action is Delete}) {
        match("any PreviewEarningSummary") { resource is PreviewEarningSummaryModel }
        match("any Invoice") { resource is InvoiceModel }
    }
    match("can view and create", { action is View || action is Create }) {
        match("any GenericFileVault") { resource is GenericFileVault }
    }
    match("can view", { action is View }) {
        match("any ProviderUnit") { resource is ProviderUnitModel }
        match("any StructuredAddress") { resource is StructuredAddress }
        match("any ContactModel") { resource is ContactModel }
        match("any StaffModel") { resource is StaffModel }
        match("any HealthSpecialistResourceBundle") { resource is HealthSpecialistResourceBundleModel }
        match("any ResourceBundleSpecialty") { resource is ResourceBundleSpecialtyModel }
        match("any ResourceBundleSpecialtyPricing") { resource is ResourceBundleSpecialtyPricingModel }
        match("any MemberModel") { resource is MemberModel }
        match("any Product") { resource is ProductModel }
    }
    match("can count", { action is Count }) {
        match("any PreviewEarningSummary") { resourceIs(PreviewEarningSummaryModel::class) }
        match("any Invoice") { resourceIs(InvoiceModel::class) }
    }
}

val controlInvoiceTissBatchGuiaProcedure = policySet {
    match(
        "can view and update and create and delete",
        { action is View || action is Update || action is Create || action is Delete }) {
        match("any Invoice") { resource is InvoiceModel }
        match("any TissBatchError") { resource is TissBatchErrorModel }
        match("any tissBatch") { resource is TissBatchModel }
        match("any Guia") { resource is GuiaModel }
        match("any GuiaProcedure") { resource is GuiaProcedureModel }
        match("any TissGuiaExpense") { resource is TissGuiaExpenseModel }
        match("any NationalReceipt") { resource is NationalReceiptModel }
        match("any InvoiceCritique") { resource is InvoiceCritiqueModel }

    }
    allows(HealthProfessionalModel::class, View)
    allows(ConsolidatedRewardsModel::class, View)
}

val createOrUpdateTussProcedureProvider = policySet {
    match("can view", { action is View }) {
        match("any Provider") { resource is ProviderModel }
    }
}

val createOrUpdateTussProcedureSpecialty = policySet {
    match("can view and create and update", { action is View || action is Create || action is Update }) {
        match("any TussProcedureSpecialty") { resource is TussProcedureSpecialtyModel }
    }
}

val validateTussProcedureProviderPrices = policySet {
    match("can count", { action is Count }) {
        match("any Guia") { resourceIs(GuiaModel::class) }
    }
}

val createTissBatchHistoric = policySet {
    allows(TissBatchHistoricModel::class, Create)
}

val generateAutomaticInvoices = policySet {
    allows(GuiaModel::class, Create, Update, View)
    allows(GuiaProcedureModel::class, Create, Update, View)
    allows(InvoiceModel::class, Create, Update, View)
    allows(TissBatchModel::class, Create, Update, View)
    allows(TissBatchErrorModel::class, Create, Update, View)

    allows(Appointment::class, View)
    allows(HealthcareResourceModel::class, View)
    allows(HealthProfessionalModel::class, View)
    allows(HealthSpecialistResourceBundleModel::class, View)
    allows(MemberModel::class, View)
    allows(ProductModel::class, View)
    allows(ProviderUnitModel::class, View)
    allows(TussProcedureSpecialtyModel::class, View)
}

val createAndViewHealthProfessionalTierHistory = policySet {
    allows(HealthProfessionalTierHistoryModel::class, Create, View)
}
