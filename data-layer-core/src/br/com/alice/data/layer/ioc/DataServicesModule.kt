package br.com.alice.data.layer.ioc

import br.com.alice.data.layer.services.*
import org.koin.dsl.module

val DataServicesModule = module(createdAtStart = true) {
    single<AliceAgoraWorkingHoursDataService> { AliceAgoraWorkingHoursDataServiceImpl(get()) }
    single<AliceTestResultBundleDataService> { AliceTestResultBundleDataServiceImpl(get()) }
    single<AliceTestResultFileDataService> { AliceTestResultFileDataServiceImpl(get()) }
    single<AnalyteOutcomeMappingDataService> { AnalyteOutcomeMappingDataServiceImpl(get()) }
    single<ScreenDetailDataService> { ScreenDetailDataServiceImpl(get()) }
    single<SiteAccreditedNetworkDataService> { SiteAccreditedNetworkDataServiceImpl(get()) }
    single<SiteAccreditedNetworkProviderDataService> { SiteAccreditedNetworkProviderDataServiceImpl(get()) }
    single<SiteAccreditedNetworkAddressDataService> { SiteAccreditedNetworkAddressDataServiceImpl(get()) }
    single<AppointmentDataService> { AppointmentDataServiceImpl(get()) }
    single<AppointmentEventDataService> { AppointmentEventDataServiceImpl(get()) }
    single<AppointmentMacroDataService> { AppointmentMacroDataServiceImpl(get()) }
    single<AppointmentFollowUpDataService> { AppointmentFollowUpDataServiceImpl(get()) }
    single<AppointmentScheduleModelDataService> { AppointmentScheduleModelDataServiceImpl(get()) }
    single<AppointmentScheduleCheckInModelDataService> { AppointmentScheduleCheckInModelDataServiceImpl(get()) }
    single<AppointmentScheduleOptionModelDataService> { AppointmentScheduleOptionModelDataServiceImpl(get()) }
    single<AppointmentCoordinationDataService> { AppointmentCoordinationDataServiceImpl(get()) }
    single<AppointmentProcedureExecutedDataService> { AppointmentProcedureExecutedDataServiceImpl(get()) }
    single<IntentionCoordinationDataService> { IntentionCoordinationDataServiceImpl(get()) }
    single<BolepixPaymentDetailModelDataService> { BolepixPaymentDetailModelDataServiceImpl(get()) }
    single<BoletoPaymentDetailModelDataService> { BoletoPaymentDetailModelDataServiceImpl(get()) }
    single<BookDataService> { BookDataServiceImpl(get()) }
    single<BillingAccountablePartyModelDataService> { BillingAccountablePartyModelDataServiceImpl(get()) }
    single<B2bBatchInvoiceReportModelDataService> { B2bBatchInvoiceReportModelDataServiceImpl(get()) }
    single<BeneficiaryModelDataService> { BeneficiaryModelDataServiceImpl(get()) }
    single<BeneficiaryOnboardingModelDataService> { BeneficiaryOnboardingModelDataServiceImpl(get()) }
    single<BeneficiaryMacoModelDataService> { BeneficiaryMacoModelDataServiceImpl(get()) }
    single<MemberProductChangeScheduleModelDataService> { MemberProductChangeScheduleModelDataServiceImpl(get()) }
    single<BudNodeDataService> { BudNodeDataServiceImpl(get()) }
    single<CassiMemberModelDataService> { CassiMemberModelDataServiceImpl(get()) }
    single<MemberContractModelDataService> { MemberContractModelDataServiceImpl(get()) }
    single<CompanyStaffModelDataService> { CompanyStaffModelDataServiceImpl(get()) }
    single<BeneficiaryHubspotModelDataService> { BeneficiaryHubspotModelDataServiceImpl(get()) }
    single<BeneficiaryOnboardingPhaseModelDataService> { BeneficiaryOnboardingPhaseModelDataServiceImpl(get()) }
    single<BeneficiaryCompiledViewModelDataService> { BeneficiaryCompiledViewModelDataServiceImpl(get()) }
    single<CaseRecordDataService> { CaseRecordDataServiceImpl(get()) }
    single<CassiSpecialistModelDataService> { CassiSpecialistModelDataServiceImpl(get()) }
    single<ClinicalOutcomesConsolidatedCalculatorConfDataService> {
        ClinicalOutcomesConsolidatedCalculatorConfDataServiceImpl(
            get()
        )
    }
    single<ChannelCommentDataService> { ChannelCommentDataServiceImpl(get()) }
    single<ChannelThemeDataService> { ChannelThemeDataServiceImpl(get()) }
    single<ChannelDataService> { ChannelDataServiceImpl(get()) }
    single<ChannelFupDataService> { ChannelFupDataServiceImpl(get()) }
    single<ChannelHistoryDataService> { ChannelHistoryDataServiceImpl(get()) }
    single<ChannelMacroDataService> { ChannelMacroDataServiceImpl(get()) }
    single<ChannelTagDataService> { ChannelTagDataServiceImpl(get()) }
    single<ChannelHealthConditionIgnoreDataService> { ChannelHealthConditionIgnoreDataServiceImpl(get()) }
    single<CityDataService> { CityDataServiceImpl(get()) }
    single<ClinicalBackgroundDataService> { ClinicalBackgroundModelDataServiceImpl(get()) }
    single<ClinicalOutcomeRecordDataService> { ClinicalOutcomeRecordDataServiceImpl(get()) }
    single<CounterReferralDataService> { CounterReferralDataServiceImpl(get()) }
    single<CounterReferralRelevanceDataService> { CounterReferralRelevanceDataServiceImpl(get()) }
    single<CuriosityNoteModelDataService> { CuriosityNoteModelDataServiceImpl(get()) }
    single<ConsentRegistrationDataService> { ConsentRegistrationDataServiceImpl(get()) }
    single<CompanyModelDataService> { CompanyModelDataServiceImpl(get()) }
    single<CompanyProductPriceListingModelDataService> { CompanyProductPriceListingModelDataServiceImpl(get()) }
    single<CompanyContractModelDataService> { CompanyContractModelDataServiceImpl(get()) }
    single<CompanyProductConfigurationModelDataService> { CompanyProductConfigurationModelDataServiceImpl(get()) }
    single<CompanySubContractModelDataService> { CompanySubContractModelDataServiceImpl(get()) }
    single<DbLaboratoryTestResultProcessDataService> { DbLaboratoryTestResultProcessDataServiceImpl(get()) }
    single<DbLaboratoryTestResultDataService> { DbLaboratoryTestResultDataServiceImpl(get()) }
    single<DeadletterQueueDataService> { DeadletterQueueDataServiceImpl(get()) }
    single<DealSalesInfoDataService> { DealSalesInfoDataServiceImpl(get()) }
    single<DeviceModelDataService> { DeviceModelDataServiceImpl(get()) }
    single<DischargeSummaryDataService> { DischargeSummaryDataServiceImpl(get()) }
    single<EinsteinAlergiaDataService> { EinsteinAlergiaDataServiceImpl(get()) }
    single<EinsteinAvaliacaoInicialDataService> { EinsteinAvaliacaoInicialDataServiceImpl(get()) }
    single<EinsteinAtendimentoDataService> { EinsteinAtendimentoDataServiceImpl(get()) }
    single<EinsteinEncaminhamentoDataService> { EinsteinEncaminhamentoDataServiceImpl(get()) }
    single<EinsteinDadosDeAltaDataService> { EinsteinDadosDeAltaDataServiceImpl(get()) }
    single<EinsteinMedicamentoDataService> { EinsteinMedicamentoDataServiceImpl(get()) }
    single<EinsteinProcedimentoDataService> { EinsteinProcedimentoDataServiceImpl(get()) }
    single<EinsteinDiagnosticoDataService> { EinsteinDiagnosticoDataServiceImpl(get()) }
    single<EinsteinResultadoExameDataService> { EinsteinResultadoExameDataServiceImpl(get()) }
    single<EinsteinResumoInternacaoDataService> { EinsteinResumoInternacaoDataServiceImpl(get()) }
    single<EinsteinStructuredTestResultDataService> { EinsteinStructuredTestResultDataServiceImpl(get()) }
    single<EligibilityCheckModelDataService> { EligibilityCheckDataServiceImpl(get()) }
    single<EmailCommunicationModelDataService> { EmailCommunicationModelDataServiceImpl(get()) }
    single<ExecIndicatorAuthorizerModelDataService> { ExecIndicatorAuthorizerDataServiceImpl(get()) }
    single<ExecIndicatorEventModelDataService> { ExecIndicatorEventDataServiceImpl(get()) }
    single<ExecutionGroupModelDataService> { ExecutionGroupDataServiceImpl(get()) }
    single<ExternalAppointmentScheduleModelDataService> { ExternalAppointmentScheduleModelDataServiceImpl(get()) }
    single<ExternalReferralModelDataService> { ExternalReferralModelDataServiceImpl(get()) }
    single<FaqContentModelDataService> { FaqContentModelDataServiceImpl(get()) }
    single<FaqFeedbackModelDataService> { FaqFeedbackModelDataServiceImpl(get()) }
    single<FaqGroupModelDataService> { FaqGroupModelDataServiceImpl(get()) }
    single<FeatureConfigModelDataService> { FeatureConfigDataServiceImpl(get()) }
    single<FederativeUnitDataService> { FederativeUnitDataServiceImpl(get()) }
    single<FhirDiagnosticReportDataService> { FhirDiagnosticReportDataServiceImpl(get()) }
    single<FhirProviderAccessDataService> { FhirProviderAccessDataServiceImpl(get()) }
    single<FhirDocumentDataService> { FhirDocumentDataServiceImpl(get()) }
    single<FileVaultDataService> { FileVaultDataServiceImpl(get()) }
    single<FileVaultPiiDataService> { FileVaultPiiDataServiceImpl(get()) }
    single<GenericFileVaultDataService> { GenericFileVaultDataServiceImpl(get()) }
    single<FleuryProcessDataService> { FleuryProcessDataServiceImpl(get()) }
    single<FleuryTestResultDataService> { FleuryTestResultDataServiceImpl(get()) }
    single<FleuryTestResultFileDataService> { FleuryTestResultFileDataServiceImpl(get()) }
    single<FollowUpHistoryDataService> { FollowUpHistoryDataServiceImpl(get()) }
    single<GenerateExternalAttendancePaDataService> { GenerateExternalAttendancePaDataServiceImpl(get()) }
    single<HaocClaimProcessDataService> { HaocClaimProcessDataServiceImpl(get()) }
    single<HaocPixRegistrationDataService> { HaocPixRegistrationDataServiceImpl(get()) }
    single<HaocProntoAtendimentoResultDataService> { HaocProntoAtendimentoResultDataServiceImpl(get()) }
    single<HaocSumarioDeAltaResultDataService> { HaocSumarioDeAltaResultDataServiceImpl(get()) }
    single<HaocDocumentDataService> { HaocDocumentDataServiceImpl(get()) }
    single<HaocFhirProcessDataService> { HaocFhirProcessDataServiceImpl(get()) }
    single<HDataOverviewDataService> { HDataOverviewDataServiceImpl(get()) }
    single<HealthEventsModelDataService> { HealthEventsModelDataServiceImpl(get()) }
    single<HealthFormOutcomeCalculatorConfDataService> { HealthFormOutcomeCalculatorConfDataServiceImpl(get()) }
    single<HealthcareMapDataService> { HealthcareMapDataServiceImpl(get()) }
    single<HealthcareTeamModelDataService> { HealthcareTeamModelDataServiceImpl(get()) }
    single<HealthCommunityUnreferencedAccessModelDataService> {
        HealthCommunityUnreferencedAccessModelDataServiceImpl(
            get()
        )
    }
    single<HealthConditionDataService> { HealthConditionDataServiceImpl(get()) }
    single<HealthConditionAxisDataService> { HealthConditionAxisDataServiceImpl(get()) }
    single<HealthConditionGroupDataService> { HealthConditionGroupDataServiceImpl(get()) }
    single<HealthConditionRelatedDataService> { HealthConditionRelatedDataServiceImpl(get()) }
    single<HealthConditionTemplateDataService> { HealthConditionTemplateDataServiceImpl(get()) }
    single<HealthDeclarationDataService> { HealthDeclarationDataServiceImpl(get()) }
    single<HealthFormAnswerGroupDataService> { HealthFormAnswerGroupDataServiceImpl(get()) }
    single<HealthFormDataService> { HealthFormDataServiceImpl(get()) }
    single<HealthFormQuestionDataService> { HealthFormQuestionDataServiceImpl(get()) }
    single<HealthFormSectionDataService> { HealthFormSectionDataServiceImpl(get()) }
    single<HealthFormQuestionAnswerDataService> { HealthFormQuestionAnswerDataServiceImpl(get()) }
    single<HealthGoalModelDataService> { HealthGoalModelDataServiceImpl(get()) }
    single<HealthLogicRecordDataService> { HealthLogicRecordDataServiceImpl(get()) }
    single<HealthLogicTrackingDataService> { HealthLogicTrackingDataServiceImpl(get()) }
    single<HealthPlanDataService> { HealthPlanDataServiceImpl(get()) }
    single<HealthPlanTaskDataService> { HealthPlanTaskDataServiceImpl(get()) }
    single<HealthPlanTaskReferralsDataService> { HealthPlanTaskReferralsDataServiceImpl(get()) }
    single<ActionPlanTaskDataService> { ActionPlanTaskDataServiceImpl(get()) }
    single<DemandActionPlanDataService> { DemandActionPlanDataServiceImpl(get()) }
    single<HealthPlanTaskTemplateDataService> { HealthPlanTaskTemplateDataServiceImpl(get()) }
    single<HealthPlanTaskGroupTemplateDataService> { HealthPlanTaskTemplateGroupDataServiceImpl(get()) }
    single<HealthPlanTaskGroupDataService> { HealthPlanTaskGroupDataServiceImpl(get()) }
    single<HealthProductSimulationDataService> { HealthProductSimulationDataServiceImpl(get()) }
    single<HealthProductSimulationGroupDataService> { HealthProductSimulationGroupDataServiceImpl(get()) }
    single<HealthMeasurementModelDataService> { HealthMeasurementModelDataServiceImpl(get()) }
    single<HealthMeasurementCategoryModelDataService> { HealthMeasurementCategoryModelDataServiceImpl(get()) }
    single<HealthMeasurementTypeModelDataService> { HealthMeasurementTypeModelDataServiceImpl(get()) }
    single<HLAdherenceDataService> { HLAdherenceDataServiceImpl(get()) }
    single<InsurancePortabilityRequestModelDataService> { InsurancePortabilityRequestModelDataServiceImpl(get()) }
    single<InsurancePortabilityRequestFileModelDataService> { InsurancePortabilityRequestFileModelDataServiceImpl(get()) }
    single<InsurancePortabilityHealthInsuranceModelDataService> {
        InsurancePortabilityHealthInsuranceModelDataServiceImpl(
            get()
        )
    }
    single<InvoiceItemModelDataService> { InvoiceItemModelDataServiceImpl(get()) }
    single<InvoicePaymentModelDataService> { InvoicePaymentModelDataServiceImpl(get()) }
    single<LaboratoryTestResultModelDataService> { LaboratoryTestResultModelDataServiceImpl(get()) }
    single<LeadDataService> { LeadDataServiceImpl(get()) }
    single<MedicalSpecialtyModelDataService> { MedicalSpecialtyModelDataServiceImpl(get()) }
    single<CboCodeModelDataService> { CboCodeModelDataServiceImpl(get()) }
    single<MedicineModelDataService> { MedicineModelDataServiceImpl(get()) }
    single<MemberModelDataService> { MemberModelDataServiceImpl(get()) }
    single<MemberLifeCycleEventsModelDataService> { MemberLifeCycleEventsModelDataServiceImpl(get()) }
    single<MemberLightweightModelDataService> { MemberLightweightModelDataServiceImpl(get()) }
    single<MemberHealthMetricModelDataService> { MemberHealthMetricModelDataServiceImpl(get()) }
    single<MemberInvoiceModelDataService> { MemberInvoiceModelDataServiceImpl(get()) }
    single<MemberInvoiceGroupModelDataService> { MemberInvoiceGroupModelDataServiceImpl(get()) }
    single<InvoiceLiquidationModelDataService> { InvoiceLiquidationModelDataServiceImpl(get()) }
    single<CancelPaymentOnAcquirerScheduleModelDataService> { CancelPaymentOnAcquirerScheduleModelDataServiceImpl(get()) }
    single<PreActivationPaymentModelDataService> { PreActivationPaymentModelDataServiceImpl(get()) }
    single<MemberProductPriceModelDataService> { MemberProductPriceModelDataServiceImpl(get()) }
    single<MemberContractTermModelDataService> { MemberContractTermModelDataServiceImpl(get()) }
    single<MvAuthorizedProcedureModelDataService> { MvAuthorizedProcedureDataServiceImpl(get()) }
    single<OnboardingBackgroundCheckModelDataService> { OnboardingBackgroundCheckModelDataServiceImpl(get()) }
    single<OnboardingContractModelDataService> { OnboardingContractModelDataServiceImpl(get()) }
    single<OutcomeConfDataService> { OutcomeConfDataServiceImpl(get()) }
    single<HealthDemandMonitoringDataService> { HealthDemandMonitoringDataServiceImpl(get()) }
    single<PersonAdditionalInfoModelDataService> { PersonAdditionalInfoModelDataServiceImpl(get()) }
    single<PersonBillingAccountablePartyModelDataService> { PersonBillingAccountablePartyModelDataServiceImpl(get()) }
    single<PersonCaseDataService> { PersonCaseDataServiceImpl(get()) }
    single<PersonClinicalAccountDataService> { PersonClinicalAccountDataServiceImpl(get()) }
    single<PersonClinicalAccountHistoryDataService> { PersonClinicalAccountHistoryDataServiceImpl(get()) }
    single<PersonModelDataService> { PersonDataServiceImpl(get(), get()) }
    single<PersonEligibilityDuquesaDataService> { PersonEligibilityDuquesaDataServiceImpl(get()) }
    single<PersonGracePeriodDataService> { PersonGracePeriodDataServiceImpl(get()) }
    single<PersonSherlockDataService> { PersonSherlockDataServiceImpl(get(), get()) }
    single<PersonHealthcareTeamRecommendationModelDataService> {
        PersonHealthcareTeamRecommendationModelDataServiceImpl(
            get()
        )
    }
    single<PersonHealthEventDataService> { PersonHealthEventDataServiceImpl(get()) }
    single<PersonHealthGoalModelDataService> { PersonHealthGoalModelDataServiceImpl(get()) }
    single<PersonHealthLogicDataService> { PersonHealthLogicDataServiceImpl(get()) }
    single<PersonInternalReferenceDataService> { PersonInternalReferenceDataServiceImpl(get()) }
    single<PersonLoginModelDataService> { PersonLoginModelDataServiceImpl(get()) }
    single<PersonOnboardingModelDataService> { PersonOnboardingModelDataServiceImpl(get()) }
    single<PersonPreferencesModelDataService> { PersonPreferencesModelDataServiceImpl(get()) }
    single<PersonRegistrationModelDataService> { PersonRegistrationModelDataServiceImpl(get()) }
    single<PersonSalesInfoDataService> { PersonSalesInfoDataServiceImpl(get()) }
    single<PersonTaskModelDataService> { PersonTaskModelDataServiceImpl(get()) }
    single<PersonTeamAssociationDataService> { PersonTeamAssociationDataServiceImpl(get()) }
    single<PersonDefaulterModelDataService> { PersonDefaulterModelDataServiceImpl(get()) }
    single<PersonIdentityValidationModelDataService> { PersonIdentityValidationModelDataServiceImpl(get()) }
    single<PixPaymentDetailModelDataService> { PixPaymentDetailModelDataServiceImpl(get()) }
    single<ProductBundleModelDataService> { ProductBundleModelDataServiceImpl(get()) }
    single<ProductModelDataService> { ProductModelDataServiceImpl(get()) }
    single<ProductGroupModelDataService> { ProductGroupModelDataServiceImpl(get()) }
    single<PrescriptionSentenceModelDataService> { PrescriptionSentenceModelDataServiceImpl(get()) }
    single<ProductOrderModelDataService> { ProductOrderModelDataServiceImpl(get()) }
    single<ProductRecommendationDataService> { ProductRecommendationDataServiceImpl(get()) }
    single<PriceListingModelDataService> { PriceListingModelDataServiceImpl(get()) }
    single<ProductPriceListingModelDataService> { ProductPriceListingModelDataServiceImpl(get()) }
    single<PromoCodeModelDataService> { PromoCodeModelDataServiceImpl(get()) }
    single<ProcedureProviderModelDataService> { ProcedureProviderDataServiceImpl(get()) }
    single<ProtocolDataService> { ProtocolDataServiceImpl(get()) }
    single<ProtocolTrackingDataService> { ProtocolTrackingDataServiceImpl(get()) }
    single<ProviderModelDataService> { ProviderDataServiceImpl(get()) }
    single<ProviderHealthDocumentModelDataService> { ProviderHealthDocumentDataServiceImpl(get()) }
    single<ProviderUnitModelDataService> { ProviderUnitDataServiceImpl(get()) }
    single<ProviderUnitGroupModelDataService> { ProviderUnitGroupDataServiceImpl(get()) }
    single<UpdateAppRuleModelDataService> { UpdateAppRuleModelDataServiceImpl(get()) }
    single<StructuredAddressDataService> { StructuredAddressDataServiceImpl(get()) }
    single<MemberOnboardingTemplateDataService> { MemberOnboardingTemplateDataServiceImpl(get()) }
    single<MemberOnboardingStepDataService> { MemberOnboardingStepDataServiceImpl(get()) }
    single<MemberOnboardingActionDataService> { MemberOnboardingActionDataServiceImpl(get()) }
    single<MemberOnboardingCheckpointDataService> { MemberOnboardingCheckpointDataServiceImpl(get()) }
    single<ProviderTestCodeModelDataService> { ProviderTestCodeModelDataServiceImpl(get()) }
    single<ProviderUnitTestCodeModelDataService> { ProviderUnitTestCodeModelDataServiceImpl(get()) }
    single<QueryResultDataService> { QueryResultDataServiceImpl(get()) }
    single<ChannelsResultDataService> { ChannelsResultDataServiceImpl(get()) }
    single<SherlockFileResultDataService> { SherlockFileResultDataServiceImpl(get()) }
    single<QueryEventDataService> { QueryEventDataServiceImpl(get()) }
    single<HealthcareAdditionalTeamDataService> { HealthcareAdditionalTeamDataServiceImpl(get()) }
    single<ServiceScriptActionDataService> { ServiceScriptActionDataServiceImpl(get()) }
    single<ServiceScriptNodeDataService> { ServiceScriptNodeDataServiceImpl(get()) }
    single<ServiceScriptRelationshipDataService> { ServiceScriptRelationshipDataServiceImpl(get()) }
    single<ServiceScriptExecutionDataService> { ServiceScriptExecutionDataServiceImpl(get()) }
    single<ServiceScriptNavigationDataService> { ServiceScriptNavigationDataServiceImpl(get()) }
    single<ServiceScriptNavigationGroupDataService> { ServiceScriptNavigationGroupDataServiceImpl(get()) }
    single<ShoppingCartModelDataService> { ShoppingCartModelDataServiceImpl(get()) }
    single<SimpleCreditCardPaymentDetailModelDataService> { SimpleCreditCardPaymentDetailModelDataServiceImpl(get()) }
    single<StaffModelDataService> { StaffModelDataServiceImpl(get()) }
    single<StaffChannelHistoryDataService> { StaffChannelHistoryDataServiceImpl(get()) }
    single<OpportunityDataService> { OpportunityDataServiceImpl(get()) }
    single<TestCodeModelDataService> { TestCodeModelDataServiceImpl(get()) }
    single<ProviderTestCodeDataIntegrationModelDataService> { ProviderTestCodeDataIntegrationModelDataServiceImpl(get()) }
    single<TestCodePackageModelDataService> { TestCodePackageModelDataServiceImpl(get()) }
    single<TestCodeAnalyteModelDataService> { TestCodeAnalyteModelDataServiceImpl(get()) }
    single<TestPreparationModelDataService> { TestPreparationModelDataServiceImpl(get()) }
    single<TestResultFeedbackDataService> { TestResultFeedbackDataServiceImpl(get()) }
    single<TestResultFileModelDataService> { TestResultFileModelDataServiceImpl(get()) }
    single<TimelineDataService> { TimelineDataServiceImpl(get()) }
    single<TimelineAiSummaryReviewDataService> { TimelineAiSummaryReviewDataServiceImpl(get()) }
    single<TrackPersonABModelDataService> { TrackPersonABDataServiceImpl(get()) }
    single<RiskDataService> { RiskDataServiceImpl(get()) }
    single<RiskCalculationConfDataService> { RiskCalculationConfDataServiceImpl(get()) }
    single<RiskGroupDataService> { RiskGroupDataServiceImpl(get()) }
    single<RoutingHistoryDataService> { RoutingHistoryDataServiceImpl(get()) }
    single<RoutingRuleDataService> { RoutingRuleDataServiceImpl(get()) }
    single<SchedulePreferenceModelDataService> { SchedulePreferenceModelDataServiceImpl(get()) }
    single<StaffSchedulePreferenceModelDataService> { StaffSchedulePreferenceModelDataServiceImpl(get()) }
    single<StaffScheduleModelDataService> { StaffScheduleModelDataServiceImpl(get()) }
    single<ExternalCalendarEventModelDataService> { ExternalCalendarEventModelDataServiceImpl(get()) }
    single<PersonDocumentsUploadModelDataService> { PersonDocumentsUploadModelDataServiceImpl(get()) }
    single<PersonBenefitModelDataService> { PersonBenefitModelDataServiceImpl(get()) }
    single<PolicyDescriptionDataService> { PolicyDescriptionDataServiceImpl(get()) }
    single<PregnancyModelDataService> { PregnancyModelDataServiceImpl(get()) }
    single<PublicTokenIntegrationDataService> { PublicTokenIntegrationDataServiceImpl(get()) }
    single<PersonCalendlyModelDataService> { PersonCalendlyModelDataServiceImpl(get()) }
    single<AppointmentEvolutionDataService> { AppointmentEvolutionDataServiceImpl(get()) }
    single<HippocratesHealthcareProfessionalModelDataService> {
        HippocratesHealthcareProfessionalModelDataServiceImpl(
            get()
        )
    }
    single<ProductPriceAdjustmentModelDataService> { ProductPriceAdjustmentModelDataServiceImpl(get()) }
    single<MemberProductPriceAdjustmentModelDataService> { MemberProductPriceAdjustmentModelDataServiceImpl(get()) }
    single<ProfessionalTierProcedureValueModelDataService> { ProfessionalTierProcedureValueDataServiceImpl(get()) }
    single<FhirBundleDataService> { FhirBundleDataServiceImpl(get()) }
    single<VideoCallDataService> { VideoCallDataServiceImpl(get()) }
    single<ProviderReadTrackingDataService> { ProviderReadTrackingDataServiceImpl(get()) }
    single<HealthProfessionalModelDataService> { HealthProfessionalModelDataServiceImpl(get()) }
    single<HealthProfessionalOpsProfileModelDataService> { HealthProfessionalOpsProfileDataServiceImpl(get()) }
    single<HealthProfessionalTierHistoryModelDataService> { HealthProfessionalTierHistoryDataServiceImpl(get()) }
    single<HospitalSummaryHistoryDataService> { HospitalSummaryHistoryDataServiceImpl(get()) }
    single<TertiaryIntentionTouchPointDataService> { TertiaryIntentionTouchPointDataServiceImpl(get()) }
    single<LegalGuardianAssociationModelDataService> { LegalGuardianAssociationModelDataServiceImpl(get()) }
    single<UpdatedPersonContactInfoTempModelDataService> { UpdatedPersonContactInfoTempModelDataServiceImpl(get()) }
    single<ExternalCalendarRecurrentEventModelDataService> { ExternalCalendarRecurrentEventModelDataServiceImpl(get()) }
    single<GuiaModelDataService> { GuiaDataServiceImpl(get()) }
    single<GuiaProcedureModelDataService> { GuiaProcedureDataServiceImpl(get()) }
    single<DraftCommandModelDataService> { DraftCommandModelDataServiceImpl(get()) }
    single<AppointmentScheduleEventTypeModelDataService> { AppointmentScheduleEventTypeModelDataServiceImpl(get()) }
    single<AbTestSpecialistRecommendationDataService> { AbTestSpecialistRecommendationDataServiceImpl(get()) }
    single<InvoiceModelDataService> { InvoiceDataServiceImpl(get()) }
    single<InvoiceCritiqueModelDataService> { InvoiceCritiqueDataServiceImpl(get()) }
    single<TissBatchModelDataService> { TissBatchDataServiceImpl(get()) }
    single<LegalGuardianInfoTempModelDataService> { LegalGuardianInfoTempModelDataServiceImpl(get()) }
    single<TissBatchErrorModelDataService> { TissBatchErrorDataServiceImpl(get()) }
    single<AppointmentReminderModelDataService> { AppointmentReminderModelDataServiceImpl(get()) }
    single<AppointmentTemplateDataService> { AppointmentTemplateDataServiceImpl(get()) }
    single<DraftCommandModelDataService> { DraftCommandModelDataServiceImpl(get()) }
    single<HealthPlanTaskStatusHistoryModelDataService> { HealthPlanTaskStatusHistoryModelDataServiceImpl(get()) }
    single<OutcomeRequestSchedulingDataService> { OutcomeRequestSchedulingDataServiceImpl(get()) }
    single<AliceExamReferenceDataService> { AliceExamReferenceDataServiceImpl(get()) }
    single<TissGuiaExpenseModelDataService> { TissGuiaExpenseDataServiceImpl(get()) }
    single<EventTypeProviderUnitModelDataService> { EventTypeProviderUnitModelDataServiceImpl(get()) }
    single<NationalReceiptModelDataService> { NationalReceiptDataServiceImpl(get()) }
    single<AppointmentScheduleEventTypeDateExceptionModelDataService> {
        AppointmentScheduleEventTypeDateExceptionModelDataServiceImpl(
            get()
        )
    }
    single<WandaCommentDataService> { WandaCommentDataServiceImpl(get()) }
    single<ZipcodeAddressDataService> { ZipcodeAddressDataServiceImpl(get()) }
    single<PreviewEarningSummaryModelDataService> { PreviewEarningSummaryDataServiceImpl(get()) }
    single<TussProcedureSpecialtyModelDataService> { TussProcedureSpecialtyDataServiceImpl(get()) }
    single<HealthLogicActionRecommendationDataService> { HealthLogicActionRecommendationDataServiceImpl(get()) }
    single<EmergencyRecommendationDataService> { EmergencyRecommendationDataServiceImpl(get()) }
    single<MagicNumbersModelDataService> { MagicNumbersDataServiceImpl(get()) }
    single<NullvsIntegrationRecordModelDataService> { NullvsIntegrationRecordModelDataServiceImpl(get()) }
    single<ItauPaymentModelDataService> { ItauPaymentModelDataServiceImpl(get()) }
    single<EitaNullvsIntegrationRecordModelDataService> { EitaNullvsIntegrationRecordModelDataServiceImpl(get()) }
    single<NullvsIntegrationLogModelDataService> { NullvsIntegrationLogModelDataServiceImpl(get()) }
    single<EitaNullvsIntegrationLogModelDataService> { EitaNullvsIntegrationLogModelDataServiceImpl(get()) }
    single<TissBatchHistoricModelDataService> { TissBatchHistoricDataServiceImpl(get()) }
    single<SpecialistOpinionDataService> { SpecialistOpinionDataServiceImpl(get()) }
    single<SpecialistOpinionMessageDataService> { SpecialistOpinionMessageDataServiceImpl(get()) }
    single<EmergencyRecommendationProviderDataService> { EmergencyRecommendationProviderDataServiceImpl(get()) }
    single<ScreeningNavigationDataService> { ScreeningNavigationDataServiceImpl(get()) }
    single<ScreenDataDataService> { ScreenDataDataServiceImpl(get()) }
    single<FinancialDataModelDataService> { FinancialDataModelDataServiceImpl(get()) }
    single<RefundModelDataService> { RefundModelDataServiceImpl(get()) }
    single<RefundFileModelDataService> { RefundFileModelDataServiceImpl(get()) }
    single<AppointmentAutofillHistoryDataService> { AppointmentAutofillHistoryHistoryDataServiceImpl(get()) }
    single<AIModelDataService> { AIModelDataServiceImpl(get()) }
    single<AISetupDataService> { AISetupDataServiceImpl(get()) }
    single<AIAssistantDataService> { AIAssistantDataServiceImpl(get()) }
    single<AITextInferenceDataService> { AITextInferenceDataServiceImpl(get()) }
    single<AITextInferenceFeedbackDataService> { AITextInferenceFeedbackDataServiceImpl(get()) }
    single<CoPaymentCostInfoModelDataService> { CoPaymentCostInfoModelDataServiceImpl(get()) }
    single<HealthcareResourceModelDataService> { HealthcareResourceDataServiceImpl(get()) }
    single<HealthcareResourceGroupAssociationModelDataService> { HealthcareResourceGroupAssociationDataServiceImpl(get()) }
    single<HealthcareResourceGroupModelDataService> { HealthcareResourceGroupDataServiceImpl(get()) }
    single<HealthcareBundleModelDataService> { HealthcareBundleDataServiceImpl(get()) }
    single<StaffSignTokenModelDataService> { StaffSignTokenModelDataServiceImpl(get()) }
    single<SalesFirmDataService> { SalesFirmDataServiceImpl(get()) }
    single<SalesAgentDataService> { SalesAgentDataServiceImpl(get()) }
    single<SalesFirmStaffDataService> { SalesFirmStaffDataServiceImpl(get()) }
    single<OngoingCompanyDealDataService> { OngoingCompanyDealDataServiceImpl(get()) }
    single<HubspotTicketDataService> { HubspotTicketDataServiceImpl(get()) }
    single<AssistanceSummaryModelDataService> { AssistanceSummaryDataServiceImpl(get()) }
    single<RefundCounterReferralModelDataService> { RefundCounterReferralModelDataServiceImpl(get()) }
    single<CompanyActivationFilesModelDataService> { CompanyActivationFilesModelDataServiceImpl(get()) }
    single<TotvsGuiaModelDataService> { TotvsGuiaDataServiceImpl(get()) }
    single<CoveredGeoRegionModelDataService> { CoveredGeoRegionModelDataServiceImpl(get()) }
    single<CompanyRefundCostInfoModelDataService> { CompanyRefundCostInfoModelDataServiceImpl(get()) }
    single<GlossAuthorizationInfoModelDataService> { GlossAuthorizationInfoDataServiceImpl(get()) }
    single<ConsolidatedAccreditedNetworkDataService> { ConsolidatedAccreditedNetworkDataServiceImpl(get()) }
    single<AccreditedNetworkFavoriteDataService> { AccreditedNetworkFavoriteDataServiceImpl(get()) }
    single<ConsolidatedRewardsModelDataService> { ConsolidatedRewardsModelDataServiceImpl(get()) }
    single<GuiaWithProceduresModelDataService> { GuiaWithProceduresDataServiceImpl(get()) }
    single<CsatDataService> { CsatDataServiceImpl(get()) }
    single<CsatTemplateDataService> { CsatTemplateDataServiceImpl(get()) }
    single<ZendeskExternalReferenceDataService> { ZendeskExternalReferenceDataServiceImpl(get()) }
    single<MemberAccreditedNetworkTrackerModelDataService> { MemberAccreditedNetworkTrackerModelDataServiceImpl(get()) }
    single<VicProductOptionDataService> { VicProductOptionDataServiceImpl(get()) }
    single<HospitalizationInfoModelDataService> { HospitalizationInfoDataServiceImpl(get()) }
    single<ChannelsZendeskTagDataService> { ChannelsZendeskTagDataServiceImpl(get()) }
    single<SiteAccreditedNetworkCategoryDataService> { SiteAccreditedNetworkCategoryDataServiceImpl(get()) }
    single<AttachmentOpmeModelDataService> { AttachmentOpmeDataServiceImpl(get()) }
    single<AttachmentChemotherapyModelDataService> { AttachmentChemotherapyDataServiceImpl(get()) }
    single<AttachmentRadiotherapyModelDataService> { AttachmentRadiotherapyDataServiceImpl(get()) }
    single<SiteAccreditedNetworkFlagshipDataService> { SiteAccreditedNetworkFlagshipDataServiceImpl(get()) }
    single<InvoiceGroupTaxReceiptModelDataService> { InvoiceGroupTaxReceiptModelDataServiceImpl(get()) }
    single<InvoiceLiquidationTaxReceiptModelDataService> { InvoiceLiquidationTaxReceiptModelDataServiceImpl(get()) }
    single<MemberOnboardingDataService> { MemberOnboardingDataServiceImpl(get()) }
    single<ContactModelDataService> { ContactModelDataServiceImpl(get()) }
    single<RefundCostInfoModelDataService> { RefundCostInfoModelDataServiceImpl(get()) }
    single<PersonHealthConditionContractualRiskModelDataService> {
        PersonHealthConditionContractualRiskModelDataServiceImpl(
            get()
        )
    }
    single<PersonContractualRiskModelDataService> { PersonContractualRiskModelDataServiceImpl(get()) }
    single<CompanyContractMacoModelDataService> { CompanyContractMacoModelDataServiceImpl(get()) }
    single<MemberTelegramTrackingModelDataService> { MemberTelegramTrackingModelDataServiceImpl(get()) }
    single<ConsolidatedRatingDataService> { ConsolidatedRatingDataServiceImpl(get()) }
    single<ConsolidatedHRCompanyReportDataService> { ConsolidatedHRCompanyReportDataServiceImpl(get()) }
    single<CompanyScoreMagentaDataService> { CompanyScoreMagentaDataServiceImpl(get()) }
    single<HealthInstitutionNegotiationModelDataService> { HealthInstitutionNegotiationDataServiceImpl(get()) }
    single<HealthSpecialistResourceBundleModelDataService> { HealthSpecialistResourceBundleDataServiceImpl(get()) }
    single<SalesFirmAgentPartnershipDataService> { SalesFirmAgentPartnershipDataServiceImpl(get()) }
    single<AppointmentProcedureExecutedAliceCodeSourceModelDataService> {
        AppointmentProcedureExecutedAliceCodeSourceDataServiceImpl(get())
    }
    single<StandardCostModelDataService> { StandardCostModelDataServiceImpl(get()) }
    single<PartnerIntegrationProviderUnitModelDataService> { PartnerIntegrationProviderUnitModelDataServiceImpl(get()) }
    single<ResourceBundleSpecialtyModelDataService> { ResourceBundleSpecialtyDataServiceImpl(get()) }
    single<ResourceBundleSpecialtyPricingModelDataService> { ResourceBundleSpecialtyPricingModelDataServiceImpl(get()) }
    single<FirstPaymentScheduleModelDataService> { FirstPaymentScheduleModelDataServiceImpl(get()) }
    single<ResourceBundleSpecialtyPricingUpdateModelDataService> {
        ResourceBundleSpecialtyPricingUpdateDataServiceImpl(
            get()
        )
    }
    single<ResourceSignTokenModelDataService> { ResourceSignTokenModelDataServiceImpl(get()) }
    single<HrMemberUploadTrackingDataService> { HrMemberUploadTrackingDataServiceImpl(get()) }
}
