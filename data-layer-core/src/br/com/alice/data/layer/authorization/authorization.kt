package br.com.alice.data.layer.authorization

import br.com.alice.authentication.RootService
import br.com.alice.common.core.Model
import br.com.alice.common.core.Subject
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.authorization.BatchAuthorizationRequest.Companion.CHUNK_SIZE
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.featureconfig.core.FeatureService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubclassOf

data class PolicyContext(
    val authorizationRequest: AuthorizationRequest,
    val parent: PolicyContext? = null,
    var description: String = "",
    var authorizations: MutableList<Authorization> = mutableListOf(),
    val onlyReturnAllPolicies: Boolean = false
) {
    fun match(
        description: String,
        condition: (AuthorizationRequest).() -> <PERSON><PERSON><PERSON>,
        subPoliciesLambda: (PolicyContext).() -> Boolean
    ): Boolean {
        // stop if it is already authorized or if the condition did not pass
        if (!onlyReturnAllPolicies && (authorized() || !condition(authorizationRequest))) return true

        this.description = description
        val child = this.copy(parent = this)
        val isLeaf = !subPoliciesLambda(child) // only leaf's subPoliciesLambda returns false
        if (isLeaf) {
            val authDescription = if (onlyReturnAllPolicies) policyDescription() else fullDescription()
            authorizations.add(Authorization(true, authDescription))
        }
        return true
    }

    fun match(description: String, condition: (AuthorizationRequest).() -> Boolean): Boolean =
        match(description, condition) { false }

    fun describe(description: String, subPoliciesLambda: (PolicyContext).() -> Boolean): Boolean =
        match(description, { true }, subPoliciesLambda)

    fun includes(policySet: (PolicyContext).() -> Boolean): Boolean {
        if (!onlyReturnAllPolicies && authorized()) return true
        val child = this.copy(parent = this)
        return policySet(child)
    }

    fun allows(
        kClass: KClass<*>,
        vararg allowedActions: Action,
    ): Boolean =
        match(allowedActions.joinToString(prefix = "can "), { allowedActions.contains(action) }) {
            match("any ${kClass.simpleName.toString()}") {
                when {
                    action is Count && allowedActions.contains(action) -> resourceIs(kClass)
                    else -> resource != null && resource::class == kClass
                }
            }
        }

    fun at(serviceName: String, condition: (PolicyContext).() -> Boolean) =
        match("at $serviceName", { rootService.name == serviceName }, condition)

    fun who(kClass: KClass<*>, condition: (PolicyContext).() -> Boolean) =
        match("the ${kClass.simpleName}", { subject::class == kClass }, condition)

    fun can(vararg allowedActions: Action, condition: (PolicyContext).() -> Boolean): Boolean =
        match(allowedActions.joinToString(prefix = "can "), { allowedActions.contains(action) }, condition)

    fun resources(
        vararg resourceKClasses: KClass<*>
    ): Boolean = match(resourceKClasses.joinToString(prefix = "any ") {
        it.simpleName.toString()
    }) {
        resourceKClasses.any { resourceIs(it) }
    }

    private fun authorized() = this.authorizations.firstOrNull()?.granted == true

    private fun fullDescription() =
        "$authorizationRequest: ${policyDescription()}"

    private fun policyDescription(): String {
        val list = mutableListOf<String?>()
        var next: PolicyContext? = this
        while (next != null) {
            list.add(next.description)
            next = next.parent
        }
        return list.reversed().joinToString(" ").trim()
    }
}

sealed class Action

object View : Action() {
    override fun toString() = "view"
}

object Update : Action() {
    override fun toString() = "update"
}

object Delete : Action() {
    override fun toString() = "delete"
}

object Count : Action() {
    override fun toString() = "count"
}

object Create : Action() {
    override fun toString() = "create"
}

data class AuthorizationRequest(
    val subject: Subject,
    val action: Action,
    val resource: Model?,
    val rootService: RootService,
    val resourceClass: KClass<*>? = null
) {
    constructor(subject: Subject, action: Action, resource: Model?, rootService: RootService) : this(
        subject,
        action,
        resource,
        rootService,
        getClass(resource)
    )

    override fun toString(): String {
        val subjectDesc = when (subject) {
            is PersonModel -> "${subject::class.simpleName}(${subject.id})"
            is StaffModel -> "Staff(${subject.email} ${subject.allRoles()})"
            else -> subject.toString()
        }
        return if (resource != null)
            "$subjectDesc $action on ${resource::class.simpleName}(${resource.id}) at $rootService"
        else
            "$subjectDesc $action on ${resourceClass?.simpleName} at $rootService"
    }

    companion object {
        fun getClass(obj: Model?) = if (obj != null) obj::class else null
    }

    fun resourceIs(kClass: KClass<*>) = resourceClass?.isSubclassOf(kClass) ?: false
}

data class BatchAuthorizationRequest(
    val requests: List<AuthorizationRequest>,
    val rootService: RootService
) {
    companion object {
        const val CHUNK_SIZE = 50
    }
}

data class Authorization(val granted: Boolean, val description: String)

class AuthorizationService(val policiesLambda: PolicyContext.() -> Boolean,
                           private val opaAuthorizer: OPAAuthorizer,
                           private val chunkSize: Int = CHUNK_SIZE) {

    suspend fun authorize(authorizationRequest: AuthorizationRequest): Authorization {
        val dataLayerResult = dataLayerAuthorization(authorizationRequest)
        if (shouldAuthorizeWithOPA(authorizationRequest.rootService.name)) {
            return registerAuthzWithOPA(authorizationRequest, dataLayerResult)
        }
        return dataLayerResult
    }

    suspend fun authorizeBatch(batchAuthorizationRequest: BatchAuthorizationRequest): List<Pair<Model, Authorization>> {
        val dataLayerResult = standardDataLayerAuthz(batchAuthorizationRequest)
        if (shouldAuthorizeWithOPA(batchAuthorizationRequest.rootService.name)) {
            return registerBatchAuthzWithOPA(batchAuthorizationRequest, dataLayerResult)
        }
        return dataLayerResult
    }

    private fun dataLayerAuthorization(authorizationRequest: AuthorizationRequest): Authorization {
        val context = PolicyContext(authorizationRequest)
        policiesLambda(context)
        return context.authorizations.firstOrNull() ?: Authorization(false, "$authorizationRequest")
    }

    private suspend fun standardDataLayerAuthz(batchAuthorizationRequest: BatchAuthorizationRequest): List<Pair<Model, Authorization>> {
        // use standard data-layer authorization one by one
        return batchAuthorizationRequest.requests.pmap { req ->
            req.resource!! to dataLayerAuthorization(req)
        }
    }

    private suspend fun registerAuthzWithOPA(
        authorizationRequest: AuthorizationRequest,
        dataLayerResult: Authorization
    ): Authorization {
        return if (shouldTrustOPA(authorizationRequest.rootService.name)) {
            callOPA(authorizationRequest, dataLayerResult) // register and trust result
        } else {
            withContext(Dispatchers.IO) {
                launch {
                    callOPA(authorizationRequest, dataLayerResult) // register but ignore result
                }
            }
            dataLayerResult
        }
    }

    private suspend fun registerBatchAuthzWithOPA(
        authorizationRequest: BatchAuthorizationRequest,
        dataLayerResult: List<Pair<Model, Authorization>>
    ): List<Pair<Model, Authorization>> {
        return if (shouldTrustOPA(authorizationRequest.rootService.name)) {
            callOPABatch(authorizationRequest, dataLayerResult) // register and trust result
        } else {
            withContext(Dispatchers.IO) {
                launch {
                    callOPABatch(authorizationRequest, dataLayerResult) // register but ignore result
                }
            }
            dataLayerResult
        }
    }

    private suspend fun callOPA(
        authorizationRequest: AuthorizationRequest,
        dataLayerResult: Authorization
    ): Authorization {
        var opaResult: Authorization? = null
        try {
            opaResult = opaAuthorizer.authorize(authorizationRequest)
            return opaResult
        } catch (e: Exception) {
            if (shouldUseDataLayerAuthzFallback()) {
                logger.error("Error authorizing with OPA, falling back to data layer result...", e)
                return dataLayerResult
            } else {
                logger.error("Error authorizing with OPA, throwing exception!", e)
                throw e
            }
        } finally {
            recordAuthzMetric(authorizationRequest, dataLayerResult, opaResult)
        }
    }

    private suspend fun callOPABatch(
        authorizationRequest: BatchAuthorizationRequest,
        dataLayerResult: List<Pair<Model, Authorization>>
    ): List<Pair<Model, Authorization>> {
        var opaResult: List<Pair<Model, Authorization>>? = null
        try {
            opaResult = authorizationRequest.requests.chunked(chunkSize).pmap {
                opaAuthorizer.authorizeBatch<Model>(BatchAuthorizationRequest(it, authorizationRequest.rootService))
            }.flatten()
            return opaResult
        } catch (e: Exception) {
            if (shouldUseDataLayerAuthzFallback()) {
                logger.error("Error authorizing with OPA, falling back to data layer result...", e)
                return dataLayerResult
            } else {
                logger.error("Error authorizing with OPA, throwing exception!", e)
                throw e
            }
        } finally {
            authorizationRequest.requests.mapIndexed { index, req ->
                recordAuthzMetric(req, dataLayerResult[index].second, opaResult?.get(index)?.second, index != 0)
            }
        }
    }

    private fun recordAuthzMetric(
        authorizationRequest: AuthorizationRequest,
        dataLayerResult: Authorization,
        opaResult: Authorization?,
        batched: Boolean = false
    ) {
        //just logging for now (only first FAIL)
        if (opaResult == null && !batched) {
            logger.warn("Authorization FAILED with OPA",
                "metric" to "data-layer_opa_authz",
                "result" to "FAIL",
                "data_layer_granted" to dataLayerResult.granted,
                "opa_granted" to null,
                "resource_id" to authorizationRequest.resource?.id,
                "resource_class" to authorizationRequest.resourceClass?.simpleName,
                "action" to authorizationRequest.action,
                "root_service" to authorizationRequest.rootService.name)
        } else if (opaResult != null && dataLayerResult.granted != opaResult.granted) {
            logger.warn("Authorization MISMATCH between data layer and OPA",
                "metric" to "data-layer_opa_authz",
                "result" to "MISS",
                "data_layer_granted" to dataLayerResult.granted,
                "opa_granted" to opaResult.granted,
                "resource_id" to authorizationRequest.resource?.id,
                "resource_class" to authorizationRequest.resourceClass?.simpleName,
                "action" to authorizationRequest.action,
                "root_service" to authorizationRequest.rootService.name)
        } else if (!batched) { //will not log batched MATCHes
            logger.info("Authorization MATCH between data layer and OPA",
                "metric" to "data-layer_opa_authz",
                "result" to "HIT",
                "data_layer_granted" to dataLayerResult.granted,
                "resource_id" to authorizationRequest.resource?.id,
                "resource_class" to authorizationRequest.resourceClass?.simpleName,
                "action" to authorizationRequest.action,
                "root_service" to authorizationRequest.rootService.name)
        }
    }

    private fun shouldAuthorizeWithOPA(rootServiceName: String): Boolean =
        FeatureService.inList(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", rootServiceName)

    private fun shouldTrustOPA(rootServiceName: String): Boolean =
        FeatureService.inList(FeatureNamespace.ENG_PLATFORM, "opa_application_trust_list", rootServiceName)

    private fun shouldUseDataLayerAuthzFallback(): Boolean =
        FeatureService.get(FeatureNamespace.ENG_PLATFORM, "should_use_data_layer_authz_fallback", true)

    fun allPoliciesDescriptions(): List<String> {
        val dummyRequest = AuthorizationRequest(object : Subject {}, Count, null, RootService(""), Any::class)
        val context = PolicyContext(dummyRequest, onlyReturnAllPolicies = true)
        policiesLambda(context)
        return context.authorizations.map { it.description }
    }

}

fun policySet(body: PolicyContext.() -> Boolean): PolicyContext.() -> Boolean = body
