package br.com.alice.data.layer.seeds

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.models.Sex.FEMALE
import br.com.alice.common.models.State
import br.com.alice.common.security.Crypto
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.helpers.TestTableFactory.buildAppointmentScheduleOptionTable
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.HealthFormQuestionType.DATE
import br.com.alice.data.layer.models.HealthFormQuestionType.MULTIPLE_OPTIONS
import br.com.alice.data.layer.models.HealthFormQuestionType.MULTIPLE_OPTIONS_AND_TEXT
import br.com.alice.data.layer.models.HealthFormQuestionType.NUMERIC
import br.com.alice.data.layer.models.HealthFormQuestionType.OPTION_BUTTONS
import br.com.alice.data.layer.models.HealthFormQuestionType.OPTION_BUTTONS_AND_TEXT
import br.com.alice.data.layer.models.HealthFormQuestionValidationType.SEX
import br.com.alice.data.layer.services.PersonToken
import br.com.alice.data.layer.services.testPersonId
import br.com.alice.data.layer.services.testPersonIds
import br.com.alice.data.layer.tables.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class EhrDatabaseSeed : Seed() {
    override fun run() {
        val staffRepo = repoFactory.get(StaffTable::class)

        val physician1 = staffRepo.add(
            StaffTable(
                id = "b049a7f7-3260-4b1b-b1f9-560e8a7846b8".toUUID(),
                email = "<EMAIL>",
                firstName = "Henry",
                lastName = "Porta Hirschfeld",
                gender = Gender.MALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val physician2 = staffRepo.add(
            StaffTable(
                id = "f16e07fe-d2da-4dc9-a5b2-d22068be54b7".toUUID(),
                email = "<EMAIL>",
                firstName = "Lilian",
                lastName = "Moreno",
                gender = Gender.FEMALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val physician3 = staffRepo.add(
            StaffTable(
                id = "21701d22-5965-45d9-80cb-595f98f47139".toUUID(),
                email = "<EMAIL>",
                firstName = "Taline",
                lastName = "Costa",
                gender = Gender.FEMALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val physician4 = staffRepo.add(
            StaffTable(
                id = "c74c7a8e-6360-4d9f-93b1-5d6deb8e0800".toUUID(),
                email = "<EMAIL>",
                firstName = "Juliana",
                lastName = "Barbosa de Barros",
                gender = Gender.FEMALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val physician5 = staffRepo.add(
            StaffTable(
                id = "2e644fb4-d775-4243-a509-317ebd9df500".toUUID(),
                email = "<EMAIL>",
                firstName = "Daniel",
                lastName = "Rocha",
                gender = Gender.MALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val physician6 = staffRepo.add(
            StaffTable(
                id = "6395bce1-6ebd-4981-b672-394f7be74200".toUUID(),
                email = "<EMAIL>",
                firstName = "Amanda",
                lastName = "Guedes",
                gender = Gender.FEMALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val physician7 = staffRepo.add(
            StaffTable(
                id = "65df3da4-ec20-42b2-8c06-f3285b923200".toUUID(),
                email = "<EMAIL>",
                firstName = "Janaine",
                lastName = "Camargo",
                gender = Gender.FEMALE,
                role = Role.MANAGER_PHYSICIAN,
                type = StaffType.PITAYA
            )
        ).get()

        val nurse1 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Mariana",
                lastName = "Pereira",
                gender = Gender.FEMALE,
                role = Role.HEALTHCARE_TEAM_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val nurse2 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Juliana",
                lastName = "Silva",
                gender = Gender.FEMALE,
                role = Role.HEALTHCARE_TEAM_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val nurse3 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Gislaine",
                lastName = "Damasceno dos Santos",
                gender = Gender.FEMALE,
                role = Role.HEALTHCARE_TEAM_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val nurse4 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Camila",
                lastName = "Kawagoe",
                gender = Gender.FEMALE,
                role = Role.HEALTHCARE_TEAM_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val nurse5 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>\n",
                firstName = "Gabriela",
                lastName = "Bontempo",
                gender = Gender.FEMALE,
                role = Role.HEALTHCARE_TEAM_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val nurseDigitalCare1 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Joana",
                lastName = "Silva",
                gender = Gender.FEMALE,
                role = Role.DIGITAL_CARE_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val nurseDigitalCare2 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Joaquina",
                lastName = "Silva",
                gender = Gender.FEMALE,
                role = Role.DIGITAL_CARE_NURSE,
                type = StaffType.PITAYA
            )
        ).get()

        val operation1 = staffRepo.add(
            StaffTable(
                email = "<EMAIL>",
                firstName = "Operation",
                lastName = "MemberModel 1",
                gender = Gender.FEMALE,
                role = Role.OPS,
                type = StaffType.PITAYA
            )
        ).get()

        println("\n\nStaffModel created:")
        println(staffRepo.getAll().joinToString("\n"))

        // Populate persons and registrations
        fun registrationForPerson(person: PersonTable): PersonLoginTable {
            val salt = Crypto.salt()
            val accessCode = Crypto.hash("0000", salt)

            return PersonLoginTable(
                personId = person.id,
                nationalId = person.nationalId,
                accessCode = accessCode,
                saltAccessCode = salt,
                expirationDate = LocalDateTime.now().plusYears(1)
            )
        }


        val personRepo = repoFactory.get(PersonTable::class)
        val registrationRepo = repoFactory.get(PersonLoginTable::class)

        fun createPerson(
            firstName: String,
            lastName: String,
            nationalId: String,
            email: String,
            nickName: String? = null,
            dateOfBirth: LocalDateTime? = null,
            sex: Sex? = null,
            gender: Gender? = null,
            addresses: List<AddressModel> = listOf(DataLayerTestModelFactory.buildAddress()),
            personId: PersonId = PersonId(),
            isTest: Boolean = false,
            profilePicture: AliceFile? = null
        ): Pair<PersonTable, PersonToken> {
            val personToken = personTokenService.createForPersonId(personId).get()
            val person = PersonTable(
                id = personToken.personPiiToken,
                firstName = firstName,
                lastName = lastName,
                nationalId = nationalId,
                email = email,
                addresses = addresses,
                nickName = nickName,
                dateOfBirth = dateOfBirth,
                sex = sex,
                gender = gender,
                isTest = isTest,
                profilePicture = profilePicture
            )

            personRepo.add(person)
            registrationRepo.add(registrationForPerson(person))

            return Pair(person, personToken)
        }

        fun dateAgo(years: Long) = LocalDateTime.now().minusYears(years)

        createPerson(
            "Rafael",
            "Tinoco",
            "38661561892",
            "<EMAIL>",
            "Tonico",
            dateAgo(29),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("6913b419-a17a-4c65-a884-63b34ab36a01")
            )
        )
        createPerson(
            "André",
            "Florence",
            "33995957862",
            "<EMAIL>",
            null,
            dateAgo(35),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("2240287d-184d-4f3a-a1ff-14026c80d201")
            )
        )
        createPerson(
            "André",
            "Leite",
            "37344700808",
            "<EMAIL>",
            null,
            dateAgo(28),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("3c22c6d5-373d-4e5a-beb1-d2721d20e501")
            )
        )
        createPerson(
            "Guilherme",
            "Azevedo",
            "18798808818",
            "<EMAIL>",
            "Gui",
            dateAgo(45),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("d7e5da5a-3c8b-4df3-8940-edb670720501")
            )
        )
        createPerson(
            "Matheus",
            "Moraes",
            "08019811664",
            "<EMAIL>",
            "Matheus",
            dateAgo(32),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("d03390f6-b374-479c-bc48-be42a82a5401")
            )
        )
        val flavioPair = createPerson(
            "Flavio",
            "Mori",
            "32697220879",
            "<EMAIL>",
            null,
            dateAgo(35),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("73eda164-6e9a-48a6-8052-5e61269de901")
            )
        )
        createPerson(
            "Ricardo",
            "Oliveira",
            "30487334850",
            "<EMAIL>",
            null,
            dateAgo(37),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("e9e37c3a-8895-460f-8f02-2fd061d6cf01")
            )
        )
        createPerson(
            "Pessoa A",
            "A",
            "09703373100",
            "<EMAIL>",
            null,
            dateAgo(30),
            FEMALE,
            Gender.FEMALE,
            personId = PersonId(
                UUID.fromString("88478e1b-1c54-432f-b41a-a82644acfc01")
            )
        )
        createPerson(
            "Pessoa B",
            "B",
            "23867349517",
            "<EMAIL>",
            null,
            dateAgo(30),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("27604f12-5c82-4eb5-8805-d628d40b5801")
            )
        )
        val personWithoutHealthcareTeam1 = createPerson(
            "Pessoa C",
            "C",
            "21584204486",
            "<EMAIL>",
            null,
            dateAgo(30),
            FEMALE,
            Gender.FEMALE,
            personId = PersonId(
                UUID.fromString("1fb65ac2-f57d-4a2e-8278-cc561a21f601")
            )
        )
        val personWithoutHealthcareTeam2 = createPerson(
            "Pessoa D",
            "D",
            "11377189430",
            "<EMAIL>",
            null,
            dateAgo(30),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("c2a6d8f5-166c-4013-b062-ab1dc9dffc01")
            )
        )
        val personWithoutHealthcareTeam3 = createPerson(
            "Pessoa E",
            "E",
            "93217559185",
            "<EMAIL>",
            null,
            dateAgo(30),
            FEMALE,
            Gender.FEMALE,
            personId = PersonId(
                UUID.fromString("00ce0500-a2d2-4d48-839c-df1e066f6d01")
            )
        )
        val personToTest = createPerson(
            firstName = "Pessoa",
            lastName = "pará Teste",
            nationalId = "**********0",
            email = "<EMAIL>",
            nickName = null,
            dateOfBirth = LocalDate.of(1991, 1, 1).atStartOfDay(),
            sex = Sex.MALE,
            gender = Gender.MALE,
            addresses = listOf(
                AddressModel(
                    state = State.SP,
                    city = "São Paulo",
                    street = "Av. Professor Ascendino Reis",
                    number = "1130",
                    neighbourhood = "Indianópolis",
                    postalCode = "04027-000",
                    complement = null
                )
            ),
            personId = testPersonId,
            profilePicture = AliceFile(
                id = RangeUUID.generate(),
                fileName = "Atom_Ant.png",
                url = "https://en.wikipedia.org/wiki/Atom_Ant#/media/File:Atom_Ant.png",
                type = "png"
            )
        )

        testPersonIds.filter { it != testPersonId }.forEachIndexed { index, personId ->
            val rightIndex = index + 1
            createPerson(
                firstName = "Pessoa",
                lastName = "para Teste $rightIndex.",
                nationalId = "**********$rightIndex",
                email = "pessoaparateste$<EMAIL>",
                nickName = "Pessoa $rightIndex",
                dateOfBirth = LocalDate.of(1991, 1, 1).atStartOfDay(),
                personId = personId,
                isTest = true
            )
        }

        val pracaPerson = createPerson(
            "Felipe",
            "Praça",
            "40170001857",
            "<EMAIL>",
            "Praca",
            dateAgo(29),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("b46caf8c-4c28-47c8-9599-72d87eff4201")
            )
        )

        println("\n\nPersons created:")
        println(personRepo.getAll().joinToString("\n"))

        println("\n\nRegistrations created:")
        println(registrationRepo.getAll().joinToString("\n"))

        // Populate HealthcareTeams
        val healthcareTeamRepo = repoFactory.get(HealthcareTeamTable::class)
        val healthcareTeamTable = healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "cc9ef47a-04e7-4f2b-a6fe-be9f008f3c00".toUUID(),
                physicianStaffId = physician1.id,
                nurseStaffId = nurse1.id,
                digitalCareNurseStaffIds = listOf(nurseDigitalCare1.id)
            )
        ).get()

        healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "402fcddb-68f9-4b58-8a3f-bf301e2eda00".toUUID(),
                physicianStaffId = physician2.id,
                nurseStaffId = nurse2.id,
                digitalCareNurseStaffIds = listOf(nurseDigitalCare2.id)
            )
        ).get()

        healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "eb71bb67-dab6-41b6-b6e9-cbc92f4faa00".toUUID(),
                physicianStaffId = physician3.id,
                nurseStaffId = nurse2.id,
                digitalCareNurseStaffIds = emptyList()
            )
        ).get()

        healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "97b3f5d1-d684-4c97-958b-eef2c2b5ba00".toUUID(),
                physicianStaffId = physician4.id,
                nurseStaffId = nurse3.id,
                digitalCareNurseStaffIds = emptyList()
            )
        ).get()

        healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "890124fd-7693-4343-ad60-647b6afcc600".toUUID(),
                physicianStaffId = physician5.id,
                nurseStaffId = nurse5.id,
                digitalCareNurseStaffIds = emptyList()
            )
        ).get()

        healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "1737f907-04bc-41f6-b262-ba20e3572300".toUUID(),
                physicianStaffId = physician6.id,
                nurseStaffId = nurse4.id,
                digitalCareNurseStaffIds = emptyList()
            )
        ).get()

        healthcareTeamRepo.add(
            HealthcareTeamTable(
                id = "e10d4240-46dc-4a6a-9435-b977415e1100".toUUID(),
                physicianStaffId = physician7.id,
                nurseStaffId = nurse5.id,
                digitalCareNurseStaffIds = emptyList()
            )
        ).get()

        val personClinicalAccountRepo = repoFactory.get(PersonClinicalAccountTable::class)
        personClinicalAccountRepo.add(
            PersonClinicalAccountTable(
                flavioPair.second.personHiToken,
                healthcareTeamTable.id
            )
        )
        personClinicalAccountRepo.add(
            PersonClinicalAccountTable(
                pracaPerson.second.personHiToken,
                healthcareTeamTable.id
            )
        )

        // members
        val personPair2 = createPerson(
            "Vinicius",
            "Rodrigues",
            "***********",
            "<EMAIL>",
            "Vinizera",
            dateAgo(29),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("7b0855d5-2276-4593-80f6-7f2ab60a6c01")
            )
        )
        val personPair3 = createPerson(
            "Henrique",
            "Sanches",
            "***********",
            "<EMAIL>",
            null,
            dateAgo(32),
            Sex.MALE,
            Gender.MALE,
            personId = PersonId(
                UUID.fromString("6fdb141b-c30d-42dd-8777-4550a7431201")
            )
        )

        var i = 0
        fun getRandomCpf(): String {
            val allowedChars = "**********"
            return (1..11).map { allowedChars.random() }.joinToString("")
        }
        repeat(1_000) {
            createPerson(
                "Pessoa $i",
                "$i",
                getRandomCpf(),
                "email$<EMAIL>",
                "Pessoa $i",
                dateAgo(30),
                Sex.MALE,
                Gender.MALE
            )
            i += 1
            if (i % 100 == 0) println(i)
        }

        with(repoFactory.get(MemberTable::class)) {
            listOf(personPair2, personPair3)
                .map { (person, _) ->
                    val activationDate = LocalDateTime.now()
                    val product = TestModelFactory.buildProduct()
                    MemberTable(
                        personId = person.id,
                        contract = ContractModel("https://www.hhs.gov/sites/default/files/privacysummary.pdf"),
                        activationDate = activationDate,
                        status = MemberStatus.ACTIVE,
                        statusHistory = listOf(
                            MemberStatusHistoryEntryModel(MemberStatus.PENDING, activationDate.minusDays(10).toString()),
                            MemberStatusHistoryEntryModel(MemberStatus.ACTIVE, activationDate.toString())
                        ),
                        selectedProduct = MemberProduct(product.id, listOf(), ProductType.B2C)
                    )
                }.forEach { member -> add(member) }
        }

        // Populate appointments
        val appointmentRepo = repoFactory.get(AppointmentTable::class)

        //personWithoutHealthcareTeam1
        val appointment1 = appointmentRepo.add(
            TestTableFactory.buildAppointmentTable(
                staffId = physician1.id,
                personId = personWithoutHealthcareTeam1.second.personHiToken,
                createdAt = LocalDateTime.now().minusDays(2)
            )
        ).get()
        appointmentRepo.add(
            TestTableFactory.buildAppointmentTable(
                staffId = physician2.id,
                personId = personWithoutHealthcareTeam1.second.personHiToken,
                createdAt = LocalDateTime.now().minusDays(1)
            )
        )
        appointmentRepo.add(
            TestTableFactory.buildAppointmentTable(
                staffId = operation1.id,
                personId = personWithoutHealthcareTeam1.second.personHiToken
            )
        )

        //personWithoutHealthcareTeam2
        appointmentRepo.add(
            TestTableFactory.buildAppointmentTable(
                staffId = operation1.id,
                personId = personWithoutHealthcareTeam2.second.personHiToken
            )
        )

        //personWithoutHealthcareTeam3
        appointmentRepo.add(
            TestTableFactory.buildAppointmentTable(
                staffId = physician3.id,
                personId = personWithoutHealthcareTeam3.second.personHiToken,
                createdAt = LocalDateTime.now().minusMonths(1)
            )
        )

        // Populate healthPlan
        val healthPlanRepo = repoFactory.get(HealthPlanTable::class)

        //personWithoutHealthcareTeam1
        healthPlanRepo.add(
            TestTableFactory.buildHealthPlan(
                personId = personWithoutHealthcareTeam1.second.personHiToken
            )
        )

        val healthPlanTaskTemplateRepo = repoFactory.get(HealthPlanTaskTemplateTable::class)

        healthPlanTaskTemplateRepo.add(TestTableFactory.buildHealthPlanTaskTemplateTable())

        val nodeCategory = ServiceScriptNodeTable(
            name = "Health",
            content = "Protocols for use in health care",
            type = ServiceScriptNodeType.CATEGORY,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeSubCategory = ServiceScriptNodeTable(
            name = "Respiratory system",
            content = "Protocols for use in health care of the respiratory system",
            type = ServiceScriptNodeType.SUB_CATEGORY,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeSubCategory2 = ServiceScriptNodeTable(
            name = "Assistencial Adulto",
            content = "",
            type = ServiceScriptNodeType.SUB_CATEGORY,
            status = ServiceScriptStatus.ACTIVE,
            id = UUID.fromString("bd096533-493e-4f30-81cc-dfc9e3f85916")
        )

        val nodeScript = ServiceScriptNodeTable(
            name = "Sore throat",
            content = "Sore throat, also known as throat pain, is pain or irritation of the throat.",
            type = ServiceScriptNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeQuestionPain = ServiceScriptNodeTable(
            name = "Pain level",
            content = "Ask the patient if the pain is very strong",
            type = ServiceScriptNodeType.QUESTION,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeQuestionFever = ServiceScriptNodeTable(
            name = "Have a fever",
            content = "Ask the patient if he has a fever",
            type = ServiceScriptNodeType.QUESTION,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeActionEmergency = ServiceScriptNodeTable(
            name = "Emergency department",
            content = "Refer the patient to the emergency department",
            type = ServiceScriptNodeType.ACTION,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeActionRest = ServiceScriptNodeTable(
            name = "Stay at rest",
            content = "Inform the patient to rest",
            type = ServiceScriptNodeType.ACTION,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeScriptScreening = ServiceScriptNodeTable(
            id = UUID.fromString("3328e2c5-a7ea-4b49-83c8-c0210cf02700"),
            name = "Protocolo de screening",
            content = "protocolo de screening para consultas do time de imersão",
            type = ServiceScriptNodeType.SCRIPT,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeScreeningGender = ServiceScriptNodeTable(
            id = UUID.fromString("1ecb379f-931c-45de-a9ec-e84528287e00"),
            name = "Protocolo de screening - Gênero",
            content = "<p>Saber gênero da pessoa</p>",
            type = ServiceScriptNodeType.QUESTION,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeTriggerAppointment = ServiceScriptNodeTable(
            id = UUID.fromString("3d4bb2a6-320a-49f6-b8ed-0e194dc73400"),
            name = "APPOINTMENT",
            content = "<p>Consulta</p>",
            type = ServiceScriptNodeType.TRIGGER,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeTriggerManual = ServiceScriptNodeTable(
            id = UUID.fromString("b45f59d7-5662-4de7-812c-785fb36aab00"),
            name = "MANUAL",
            content = "<p>Manual</p>",
            type = ServiceScriptNodeType.TRIGGER,
            status = ServiceScriptStatus.ACTIVE
        )

        val nodeTriggerHealthForm = ServiceScriptNodeTable(
            id = UUID.fromString("b2fd5478-122e-464c-b0e8-ec0503c6d800"),
            name = "HEALTH_FORM",
            content = "<p>Forms pré imersão</p>",
            type = ServiceScriptNodeType.TRIGGER,
            status = ServiceScriptStatus.ACTIVE
        )

        val serviceScriptNodeRepo = repoFactory.get(ServiceScriptNodeTable::class)
        listOf(
            nodeCategory,
            nodeSubCategory,
            nodeSubCategory2,
            nodeScript,
            nodeQuestionPain,
            nodeQuestionFever,
            nodeActionEmergency,
            nodeActionRest,
            nodeScriptScreening,
            nodeScreeningGender,
            nodeTriggerAppointment,
            nodeTriggerManual,
            nodeTriggerHealthForm,
        ).forEach { serviceScriptNodeRepo.add(it) }

        val serviceScriptRelationshipRepo = repoFactory.get(ServiceScriptRelationshipTable::class)
        listOf(
            ServiceScriptRelationshipTable(
                name = nodeSubCategory.name,
                nodeParentId = nodeCategory.id,
                nodeChildId = nodeSubCategory.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = nodeScript.name,
                nodeParentId = nodeSubCategory.id,
                nodeChildId = nodeScript.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = nodeQuestionPain.name,
                nodeParentId = nodeScript.id,
                nodeChildId = nodeQuestionPain.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = "Yes",
                nodeParentId = nodeQuestionPain.id,
                nodeChildId = nodeQuestionFever.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = "No",
                nodeParentId = nodeQuestionPain.id,
                nodeChildId = nodeActionRest.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = "Yes",
                nodeParentId = nodeQuestionFever.id,
                nodeChildId = nodeActionEmergency.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = "No",
                nodeParentId = nodeQuestionFever.id,
                nodeChildId = nodeActionRest.id,
                status = ServiceScriptStatus.ACTIVE
            ),
            ServiceScriptRelationshipTable(
                name = "Gênero",
                nodeParentId = nodeScriptScreening.id,
                nodeChildId = nodeScreeningGender.id,
                status = ServiceScriptStatus.ACTIVE
            )
        ).forEach { serviceScriptRelationshipRepo.add(it) }

        // Populate Clinical Background
        val clinicalBackgroundRepo = repoFactory.get(ClinicalBackgroundTable::class)

        clinicalBackgroundRepo.add(
            TestTableFactory.buildClinicalBackground(
                personId = personToTest.second.personHiToken,
                addedByStaffId = physician1.id,
                appointmentId = appointment1.id
            )
        )

        // Populate medicines
        val medicineRepo = repoFactory.get(MedicineTable::class)
        val pharmaceuticalForm = MedicineUnit.values().toList().shuffled().first()

        repeat(10_000) { i ->
            medicineRepo.add(
                MedicineTable(
                    ean = "EAN$i",
                    tissCmed = "tissCmed$i",
                    name = "medicine$i plus version",
                    presentation = "apresentação$i",
                    concentration = "100$i",
                    unit = "mg",
                    pharmaceuticalForm = pharmaceuticalForm.singular,
                    routeOfAdministration = "VO",
                    quantityPerPackage = "60",
                    drugId = i,
                    drugName = "fármaco$i",
                    atcCode = "code$i",
                    medicineType = "REFERENCIA",
                    mip = true,
                    portaria344 = ""
                )
            )
            if (i % 100 == 0) println(i)
        }

        medicineRepo.add(
            MedicineTable(
                ean = "EANSPECIAL",
                tissCmed = "tissCmedSPECIAL",
                name = "medicineSPECIAL plus version",
                presentation = "apresentaçãoSPECIAL",
                concentration = "100",
                unit = "mg",
                pharmaceuticalForm = pharmaceuticalForm.singular,
                routeOfAdministration = "VO",
                quantityPerPackage = "60",
                drugId = 99999999,
                drugName = "fármacoSPECIAL",
                atcCode = "codeSPECIAL",
                medicineType = "REFERENCIA",
                mip = false,
                portaria344 = ""
            )
        )

        // Populate sentences
        val prescriptionSentenceRepo = repoFactory.get(PrescriptionSentenceTable::class)

        repeat(10_000) { i ->
            prescriptionSentenceRepo.add(
                PrescriptionSentenceTable(
                    tokens = listOf(
                        "fármaco$i-${pharmaceuticalForm.singular}-100$i-MG".uppercase(),
                        "fármaco$i-${pharmaceuticalForm}-100$i-mg".uppercase()
                    ),
                    dose = i.toFloat(),
                    unit = MedicineUnit.values().toList().shuffled().first(),
                    routeOfAdministration = RouteOfAdministration.values().toList().shuffled().first(),
                    action = ActionType.values().toList().shuffled().first(),
                    start = StartType.values().toList().shuffled().first(),
                    frequency = FrequencyModel(
                        type = FrequencyType.values().toList().shuffled().first(),
                        unit = PeriodUnit.values().toList().shuffled().first(),
                        quantity = i
                    ),
                    deadline = DeadlineModel(
                        unit = PeriodUnit.values().toList().shuffled().first(),
                        quantity = i
                    )
                )
            )
            if (i % 100 == 0) println(i)
        }

        // Populate DBLaboratoryTestResult
        val dbLaboratoryTestResultRepo = repoFactory.get(DbLaboratoryTestResultTable::class)
        dbLaboratoryTestResultRepo.add(
            DbLaboratoryTestResultTable(
                personId = personToTest.second.personHiToken,
                claimId = 637278,
                attendanceId = "",
                items = listOf(
                    DbResultadoProcedimento(
                        codigoExameDB = "TSH",
                        versaoLaudo = "07/10/2019 00:00:00",
                        descricaoMetodologia = "QUIMIOLUMINESCÊNCIA",
                        descricaoRegiaoColeta = null,
                        dataHoraLiberacaoClinica = LocalDateTime.parse("2020-05-29T11:40:00"),
                        nomeLiberadorClinico = "CRF/SP:  00.111 Dr. Teste",
                        observacao1 = "teste",
                        observacao2 = null,
                        observacao3 = null,
                        observacao4 = null,
                        observacao5 = null,
                        material = "SORO",
                        identificacaoExameApoiado = null,
                        materialApoiado = null,
                        descricaoMaterialApoiado = null,
                        descricaoExameApoiado = null,
                        listaResultadoText = listOf(
                            DbResultadoTexto(
                                codigoParametroDB = "RES2",
                                descricaoParametroDB = "TSH - HORMÔNIO TIREOESTIMULANTE",
                                valorResultado = "1",
                                unidadeMedida = "µUI/mL",
                                valorReferencia = "Prematuros (28 a 36 semanas): 0,70 a 27,00 µUI/mL\n" +
                                        "Recém nascidos  (1 a 4 dias): 1,00 a 39,00 µUI/mL\n" +
                                        "2 a 20 semanas..............: 1,70 a  9,10 µUI/mL\n" +
                                        "5 meses a 20 anos...........: 0,70 a  6,40 µUI/mL\n" +
                                        "\n" +
                                        "Adultos: 0,38 a 5,33 µUI/mL\n" +
                                        "\n" +
                                        "Gravidez:\n" +
                                        "1° trimestre: 0,05 a 3,70 µUI/mL\n" +
                                        "2° trimestre: 0,31 a 4,35 µUI/mL\n" +
                                        "3º trimestre: 0,41 a 5,18 µUI/mL"
                            )
                        )
                    )
                )
            )
        )

        val personHealthEventRepo = repoFactory.get(PersonHealthEventTable::class)
        personHealthEventRepo.add(
            PersonHealthEventTable(
                personId = personToTest.second.personHiToken,
                category = PersonHealthEventCategory.AA_FOLLOW_UP,
                title = "PersonModel health event Title test #1",
                description = "PersonModel health event Description test #1",
                status = PersonHealthEventStatus.NOT_STARTED,
                dueDate = LocalDateTime.now().plusDays(2),
                eventDate = LocalDateTime.now().plusDays(2),
                healthcareTeamId = healthcareTeamTable.id,
                staffId = healthcareTeamTable.physicianStaffId
            )
        )

        personHealthEventRepo.add(
            PersonHealthEventTable(
                personId = personToTest.second.personHiToken,
                category = PersonHealthEventCategory.APPOINTMENT_IMMERSION,
                title = "PersonModel health event Title test #2",
                description = "PersonModel health event Description test #2",
                status = PersonHealthEventStatus.NOT_STARTED,
                dueDate = LocalDateTime.now().plusDays(2),
                eventDate = LocalDateTime.now().plusDays(1),
                healthcareTeamId = healthcareTeamTable.id,
                staffId = healthcareTeamTable.physicianStaffId
            )
        )

        val personInternalReferenceRepo = repoFactory.get(PersonInternalReferenceTable::class)
        personInternalReferenceRepo.add(
            PersonInternalReferenceTable(
                personId = personToTest.second.personHiToken,
                channelPersonId = UUID.fromString("7481c36b-85be-45b7-a2e9-a88f9e91d100"),
                internalCode = "CN19090"
            )
        )

        seedAppointmentScheduleOptions(physician1.id, nurse1.id)

        val personAdditionalInfoRepo = repoFactory.get(PersonAdditionalInfoTable::class)
        personAdditionalInfoRepo.add(
            PersonAdditionalInfoTable(
                personId = personToTest.second.personPiiToken,
                emergencyContactName = "Jorge",
                emergencyContactRelationship = "Pai",
                emergencyContactEmail = "jorge@jorjão.jorge.com",
                emergencyContactPhone = "11912345678",
                children = 2,
                education = "Superior",
                occupation = "Sonhador",
                relationship = "Solteiro",
                livesWith = "Pais e filhos"
            )
        )

        val curiosityNoteRepo = repoFactory.get(CuriosityNoteTable::class)
        curiosityNoteRepo.add(
            CuriosityNoteTable(
                personId = personToTest.second.personHiToken,
                curiosity = "Gosta de animais e de mitologia"
            )
        )

        val medicalSpecialtyRepo = repoFactory.get(MedicalSpecialtyTable::class)
        val medicalSpecialtyId = RangeUUID.generate()
        medicalSpecialtyRepo.add(
            MedicalSpecialtyTable(
                id = medicalSpecialtyId,
                name = "Ortopedia",
                type = MedicalSpecialtyType.SPECIALTY,
                urlSlug = "ortopedia"
            )
        )

        val providerId = RangeUUID.generate()
        val providerRepo = repoFactory.get(ProviderTable::class)
        providerRepo.add(
            ProviderTable(
                name = "fleury",
                id = providerId,
                site = "www.fleury.com.br",
                type = ProviderType.HOSPITAL
            )
        )
        seedTestCodes(providerId)

        seedHealthForm()

        seedHaocResults(personToTest.second)

        seedFleuryProcess("112233", personToTest.second)
        seedFleuryTestResult("112233", personToTest.second)
    }

    private fun seedAppointmentScheduleOptions(physicianStaffId: UUID, nurseStaffId: UUID) {
        val appointmentScheduleOptionRepo = repoFactory.get(AppointmentScheduleOptionTable::class)

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Imersão com o Time de Saúde",
                calendarUrl = "https://calendar.alice.com.br/imercao",
                imageUrl = "https://assets.alice.com.br/imercao.jpg",
                type = AppointmentScheduleType.IMMERSION,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Retorno com Taline",
                calendarUrl = "https://calendar.alice.com.br/taline",
                imageUrl = "https://assets.alice.com.br/taline.jpg",
                type = AppointmentScheduleType.HEALTHCARE_TEAM,
                staffId = physicianStaffId,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Conexão com Silvia",
                description = "Acompanhamento trimestral",
                calendarUrl = "https://calendar.alice.com.br/silvia",
                imageUrl = "https://assets.alice.com.br/silvia.jpg",
                type = AppointmentScheduleType.HEALTHCARE_TEAM,
                staffId = nurseStaffId,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Fisioterapeutas",
                calendarUrl = "https://calendar.alice.com.br/fisio",
                imageUrl = "https://assets.alice.com.br/fisio.jpg",
                type = AppointmentScheduleType.PHYSIOTHERAPIST,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Leo Fukuda",
                calendarUrl = "https://calendar.alice.com.br/fukuda",
                imageUrl = "https://assets.alice.com.br/fukuda.jpg",
                type = AppointmentScheduleType.PHYSICAL_EDUCATOR,
                staffId = RangeUUID.generate(),
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Caio Sadao",
                calendarUrl = "https://calendar.alice.com.br/sadao",
                imageUrl = "https://assets.alice.com.br/sadao.jpg",
                type = AppointmentScheduleType.PHYSICAL_EDUCATOR,
                staffId = RangeUUID.generate(),
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Ale Carreiro",
                description = "Nutrição comportamental",
                calendarUrl = "https://calendar.alice.com.br/ale_carreiro",
                imageUrl = "https://assets.alice.com.br/ale_carreiro.jpg",
                type = AppointmentScheduleType.NUTRITIONIST,
                staffId = RangeUUID.generate(),
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Ale Godoy",
                description = "Nutrição esportiva",
                calendarUrl = "https://calendar.alice.com.br/ale_godoy",
                imageUrl = "https://assets.alice.com.br/ale_godoy.jpg",
                type = AppointmentScheduleType.NUTRITIONIST,
                staffId = RangeUUID.generate(),
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Jacque Wahrhaftig",
                description = "Nutrição",
                calendarUrl = "https://calendar.alice.com.br/jacqueline",
                imageUrl = "https://assets.alice.com.br/jacqueline.jpg",
                type = AppointmentScheduleType.NUTRITIONIST,
                staffId = RangeUUID.generate(),
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Bioimpedância",
                calendarUrl = "https://calendar.alice.com.br/bioimpedancia_casa_alice",
                imageUrl = "https://assets.alice.com.br/test.jpg",
                type = AppointmentScheduleType.TEST,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Exames Laboratoriais",
                description = "Sangue, Urina e Fezes",
                calendarUrl = "https://calendar.alice.com.br/exames_laboratoriais_casa_alice",
                imageUrl = "https://assets.alice.com.br/test.jpg",
                type = AppointmentScheduleType.TEST,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Eletrocardiograma",
                calendarUrl = "https://calendar.alice.com.br/eletrocardiograma_casa_alice",
                imageUrl = "https://assets.alice.com.br/test.jpg",
                type = AppointmentScheduleType.TEST,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Outros",
                imageUrl = "https://assets.alice.com.br/test.jpg",
                type = AppointmentScheduleType.TEST,
                active = true,
                showOnApp = true
            )
        )

        appointmentScheduleOptionRepo.add(
            buildAppointmentScheduleOptionTable(
                title = "Médicos especialistas",
                imageUrl = "https://assets.alice.com.br/health_community.jpg",
                type = AppointmentScheduleType.COMMUNITY,
                active = true,
                showOnApp = true
            )
        )
    }

    private val charPool: List<Char> = ('A'..'Z').toList()
    private fun providerCode() =
        (1..4).map { kotlin.random.Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")

    private fun seedTestCodes(providerId: UUID) {
        val testCodeTableRepo = repoFactory.get(TestCodeTable::class)
        val providerTestCodeTableRepo = repoFactory.get(ProviderTestCodeTable::class)
        val testCodePackageTableRepo = repoFactory.get(TestCodePackageTable::class)

        val testCodeList = mutableListOf<TestCodeTable>()

        repeat(108) { index ->
            val testCode = if (index % 2 == 0) {
                TestCodeTable(
                    code = "test code $index",
                    description = "description test code $index",
                    internalDescription = "internal description test code $index",
                    priority = false,
                    sensitiveResult = false,
                    resultExpirationInDays = 180,
                    active = true,
                )
            } else {
                TestCodeTable(
                    code = "exame $index",
                    description = "descrição do exame $index",
                    internalDescription = "descrição interna do exame $index",
                    priority = false,
                    sensitiveResult = false,
                    resultExpirationInDays = 180,
                    active = true,
                )
            }

            if (index % 3 == 0) {
                testCodeList.add(testCode)
            }

            testCodeTableRepo.add(testCode).get()
            providerTestCodeTableRepo.add(
                ProviderTestCodeTable(
                    testCodeId = testCode.id,
                    providerId = providerId,
                    providerCode = providerCode(),
                    providerBrand = ProviderTestCode.Brand.DB
                )
            ).get()
        }

        repeat(3) { index ->
            val group = TestCodePackageTable(
                name = "Pacote $index",
                testCodeIds = testCodeList.subList(0, 10).map { it.id }.toSet()
            )

            testCodeList.removeAll(testCodeList.subList(0, 10))
            testCodePackageTableRepo.add(group)
        }
    }

    private fun seedHealthForm() {
        val healthFormTableRepo = repoFactory.get(HealthFormTable::class)
        val healthFormQuestionTableRepo = repoFactory.get(HealthFormQuestionTable::class)
        val healthFormSectionTableRepo = repoFactory.get(HealthFormSectionTable::class)

        val form = HealthFormTable(
            id = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
            name = "Pre Imersão",
            key = "PRE_IMMERSION",
            type = FormType.HEALTH
        )

        healthFormTableRepo.add(form)

        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Estado atual",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/body.png",
                id = "928d347b-b5f5-4b77-a1fb-cf76a4454200".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Medicamentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/pill.png",
                id = "419840ca-5a1d-49ba-a666-eece03201e00".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Alergias",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/emergency.png",
                id = "8c657288-0727-46de-972e-f2542ee6b200".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Sexo",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/empty.png",
                id = "c475b6d4-a48a-47f2-af84-48ae40bde300".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Saúde da mulher",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/ginecologia.png",
                id = "859c8fa2-df9e-4a7e-aa0e-d0244f8ebc00".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Euroqol",
                details = "Euroqol é um protocolo internacional que tem como finalidade monitorar sua saúde pela vida toda",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/mental.png",
                id = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "GAD-2 / PHQ-2",
                details = "O GAD-2 e PHQ-2 são protocolos de monitoramento da saúde mental.",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/mental.png",
                id = "958965c9-5524-46ad-a987-0fed73efbe00".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Antecedentes familiares",
                details = "Para ajudar na prevenção da sua saúde, queremos saber sobre o seu histórico familiar.",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/paper.png",
                id = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Atividade Física",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/sport.png",
                id = "caee1dc7-3e33-4ac8-8f25-74a494b63200".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Sono",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/sleep.png",
                id = "ce5c21c0-52c9-406d-ae07-d93a64adb000".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Alimentação",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/food.png",
                id = "2e4edebc-ce6a-4c62-a0fd-c030430b4700".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Consumos e vícios",
                details = "As próximas perguntas são sobre bebidas alcoólicas, cigarro e drogas recreativa",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/lab.png",
                id = "eefaa87c-dd92-48f3-978a-2e830e22d100".toUUID()
            )
        )
        healthFormSectionTableRepo.add(
            HealthFormSectionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                title = "Geral",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_form/pda.png",
                id = "c3752c22-81b7-4581-bacc-571eafb79200".toUUID()
            )
        )

        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "cbbd1a09-d2f9-4a09-9f34-c86bea112a00".toUUID(),
                active = true,
                healthFormSectionId = "928d347b-b5f5-4b77-a1fb-cf76a4454200".toUUID(),
                question = "Tem algo te incomodando agora que você gostaria de tratar? {Pode ser alguma dor, desconforto, qualquer coisa!}",
                summaryQuestion = "Tem algo específico que gostaria de tratar?",
                details = "Tem algo te incomodando agora que você gostaria de tratar? {Pode ser alguma dor, desconforto, qualquer coisa!}",
                index = 1,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 2, value = "Sim", label = "Sim"),
                    HealthFormQuestionOption(next = 2, value = "Não", label = "Não")
                ),
                defaultNext = 2
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "a9ee270d-ce56-42c7-b4a3-8aa1c6b4f700".toUUID(),
                active = true,
                healthFormSectionId = "419840ca-5a1d-49ba-a666-eece03201e00".toUUID(),
                question = "Você toma algum medicamento de uso contínuo? Se sim, por favor nos especifique qual(is) e a(s) respectiva(s) dose(s), conforme exemplo: medicamento X, 25 mg diariamente.",
                summaryQuestion = "Medicamento de uso contínuo",
                details = "Você toma algum medicamento de uso contínuo? Se sim, por favor nos especifique qual(is) e a(s) respectiva(s) dose(s), conforme exemplo: medicamento X, 25 mg diariamente.",
                index = 2,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 3, value = "Sim", label = "Sim"),
                    HealthFormQuestionOption(next = 3, value = "Não", label = "Não")
                ),
                defaultNext = 3
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "8d1cdfc8-ed62-4f44-a2e9-02e53fd14300".toUUID(),
                active = true,
                healthFormSectionId = "8c657288-0727-46de-972e-f2542ee6b200".toUUID(),
                question = "Você tem alergia a alguma medicação, alimento ou material? Se sim, detalhe suas alergias.",
                summaryQuestion = "Alergias?",
                details = "Você tem alergia a alguma medicação, alimento ou material",
                index = 3,
                type = MULTIPLE_OPTIONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 4,
                        value = "Não tenho alergias",
                        label = "Não tenho alergias"
                    ),
                    HealthFormQuestionOption(next = 4, value = "Alimento", label = "Alimento"),
                    HealthFormQuestionOption(
                        next = 4,
                        value = "Material (como metal ou látex)",
                        label = "Material (como metal ou látex)"
                    ),
                    HealthFormQuestionOption(next = 4, value = "Medicação", label = "Medicação"),
                    HealthFormQuestionOption(next = 4, value = "Outros", label = "Outros")
                ),
                defaultNext = 4
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "fb518835-b116-487f-8ff0-8ee4ab497d00".toUUID(),
                active = true,
                healthFormSectionId = "c475b6d4-a48a-47f2-af84-48ae40bde300".toUUID(),
                question = "Sobre sexo ... você é sexualmente ativa(o)?",
                summaryQuestion = "Sexualmente ativa(o)",
                details = "Sobre sexo ... você é sexualmente ativa(o)?",
                index = 4,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 5, value = "Sim", label = "Sim"),
                    HealthFormQuestionOption(next = 5, value = "Não", label = "Não")
                ),
                defaultNext = 5
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "07c8b672-82c8-4b24-a279-2402a1020400".toUUID(),
                active = true,
                healthFormSectionId = "c475b6d4-a48a-47f2-af84-48ae40bde300".toUUID(),
                question = "Ainda sobre sexo... você utiliza algum método contraceptivo? Se sim, qual?",
                summaryQuestion = "Método contraceptivo",
                details = "Ainda sobre sexo... você utiliza algum método contraceptivo? Se sim, qual?",
                index = 5,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 6, value = "Sim", label = "Sim"),
                    HealthFormQuestionOption(next = 6, value = "Não", label = "Não")
                ),
                defaultNext = 6
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "faca8fe5-6f4d-435f-87ff-0bb9dce6c800".toUUID(),
                active = true,
                healthFormSectionId = "859c8fa2-df9e-4a7e-aa0e-d0244f8ebc00".toUUID(),
                question = "Qual foi a data da sua última menstruação (pode ser aproximada)",
                summaryQuestion = "Data última menstruação",
                details = "Qual foi a data da sua última menstruação (pode ser aproximada)",
                index = 6,
                type = DATE,
                options = listOf(HealthFormQuestionOption(next = 7)),
                validations = mapOf(SEX to FEMALE),
                defaultNext = 7
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "d93d2a75-911c-4af2-8b18-2556aa2bc800".toUUID(),
                active = true,
                healthFormSectionId = "859c8fa2-df9e-4a7e-aa0e-d0244f8ebc00".toUUID(),
                question = "E qual a data do seu último exame de papanicolau? (pode ser aproximado)",
                summaryQuestion = "Data último papanicolau",
                details = "E qual a data do seu último exame de papanicolau? (pode ser aproximado)",
                index = 7,
                type = DATE,
                options = listOf(HealthFormQuestionOption(next = 8)),
                validations = mapOf(SEX to FEMALE),
                defaultNext = 8
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "e3dd8471-84ff-4864-ae5b-6a1069358100".toUUID(),
                active = true,
                healthFormSectionId = "859c8fa2-df9e-4a7e-aa0e-d0244f8ebc00".toUUID(),
                question = "Você já engravidou? Se sim, quantas vezes?",
                summaryQuestion = "Gravidez",
                details = "Você já engravidou? Se sim, quantas vezes?",
                index = 8,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 9, value = "Sim", label = "Sim"),
                    HealthFormQuestionOption(next = 9, value = "Não", label = "Não")
                ),
                validations = mapOf(SEX to FEMALE),
                defaultNext = 82
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "d119d243-ab97-4567-a07d-86b8c9d59b00".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Sobre sua mobilidade HOJE",
                summaryQuestion = "Mobilidade",
                details = "Sobre sua mobilidade HOJE",
                index = 9,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 10,
                        value = "Não tenho problemas em andar",
                        label = "Não tenho problemas em andar"
                    ),
                    HealthFormQuestionOption(
                        next = 10,
                        value = "Tenho problemas leves em andar",
                        label = "Tenho problemas leves em andar"
                    ),
                    HealthFormQuestionOption(
                        next = 10,
                        value = "Tenho problemas moderados em andar",
                        label = "Tenho problemas moderados em andar"
                    ),
                    HealthFormQuestionOption(
                        next = 10,
                        value = "Tenho problemas graves em andar",
                        label = "Tenho problemas graves em andar"
                    ),
                    HealthFormQuestionOption(next = 10, value = "Sou incapaz de andar", label = "Sou incapaz de andar")
                ),
                defaultNext = 10
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "85ca67d6-fc21-49da-a9f3-6b6e7641e000".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Sobre cuidados pessoais HOJE",
                summaryQuestion = "Cuidados Pessoais",
                details = "Sobre cuidados pessoais HOJE",
                index = 10,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 11,
                        value = "Não tenho problemas para me lavar ou me vestir",
                        label = "Não tenho problemas para me lavar ou me vestir"
                    ),
                    HealthFormQuestionOption(
                        next = 11,
                        value = "Tenho problemas leves para me lavar ou me vestir",
                        label = "Tenho problemas leves para me lavar ou me vestir"
                    ),
                    HealthFormQuestionOption(
                        next = 11,
                        value = "Tenho problemas moderados para me lavar ou me vestir",
                        label = "Tenho problemas moderados para me lavar ou me vestir"
                    ),
                    HealthFormQuestionOption(
                        next = 11,
                        value = "Tenho problemas graves para me lavar ou me vestir",
                        label = "Tenho problemas graves para me lavar ou me vestir"
                    ),
                    HealthFormQuestionOption(
                        next = 11,
                        value = "Sou incapaz de me lavar ou me vestir",
                        label = "Sou incapaz de me lavar ou me vestir"
                    )
                ),
                defaultNext = 11
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "9ad5c6fb-0c16-4c31-b8b0-12096c015f00".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Suas atividades habituais HOJE",
                summaryQuestion = "Atividades Habituais",
                details = "Suas atividades habituais HOJE (ex. trabalhar, estudar, etc.)",
                index = 11,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 12,
                        value = "Não tenho problemas em realizar minhas atividades habituais",
                        label = "Não tenho problemas em realizar minhas atividades habituais"
                    ),
                    HealthFormQuestionOption(
                        next = 12,
                        value = "Tenho problemas leves em realizar minhas atividades habituais",
                        label = "Tenho problemas leves em realizar minhas atividades habituais"
                    ),
                    HealthFormQuestionOption(
                        next = 12,
                        value = "Tenho problemas moderados em realizar minhas atividades habituais",
                        label = "Tenho problemas moderados em realizar minhas atividades habituais"
                    ),
                    HealthFormQuestionOption(
                        next = 12,
                        value = "Tenho problemas graves em realizar minhas atividades habituais",
                        label = "Tenho problemas graves em realizar minhas atividades habituais"
                    ),
                    HealthFormQuestionOption(
                        next = 12,
                        value = "Sou incapaz de realizar minhas atividades habituais",
                        label = "Sou incapaz de realizar minhas atividades habituais"
                    )
                ),
                defaultNext = 12
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "deb4346c-954a-4103-8428-82b98ecb9c00".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Sua dor / mal-estar HOJE",
                summaryQuestion = "Dor / Mal-estar",
                details = "Sua dor / mal-estar HOJE",
                index = 12,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 13,
                        value = "Não tenho dor ou mal-estar",
                        label = "Não tenho dor ou mal-estar"
                    ),
                    HealthFormQuestionOption(
                        next = 13,
                        value = "Tenho dores ou mal-estar leves",
                        label = "Tenho dores ou mal-estar leves"
                    ),
                    HealthFormQuestionOption(
                        next = 13,
                        value = "Tenho dores ou mal-estar moderados",
                        label = "Tenho dores ou mal-estar moderados"
                    ),
                    HealthFormQuestionOption(
                        next = 13,
                        value = "Tenho dores ou mal-estar fortes",
                        label = "Tenho dores ou mal-estar fortes"
                    ),
                    HealthFormQuestionOption(
                        next = 13,
                        value = "Tenho dores ou mal-estar extremos",
                        label = "Tenho dores ou mal-estar extremos"
                    )
                ),
                defaultNext = 13
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "26066a9f-c5b8-4a3e-924e-41be8239d500".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Sua ansiedade / depressão HOJE",
                summaryQuestion = "Ansiedade / Depressão",
                details = "Sua ansiedade / depressão HOJE",
                index = 13,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 20,
                        value = "Não estou ansiosa(o) ou deprimida(o)",
                        label = "Não estou ansiosa(o) ou deprimida(o)"
                    ),
                    HealthFormQuestionOption(
                        next = 14,
                        value = "Estou levemente ansiosa(o) ou deprimida(o)",
                        label = "Estou levemente ansiosa(o) ou deprimida(o)"
                    ),
                    HealthFormQuestionOption(
                        next = 14,
                        value = " Estou moderadamente ansiosa(o) ou deprimida(o)",
                        label = " Estou moderadamente ansiosa(o) ou deprimida(o)"
                    ),
                    HealthFormQuestionOption(
                        next = 14,
                        value = "Estou muito ansiosa(o) ou deprimida(o)",
                        label = "Estou muito ansiosa(o) ou deprimida(o)"
                    ),
                    HealthFormQuestionOption(
                        next = 14,
                        value = "Estou extremamente ansiosa(o) ou deprimida(o)",
                        label = "Estou extremamente ansiosa(o) ou deprimida(o)"
                    )
                ),
                defaultNext = 14
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "27dbba71-e79c-41a4-8c15-e2fec2ce8200".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Numa escala de 0 a 100, como você avalia a sua saúde HOJE?",
                summaryQuestion = "Avaliação da saúde",
                details = "Numa escala de 0 a 100, como você avalia a sua saúde HOJE?",
                index = 14,
                type = NUMERIC,
                options = listOf(HealthFormQuestionOption(next = 15, value = "", label = "")),
                defaultNext = 15
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "f1327543-1517-475c-a2ab-97d1ed601b00".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Sentir-se nervosa(o), ansiosa(o) ou muito tensa(o)",
                summaryQuestion = "Sentir-se nervosa(o), ansiosa(o) ou muito tensa(o)",
                details = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Sentir-se nervosa(o), ansiosa(o) ou muito tensa(o)",
                index = 15,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 16, value = "Nenhuma vez", label = "Nenhuma vez"),
                    HealthFormQuestionOption(next = 16, value = "Vários dias", label = "Vários dias"),
                    HealthFormQuestionOption(
                        next = 16,
                        value = "Mais da metade dos dias",
                        label = "Mais da metade dos dias"
                    ),
                    HealthFormQuestionOption(next = 16, value = "Quase todos os dias", label = "Quase todos os dias")
                ),
                defaultNext = 16
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "91fd1ebf-e10e-4229-a274-38aea1548100".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Não ser capaz de impedir ou controlar as preocupações",
                summaryQuestion = "Não ser capaz de impedir ou controlar as preocupações",
                details = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Não ser capaz de impedir ou controlar as preocupações",
                index = 16,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 17, value = "Nenhuma vez", label = "Nenhuma vez"),
                    HealthFormQuestionOption(next = 17, value = "Vários dias", label = "Vários dias"),
                    HealthFormQuestionOption(
                        next = 17,
                        value = "Mais da metade dos dias",
                        label = "Mais da metade dos dias"
                    ),
                    HealthFormQuestionOption(next = 17, value = "Quase todos os dias", label = "Quase todos os dias")
                ),
                defaultNext = 17
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "e2c446c0-904e-4d39-8400-c99a8542ce00".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Ter pouco interesse ou prazer ao fazer as coisas",
                summaryQuestion = "Ter pouco interesse ou prazer ao fazer as coisas",
                details = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Ter pouco interesse ou prazer ao fazer as coisas",
                index = 17,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 18, value = "Nenhuma vez", label = "Nenhuma vez"),
                    HealthFormQuestionOption(next = 18, value = "Vários dias", label = "Vários dias"),
                    HealthFormQuestionOption(
                        next = 18,
                        value = "Mais da metade dos dias",
                        label = "Mais da metade dos dias"
                    ),
                    HealthFormQuestionOption(next = 18, value = "Quase todos os dias", label = "Quase todos os dias")
                ),
                defaultNext = 18
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "5a0550d1-e907-44dc-ab9d-a28678456e00".toUUID(),
                active = true,
                healthFormSectionId = "3b0dd651-7276-4dd4-a8cf-01375f2bda00".toUUID(),
                question = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Sentir-se pra baixo, deprimida(o) ou sem perspectiva",
                summaryQuestion = "Sentir-se pra baixo, deprimida(o) ou sem perspectiva",
                details = "Durante as últimas 2 semanas, com que frequência você foi incomodada(o) por: Sentir-se pra baixo, deprimida(o) ou sem perspectiva",
                index = 18,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 19, value = "Nenhuma vez", label = "Nenhuma vez"),
                    HealthFormQuestionOption(next = 19, value = "Vários dias", label = "Vários dias"),
                    HealthFormQuestionOption(
                        next = 19,
                        value = "Mais da metade dos dias",
                        label = "Mais da metade dos dias"
                    ),
                    HealthFormQuestionOption(next = 19, value = "Quase todos os dias", label = "Quase todos os dias")
                ),
                defaultNext = 19
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "d0f6ba32-e956-4ab7-99e2-762377e35000".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Acidente Vascular Cerebral (AVC ou derrame)? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Acidente Vascular Cerebral (AVC ou derrame)",
                details = "Alguém na sua família tem ou teve Acidente Vascular Cerebral (AVC ou derrame)? Pode selecionar todas as opções aplicáveis.",
                index = 19,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 20, value = "", label = ""),
                    HealthFormQuestionOption(next = 20, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 20, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 20, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 20, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 20, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 20, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 20
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "510722a4-986d-4f2a-8fac-90cfc0441600".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Infarto? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Infarto",
                details = "Alguém na sua família tem ou teve Infarto? Pode selecionar todas as opções aplicáveis.",
                index = 20,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 21, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 21, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 21, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 21, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 21, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 21, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 21
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "91fb5e86-5ff2-48d9-b12b-0160ee2e1e00".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Pressão alta? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Pressão alta",
                details = "Alguém na sua família tem ou teve Pressão alta? Pode selecionar todas as opções aplicáveis.",
                index = 21,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 22, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 22, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 22, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 22, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 22, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 22, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 22
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "ff30e5f8-196a-458d-bbcd-60a418b92600".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Diabetes? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Diabetes",
                details = "Alguém na sua família tem ou teve Diabetes? Pode selecionar todas as opções aplicáveis.",
                index = 22,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 23, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 23, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 23, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 23, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 23, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 23, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 23
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "d4bda578-d38a-4e94-8a92-03db098ccf00".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Doenças reumatológicas (como lupus, artrite reumatoide, espondilite anquilosnate)? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Doenças reumatológicas (como lupus, artrite reumatoide, espondilite anquilosnate)",
                details = "Alguém na sua família tem ou teve Doenças reumatológicas (como lupus, artrite reumatoide, espondilite anquilosnate)? Pode selecionar todas as opções aplicáveis.",
                index = 23,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 24, value = "", label = ""),
                    HealthFormQuestionOption(next = 24, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 24, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 24, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 24, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 24, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 24, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 24
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "92032dd9-6412-4f9e-9a81-b21d2b6fed00".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Câncer de mama? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Câncer de mama",
                details = "Alguém na sua família tem ou teve Câncer de mama? Pode selecionar todas as opções aplicáveis.",
                index = 24,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 25, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 25, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 25, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 25, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 25, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 25, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 25
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "62795938-1f87-4a8a-9265-6a219c3a6d00".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Câncer de Estômago? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Câncer de Estômago",
                details = "Alguém na sua família tem ou teve Câncer de Estômago? Pode selecionar todas as opções aplicáveis.",
                index = 25,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 26, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 26, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 26, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 26, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 26, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 26, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 26
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "0c4f8c04-76c2-4d44-ab3c-ca4cd9c25900".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Câncer de Colon? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Câncer de Colon",
                details = "Alguém na sua família tem ou teve Câncer de Colon? Pode selecionar todas as opções aplicáveis.",
                index = 26,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 27, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 27, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 27, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 27, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 27, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 27, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 27
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "f29446ce-fb24-4b81-85f9-a2d248698c00".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Câncer de Pulmão? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Câncer de Pulmão",
                details = "Alguém na sua família tem ou teve Câncer de Pulmão? Pode selecionar todas as opções aplicáveis.",
                index = 27,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(next = 28, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 28, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 28, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 28, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 28, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 28, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 28
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "2a6ac9bd-185e-4519-904f-2a0b80d97a00".toUUID(),
                active = true,
                healthFormSectionId = "bbcc5a3d-0935-450f-9bab-1236a3639900".toUUID(),
                question = "Alguém na sua família tem ou teve Outro tipo de Câncer (por favor especifique)? Pode selecionar todas as opções aplicáveis.",
                summaryQuestion = "Outro tipo de Câncer (por favor especifique)",
                details = "Alguém na sua família tem ou teve Outro tipo de Câncer (por favor especifique)? Pode selecionar todas as opções aplicáveis.",
                index = 28,
                type = MULTIPLE_OPTIONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 29, value = "Pai", label = "Pai"),
                    HealthFormQuestionOption(next = 29, value = "Mãe", label = "Mãe"),
                    HealthFormQuestionOption(next = 29, value = "Avôs paternos", label = "Avôs paternos"),
                    HealthFormQuestionOption(next = 29, value = "Avôs maternos", label = "Avôs maternos"),
                    HealthFormQuestionOption(next = 29, value = "Irmãos", label = "Irmãos"),
                    HealthFormQuestionOption(next = 29, value = "Ninguém", label = "Ninguém")
                ),
                defaultNext = 29
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "d4cdf33e-21dc-4d50-ab53-be308c5ad100".toUUID(),
                active = true,
                healthFormSectionId = "caee1dc7-3e33-4ac8-8f25-74a494b63200".toUUID(),
                question = "Sobre atividade física... Quantos minutos de atividade física moderada ou intensa você pratica em uma semana? (Dica: Atividade física moderada ou intensa é aquela que você não consegue falar enquanto pratica, fica muito ofegante)",
                summaryQuestion = "Atividade física",
                details = "Sobre atividade física... Quantos minutos de atividade física moderada ou intensa você pratica em uma semana? (Dica: Atividade física moderada ou intensa é aquela que você não consegue falar enquanto pratica, fica muito ofegante)",
                index = 29,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 30,
                        value = "Não pratico atividade física",
                        label = "Não pratico atividade física"
                    ),
                    HealthFormQuestionOption(
                        next = 30,
                        value = " Até 1 hora por semana",
                        label = " Até 1 hora por semana"
                    ),
                    HealthFormQuestionOption(
                        next = 30,
                        value = "De 1 à 3 horas por semana",
                        label = "De 1 à 3 horas por semana"
                    ),
                    HealthFormQuestionOption(
                        next = 30,
                        value = "De 3 a 5 horas por semana",
                        label = "De 3 a 5 horas por semana"
                    ),
                    HealthFormQuestionOption(
                        next = 30,
                        value = "Acima de 5 horas por semana",
                        label = "Acima de 5 horas por semana"
                    )
                ),
                defaultNext = 30
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "55a2a13a-a0d6-4426-bbc0-2de88804e600".toUUID(),
                active = true,
                healthFormSectionId = "ce5c21c0-52c9-406d-ae07-d93a64adb000".toUUID(),
                question = "Sobre sono... você considera o seu descanso reparador?",
                summaryQuestion = "Sono",
                details = "Sono... você considera o seu descanso reparador?",
                index = 30,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 31, value = "Sim", label = "Sim"),
                    HealthFormQuestionOption(next = 31, value = "Não", label = "Não"),
                    HealthFormQuestionOption(next = 31, value = "É irregular", label = "É irregular")
                ),
                defaultNext = 31
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "80a6d243-bc0a-461b-98d4-849c41615200".toUUID(),
                active = true,
                healthFormSectionId = "2e4edebc-ce6a-4c62-a0fd-c030430b4700".toUUID(),
                question = "Qual o seu principal objetivo com a sua alimentação hoje?",
                summaryQuestion = "Objetivo alimentação",
                details = "Qual o seu principal objetivo com a sua alimentação hoje?",
                index = 31,
                type = MULTIPLE_OPTIONS,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 32,
                        value = "Nenhum, estou satisfeito com o jeito que como hoje",
                        label = "Nenhum, estou satisfeito com o jeito que como hoje"
                    ),
                    HealthFormQuestionOption(next = 32, value = "Estética", label = "Estética"),
                    HealthFormQuestionOption(
                        next = 32,
                        value = "Saúde/ Sintomas relacionados com a alimentação",
                        label = "Saúde/ Sintomas relacionados com a alimentação"
                    ),
                    HealthFormQuestionOption(
                        next = 32,
                        value = "Melhora dos meus exames de sangue",
                        label = "Melhora dos meus exames de sangue"
                    ),
                    HealthFormQuestionOption(next = 32, value = "Performance", label = "Performance"),
                    HealthFormQuestionOption(
                        next = 32,
                        value = "Melhorar a relação com meu corpo/comida",
                        label = "Melhorar a relação com meu corpo/comida"
                    ),
                    HealthFormQuestionOption(
                        next = 32,
                        value = "Avaliar como minha alimentação está hoje",
                        label = "Avaliar como minha alimentação está hoje"
                    )
                ),
                defaultNext = 32
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "b2ea2c59-89da-41da-9226-f5af89713100".toUUID(),
                active = true,
                healthFormSectionId = "2e4edebc-ce6a-4c62-a0fd-c030430b4700".toUUID(),
                question = "Com que frequência você se preocupa com a sua alimentação? {o que vai comer, quanto comer, como preparar o alimento ou se deve ou não comer}",
                summaryQuestion = "Preocupação com alimentação",
                details = "Com que frequência você se preocupa com a sua alimentação? {o que vai comer, quanto comer, como preparar o alimento ou se deve ou não comer}",
                index = 32,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 33, value = "Sempre", label = "Sempre"),
                    HealthFormQuestionOption(next = 33, value = "Muito frequentemente", label = "Muito frequentemente"),
                    HealthFormQuestionOption(next = 33, value = "Frequentemente", label = "Frequentemente"),
                    HealthFormQuestionOption(next = 33, value = "Às vezes", label = "Às vezes"),
                    HealthFormQuestionOption(next = 33, value = "Raramente/Nunca", label = "Raramente/Nunca")
                ),
                defaultNext = 33
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "14463540-ad15-41ca-8f87-cbe41b23ad00".toUUID(),
                active = true,
                healthFormSectionId = "2e4edebc-ce6a-4c62-a0fd-c030430b4700".toUUID(),
                question = "Sobre alimentação... você é vegetariano ou vegano?",
                summaryQuestion = "Vegetariano ou vegano?",
                details = "Alimentação... você é vegetariano ou vegano?",
                index = 33,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 34, value = "Não", label = "Não"),
                    HealthFormQuestionOption(next = 34, value = "Vegetariano", label = "Vegetariano"),
                    HealthFormQuestionOption(next = 34, value = "Vegano", label = "Vegano")
                ),
                defaultNext = 34
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "6d1b70dd-6b2b-410e-893c-3f0d4f6b5f00".toUUID(),
                active = true,
                healthFormSectionId = "eefaa87c-dd92-48f3-978a-2e830e22d100".toUUID(),
                question = "Você consome alcool? Se sim em média quantas vezes por semana?",
                summaryQuestion = "Bebida alcoólica (qtd. de doses por semana)",
                details = "Você consome alcool? Se sim em média quantas vezes por semana?",
                index = 34,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(
                        next = 35,
                        value = "Não bebo toda semana",
                        label = "Não bebo toda semana"
                    ),
                    HealthFormQuestionOption(next = 35, value = "1 a 2 doses", label = "1 a 2 doses"),
                    HealthFormQuestionOption(next = 35, value = "3 a 7 doses", label = "3 a 7 doses"),
                    HealthFormQuestionOption(next = 35, value = "Mais de 7 doses", label = "Mais de 7 doses")
                ),
                defaultNext = 35
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "673af9ae-990f-4bd9-9db8-e8c1068ae400".toUUID(),
                active = true,
                healthFormSectionId = "eefaa87c-dd92-48f3-978a-2e830e22d100".toUUID(),
                question = "Com que frequência você toma 'cinco ou mais doses' de uma vez?",
                summaryQuestion = "Bebida alcoólica (5 ou + doses de uma vez)",
                details = "Com que frequência você toma 'cinco ou mais doses' de uma vez?",
                index = 35,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 36, value = "Nunca", label = "Nunca"),
                    HealthFormQuestionOption(
                        next = 36,
                        value = "Menos de 1 vez ao mês",
                        label = "Menos de 1 vez ao mês"
                    ),
                    HealthFormQuestionOption(
                        next = 36,
                        value = "Cerca de 1 vez ao mês",
                        label = "Cerca de 1 vez ao mês"
                    ),
                    HealthFormQuestionOption(
                        next = 36,
                        value = "Cerca de 1 vez por semana",
                        label = "Cerca de 1 vez por semana"
                    ),
                    HealthFormQuestionOption(
                        next = 36,
                        value = "Mais de 1 vez por semana",
                        label = "Mais de 1 vez por semana"
                    )
                ),
                defaultNext = 36
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "aa3a7c7d-7a1a-4952-b6e2-022458c3c400".toUUID(),
                active = true,
                healthFormSectionId = "eefaa87c-dd92-48f3-978a-2e830e22d100".toUUID(),
                question = "Você fuma ou já fumou? Se sim, por quanto tempo fuma / fumou, e qual a média de cigarros você fuma / fumava por dia?",
                summaryQuestion = "Cigarro (sim / não)",
                details = "Você fuma ou já fumou?",
                index = 36,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 37, value = "Nunca fumei", label = "Nunca fumei"),
                    HealthFormQuestionOption(
                        next = 37,
                        value = "Já fumei, mas atualmente não fumo",
                        label = "Já fumei, mas atualmente não fumo"
                    ),
                    HealthFormQuestionOption(
                        next = 37,
                        value = "Fumo e tenho intenção de parar",
                        label = "Fumo e tenho intenção de parar"
                    ),
                    HealthFormQuestionOption(
                        next = 37,
                        value = "Fumo e não tenho intenção de parar",
                        label = "Fumo e não tenho intenção de parar"
                    )
                ),
                defaultNext = 37
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "8bce660a-405b-4aee-8a92-f156e5888900".toUUID(),
                active = true,
                healthFormSectionId = "eefaa87c-dd92-48f3-978a-2e830e22d100".toUUID(),
                question = "Você usa drogas recreativas (como maconha ou outras drogas sintéticas)? Se sim, quais?",
                summaryQuestion = "Drogas recreativas",
                details = "Você usa drogas recreativas (como maconha ou outras drogas sintéticas)? Se sim, quais?",
                index = 37,
                type = OPTION_BUTTONS_AND_TEXT,
                options = listOf(
                    HealthFormQuestionOption(next = 38, value = "Não uso", label = "Não uso"),
                    HealthFormQuestionOption(
                        next = 38,
                        value = "Uso esporadicamente (menos de 1x/mês)",
                        label = "Uso esporadicamente (menos de 1x/mês)"
                    ),
                    HealthFormQuestionOption(
                        next = 38,
                        value = "Uso semanalmente ou diariamente",
                        label = "Uso semanalmente ou diariamente"
                    )
                ),
                defaultNext = 38
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "f31f0ab5-fb3a-40d9-90d3-e67181545500".toUUID(),
                active = true,
                healthFormSectionId = "c3752c22-81b7-4581-bacc-571eafb79200".toUUID(),
                question = "O quão comprometido você está em mudar AGORA sua alimentação?",
                summaryQuestion = "Comprometido para mudar AGORA a alimentação",
                details = "O quão comprometido você está em mudar AGORA a alimentação",
                index = 38,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(next = 39, value = "Nada", label = "Nada"),
                    HealthFormQuestionOption(next = 39, value = "Pouco", label = "Pouco"),
                    HealthFormQuestionOption(next = 39, value = "Mais ou menos", label = "Mais ou menos"),
                    HealthFormQuestionOption(next = 39, value = "Muito", label = "Muito"),
                    HealthFormQuestionOption(next = 39, value = "Extremamente", label = "Extremamente")
                ),
                defaultNext = 39
            )
        )
        healthFormQuestionTableRepo.add(
            HealthFormQuestionTable(
                healthFormId = "278c9247-41d5-4f38-96d0-311454d08f00".toUUID(),
                id = "93d8882f-66e8-434d-b486-245e507a3900".toUUID(),
                active = true,
                healthFormSectionId = "c3752c22-81b7-4581-bacc-571eafb79200".toUUID(),
                question = "O quão comprometido você está em mudar AGORA sua atividade física?",
                summaryQuestion = "Comprometido para mudar AGORA a atividade física",
                details = "O quão comprometido você está em mudar AGORA a atividade física",
                index = 39,
                type = OPTION_BUTTONS,
                options = listOf(
                    HealthFormQuestionOption(value = "Nada", label = "Nada"),
                    HealthFormQuestionOption(value = "Pouco", label = "Pouco"),
                    HealthFormQuestionOption(value = "Mais ou menos", label = "Mais ou menos"),
                    HealthFormQuestionOption(value = "Muito", label = "Muito"),
                    HealthFormQuestionOption(value = "Extremamente", label = "Extremamente")
                )
            )
        )
    }

    private fun seedHaocResults(person: PersonToken) {
        val haocDocumentTableRepo = repoFactory.get(HaocDocumentTable::class)
        val haocPAResultTableRepo = repoFactory.get(HaocProntoAtendimentoResultTable::class)
        val haocSumarioDeAltaResultTableRepo = repoFactory.get(HaocSumarioDeAltaResultTable::class)

        val documentPA = HaocDocumentTable(
            personId = person.personHiToken,
            content = "document content",
            documentIdHash = RangeUUID.generate().toString(),
            documentType = HaocDocumentType.PRONTO_ATENDIMENTO,
            claimId = null
        )
        val documentAlta = HaocDocumentTable(
            personId = person.personHiToken,
            content = "document content",
            documentIdHash = RangeUUID.generate().toString(),
            documentType = HaocDocumentType.SUMARIO_DE_ALTA,
            claimId = null
        )

        val documentPaAdded = haocDocumentTableRepo.add(documentPA).get()
        val documentAltaAdded = haocDocumentTableRepo.add(documentAlta).get()

        val haocPAResult = HaocProntoAtendimentoResultTable(
            personId = person.personHiToken,
            claimId = null,
            motivoAtendimento = "Dor de barriga",
            atendimentos = listOf(
                HaocAtendimento(
                    alergias = listOf(
                        HaocAlergia(
                            agente = "agente",
                            categoria = "categoria"

                        )
                    ),
                    escalasDeAvaliacao = listOf(
                        EscalaAvaliacao(
                            classificacao = "classificação",
                            classificador = "classificador"
                        )
                    ),
                    profissionalDoAtendimento = HaocProfissional(
                        nome = "Dr. Shawn Murphy",
                        ocupacao = "Clinico Geral",
                        uf = "SP",
                        conselho = "CRM",
                        numeroRegistro = "987654321"
                    )
                )
            ),
            caracterizacaoAtendimento = CaracterizacaoAtendimento(
                local = Unidade.PAULISTA,
                procedencia = "procedencia",
                dataChegada = LocalDateTime.of(2021, 4, 4, 11, 11)
            ),
            desfecho = HaocDesfecho(
                motivo = "motivo",
                dataSaida = LocalDateTime.of(2021, 4, 5, 9, 11),
                profissionalDeAlta = HaocProfissional(
                    nome = "Dr. Shawn Murphy",
                    ocupacao = "Clinico Geral",
                    uf = "SP",
                    conselho = "CRM",
                    numeroRegistro = "987654321"
                )
            ),
            haocDocumentId = documentPaAdded.id
        )

        val haocAltaResult = HaocSumarioDeAltaResultTable(
            personId = person.personHiToken,
            claimId = null,
            motivoAdmissao = HaocMotivoAdmissao(
                diagnostico = emptyList(),
            ),
            caracterizacaoAtendimento = HaocCaracterizacaoChegada(
                procedencia = "procedencia",
                local = Unidade.PAULISTA.name,
                caraterDaInternacao = "caraterDaInternacao",
                dataInternacao = LocalDateTime.of(2021, 4, 5, 9, 11)
            ),
            evolucaoClinica = "evolucaoClinica",
            desfecho = HaocDesfechoInternacao(
                motivo = "motivo",
                dataSaida = LocalDateTime.of(2021, 4, 6, 9, 11),
                diasUTI = 1,
                profissionalDeAlta = HaocProfissional(
                    nome = "Dr. Shawn Murphy",
                    ocupacao = "Clinico Geral",
                    uf = "SP",
                    conselho = "CRM",
                    numeroRegistro = "987654321"
                )
            ),
            haocDocumentId = documentAltaAdded.id
        )

        haocPAResultTableRepo.add(haocPAResult).get()
        haocSumarioDeAltaResultTableRepo.add(haocAltaResult).get()

    }

    private fun seedFleuryProcess(claimId: String, person: PersonToken) {
        val fleuryProcessRepo = repoFactory.get(FleuryProcessTable::class)

        val fleuryProcessTable = FleuryProcessTable(
            claimId = claimId,
            personId = person.personHiToken,
            status = FleuryProcessType.PENDING,
            executionGroupId = RangeUUID.generate(),
            totvsGuiaId = RangeUUID.generate(),
        )

        fleuryProcessRepo.add(fleuryProcessTable).get()
    }

    private fun seedFleuryTestResult(claimId: String, person: PersonToken) {
        val fleuryTestResultRepo = repoFactory.get(FleuryTestResultTable::class)

        val fleuryTestResultTable1 = FleuryTestResultTable(
            personId = person.personHiToken,
            idFicha = "1200020699",
            idItem = "900",
            idProduto = "100",
            idUnidade = "200",
            claimId = claimId,
            procedimento = "Homocisteína, plasma",
            type = FleuryResultType.STRUCTURED,
            laudos = listOf(
                FleuryLaudo(
                    analitoId = "40",
                    nome = "Homocisteína",
                    nomeReduzido = "Homocisteína",
                    unidadeMedida = "micromol/L",
                    listaFaixaReferenciaCritica = emptyList(),
                    listaFaixaReferenciaNormal = listOf(
                        FaixaReferenciaNormal(
                            descricao = "amostra basal: 5,0 a 14,0",
                            limiteInferior = "5,0",
                            limiteSuperior = "14,0",
                            idicativoFaixaReferenciaQualitativa = false,
                            idicativoFaixaNormal = true,
                            ordemExibicao = "24161",
                            unidadeMedida = "micromol/L",
                        ),
                        FaixaReferenciaNormal(
                            descricao = "Desejável: inferior a 10",
                            limiteSuperior = "10",
                            idicativoFaixaReferenciaQualitativa = false,
                            idicativoFaixaNormal = false,
                            ordemExibicao = "24162",
                            unidadeMedida = "micromol/L",
                        ),
                        FaixaReferenciaNormal(
                            descricao = "6 horas após sobrecarga oral de metionin: 18,0 a 38,0",
                            limiteInferior = "18",
                            limiteSuperior = "38",
                            idicativoFaixaReferenciaQualitativa = false,
                            idicativoFaixaNormal = false,
                            ordemExibicao = "24163",
                            unidadeMedida = "micromol/L",
                        )
                    ),
                    resultado = "10.8",
                    indicativoVisualizacao = true,
                    codigoCondicaoResultado = "N",
                    codigoTipoResultado = "NM"
                )
            )
        )

        val fleuryTestResultTable2 = FleuryTestResultTable(
            personId = person.personHiToken,
            idFicha = "1200020699",
            idItem = "100",
            idProduto = "200",
            idUnidade = "200",
            claimId = claimId,
            procedimento = "Tromboplastina Parcial Ativada, Tempo de, plasma",
            type = FleuryResultType.STRUCTURED,
            laudos = listOf(
                FleuryLaudo(
                    analitoId = "173",
                    nome = "TTPA",
                    nomeReduzido = "TTPA",
                    listaFaixaReferenciaCritica = emptyList(),
                    listaFaixaReferenciaNormal = listOf(
                        FaixaReferenciaNormal(
                            idicativoFaixaReferenciaQualitativa = false,
                            idicativoFaixaNormal = false,
                            ordemExibicao = "24163",
                        )
                    ),
                    resultado = "40.6",
                    indicativoVisualizacao = true,
                    codigoCondicaoResultado = "N",
                    codigoTipoResultado = "NM"
                ),
                FleuryLaudo(
                    analitoId = "174",
                    nome = "TTPA-Paciente/Normal",
                    nomeReduzido = "TTPA-Paciente/Normal",
                    listaFaixaReferenciaCritica = emptyList(),
                    listaFaixaReferenciaNormal = listOf(
                        FaixaReferenciaNormal(
                            descricao = "0,86 a 1,19",
                            limiteInferior = "0,86",
                            limiteSuperior = "1,19",
                            idicativoFaixaReferenciaQualitativa = false,
                            idicativoFaixaNormal = false,
                            ordemExibicao = "26402",
                        )
                    ),
                    resultado = "1.16",
                    indicativoVisualizacao = true,
                    codigoCondicaoResultado = "N",
                    codigoTipoResultado = "NM"
                ),
                FleuryLaudo(
                    analitoId = "218",
                    nome = "TTPA-Normal do dia",
                    nomeReduzido = "TTPA-Normal do dia",
                    listaFaixaReferenciaCritica = emptyList(),
                    listaFaixaReferenciaNormal = listOf(
                        FaixaReferenciaNormal(
                            idicativoFaixaReferenciaQualitativa = false,
                            idicativoFaixaNormal = false,
                            ordemExibicao = "30240",
                        )
                    ),
                    resultado = "35.0",
                    indicativoVisualizacao = true,
                    codigoCondicaoResultado = "N",
                    codigoTipoResultado = "NM"
                )
            ),
        )
        fleuryTestResultRepo.add(fleuryTestResultTable1)
        fleuryTestResultRepo.add(fleuryTestResultTable2)

    }
}
