package br.com.alice.data.layer

import br.com.alice.common.Database
import br.com.alice.common.DatabaseMigrator
import br.com.alice.common.core.exceptions.DatabaseException
import br.com.alice.common.core.extensions.toSnakeCase
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.services.PersonNonPiiToken
import br.com.alice.data.layer.services.PersonPiiToken
import br.com.alice.data.layer.tables.BookTable
import br.com.alice.data.layer.tables.ConsolidatedAccreditedNetworkTable
import br.com.alice.data.layer.tables.HrMemberUploadTrackingTable
import br.com.alice.data.layer.tables.PersonTokenTable
import br.com.alice.data.layer.tables.StructuredAddressTable
import br.com.alice.data.layer.tables.Table
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.reflections.Reflections
import java.time.temporal.Temporal
import java.util.UUID
import kotlin.reflect.KClass
import kotlin.reflect.KType
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.jvmErasure
import kotlin.test.Test

class TablesTest {

    private val mainJdbi = Database.get(DatabaseMigrator.migrate("main"))

    private val query = "SELECT column_name, column_default, is_nullable, data_type FROM information_schema.columns  WHERE table_name = :tableName"

    private val skipTables = listOf(
        ConsolidatedAccreditedNetworkTable::class, // exclude because `geo_location` doesn't exists in test environment
        StructuredAddressTable::class, // exclude because `geo_location` doesn't exists in test environment
        HrMemberUploadTrackingTable::class, // hr_member_upload_tracking.updated_by doesn't exist on table
        BookTable::class, // exclude because `geo_location` doesn't exists in test environment
        PersonTokenTable::class // doesn't exists in main db
    )

    @Test
    fun `#validate table classes with database tables`() = runBlocking<Unit> {
        tableModels()
            .map { tableClass ->

                val columns = describe(tableClass)

                validateModelToDatabase(tableClass, columns)
                validateColumns(tableClass, columns)
            }
    }

    private fun validateColumns(
        tableClass: KClass<out Table<*>>,
        columns: Map<String, ColumnDescribe>,
    ) {
        val properties = tableClass.memberProperties

        if (columns.size > properties.size) {
            val propertiesString = properties.map { it.name.toSnakeCase() }

            val columnsNotMapped = columns
                .filterNot { propertiesString.contains(it.key) }
                .filterNot { it.value.isNullable || it.value.hasDefault }

            if (columnsNotMapped.isNotEmpty()) throw Exception(
                "Table: ${tableClass.name()} have NOT NULL without DEFAULT VALUE columns: ${columnsNotMapped.keys}"
            )
        }
    }

    private fun validateModelToDatabase(
        tableClass: KClass<out Table<*>>,
        columns: Map<String, ColumnDescribe>
    ) {
        tableClass.memberProperties.forEach { property ->
            val propertyName = property.name.toSnakeCase()
            val column = columns[propertyName] ?: throw Exception(
                "${tableClass.simpleName} ${tableClass.name()}.$propertyName doesn't exist on table"
            )

            assertThat(column.dataType)
                .`as`("${tableClass.name()}.$propertyName invalid type: ${column.dataType}")
                .isIn(property.returnType.asDbType())
        }
    }

    private fun tableModels() = Reflections("br.com.alice.data.layer.tables")
        .getSubTypesOf(Table::class.java)
        .map { it.kotlin }
        .filterNot { skipTables.contains(it) || it.java.isInterface }

    private fun describe(tableName: KClass<out Table<*>>) =
        mainJdbi.withHandle<List<ColumnDescribe>, DatabaseException> { handler ->
            handler.createQuery(query)
                .bind("tableName", tableName.name())
                .map { rs, _ ->
                ColumnDescribe(
                    name = rs.getString(1),
                    hasDefault = rs.getString(2) != null,
                    isNullable = rs.getString(3) == "YES",
                    dataType = rs.getString(4).generalizeType()
                )
            }.toList()
        }.associateBy { it.name }

    private fun KClass<out Table<*>>.name() = simpleName!!.toSnakeCase().removeSuffix("_table")

    private fun KType.asDbType() : List<String> =
        when (this.classifier) {
            Boolean::class -> listOf("boolean")
            TsVector::class -> listOf("tsvector")
            String::class -> listOf("text", "character varying", "jsonb", "json")
            PersonPiiToken::class, PersonNonPiiToken::class, UUID::class -> listOf("uuid", "text")
            Map::class -> listOf("jsonb", "json", "text")
            else -> {
                val kClass = jvmErasure

                when {
                    kClass.isData -> listOf("jsonb", "json")
                    kClass.isCollection() -> listOf("jsonb", "json")
                    kClass.isNumber() -> listOf("integer", "numeric", "bigint", "double precision")
                    kClass.isTemporal() -> listOf("timestamp", "date", "time")
                    kClass.isEnum() -> listOf("text", "USER-DEFINED")
                    else -> listOf("unmapped type: $kClass")
                }
            }
        }

    private fun KClass<*>.isCollection(): Boolean = this.isSubclassOf(Collection::class)
    private fun KClass<*>.isTemporal(): Boolean = this.isSubclassOf(Temporal::class)
    private fun KClass<*>.isEnum(): Boolean = this.isSubclassOf(Enum::class)
    private fun KClass<*>.isNumber(): Boolean = this.isSubclassOf(Number::class)

    private fun String.generalizeType() : String =
        when (this) {
            "time without time zone" -> "time"
            "date without time zone" -> "date"
            "timestamp without time zone" -> "timestamp"
            else -> this
        }.trim()

    data class ColumnDescribe(
        val name: String,
        val hasDefault: Boolean,
        val isNullable: Boolean,
        val dataType: String
    )
}
