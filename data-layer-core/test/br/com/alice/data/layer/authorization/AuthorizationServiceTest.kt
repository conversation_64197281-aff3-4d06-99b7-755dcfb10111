package br.com.alice.data.layer.authorization

import br.com.alice.authentication.RootService
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.logging.Logger
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.MEMBER_ROOT_SERVICE_NAME
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.DeviceModel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.subjects.Unauthenticated
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class AuthorizationServiceTest {
    private val opaAuthorizer: OPAAuthorizer = mockk()
    private val staff = DataLayerTestModelFactory.buildStaff()
    private val person = DataLayerTestModelFactory.buildPerson()
    private val appointment = TestModelFactory.buildAppointment(staff.id, person.id)
    private val clinicalBackground = TestModelFactory.buildUnstructuredClinicalBackground(
        personId = person.id,
        addedByStaffId = staff.id,
        appointmentId = appointment.id
    )
    private val otherPerson = DataLayerTestModelFactory.buildPerson()
    private val device = DeviceModel(personId = person.id, deviceId = "device_id")
    private val company = TestModelFactory.buildCompany()

    private val ehrEnvironment = RootService(EHR_API_ROOT_SERVICE_NAME)

    private val staffViewAppointment =
        AuthorizationRequest(staff, View, appointment,  ehrEnvironment)
    private val staffViewClinicalBackground =
        AuthorizationRequest(staff, View, clinicalBackground,  ehrEnvironment)

    private val staffUpdateAppointment =
        AuthorizationRequest(staff, Update, appointment,  ehrEnvironment)

    private val staffCountAppointment =
        AuthorizationRequest(staff, Count, null,  ehrEnvironment, Appointment::class)

    private val personViewAppointment =
        AuthorizationRequest(person, View, appointment,  ehrEnvironment)
    private val personViewClinicalBackground =
        AuthorizationRequest(person, View, clinicalBackground,  ehrEnvironment)

    private val otherPersonViewAppointment = AuthorizationRequest(otherPerson, View, appointment,  ehrEnvironment)
    private val otherPersonViewClinicalBackground =
        AuthorizationRequest(otherPerson, View, clinicalBackground,  ehrEnvironment)

    private val staffViewAppointmentWrongEnvironment =
        AuthorizationRequest(staff, View, appointment,  RootService(MEMBER_ROOT_SERVICE_NAME))

    private val unauthenticatedViewAppointment =
        AuthorizationRequest(Unauthenticated(""), View, appointment,  ehrEnvironment)

    private val unauthenticatedViewCompany =
        AuthorizationRequest(Unauthenticated(""), View, company,  ehrEnvironment)

    private val unauthenticatedCountCompany =
        AuthorizationRequest(Unauthenticated(""), Count, null,  ehrEnvironment, Company::class)

    private val unauthenticatedViewDevice =
        AuthorizationRequest(Unauthenticated(""), View, device,  ehrEnvironment)

    private val unauthenticatedCountDevice =
        AuthorizationRequest(Unauthenticated(""), Count, null,  ehrEnvironment, DeviceModel::class)

    private val unauthenticatedUpdateDevice =
        AuthorizationRequest(Unauthenticated(""), Update, device,  ehrEnvironment)

    private val policies: PolicyContext.() -> Boolean =
        policySet {
            includes {
                match("at ehr-api", { rootService.name == EHR_API_ROOT_SERVICE_NAME }) {
                    match("Physician", { subject is StaffModel }) {
                        match("can view", { action is View }) {
                            match("any Appointment") { resource is Appointment }
                            match("any ClinicalBackground") { resource is ClinicalBackground }
                        }
                        match("can update", { action is Update }) {
                            match("any Appointment") { resource is Appointment }
                            match("any ClinicalBackground") { resource is ClinicalBackground }
                        }
                        match("can count", { action is Count }) {
                            match("Appointments") { resourceIs(Appointment::class) }
                        }
                    }
                }
            }
            includes {
                match("at ehr-api", { rootService.name == EHR_API_ROOT_SERVICE_NAME }) {
                    match("PersonModel", { subject is PersonModel }) {
                        match("can view", { action is View }) {
                            match("her own Appointment") { resource is Appointment && (resource as Appointment).personId == (subject as PersonModel).id }
                            match("ClinicalBackground", { resource is ClinicalBackground }) {
                                match("of herself") { (resource as ClinicalBackground).personId == (subject as PersonModel).id }
                            }
                        }
                    }
                    match("Unauthenticated", { subject is Unauthenticated }) {
                        allows(Company::class, View, Count)
                        allows(DeviceModel::class, View)
                    }
                }
            }
        }

    @Test
    fun `#authorize authorizes correctly`(): Unit = runBlocking {
        val authorizationService = AuthorizationService(policies, opaAuthorizer)

        val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
        val staffViewClinicalBackgroundAuth = authorizationService.authorize(staffViewClinicalBackground)

        val staffUpdateAppointmentAuth = authorizationService.authorize(staffUpdateAppointment)

        val staffCountAppointmentAuth = authorizationService.authorize(staffCountAppointment)

        val personViewAppointmentAuth = authorizationService.authorize(personViewAppointment)
        val personViewClinicalBackgroundAuth = authorizationService.authorize(personViewClinicalBackground)

        val otherPersonViewAppointmentAuth = authorizationService.authorize(otherPersonViewAppointment)
        val otherPersonViewClinicalBackgroundAuth = authorizationService.authorize(otherPersonViewClinicalBackground)

        val staffViewAppointmentWrongEnvironmentAuth =
            authorizationService.authorize(staffViewAppointmentWrongEnvironment)

        val unauthenticatedViewAppointmentAuth = authorizationService.authorize(unauthenticatedViewAppointment)
        val unauthenticatedViewCompanyAuth = authorizationService.authorize(unauthenticatedViewCompany)
        val unauthenticatedCountCompanyAuth = authorizationService.authorize(unauthenticatedCountCompany)
        val unauthenticatedViewDeviceAuth = authorizationService.authorize(unauthenticatedViewDevice)
        val unauthenticatedCountDeviceAuth = authorizationService.authorize(unauthenticatedCountDevice)
        val unauthenticatedUpdateDeviceAuth = authorizationService.authorize(unauthenticatedUpdateDevice)

        assertThat(staffViewAppointmentAuth.granted).isTrue
        assertThat(staffViewAppointmentAuth.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
        )
        assertThat(staffViewClinicalBackgroundAuth.granted).isTrue
        assertThat(staffViewClinicalBackgroundAuth.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on ClinicalBackground(${clinicalBackground.id}) at ehr-api: at ehr-api Physician can view any ClinicalBackground"
        )

        assertThat(staffUpdateAppointmentAuth.granted).isTrue
        assertThat(staffUpdateAppointmentAuth.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) update on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can update any Appointment"
        )

        assertThat(staffCountAppointmentAuth.granted).isTrue
        assertThat(staffCountAppointmentAuth.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) count on Appointment at ehr-api: at ehr-api Physician can count Appointments"
        )

        assertThat(personViewAppointmentAuth.granted).isTrue
        assertThat(personViewAppointmentAuth.description).isEqualTo(
            "PersonModel(${person.id}) view on Appointment(${appointment.id}) at ehr-api: at ehr-api PersonModel can view her own Appointment"
        )
        assertThat(personViewClinicalBackgroundAuth.granted).isTrue
        assertThat(personViewClinicalBackgroundAuth.description).isEqualTo(
            "PersonModel(${person.id}) view on ClinicalBackground(${clinicalBackground.id}) at ehr-api: at ehr-api PersonModel can view ClinicalBackground of herself"
        )

        assertThat(otherPersonViewAppointmentAuth.granted).isFalse
        assertThat(otherPersonViewAppointmentAuth.description).isEqualTo(
            "PersonModel(${otherPerson.id}) view on Appointment(${appointment.id}) at ehr-api"
        )
        assertThat(otherPersonViewClinicalBackgroundAuth.granted).isFalse
        assertThat(otherPersonViewClinicalBackgroundAuth.description).isEqualTo(
            "PersonModel(${otherPerson.id}) view on ClinicalBackground(${clinicalBackground.id}) at ehr-api"
        )

        assertThat(staffViewAppointmentWrongEnvironmentAuth.granted).isFalse
        assertThat(staffViewAppointmentWrongEnvironmentAuth.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at member-api"
        )

        assertThat(unauthenticatedViewAppointmentAuth.granted).isFalse
        assertThat(unauthenticatedViewAppointmentAuth.description).isEqualTo(
            "Unauthenticated() view on Appointment(${appointment.id}) at ehr-api"
        )

        assertThat(unauthenticatedViewCompanyAuth.granted).isTrue
        assertThat(unauthenticatedViewCompanyAuth.description).isEqualTo(
            "Unauthenticated() view on Company(${company.id}) at ehr-api: at ehr-api Unauthenticated can view, count any Company"
        )

        assertThat(unauthenticatedCountCompanyAuth.granted).isTrue
        assertThat(unauthenticatedCountCompanyAuth.description).isEqualTo(
            "Unauthenticated() count on Company at ehr-api: at ehr-api Unauthenticated can view, count any Company"
        )

        assertThat(unauthenticatedViewDeviceAuth.granted).isTrue
        assertThat(unauthenticatedViewDeviceAuth.description).isEqualTo(
            "Unauthenticated() view on DeviceModel(${device.id}) at ehr-api: at ehr-api Unauthenticated can view any DeviceModel"
        )

        assertThat(unauthenticatedCountDeviceAuth.granted).isFalse
        assertThat(unauthenticatedCountDeviceAuth.description).isEqualTo(
            "Unauthenticated() count on DeviceModel at ehr-api"
        )

        assertThat(unauthenticatedUpdateDeviceAuth.granted).isFalse
        assertThat(unauthenticatedUpdateDeviceAuth.description).isEqualTo(
            "Unauthenticated() update on DeviceModel(${device.id}) at ehr-api"
        )
    }

    @Test
    fun `#authorize does not fail even with buggy match not executed`(): Unit = runBlocking {
        val policies: PolicyContext.() -> Boolean =
            {
                match("ClinicalBackground", { subject is ClinicalBackground }, {
                    match("some error") { throw RuntimeException() } // to test that do not throw exception
                })
                match("Physician", { subject is StaffModel }, {
                    match("can view", { action == View }, {
                        match("any Appointment") { resource is Appointment }
                    })
                })
                match("ClinicalBackground", { subject is ClinicalBackground }, {
                    match("some error") { throw RuntimeException() } // to test that do not throw exception
                })
            }

        val authorizationService = AuthorizationService(policies, opaAuthorizer)

        val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
        val otherPersonViewAppointmentAuth = authorizationService.authorize(otherPersonViewAppointment)

        assertThat(staffViewAppointmentAuth.granted).isTrue
        assertThat(staffViewAppointmentAuth.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: Physician can view any Appointment"
        )

        assertThat(otherPersonViewAppointmentAuth.granted).isFalse
        assertThat(otherPersonViewAppointmentAuth.description).isEqualTo(
            "PersonModel(${otherPerson.id}) view on Appointment(${appointment.id}) at ehr-api"
        )
    }

    @Test
    fun `#allPoliciesDescriptions returns all policies descriptions`(): Unit = runBlocking {
        val authorizationService = AuthorizationService(policies, opaAuthorizer)

        val descriptions = authorizationService.allPoliciesDescriptions()

        assertThat(descriptions).isEqualTo(listOf(
            "at ehr-api Physician can view any Appointment",
            "at ehr-api Physician can view any ClinicalBackground",
            "at ehr-api Physician can update any Appointment",
            "at ehr-api Physician can update any ClinicalBackground",
            "at ehr-api Physician can count Appointments",
            "at ehr-api PersonModel can view her own Appointment",
            "at ehr-api PersonModel can view ClinicalBackground of herself",
            "at ehr-api Unauthenticated can view, count any Company",
            "at ehr-api Unauthenticated can view any DeviceModel"
        ))
    }

    @Test
    fun `it authorizes correctly with shortcut`(): Unit = runBlocking {
        val policies: PolicyContext.() -> Boolean =
            {
                at(EHR_API_ROOT_SERVICE_NAME) {
                    who(StaffModel::class) {
                        can(Update) {
                            resources(Appointment::class)
                        }
                    }
                    who(Unauthenticated::class) {
                        can(View) {
                            resources(Company::class, Appointment::class)
                        }
                        can(Count) {
                            resources(DeviceModel::class)
                        }
                    }
                }
            }

        val authorizationService = AuthorizationService(policies, opaAuthorizer)

        val updateAppointment = authorizationService.authorize(staffUpdateAppointment)

        val viewCompany = authorizationService.authorize(
            AuthorizationRequest(Unauthenticated(""), View, company,  ehrEnvironment)
        )

        val unauthenticatedUpdateAppointment = authorizationService.authorize(
            AuthorizationRequest(Unauthenticated(""), Update, appointment,  ehrEnvironment)
        )

        val countDevice = authorizationService.authorize(
            AuthorizationRequest(Unauthenticated(""), Count, null,  ehrEnvironment, DeviceModel::class)
        )

        assertThat(updateAppointment.granted).isTrue
        assertThat(updateAppointment.description).isEqualTo(
            "Staff(<EMAIL> [MANAGER_PHYSICIAN]) update on Appointment(${appointment.id}) at ehr-api: at ehr-api the StaffModel can update any Appointment"
        )

        assertThat(unauthenticatedUpdateAppointment.granted).isFalse
        assertThat(unauthenticatedUpdateAppointment.description).isEqualTo(
            "Unauthenticated() update on Appointment(${appointment.id}) at ehr-api"
        )

        assertThat(viewCompany.granted).isTrue
        assertThat(viewCompany.description).isEqualTo(
            "Unauthenticated() view on Company(${company.id}) at ehr-api: at ehr-api the Unauthenticated can view any Company, Appointment"
        )

        assertThat(countDevice.granted).isTrue
        assertThat(countDevice.description).isEqualTo(
            "Unauthenticated() count on DeviceModel at ehr-api: at ehr-api the Unauthenticated can count any DeviceModel"
        )
    }

    @Test
    fun `#authorize calls OPA Authorizer when allow FF is enabled for root service and registers HIT`(): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf(EHR_API_ROOT_SERVICE_NAME)) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } returns Authorization(true, "bla")

            mockkObject(Logger) {
                val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
                assertThat(staffViewAppointmentAuth.granted).isTrue
                assertThat(staffViewAppointmentAuth.description).isEqualTo(
                    "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                )

                coVerifyOnce { opaAuthorizer.authorize(any()) }
                coVerifyOnce { Logger.info("Authorization MATCH between data layer and OPA",
                    "metric" to "data-layer_opa_authz",
                    "result" to "HIT",
                    "data_layer_granted" to true,
                    "resource_id" to appointment.id,
                    "resource_class" to Appointment::class.simpleName,
                    "action" to View,
                    "root_service" to EHR_API_ROOT_SERVICE_NAME
                ) }
            }
        }
    }

    @Test
    fun `#authorize calls OPA Authorizer when both FFs are enabled for root service and returns OPA result`(): Unit = runBlocking {
        withFeatureFlags(FeatureNamespace.ENG_PLATFORM, mapOf(
            "opa_application_allow_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
            "opa_application_trust_list" to listOf(EHR_API_ROOT_SERVICE_NAME)
        )) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } returns Authorization(false, "bla")

            mockkObject(Logger) {
                val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
                assertThat(staffViewAppointmentAuth.granted).isFalse()
                assertThat(staffViewAppointmentAuth.description).isEqualTo("bla")

                coVerifyOnce { opaAuthorizer.authorize(any()) }
                coVerifyOnce { Logger.warn("Authorization MISMATCH between data layer and OPA",
                    "metric" to "data-layer_opa_authz",
                    "result" to "MISS",
                    "data_layer_granted" to true,
                    "opa_granted" to false,
                    "resource_id" to appointment.id,
                    "resource_class" to Appointment::class.simpleName,
                    "action" to View,
                    "root_service" to EHR_API_ROOT_SERVICE_NAME
                ) }
            }
        }
    }

    @Test
    fun `#authorize calls OPA Authorizer when allow FF is enabled for root service and registers MISS`(): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf(EHR_API_ROOT_SERVICE_NAME)) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } returns Authorization(false, "bla")

            mockkObject(Logger) {
                val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
                assertThat(staffViewAppointmentAuth.granted).isTrue
                assertThat(staffViewAppointmentAuth.description).isEqualTo(
                    "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                )

                coVerifyOnce { opaAuthorizer.authorize(any()) }
                coVerifyOnce { Logger.warn("Authorization MISMATCH between data layer and OPA",
                    "metric" to "data-layer_opa_authz",
                    "result" to "MISS",
                    "data_layer_granted" to true,
                    "opa_granted" to false,
                    "resource_id" to appointment.id,
                    "resource_class" to Appointment::class.simpleName,
                    "action" to View,
                    "root_service" to EHR_API_ROOT_SERVICE_NAME
                ) }
            }
        }
    }

    @Test
    fun `#authorize calls OPA Authorizer when allow FF is enabled for root service and registers FAIL on fail`(): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf(EHR_API_ROOT_SERVICE_NAME)) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } throws RuntimeException("SOME BOGUS MAN")

            mockkObject(Logger) {
                val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
                assertThat(staffViewAppointmentAuth.granted).isTrue
                assertThat(staffViewAppointmentAuth.description).isEqualTo(
                    "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                )

                coVerifyOnce { opaAuthorizer.authorize(any()) }
                coVerifyOnce { Logger.warn("Authorization FAILED with OPA",
                    "metric" to "data-layer_opa_authz",
                    "result" to "FAIL",
                    "data_layer_granted" to true,
                    "opa_granted" to null,
                    "resource_id" to appointment.id,
                    "resource_class" to Appointment::class.simpleName,
                    "action" to View,
                    "root_service" to EHR_API_ROOT_SERVICE_NAME
                ) }
            }
        }
    }

    @Test
    fun `#authorize calls OPA Authorizer when both FFs are enabled for root service and returns DL result on fail`(): Unit = runBlocking {
        withFeatureFlags(FeatureNamespace.ENG_PLATFORM, mapOf(
            "opa_application_allow_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
            "opa_application_trust_list" to listOf(EHR_API_ROOT_SERVICE_NAME)
        )) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } throws RuntimeException("SOME BOGUS MAN")

            mockkObject(Logger) {
                val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
                assertThat(staffViewAppointmentAuth.granted).isTrue
                assertThat(staffViewAppointmentAuth.description).isEqualTo(
                    "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                )

                coVerifyOnce { opaAuthorizer.authorize(any()) }
                coVerifyOnce { Logger.warn("Authorization FAILED with OPA",
                    "metric" to "data-layer_opa_authz",
                    "result" to "FAIL",
                    "data_layer_granted" to true,
                    "opa_granted" to null,
                    "resource_id" to appointment.id,
                    "resource_class" to Appointment::class.simpleName,
                    "action" to View,
                    "root_service" to EHR_API_ROOT_SERVICE_NAME
                ) }
            }
        }
    }

    @Test
    fun `#authorize calls OPA Authorizer when both FFs are enabled for root service and throws error on fail is FF is false`(): Unit = runBlocking {
        withFeatureFlags(FeatureNamespace.ENG_PLATFORM, mapOf(
            "opa_application_allow_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
            "opa_application_trust_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
            "should_use_data_layer_authz_fallback" to false
        )) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } throws RuntimeException("SOME BOGUS MAN")

            mockkObject(Logger) {
                assertThrows<RuntimeException> { runBlocking { authorizationService.authorize(staffViewAppointment) } }

                coVerifyOnce { opaAuthorizer.authorize(any()) }
                coVerifyOnce { Logger.warn("Authorization FAILED with OPA",
                    "metric" to "data-layer_opa_authz",
                    "result" to "FAIL",
                    "data_layer_granted" to true,
                    "opa_granted" to null,
                    "resource_id" to appointment.id,
                    "resource_class" to Appointment::class.simpleName,
                    "action" to View,
                    "root_service" to EHR_API_ROOT_SERVICE_NAME
                ) }
            }
        }
    }

    @Test
    fun `#authorize does not delegate to OPA Authorizer when allow FF is enabled for other service`(): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf("BANANA")) {
            val authorizationService = AuthorizationService(policies, opaAuthorizer)
            coEvery { opaAuthorizer.authorize(staffViewAppointment) } returns Authorization(true, "bla")

            val staffViewAppointmentAuth = authorizationService.authorize(staffViewAppointment)
            assertThat(staffViewAppointmentAuth.granted).isTrue
            assertThat(staffViewAppointmentAuth.description).isEqualTo(
                "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
            )

            coVerifyNone { opaAuthorizer.authorize(any()) }
        }
    }

    @Nested
    inner class BatchAuthorizationsTest {
        private val appointment2 = TestModelFactory.buildAppointment(staff.id, person.id)
        private val staffViewAppointment2 =
            AuthorizationRequest(staff, View, appointment2,  ehrEnvironment)

        @Test
        fun `#authorizeBatch calls OPA Authorizer when allow FF is enabled for root service and registers HITs`(): Unit = runBlocking {
            val batchAuthorizationRequest = BatchAuthorizationRequest(listOf(staffViewAppointment, staffViewAppointment2), ehrEnvironment)
            withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf(EHR_API_ROOT_SERVICE_NAME)) {
                val authorizationService = AuthorizationService(policies, opaAuthorizer)
                coEvery { opaAuthorizer.authorizeBatch<Appointment>(batchAuthorizationRequest) } returns listOf(
                    appointment to Authorization(true, "app1"),
                    appointment2 to Authorization(true, "app2")
                )

                mockkObject(Logger) {
                    val staffViewAppointmentAuth = authorizationService.authorizeBatch(batchAuthorizationRequest)
                    assertThat(staffViewAppointmentAuth[0].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[0].second.description).isEqualTo(
                        "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                    )
                    assertThat(staffViewAppointmentAuth[1].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[1].second.description).isEqualTo(
                        "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment2.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                    )

                    coVerifyOnce { opaAuthorizer.authorizeBatch<Appointment>(any()) }
                    //logs MATCH once
                    coVerifyOnce { Logger.info("Authorization MATCH between data layer and OPA",
                        "metric" to "data-layer_opa_authz",
                        "result" to "HIT",
                        "data_layer_granted" to true,
                        "resource_id" to appointment.id,
                        "resource_class" to Appointment::class.simpleName,
                        "action" to View,
                        "root_service" to EHR_API_ROOT_SERVICE_NAME
                    ) }
                }
            }
        }

        @Test
        fun `#authorizeBatch calls OPA Authorizer when allow FF is enabled for root service and registers FAILs once`(): Unit = runBlocking {
            val batchAuthorizationRequest = BatchAuthorizationRequest(listOf(staffViewAppointment, staffViewAppointment2), ehrEnvironment)
            withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf(EHR_API_ROOT_SERVICE_NAME)) {
                val authorizationService = AuthorizationService(policies, opaAuthorizer)
                coEvery { opaAuthorizer.authorizeBatch<Appointment>(batchAuthorizationRequest) } throws RuntimeException("SOME BOGUS MAN")

                mockkObject(Logger) {
                    val staffViewAppointmentAuth = authorizationService.authorizeBatch(batchAuthorizationRequest)
                    assertThat(staffViewAppointmentAuth[0].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[0].second.description).isEqualTo(
                        "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                    )
                    assertThat(staffViewAppointmentAuth[1].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[1].second.description).isEqualTo(
                        "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment2.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                    )

                    coVerifyOnce { opaAuthorizer.authorizeBatch<Appointment>(any()) }
                    //logs FAIL once
                    coVerifyOnce { Logger.warn("Authorization FAILED with OPA",
                        "metric" to "data-layer_opa_authz",
                        "result" to "FAIL",
                        "data_layer_granted" to true,
                        "opa_granted" to null,
                        "resource_id" to appointment.id,
                        "resource_class" to Appointment::class.simpleName,
                        "action" to View,
                        "root_service" to EHR_API_ROOT_SERVICE_NAME
                    ) }
                }
            }
        }

        @Test
        fun `#authorizeBatch calls OPA Authorizer with different batch size`(): Unit = runBlocking {
            val batchAuthorizationRequest = BatchAuthorizationRequest(listOf(staffViewAppointment, staffViewAppointment2), ehrEnvironment)
            val batchAuthorizationRequest1 = BatchAuthorizationRequest(listOf(staffViewAppointment), ehrEnvironment)
            val batchAuthorizationRequest2 = BatchAuthorizationRequest(listOf(staffViewAppointment2), ehrEnvironment)
            withFeatureFlag(FeatureNamespace.ENG_PLATFORM, "opa_application_allow_list", listOf(EHR_API_ROOT_SERVICE_NAME)) {
                val authorizationService = AuthorizationService(policies, opaAuthorizer, 1) // batch size 1
                coEvery { opaAuthorizer.authorizeBatch<Appointment>(batchAuthorizationRequest1) } returns listOf(
                    appointment to Authorization(true, "app1")
                )
                coEvery { opaAuthorizer.authorizeBatch<Appointment>(batchAuthorizationRequest2) } returns listOf(
                    appointment2 to Authorization(true, "app2")
                )

                mockkObject(Logger) {
                    val staffViewAppointmentAuth = authorizationService.authorizeBatch(batchAuthorizationRequest)
                    assertThat(staffViewAppointmentAuth[0].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[0].second.description).isEqualTo(
                        "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                    )
                    assertThat(staffViewAppointmentAuth[1].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[1].second.description).isEqualTo(
                        "Staff(<EMAIL> [MANAGER_PHYSICIAN]) view on Appointment(${appointment2.id}) at ehr-api: at ehr-api Physician can view any Appointment"
                    )

                    coVerify(exactly = 2) { opaAuthorizer.authorizeBatch<Appointment>(any()) }
                }
            }
        }

        @Test
        fun `#authorizeBatch calls OPA Authorizer when allow FF is enabled and trusts OPA result if FF enabled`(): Unit = runBlocking {
            val batchAuthorizationRequest = BatchAuthorizationRequest(listOf(staffViewAppointment, staffViewAppointment2), ehrEnvironment)
            withFeatureFlags(FeatureNamespace.ENG_PLATFORM, mapOf(
                "opa_application_allow_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
                "opa_application_trust_list" to listOf(EHR_API_ROOT_SERVICE_NAME)
            )) {
                val authorizationService = AuthorizationService(policies, opaAuthorizer)
                coEvery { opaAuthorizer.authorizeBatch<Appointment>(batchAuthorizationRequest) } returns listOf(
                    appointment to Authorization(true, "app1"),
                    appointment2 to Authorization(true, "app2")
                )

                mockkObject(Logger) {
                    val staffViewAppointmentAuth = authorizationService.authorizeBatch(batchAuthorizationRequest)
                    assertThat(staffViewAppointmentAuth[0].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[0].second.description).isEqualTo("app1")
                    assertThat(staffViewAppointmentAuth[1].second.granted).isTrue
                    assertThat(staffViewAppointmentAuth[1].second.description).isEqualTo("app2")

                    coVerifyOnce { opaAuthorizer.authorizeBatch<Appointment>(any()) }
                    //logs MATCH once
                    coVerifyOnce { Logger.info("Authorization MATCH between data layer and OPA",
                        "metric" to "data-layer_opa_authz",
                        "result" to "HIT",
                        "data_layer_granted" to true,
                        "resource_id" to appointment.id,
                        "resource_class" to Appointment::class.simpleName,
                        "action" to View,
                        "root_service" to EHR_API_ROOT_SERVICE_NAME
                    ) }
                }
            }
        }

        @Test
        fun `#authorize calls OPA Authorizer when both FFs are enabled for root service and throws error on fail is FF is false`(): Unit = runBlocking {
            val batchAuthorizationRequest = BatchAuthorizationRequest(listOf(staffViewAppointment, staffViewAppointment2), ehrEnvironment)
            withFeatureFlags(FeatureNamespace.ENG_PLATFORM, mapOf(
                "opa_application_allow_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
                "opa_application_trust_list" to listOf(EHR_API_ROOT_SERVICE_NAME),
                "should_use_data_layer_authz_fallback" to false
            )) {
                val authorizationService = AuthorizationService(policies, opaAuthorizer)

                coEvery { opaAuthorizer.authorizeBatch<Appointment>(batchAuthorizationRequest) } throws RuntimeException("SOME BOGUS MAN")

                mockkObject(Logger) {
                    assertThrows<RuntimeException> { runBlocking { authorizationService.authorizeBatch(batchAuthorizationRequest) } }

                    coVerifyOnce { opaAuthorizer.authorizeBatch<Appointment>(any()) }
                    coVerifyOnce { Logger.warn("Authorization FAILED with OPA",
                        "metric" to "data-layer_opa_authz",
                        "result" to "FAIL",
                        "data_layer_granted" to true,
                        "opa_granted" to null,
                        "resource_id" to appointment.id,
                        "resource_class" to Appointment::class.simpleName,
                        "action" to View,
                        "root_service" to EHR_API_ROOT_SERVICE_NAME
                    ) }
                }
            }
        }
    }
}
