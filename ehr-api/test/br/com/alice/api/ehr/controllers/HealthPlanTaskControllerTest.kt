package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.controllers.HealthPlanTaskController.Companion.EATING_TITLE
import br.com.alice.api.ehr.controllers.HealthPlanTaskController.Companion.EMERGENCY_TITLE
import br.com.alice.api.ehr.controllers.HealthPlanTaskController.Companion.MOOD_TITLE
import br.com.alice.api.ehr.controllers.HealthPlanTaskController.Companion.OTHERS_TITLE
import br.com.alice.api.ehr.controllers.HealthPlanTaskController.Companion.PHYSICAL_ACTIVITY_TITLE
import br.com.alice.api.ehr.controllers.HealthPlanTaskController.Companion.SLEEP_TITLE
import br.com.alice.api.ehr.helpers.TestTransportFactory
import br.com.alice.api.ehr.services.internal.task.TaskReferralSessionsService
import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.healthplan.client.HealthPlanTaskCounters
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.converters.HealthPlanTaskConverter
import br.com.alice.healthplan.converters.ReferralSuggestionResponseConverter
import br.com.alice.healthplan.models.EmergencyTransport
import br.com.alice.healthplan.models.FollowUpRequestTransport
import br.com.alice.healthplan.models.GenericTaskTransport
import br.com.alice.healthplan.models.HLAdherenceValidationResponse
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.models.SAO_PAULO_CITY_ID
import br.com.alice.healthplan.models.SurgeryPrescriptionTransport
import br.com.alice.healthplan.models.TestRequestTransport
import br.com.alice.healthplan.models.ValidatedDemand
import br.com.alice.secondary.attention.models.HealthPlanTaskSessionsCountResponse
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDate
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class HealthPlanTaskControllerTest : ControllerTestHelper() {

    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val staffService: StaffService = mockk()
    private val taskReferralSessionsService: TaskReferralSessionsService = mockk()
    private val healthPlanTaskController = HealthPlanTaskController(
        healthPlanTaskService,
        taskReferralSessionsService,
        staffService
    )

    private val transport = TestTransportFactory.buildPrescriptionTransport(personId = personId)
        .copy(id = RangeUUID.generate(), demand = caseRecordDetails)
    private val model = HealthPlanTaskConverter.convert(transport, staff.id)
    private val tasksTransport = HealthPlanTasksTransport()
    private val referral = TestModelFactory.buildHealthPlanTaskReferral().specialize<Referral>().copy(
        suggestedSpecialist = SuggestedSpecialist(
            name = "João",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF,
            tier = SpecialistTier.TALENTED,
            memberTier = ReferralMemberTier(
                isInMemberTier = false,
                memberProduct = ReferralMemberProduct(
                    id = RangeUUID.generate(),
                    name = "Alice Plan"
                ),
                suggestedReason = ReferralSuggestedSpecialistReason(
                    reason = ReferralSuggestedSpecialistReasonType.OTHER,
                    description = ""
                )
            )
        )
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { healthPlanTaskController }
    }

    @AfterTest
    fun confirmMocks() =
        confirmVerified(
            healthPlanTaskService,
            taskReferralSessionsService,
            staffService
        )

    companion object {
        private val personId = PersonId()
        private val caseRecordDetails = CaseRecordDetails(
            caseId = RangeUUID.generate(),
            description = DiseaseDetails(
                type = Disease.Type.CID_10,
                value = "CID10"
            ),
            severity = CaseSeverity.COMPENSATED
        )

        val genericTransport = GenericTaskTransport(
            personId = personId.toUUID(),
            title = "Teste",
            description = "Teste",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.MOOD,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        @JvmStatic
        fun provideFollowUpRequestTaskScenarios() = listOf(
            FollowUpRequestTransport(
                genericTransport.copy(type = HealthPlanTaskType.FOLLOW_UP_REQUEST),
                providerType = null,
                followUpInterval = null,
            ),
            FollowUpRequestTransport(
                genericTransport.copy(type = HealthPlanTaskType.FOLLOW_UP_REQUEST),
                providerType = FollowUpProviderType.REMOTE,
                followUpInterval = null,
            ),
            FollowUpRequestTransport(
                genericTransport.copy(type = HealthPlanTaskType.FOLLOW_UP_REQUEST),
                providerType = FollowUpProviderType.REMOTE,
                followUpInterval = FollowUpInterval(
                    type = FollowUpIntervalType.AFTER_MEDICAL_TREATMENT,
                    unit = null,
                    quantity = null,
                ),
            ),
            FollowUpRequestTransport(
                genericTransport.copy(type = HealthPlanTaskType.FOLLOW_UP_REQUEST),
                providerType = FollowUpProviderType.REMOTE,
                followUpInterval = FollowUpInterval(
                    type = FollowUpIntervalType.DATE_INTERVAL,
                    unit = PeriodUnit.DAY,
                    quantity = 2,
                ),
            ),
        )
    }

    @Test
    fun `#changeStatus should update HealthPlanTask status`() {
        val id = RangeUUID.generate()
        coEvery { healthPlanTaskService.markAsDoneUndone(id, staff.id) } returns transport.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/ehr/persons/${transport.personId}/task/$id/change_status") { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.markAsDoneUndone(any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#delete should update HealthPlanTask status to DELETED`() {
        val id = RangeUUID.generate()
        coEvery { healthPlanTaskService.delete(id, staff.id) } returns true.success()

        authenticatedAs(idToken, staffTest) {
            delete("/ehr/persons/${transport.personId}/task/$id") { response ->
                assertThat(response).isOKWithData(true)
            }
        }

        coVerifyOnce { healthPlanTaskService.delete(any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#update should update HealthPlanTask`() {
        coEvery { healthPlanTaskService.update(model, staff.id) } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/ehr/persons/${transport.personId}/task/${transport.id!!}/", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.update(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#update should update and ignore adherence when community specialist`() {
        val staff = staff.copy(
            role = Role.COMMUNITY,
            type = StaffType.COMMUNITY_SPECIALIST
        )
        val transport = TestRequestTransport(
            healthPlanTaskTransport = GenericTaskTransport(
                id = RangeUUID.generate(),
                personId = personId.toUUID(),
                title = "Hemoglobina",
                description = "Hemoglobina",
                dueDate = null,
                status = HealthPlanTaskStatus.ACTIVE,
                type = HealthPlanTaskType.TEST_REQUEST,
                favorite = false,
                caseRecordDetails = listOf(caseRecordDetails)
            ),
            code = "1234"
        )
        val model = HealthPlanTaskConverter.convert(transport, staff.id)

        coEvery { healthPlanTaskService.update(model, staff.id) } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            put(to = "/ehr/persons/${transport.personId}/task/${transport.id!!}/", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.update(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#update should conflict to update HealthPlanTask`() {
        coEvery { staffService.get(staff.id) } returns staff.success()
        coEvery { healthPlanTaskService.update(model, staff.id) } returns ConflictException("").failure()

        authenticatedAs(idToken, staffTest) {
            put(to = "/ehr/persons/${transport.personId}/task/${transport.id!!}/", body = transport) { response ->
                assertThat(response).isConflictWithErrorCode("conflict")
            }
        }

        coVerifyOnce { healthPlanTaskService.update(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask`() {
        val model = model.copy(createdBy = TaskUpdatedBy(staff.id, TaskSourceType.STAFF))

        coEvery { healthPlanTaskService.create(model, staff.id) } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is MOOD and generating automatic title`() {
        val transport = GenericTaskTransport(
            personId = personId.toUUID(),
            title = "Teste",
            description = "Teste",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.MOOD,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = MOOD_TITLE
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is SLEEP and generating automatic title`() {
        val transport = GenericTaskTransport(
            personId = personId.toUUID(),
            title = "Teste",
            description = "Teste",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.SLEEP,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = SLEEP_TITLE
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is PHYSICAL_ACTIVITY and generating automatic title`() {
        val transport = GenericTaskTransport(
            personId = personId.toUUID(),
            title = "Teste",
            description = "Teste",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.PHYSICAL_ACTIVITY,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = PHYSICAL_ACTIVITY_TITLE
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is EATING and generating automatic title`() {
        val transport = GenericTaskTransport(
            personId = personId.toUUID(),
            title = "Teste",
            description = "Teste",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.EATING,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = EATING_TITLE
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is OTHERS and generating automatic title`() {
        val transport = GenericTaskTransport(
            personId = personId.toUUID(),
            title = "Teste",
            description = "Teste",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.OTHERS,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = OTHERS_TITLE
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is REFERRAL to specific specialist and generating automatic title`() {
        val transport = ReferralTransport(
            suggestedSpecialist = SuggestedSpecialist(
                name = "João",
                id = RangeUUID.generate(),
                type = SpecialistType.STAFF,
                tier = SpecialistTier.TALENTED,
                memberTier = ReferralMemberTier(
                    isInMemberTier = false,
                    memberProduct = ReferralMemberProduct(
                        id = RangeUUID.generate(),
                        name = "Alice Plan"
                    ),
                    suggestedReason = ReferralSuggestedSpecialistReason(
                        reason = ReferralSuggestedSpecialistReasonType.OTHER,
                        description = ""
                    )
                )
            ),
            diagnosticHypothesis = "Hypothesis",
            specialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "Specialty"
            ),
            subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "SubSpecialty"
            ),
            sessionsQuantity = 1,
            followUpMaxQuantity = 5,
            healthPlanTaskTransport = GenericTaskTransport(
                personId = personId.toUUID(),
                title = "Teste",
                description = "Teste",
                dueDate = null,
                status = HealthPlanTaskStatus.ACTIVE,
                type = HealthPlanTaskType.REFERRAL,
                favorite = false,
                caseRecordDetails = listOf(caseRecordDetails)
            )
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = "Encaminhamento para Specialty com João"
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is REFERRAL and generating automatic title`() {
        val transport = ReferralTransport(
            suggestedSpecialist = null,
            diagnosticHypothesis = "Hypothesis",
            specialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "Specialty"
            ),
            subSpecialty = ReferralSpecialty(
                id = RangeUUID.generate(),
                name = "SubSpecialty"
            ),
            sessionsQuantity = 1,
            followUpMaxQuantity = 5,
            healthPlanTaskTransport = GenericTaskTransport(
                personId = personId.toUUID(),
                title = "Teste",
                description = "Teste",
                dueDate = null,
                status = HealthPlanTaskStatus.ACTIVE,
                type = HealthPlanTaskType.REFERRAL,
                favorite = false,
                caseRecordDetails = listOf(caseRecordDetails)
            )
        )

        val model = HealthPlanTaskConverter.convert(transport, staff.id).copy(
            title = "Encaminhamento para Specialty"
        )

        coEvery {
            healthPlanTaskService.create(match {
                it.title == model.title
            }, staff.id)
        } returns transport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @ParameterizedTest
    @MethodSource("provideFollowUpRequestTaskScenarios")
    fun `#create should create a new HealthPlanTask when type is FOLLOW_UP_REQUEST and generating automatic title`(
        transport: FollowUpRequestTransport
    ) {
        coEvery {
            healthPlanTaskService.create(
                match {
                    it.title == "Retorno com ${staff.fullName}"
                },
                staff.id
            )
        } returns transport.success()

        coEvery {
            staffService.get(staff.id)
        } returns staff.success()

        authenticatedAs(idToken, staff) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#create should create a new HealthPlanTask when type is FOLLOW_UP_REQUEST and generating automatic title`() {
        val transport = FollowUpRequestTransport(
            healthPlanTaskTransport = GenericTaskTransport(
                personId = personId.toUUID(),
                title = "Teste",
                description = "Teste",
                dueDate = null,
                status = HealthPlanTaskStatus.ACTIVE,
                type = HealthPlanTaskType.FOLLOW_UP_REQUEST,
                favorite = false,
                caseRecordDetails = listOf(caseRecordDetails)
            ),
            providerType = null,
            followUpInterval = null,
        )

        coEvery {
            staffService.get(staff.id)
        } returns staff.success()

        coEvery {
            healthPlanTaskService.create(
                match {
                    it.title == "Retorno com ${staff.fullName}"
                },
                staff.id
            )
        } returns transport.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                assertThat(response).isOKWithData(transport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
    }

    @Test
    fun `#create should create a new emergency HealthPlanTask with default city id`() {
        val genericTransport = GenericTaskTransport(
            id = RangeUUID.generate(),
            personId = personId.toUUID(),
            title = "Pronto Socorro",
            description = "PS",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.EMERGENCY,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val emergencyTransport = EmergencyTransport(
            genericTransport,
            null,
            null,
            null
        )

        val emergencyModel = HealthPlanTaskConverter.convert(emergencyTransport, staff.id)

        val model = emergencyModel.copy(
            createdBy = TaskUpdatedBy(staff.id, TaskSourceType.STAFF),
            content = mapOf(
                "cityId" to SAO_PAULO_CITY_ID
            ),
            title = EMERGENCY_TITLE
        )

        coEvery { healthPlanTaskService.create(model, staff.id) } returns emergencyTransport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = emergencyTransport) { response ->
                assertThat(response).isOKWithData(emergencyTransport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#create should create a new emergency HealthPlanTask with request data`() {
        val genericTransport = GenericTaskTransport(
            id = RangeUUID.generate(),
            personId = personId.toUUID(),
            title = "Pronto Socorro",
            description = "PS",
            dueDate = null,
            status = HealthPlanTaskStatus.ACTIVE,
            type = HealthPlanTaskType.EMERGENCY,
            favorite = false,
            caseRecordDetails = listOf(caseRecordDetails)
        )

        val emergencyTransport = EmergencyTransport(
            genericTransport,
            null,
            null,
            null
        ).copy(
            cityId = "300000"
        )

        val emergencyModel = HealthPlanTaskConverter.convert(emergencyTransport, staff.id)

        val model = emergencyModel.copy(
            createdBy = TaskUpdatedBy(staff.id, TaskSourceType.STAFF),
            content = mapOf(
                "cityId" to "300000"
            ),
            title = EMERGENCY_TITLE
        )

        coEvery { healthPlanTaskService.create(model, staff.id) } returns emergencyTransport.success()
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/persons/${transport.personId}/task", body = emergencyTransport) { response ->
                assertThat(response).isOKWithData(emergencyTransport)
            }
        }

        coVerifyOnce { healthPlanTaskService.create(any(), any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerify { taskReferralSessionsService wasNot called }
    }

    @Test
    fun `#getAll should get all tasks by personId`() {
        val personId = PersonId()

        coEvery { healthPlanTaskService.getByPerson(personId, checkAdherence = true) } returns tasksTransport.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task") { response ->
                assertThat(response).isOKWithData(tasksTransport)
            }
        }

        coVerifyOnce { healthPlanTaskService.getByPerson(any(), any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getAll pass the correct filter options`() {
        val personId = PersonId()

        coEvery {
            healthPlanTaskService.getByPerson(
                personId,
                mapOf("status" to listOf("active", "draft")),
                checkAdherence = true
            )
        } returns tasksTransport.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task?status=active&status=draft") { response ->
                assertThat(response).isOKWithData(tasksTransport)
            }
        }

        coVerifyOnce { healthPlanTaskService.getByPerson(any(), any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getTaskReferralByPersonAndCurrentStaff should return task of type referral`() {
        val healthPlanTask = referral.generalize().copy(
            lastRequesterStaffId = staff.id,
            requestersStaffIds = setOf(staff.id),
            releasedByStaffId = staff.id,
            groupId = null
        )

        val group = HealthPlanTaskGroupTransport(
            id = RangeUUID.generate(),
            healthPlanId = healthPlanTask.healthPlanId,
            name = "Name",
            personId = personId,
        )
        val result = ReferralSuggestionResponseConverter.convert(healthPlanTask.specialize(), group)

        coEvery { staffService.get(staff.id) } returns staff.success()
        coEvery { healthPlanTaskService.getTaskReferralByPersonAndStaffId(personId, staff.id) } returns result.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task/referral/staff") { response ->
                assertThat(response).isOKWithData(result)
            }
        }

        coVerifyOnce { healthPlanTaskService.getTaskReferralByPersonAndStaffId(any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getTaskReferralByPersonAndCurrentStaff should return success if return NotFoundException`() {
        coEvery { staffService.get(staff.id) } returns staff.success()
        coEvery {
            healthPlanTaskService.getTaskReferralByPersonAndStaffId(
                personId,
                staff.id
            )
        } returns NotFoundException("Task of referral not found for staff id ${staff.id}").failure()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task/referral/staff") { response ->
                assertThat(response).isOKWithData(false)
            }
        }

        coVerifyOnce { healthPlanTaskService.getTaskReferralByPersonAndStaffId(any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getCounters returns counter by person id`() {
        val expected = HealthPlanTaskCounters()

        coEvery { healthPlanTaskService.getCountsByPersonId(personId) } returns expected.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task/counters") { response ->
                assertThat(response).isOKWithData(expected)
            }

            coVerifyOnce { healthPlanTaskService.getCountsByPersonId(any(), any()) }
            coVerify { taskReferralSessionsService wasNot called }
            coVerify { staffService wasNot called }
        }
    }

    @Test
    fun `#getCounters returns counter by person id and filters`() {
        val expected = HealthPlanTaskCounters()

        coEvery {
            healthPlanTaskService.getCountsByPersonId(
                personId,
                mapOf(
                    "status" to listOf("active"),
                    "type" to listOf("prescription")
                )
            )
        } returns expected.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task/counters?status=active&type=prescription") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { healthPlanTaskService.getCountsByPersonId(any(), any()) }
        coVerify { taskReferralSessionsService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getTaskReferralSessionsCount returns sessions count by taskId and personId`() {
        val referral = TestModelFactory.buildHealthPlanTaskReferral(
            personId = personId,
            lastRequesterStaffId = staff.id,
            releasedByStaffId = staff.id
        ).specialize<Referral>()

        val transport = TestTransportFactory.buildReferralTransport(
            listOf(referral),
            staff = staff
        )[0]

        val expected = HealthPlanTaskSessionsCountResponse(
            totalSessionsQuantity = 10,
            completedSessionsQuantity = 5,
            availableSessionsQuantity = 5,
            task = transport
        )

        coEvery {
            taskReferralSessionsService.getTaskReferralSessionsCount(personId, referral.id)
        } returns expected.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task/${referral.id}/sessions") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { taskReferralSessionsService.getTaskReferralSessionsCount(any(), any()) }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getTaskReferralSessionsCount returns sessions count by taskId and personId without session`() {
        val referral = TestModelFactory.buildHealthPlanTaskReferral(
            personId = personId,
            lastRequesterStaffId = staff.id,
            releasedByStaffId = staff.id
        ).specialize<Referral>()

        val transport = TestTransportFactory.buildReferralTransport(
            listOf(referral),
            staff = staff
        )[0]

        val expected = HealthPlanTaskSessionsCountResponse(
            totalSessionsQuantity = null,
            completedSessionsQuantity = null,
            availableSessionsQuantity = null,
            task = transport
        )

        coEvery {
            taskReferralSessionsService.getTaskReferralSessionsCount(personId, referral.id)
        } returns expected.success()

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/$personId/task/${referral.id}/sessions") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { taskReferralSessionsService.getTaskReferralSessionsCount(any(), any()) }
        coVerify { staffService wasNot called }
    }

    @Nested
    inner class AdherenceLogicTests {

        @Test
        fun `#update should update HealthPlanTask and return referral task with adherence validation as ADHERENT`() =
            runBlocking {
                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails)
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.ADHERENT,
                    model = null,
                    validatedDemand = null
                )

                val model = HealthPlanTaskConverter.convert(referralTransport, staff.id)
                val expectedResponse = referralTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)
                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery {
                        healthPlanTaskService.update(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        put(
                            to = "/ehr/persons/${personId}/task/${referralTransport.id!!}/",
                            body = referralTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#update should update HealthPlanTask and return test_request task with adherence validation as ADHERENT`() =
            runBlocking {
                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val testRequestTransport = TestRequestTransport(
                    healthPlanTaskTransport = GenericTaskTransport(
                        id = RangeUUID.generate(),
                        personId = personId.toUUID(),
                        title = "Hemoglobina",
                        description = "Hemoglobina",
                        dueDate = null,
                        status = HealthPlanTaskStatus.ACTIVE,
                        type = HealthPlanTaskType.TEST_REQUEST,
                        favorite = false,
                        caseRecordDetails = listOf(caseRecordDetails)
                    ),
                    code = "1234"
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.ADHERENT,
                    model = null,
                    validatedDemand = null
                )

                val model = HealthPlanTaskConverter.convert(testRequestTransport, staff.id)
                val expectedResponse = testRequestTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery {
                        healthPlanTaskService.update(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        put(
                            to = "/ehr/persons/${personId}/task/${testRequestTransport.id!!}/",
                            body = testRequestTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#update should update HealthPlanTask and return referral task with adherence validation as NON_ADHERENT`() =
            runBlocking {
                val program = TestModelFactory.buildServiceScriptNode(rootNodeId = RangeUUID.generate())

                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails)
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.NON_ADHERENT,
                    model = HLAdherence.AdherenceReferenceModel.PROGRAM,
                    validatedDemand = listOf(
                        ValidatedDemand(
                            id = program.id,
                            healthLogicId = program.rootNodeId,
                            healthConditionId = caseRecordDetails.description.id!!,
                            healthConditionCode = caseRecordDetails.description.value
                        )
                    )
                )

                val model = HealthPlanTaskConverter.convert(referralTransport, staff.id)
                val expectedResponse = referralTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {

                    coEvery {
                        healthPlanTaskService.update(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        put(
                            to = "/ehr/persons/${personId}/task/${referralTransport.id!!}/",
                            body = referralTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#update should update HealthPlanTask and return test_request task with adherence validation as NON_ADHERENT`() =
            runBlocking {
                val program = TestModelFactory.buildServiceScriptNode(rootNodeId = RangeUUID.generate())

                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val testRequestTransport = TestRequestTransport(
                    healthPlanTaskTransport = GenericTaskTransport(
                        id = RangeUUID.generate(),
                        personId = personId.toUUID(),
                        title = "Hemoglobina",
                        description = "Hemoglobina",
                        dueDate = null,
                        status = HealthPlanTaskStatus.ACTIVE,
                        type = HealthPlanTaskType.TEST_REQUEST,
                        favorite = false,
                        caseRecordDetails = listOf(caseRecordDetails)
                    ),
                    code = "1234"
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.NON_ADHERENT,
                    model = HLAdherence.AdherenceReferenceModel.PROGRAM,
                    validatedDemand = listOf(
                        ValidatedDemand(
                            id = program.id,
                            healthLogicId = program.rootNodeId,
                            healthConditionId = caseRecordDetails.description.id!!,
                            healthConditionCode = caseRecordDetails.description.value
                        )
                    )
                )

                val model = HealthPlanTaskConverter.convert(testRequestTransport, staff.id)
                val expectedResponse = testRequestTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery {
                        healthPlanTaskService.update(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        put(
                            to = "/ehr/persons/${personId}/task/${testRequestTransport.id!!}/",
                            body = testRequestTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#update should update HealthPlanTask and don't validate adherence when condition is in flag`() =
            runBlocking {
                val ignoredHealthConditionsIds = listOf(RangeUUID.generate(), RangeUUID.generate())

                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = ignoredHealthConditionsIds[0].toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = model.personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails)
                )

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to "${ignoredHealthConditionsIds[0]},${ignoredHealthConditionsIds[1]}",
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery { healthPlanTaskService.update(model, staff.id, false) } returns referralTransport.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        put(
                            to = "/ehr/persons/${transport.personId}/task/${transport.id!!}/",
                            body = transport
                        ) { response ->
                            assertThat(response).isOKWithData(referralTransport)
                        }
                    }

                    coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#update should update HealthPlanTask and don't validate adherence when second condition is in flag`() =
            runBlocking {
                val ignoredHealthConditionsIds = listOf(RangeUUID.generate(), RangeUUID.generate())

                val caseRecordDetails0 = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = ignoredHealthConditionsIds[0].toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val caseRecordDetails1 = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = ignoredHealthConditionsIds[1].toString(),
                        type = Disease.Type.CID_10,
                        value = "20",
                        description = "Ciático",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = model.personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails0, caseRecordDetails1)
                )

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to ignoredHealthConditionsIds[1].toString(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery { healthPlanTaskService.update(model, staff.id, false) } returns referralTransport.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        put(
                            to = "/ehr/persons/${transport.personId}/task/${transport.id!!}/",
                            body = transport
                        ) { response ->
                            assertThat(response).isOKWithData(referralTransport)

                        }
                    }

                    coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#update should update HealthPlanTask and don't validate adherence when flag is false`() = runBlocking {
            withFeatureFlag(FeatureNamespace.HEALTH_LOGICS, "enable_consumer_adherence_from_health_plan_task", false) {
                coEvery { healthPlanTaskService.update(model, staff.id, false) } returns transport.success()
                coEvery { staffService.get(staff.id) } returns staff.success()

                authenticatedAs(idToken, staffTest) {
                    put(
                        to = "/ehr/persons/${transport.personId}/task/${transport.id!!}/",
                        body = transport
                    ) { response ->
                        assertThat(response).isOKWithData(transport)
                    }
                }

                coVerifyOnce { healthPlanTaskService.update(any(), any(), any()) }
                coVerifyOnce { staffService.get(any()) }
                coVerify { taskReferralSessionsService wasNot called }
            }
        }

        @Test
        fun `#create should create HealthPlanTask and return referral task with adherence validation as ADHERENT`() =
            runBlocking {
                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails)
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.ADHERENT,
                    model = null,
                    validatedDemand = null
                )

                val model = HealthPlanTaskConverter.convert(referralTransport, staff.id)
                    .copy(title = "Encaminhamento para Ortopedia")
                val expectedResponse = referralTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)
                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery {
                        healthPlanTaskService.create(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        post(
                            to = "/ehr/persons/${personId}/task",
                            body = referralTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#create should create HealthPlanTask and return test_request task with adherence validation as ADHERENT`() =
            runBlocking {
                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val testRequestTransport = TestRequestTransport(
                    healthPlanTaskTransport = GenericTaskTransport(
                        id = RangeUUID.generate(),
                        personId = personId.toUUID(),
                        title = "Hemoglobina",
                        description = "Hemoglobina",
                        dueDate = null,
                        status = HealthPlanTaskStatus.ACTIVE,
                        type = HealthPlanTaskType.TEST_REQUEST,
                        favorite = false,
                        caseRecordDetails = listOf(caseRecordDetails)
                    ),
                    code = "1234"
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.ADHERENT,
                    model = null,
                    validatedDemand = null
                )

                val model = HealthPlanTaskConverter.convert(testRequestTransport, staff.id)
                val expectedResponse = testRequestTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery {
                        healthPlanTaskService.create(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        post(
                            to = "/ehr/persons/${personId}/task",
                            body = testRequestTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#create should create HealthPlanTask and return referral task with adherence validation as NON_ADHERENT`() =
            runBlocking {
                val program = TestModelFactory.buildServiceScriptNode(rootNodeId = RangeUUID.generate())

                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails)
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.NON_ADHERENT,
                    model = HLAdherence.AdherenceReferenceModel.PROGRAM,
                    validatedDemand = listOf(
                        ValidatedDemand(
                            id = program.id,
                            healthLogicId = program.rootNodeId,
                            healthConditionId = caseRecordDetails.description.id!!,
                            healthConditionCode = caseRecordDetails.description.value
                        )
                    )
                )

                val model = HealthPlanTaskConverter.convert(referralTransport, staff.id)
                    .copy(title = "Encaminhamento para Ortopedia")
                val expectedResponse = referralTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {

                    coEvery {
                        healthPlanTaskService.create(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        post(
                            to = "/ehr/persons/${personId}/task",
                            body = referralTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#create should create HealthPlanTask and return test_request task with adherence validation as NON_ADHERENT`() =
            runBlocking {
                val program = TestModelFactory.buildServiceScriptNode(rootNodeId = RangeUUID.generate())

                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val testRequestTransport = TestRequestTransport(
                    healthPlanTaskTransport = GenericTaskTransport(
                        id = RangeUUID.generate(),
                        personId = personId.toUUID(),
                        title = "Hemoglobina",
                        description = "Hemoglobina",
                        dueDate = null,
                        status = HealthPlanTaskStatus.ACTIVE,
                        type = HealthPlanTaskType.TEST_REQUEST,
                        favorite = false,
                        caseRecordDetails = listOf(caseRecordDetails)
                    ),
                    code = "1234"
                )

                val expectedAdherenceValidation = HLAdherenceValidationResponse(
                    result = HLAdherence.AdherenceResultType.NON_ADHERENT,
                    model = HLAdherence.AdherenceReferenceModel.PROGRAM,
                    validatedDemand = listOf(
                        ValidatedDemand(
                            id = program.id,
                            healthLogicId = program.rootNodeId,
                            healthConditionId = caseRecordDetails.description.id!!,
                            healthConditionCode = caseRecordDetails.description.value
                        )
                    )
                )

                val model = HealthPlanTaskConverter.convert(testRequestTransport, staff.id)
                val expectedResponse = testRequestTransport.copy(hlAdherenceValidation = expectedAdherenceValidation)

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to emptyList<UUID>(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery {
                        healthPlanTaskService.create(model, staff.id, true)
                    } returns expectedResponse.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        post(
                            to = "/ehr/persons/${personId}/task",
                            body = testRequestTransport
                        ) { response ->
                            assertThat(response).isOKWithData(expectedResponse)
                        }
                    }

                    coVerifyOnce { staffService.get(any()) }
                    coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#create should create HealthPlanTask and don't validate adherence when condition is in flag`() =
            runBlocking {
                val ignoredHealthConditionsIds = listOf(RangeUUID.generate(), RangeUUID.generate())

                val caseRecordDetails = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = ignoredHealthConditionsIds[0].toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = model.personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails)
                )

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to "${ignoredHealthConditionsIds[0]},${ignoredHealthConditionsIds[1]}",
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery { healthPlanTaskService.create(model, staff.id, false) } returns referralTransport.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        post(
                            to = "/ehr/persons/${transport.personId}/task",
                            body = transport
                        ) { response ->
                            assertThat(response).isOKWithData(referralTransport)
                        }
                    }

                    coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#create should create HealthPlanTask and don't validate adherence when second condition is in flag`() =
            runBlocking {
                val ignoredHealthConditionsIds = listOf(RangeUUID.generate(), RangeUUID.generate())

                val caseRecordDetails0 = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = ignoredHealthConditionsIds[0].toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val caseRecordDetails1 = CaseRecordDetails(
                    description = DiseaseDetails(
                        id = ignoredHealthConditionsIds[1].toString(),
                        type = Disease.Type.CID_10,
                        value = "20",
                        description = "Ciático",
                    ),
                    severity = CaseSeverity.ONGOING
                )

                val referralTransport = ReferralTransport(
                    id = RangeUUID.generate(),
                    personId = model.personId.toUUID(),
                    title = "Ortopedista",
                    description = "Joelho",
                    dueDate = null,
                    status = HealthPlanTaskStatus.ACTIVE,
                    type = HealthPlanTaskType.REFERRAL,
                    diagnosticHypothesis = "Lesão por esforço repetitivo",
                    specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
                    subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
                    favorite = false,
                    caseRecordDetails = listOf(caseRecordDetails0, caseRecordDetails1)
                )

                withFeatureFlags(
                    FeatureNamespace.HEALTH_LOGICS,
                    mapOf(
                        "ignored_health_conditions_ids" to ignoredHealthConditionsIds[1].toString(),
                        "enable_consumer_adherence_from_health_plan_task" to true
                    )
                ) {
                    coEvery { healthPlanTaskService.create(model, staff.id, false) } returns referralTransport.success()
                    coEvery { staffService.get(staff.id) } returns staff.success()

                    authenticatedAs(idToken, staffTest) {
                        post(
                            to = "/ehr/persons/${transport.personId}/task",
                            body = transport
                        ) { response ->
                            assertThat(response).isOKWithData(referralTransport)

                        }
                    }

                    coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                    coVerifyOnce { staffService.get(any()) }
                    coVerify { taskReferralSessionsService wasNot called }
                }
            }

        @Test
        fun `#create should create HealthPlanTask and don't validate adherence when flag is false`() = runBlocking {
            withFeatureFlag(FeatureNamespace.HEALTH_LOGICS, "enable_consumer_adherence_from_health_plan_task", false) {
                coEvery { healthPlanTaskService.create(model, staff.id, false) } returns transport.success()
                coEvery { staffService.get(staff.id) } returns staff.success()

                authenticatedAs(idToken, staffTest) {
                    post(
                        to = "/ehr/persons/${transport.personId}/task",
                        body = transport
                    ) { response ->
                        assertThat(response).isOKWithData(transport)
                    }
                }

                coVerifyOnce { healthPlanTaskService.create(any(), any(), any()) }
                coVerifyOnce { staffService.get(any()) }
                coVerify { taskReferralSessionsService wasNot called }
            }
        }

        @Test
        fun `#create should create a new HealthPlanTask when type is SURGERY_PRESCRIPTION and generating automatic title`() {
            val staffName = staff.fullName
            val fixedText =
                """
                    Olá! Somos do time de cirurgias aqui na Alice.
        
                    Vimos que você tem uma indicação por $staffName para realizar uma cirurgia. A partir de agora, nosso time administrativo fará uma pré-análise de cobertura do procedimento.
                    É importante destacar que, conforme a regulamentação da Agência Nacional de Saúde (ANS), o prazo para análise da cobertura do procedimento cirúrgico é de até 10 dias úteis. 
                    Em breve, nosso time entrará em contato aqui no App dar uma devolutiva sobre a cobertura ou não da cirurgia e orientar quais são os próximos passos.
                """.trimIndent()
            val expectedTitle = "Indicação de procedimento cirúrgico solicitado por $staffName"

            val transport = SurgeryPrescriptionTransport(
                genericTransport.copy(
                    type = HealthPlanTaskType.SURGERY_PRESCRIPTION,
                    description = fixedText, title = expectedTitle
                ),
                reason = "Teste 3",
                expectedDate = LocalDate.of(2024, 9, 12).toString(),
                provider = Hospital(name = "Hospital da Criança", id = RangeUUID.generate()),
                procedures = listOf(SurgicalProcedure(description = "Corte", tussCode = "tussCode12412"))
            )

            coEvery {
                staffService.get(staff.id)
            } returns staff.success()

            coEvery {
                healthPlanTaskService.create(
                    match {
                        it.title == expectedTitle
                    },
                    staff.id
                )
            } returns transport.success()

            authenticatedAs(idToken, staffTest) {
                post(to = "/ehr/persons/${transport.personId}/task", body = transport) { response ->
                    assertThat(response).isOKWithData(transport)
                }
            }

            coVerifyOnce { healthPlanTaskService.create(any(), any()) }
            coVerify(atLeast = 1) { staffService.get(any()) }
        }
    }

}
