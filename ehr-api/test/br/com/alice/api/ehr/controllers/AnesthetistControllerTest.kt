package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.converters.AnesthetistConverter
import br.com.alice.api.ehr.converters.AnesthetistConverter.toCouncil
import br.com.alice.api.ehr.model.AnesthetistRequest
import br.com.alice.api.ehr.model.AnesthetistResponse
import br.com.alice.api.ehr.model.CrmAnesthetist
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.staff.client.HealthProfessionalFilters
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.models.StaffHpKey
import br.com.alice.staff.models.StaffValidation
import br.com.alice.staff.models.StaffValidationErrors
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class AnesthetistControllerTest : ControllerTestHelper() {
    val staffService: StaffService = mockk()
    val healthProfessionalService: HealthProfessionalService = mockk()

    val controller = AnesthetistController(
        staffService,
        healthProfessionalService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `addAnesthetist should add anesthetist`() = mockRangeUuidAndDateTime { uuid, _ ->
        val request = AnesthetistRequest(
            name = "Dr. John Doe",
            crm = CrmAnesthetist(
                number = "123456",
                state = State.SP
            ),
            email = "joã**************"
        )
        val expected = AnesthetistResponse(
            id = uuid,
            name = request.name,
            crm = request.crm,
            email = request.email,
        )

        val (staff, hp) = AnesthetistConverter.toModel(request)

        coEvery { staffService.addAndPublishAnesthetist(staff, hp) } returns staff.success()
        coEvery {
            staffService.validHealthProfessional(
                StaffHpKey(
                    email = request.email,
                    council = request.crm.toCouncil()
                )
            )
        } returns StaffValidation().success()

        authenticatedAs(idToken, staffTest) {
            post("ehr/anesthetist", request) { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { staffService.addAndPublishAnesthetist(any(), any()) }
        coVerifyOnce { staffService.validHealthProfessional(any()) }
    }

    @Test
    fun `addAnesthetist should return 400 when staff exists`() {
        val request = AnesthetistRequest(
            name = "Dr. John Doe",
            crm = CrmAnesthetist(
                number = "123456",
                state = State.SP
            ),
            email = "joã**************"
        )
        val errors = StaffValidation(
            errors = listOf(
                StaffValidationErrors("email_exist", "Email already exists"),
                StaffValidationErrors("hp_exist", "hp exists")
            )
        )
        coEvery {
            staffService.validHealthProfessional(
                StaffHpKey(
                    email = request.email,
                    council = request.crm.toCouncil()
                )
            )
        } returns errors.success()

        authenticatedAs(idToken, staffTest) {
            post("ehr/anesthetist", request) { response ->
                assertThat(response).isBadRequestWithData(errors)
            }
        }

        coVerifyOnce { staffService.validHealthProfessional(any()) }
        coVerifyNone { staffService.addAndPublishAnesthetist(any(), any()) }
    }

    @Test
    fun `index should return anesthetist`() {
        val hp = TestModelFactory.buildHealthProfessional(
            role = Role.ANESTHETIST,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            name = "João",
            email = "joã**************"
        )

        val expected = hp.let { AnesthetistConverter.hpToResponse(it) }
        val term = "joão"
        coEvery {
            healthProfessionalService.findBy(
                HealthProfessionalFilters(
                    roles = listOf(Role.ANESTHETIST),
                    types = listOf(StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL),
                    searchTerm = term,
                    range = IntRange(0, 19)
                )
            )
        } returns listOf(hp).success()

        authenticatedAs(idToken, staffTest) {
            get("ehr/anesthetist?q=joão") { response ->
                assertThat(response).isOKWithData(listOf(expected))
            }
        }

        coEvery { staffService.findBy(any()) }
    }

    @Test
    fun `types should return anesthetist types`() {
        authenticatedAs(idToken, staffTest) {
            get("ehr/anesthetist/types") { response ->
                assertThat(response).isOKWithData(
                    AnesthetistType.values().map {
                        AnesthetistTypeResponse(it.name, it.description)
                    }
                )
            }
        }
    }
}
