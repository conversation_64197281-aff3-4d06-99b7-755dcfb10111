package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.converters.AnesthetistConverter.toCouncil
import br.com.alice.api.ehr.model.AnesthetistRequest
import br.com.alice.api.ehr.model.AnesthetistResponse
import br.com.alice.api.ehr.model.CrmAnesthetist
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.Staff
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class AnesthetistConverterTest {

    private val request = AnesthetistRequest(
        name = "John Doe",
        crm = CrmAnesthetist(number = "123456", state = State.SP),
        email = "joã**************",
    )

    @ParameterizedTest(name = "{0}")
    @ValueSource(strings = ["John Doe", "Jane Smith Silva", "Alice"])
    fun `toModel converter anesthetist request in model`(name: String) = mockRangeUuidAndDateTime { id, _ ->
        val request = request.copy(name = name)

        val firstName = name.split(" ").firstOrNull() ?: ""
        val lastName = name.split(" ").drop(1).joinToString(" ")

        val expected = Staff(
            firstName = firstName,
            lastName = lastName,
            email = "joã**************",
            role = Role.ANESTHETIST,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            gender = Gender.NO_ANSWER,
            active = false
        ) to HealthProfessional(
            staffId = id,
            council = request.crm.toCouncil(),
            name = request.name,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            role = Role.ANESTHETIST,
            email = request.email,
            status = SpecialistStatus.INACTIVE
        )

        val model = AnesthetistConverter.toModel(request)

        Assertions.assertThat(model).isEqualTo(expected)
    }

    @Test
    fun `requestToResponse should return response transport`() {
        mockRangeUUID { staffId ->
            val request = AnesthetistRequest(
                name = "John Doe",
                crm = CrmAnesthetist(number = "123456", state = State.SP),
                email = "joã**************",
            )
            val response = AnesthetistConverter.requestToResponse(request, staffId)

            Assertions.assertThat(response).isEqualTo(
                AnesthetistResponse(
                    name = request.name,
                    crm = request.crm,
                    email = request.email,
                    id = staffId
                )
            )
        }
    }

    @Test
    fun `hpToResponse should converter health professional in transport`() {
        mockRangeUuidAndDateTime { id, _ ->
            val healthProfessional = HealthProfessional(
                staffId = id,
                council = request.crm.toCouncil(),
                name = request.name,
                type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
                role = Role.ANESTHETIST,
                email = request.email,
                status = SpecialistStatus.INACTIVE
            )

            val response = AnesthetistConverter.hpToResponse(healthProfessional)

            Assertions.assertThat(response).isEqualTo(
                AnesthetistResponse(
                    name = healthProfessional.name,
                    crm = CrmAnesthetist(
                        number = healthProfessional.council.number,
                        state = healthProfessional.council.state
                    ),
                    email = healthProfessional.email,
                    id = healthProfessional.staffId
                )
            )
        }
    }

    @Test
    fun `toCouncil should convert to model`() {
        val crm = CrmAnesthetist(
            number = "123456",
            state = State.SP
        )
        val council = crm.toCouncil()

        Assertions.assertThat(council).isEqualTo(
            Council(
                number = crm.number,
                state = crm.state,
                type = CouncilType.CRM
            )
        )
    }
}
