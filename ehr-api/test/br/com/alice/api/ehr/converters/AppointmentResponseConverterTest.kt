package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.AnesthetistAppointmentResponse
import br.com.alice.api.ehr.controllers.model.AnesthetistResponse
import br.com.alice.api.ehr.controllers.model.AppointmentResponse
import br.com.alice.api.ehr.controllers.model.AppointmentStaffResponse
import br.com.alice.api.ehr.controllers.model.CaseRecordDetailsAppointmentResponse
import br.com.alice.api.ehr.controllers.model.CouncilResponse
import br.com.alice.api.ehr.controllers.model.OutcomeResponse
import br.com.alice.api.ehr.controllers.model.SessionResponse
import br.com.alice.api.ehr.helpers.TestTransportFactory.convertHealthPlanTaskTransport
import br.com.alice.api.ehr.model.CrmAnesthetist
import br.com.alice.appointment.client.ChannelReference
import br.com.alice.client.dalya.models.DalyaPredictionValues
import br.com.alice.client.dalya.models.DalyaTriageRecommendationResponse
import br.com.alice.client.dalya.models.DalyaUncertaintiesRangeValues
import br.com.alice.client.dalya.models.DalyaUncertaintiesValues
import br.com.alice.client.dalya.models.TriageSuggestedOutput
import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.Appointment.ReferencedLink
import br.com.alice.data.layer.models.AppointmentComponentType.SUBJECTIVE
import br.com.alice.healthplan.models.EmergencyTransport
import br.com.alice.healthplan.models.GenericTaskTransport
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.PrescriptionTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.models.TestRequestTransport
import br.com.alice.secondary.attention.models.HealthPlanTaskSessionsCount
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import br.com.alice.staff.models.StaffWithHealthProfessional
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import org.assertj.core.api.Assertions.assertThat

class AppointmentResponseConverterTest {

    private val staff = TestModelFactory.buildStaff()
    private val anesthetist = TestModelFactory.buildStaff()
    private val anesthetistHp = TestModelFactory.buildHealthProfessional(
        staffId = anesthetist.id,
    )

    private val staffId = staff.id
    private val staffMap = mapOf(
        staffId to StaffWithHealthProfessional(staff, null),
        anesthetist.id to StaffWithHealthProfessional(anesthetist, anesthetistHp)
    )
    private val referral = TestModelFactory.buildHealthPlanTaskReferral()

    private val session = HealthPlanTaskSessionsCount(
        totalSessionsQuantity = 1,
        completedSessionsQuantity = 0,
        availableSessionsQuantity = 1,
        healthPlanTask = referral.specialize()
    )

    @Test
    fun `#convert returns appointment empty`() {
        val appointment = Appointment(
            type = AppointmentType.DEFAULT,
            staffId = staff.id,
            personId = PersonId()
        )

        val response = AppointmentResponse(
            physician = AppointmentStaffResponse(
                id = staff.id.toString(),
                firstName = staff.firstName,
                fullName = staff.fullName,
                profileImageUrl = staff.profileImageUrl,
                description = StaffGenderDescriptionConverter.convert(staff),
                council = CouncilResponse("", "", "")
            ),
            personId = appointment.personId.toString(),
            id = appointment.id.toString(),
            type = AppointmentType.DEFAULT,
            status = AppointmentStatus.DRAFT,
            description = null,
            isEditable = true,
            guidance = null,
            excuseNotesCount = 0,
            createdAt = appointment.createdAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            updatedAt = appointment.updatedAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            version = 0,
            complaint = "",
            conduct = "",
            excuseNotes = emptyList()
        )

        val result = AppointmentResponseConverter.convert(
            source = appointment,
            loggedStaffId = staffId,
            staffMap = staffMap,
            referralsDetails = null
        )
        assertThat(result).isEqualTo(response)
    }

    @Test
    fun `#convert returns appointment full mapping`() {
        val appointmentId = RangeUUID.generate()
        val scriptNode = TestModelFactory.buildServiceScriptNode()
        val channelReference = ChannelReference("channelId", "channelName")
        val evolutions = listOf(TestModelFactory.buildAppointmentEvolution(appointmentId))
        val healthConditionId = RangeUUID.generate()

        val appointment = Appointment(
            staffId = staffId,
            personId = PersonId(),
            description = "description",
            guidance = "guidance",
            subjectiveCodes = listOf(Disease(Disease.Type.CIAP_2, "A03", "subjective - CIAP A03")),
            objectiveCodes = listOf(Disease(Disease.Type.CIAP_2, "A03", "objective - CIAP A03")),
            objective = "objective",
            subjective = "subjective",
            components = listOf(AppointmentComponent(SUBJECTIVE, 0)),
            plan = "plan",
            excuseNotes = listOf(
                ExcuseNote(
                    description = "presente no período da manhã para consulta",
                    id = "0c56bf1b-bfa6-4cff-964b-99473917a36e"
                )
            ),
            type = AppointmentType.DEFAULT,
            serviceScriptId = scriptNode.id,
            completedAt = LocalDateTime.now(),
            ownerStaffIds = setOf(staffId),
            startedAt = LocalDateTime.now(),
            endedAt = LocalDateTime.now(),
            attachments = listOf(
                Attachment(
                    id = RangeUUID.generate(),
                    type = "type",
                    fileName = "fileName",
                    thumbnailId = RangeUUID.generate()
                )
            ),
            specialty = AppointmentSpecialty("Test", RangeUUID.generate()),
            specialist = AppointmentSpecialist("Test", RangeUUID.generate()),
            referencedLinks = listOf(
                ReferencedLink(id = RangeUUID.generate(), model = Appointment.ReferenceLinkModel.SCHEDULE),
                ReferencedLink(RangeUUID.generate(), Appointment.ReferenceLinkModel.HEALTH_PLAN_TASK)
            ),
            status = AppointmentStatus.DRAFT,
            discardedType = AppointmentDiscardedType.OTHERS,
            discardedReason = "discardedReason",
            finishType = AppointmentFinishType.MANUAL,
            clinicalEvaluation = "clinicalEvaluation",
            treatedBy = TreatedBy.PHYSICIAN,
            outcome = Outcome.ATTENDANCE_OR_DIGITAL_PRESCRIPTION,
            channelId = "channelId",
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    caseId = RangeUUID.generate(),
                    description = DiseaseDetails(
                        id = healthConditionId.toString(),
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING,
                    dalyaRecommendation = DalyaTriageRecommendationResponse(
                        memberInternalCode = "MEM-BER",
                        screeningId = RangeUUID.generate(),
                        dalyaChoice = TriageSuggestedOutput.PA_DIGITAL,
                        dalyaPrediction = DalyaPredictionValues(1.0f, 0.0f, 0.0f, 0.0f),
                        dalyaUncertainties = DalyaUncertaintiesValues(
                            paDigital = DalyaUncertaintiesRangeValues(0.0f, 0.0f),
                            ps = DalyaUncertaintiesRangeValues(0.0f, 0.0f),
                            aps = DalyaUncertaintiesRangeValues(0.0f, 0.0f),
                            specialist = DalyaUncertaintiesRangeValues(0.0f, 0.0f)
                        )
                    )
                )
            ),
            draftGroupStaffIds = listOf(staffId),
            event = AppointmentEventDetail(
                name = "name",
                referenceModelId = "id",
                referenceModel = AppointmentEventReferenceModel.CHANNEL
            ),
            name = "name",
            templateType = TemplateType.SOAP,
            staffRole = staff.role,
            id = appointmentId,
            version = 0,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            updatedBy = null,
            emptyEventReason = "Reason",
            protocolNavigationHistory = listOf(
                ProtocolNavigation(
                    rootNodeId = RangeUUID.generate(),
                    protocolName = "protocolName",
                    nodeId = RangeUUID.generate(),
                    question = "question",
                    answer = "answer",
                    date = LocalDateTime.now(),
                    type = "type",
                    index = 0
                ),
                ProtocolNavigation(
                    nodeId = RangeUUID.generate(),
                    question = "question",
                    date = LocalDateTime.now(),
                    index = 0
                )
            ),
            anesthetist = Anesthetist(
                type = AnesthetistType.INDIVIDUAL,
                staffId = anesthetist.id
            )
        )

        val personId = appointment.personId
        val healthPlan = TestModelFactory.buildHealthPlan(personId = personId)
        val group = HealthPlanTaskGroupTransport(
            id = RangeUUID.generate(),
            healthPlanId = healthPlan.id,
            name = "Correr Maratona",
            personId = healthPlan.personId
        )
        val prescriptionTask =
            TestModelFactory.buildHealthPlanTaskPrescription(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                groupId = group.id
            )
        val eatingTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.EATING,
                groupId = group.id,
                caseId = RangeUUID.generate()
            )
        val physicalActivityTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.PHYSICAL_ACTIVITY,
                groupId = group.id,
                caseId = RangeUUID.generate()
            )
        val sleepTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.SLEEP,
                groupId = group.id,
                caseId = RangeUUID.generate()
            )
        val moodTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.MOOD,
                groupId = group.id
            )
        val othersTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.OTHERS,
                groupId = group.id
            )
        val testRequestTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.TEST_REQUEST,
                groupId = group.id
            )

        val referralTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.REFERRAL,
                groupId = group.id
            )

        val emergencyTask =
            TestModelFactory.buildHealthPlanTask(
                personId = personId,
                staffId = staff.id,
                healthPlanId = healthPlan.id,
                type = HealthPlanTaskType.EMERGENCY,
                groupId = group.id
            )

        val healtPlanTasksTransport = HealthPlanTasksTransport(
            prescription = convertHealthPlanTaskTransport<Prescription, PrescriptionTransport>(
                listOf(prescriptionTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.PRESCRIPTION,
                group.let { mapOf(it.id!! to it) }
            ),
            eating = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
                listOf(eatingTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.EATING,
                group.let { mapOf(it.id!! to it) }
            ),
            physicalActivity = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
                listOf(physicalActivityTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.PHYSICAL_ACTIVITY,
                group.let { mapOf(it.id!! to it) }
            ),
            sleep = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
                listOf(sleepTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.SLEEP,
                group.let { mapOf(it.id!! to it) }
            ),
            mood = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
                listOf(moodTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.MOOD,
                group.let { mapOf(it.id!! to it) }
            ),
            others = convertHealthPlanTaskTransport<GenericTask, GenericTaskTransport>(
                listOf(othersTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.OTHERS,
                group.let { mapOf(it.id!! to it) }
            ),
            testRequest = convertHealthPlanTaskTransport<TestRequest, TestRequestTransport>(
                listOf(testRequestTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.TEST_REQUEST,
                group.let { mapOf(it.id!! to it) }
            ),
            referral = convertHealthPlanTaskTransport<Referral, ReferralTransport>(
                listOf(referralTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.REFERRAL,
                group.let { mapOf(it.id!! to it) }
            ),
            emergency = convertHealthPlanTaskTransport<Emergency, EmergencyTransport>(
                listOf(emergencyTask),
                mapOf(staff.id to staff),
                HealthPlanTaskType.EMERGENCY,
                group.let { mapOf(it.id!! to it) }
            ),
        )

        val caseRecordsResponse = CaseRecordDetailsAppointmentResponse(
            caseId = appointment.caseRecordDetails?.first()?.caseId,
            description = appointment.caseRecordDetails?.first()?.description!!,
            cipes = appointment.caseRecordDetails?.first()?.cipes,
            severity = appointment.caseRecordDetails?.first()?.severity!!,
            follow = appointment.caseRecordDetails?.first()?.follow,
            observation = appointment.caseRecordDetails?.first()?.observation,
            channel = appointment.caseRecordDetails?.first()?.channel,
            specialistOpinion = appointment.caseRecordDetails?.first()?.specialistOpinion,
            seriousness = appointment.caseRecordDetails?.first()?.seriousness,
            healthPlanTasks = healtPlanTasksTransport.eating,
            dalyaRecommendation = appointment.caseRecordDetails?.first()?.dalyaRecommendation
        )
        val sessionResponse = SessionResponse(
            totalQuantity = 1,
            completedQuantity = 0,
            availableQuantity = 1
        )

        val response = AppointmentResponse(
            physician = AppointmentStaffResponseConverter.convert(staff, null),
            personId = appointment.personId.toString(),
            id = appointment.id.toString(),
            type = AppointmentType.DEFAULT,
            status = AppointmentStatus.DRAFT,
            discardedType = appointment.discardedType,
            description = appointment.description!!,
            isEditable = true,
            guidance = appointment.guidance!!,
            excuseNotes = appointment.excuseNotes,
            excuseNotesCount = 1,
            serviceScript = scriptNode,
            createdAt = appointment.createdAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            updatedAt = appointment.updatedAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            startedAt = appointment.startedAt,
            endedAt = appointment.endedAt,
            attachments = appointment.attachments,
            version = appointment.version,
            evolutions = evolutions.map { AppointmentEvolutionResponseConverter.convert(it, staffMap) },
            subjectiveCodes = appointment.subjectiveCodes,
            objectiveCodes = appointment.objectiveCodes,
            clinicalEvaluationCodes = appointment.objectiveCodes,
            specialty = appointment.specialty,
            specialist = appointment.specialist,
            channel = channelReference,
            referencedLinks = appointment.referencedLinks,
            clinicalEvaluation = appointment.clinicalEvaluation,
            outcome = appointment.outcome,
            outcomeResponse = OutcomeResponse(
                appointment.outcome!!.name,
                appointment.outcome!!.description,
                appointment.outcome!!.severity
            ),
            treatedBy = appointment.treatedBy,
            channelId = appointment.channelId,
            plan = appointment.plan,
            objective = appointment.objective,
            complaint = appointment.description!!,
            conduct = appointment.guidance!!,
            completed = true,
            channelReference = channelReference,
            caseRecordDetails = listOf(caseRecordsResponse),
            draftGroup = listOf(RelatedStaff(staff.fullName, staffId, staff.fullName)),
            subjective = appointment.subjective,
            event = appointment.event,
            name = appointment.name,
            components = appointment.components,
            templateType = appointment.templateType,
            emptyEventReason = "Reason",
            protocolNavigationHistory = appointment.protocolNavigationHistory,
            session = sessionResponse,
            anesthetist = AnesthetistAppointmentResponse(
                type = AnesthetistType.INDIVIDUAL,
                staff = AnesthetistResponse(
                    id = anesthetist.id,
                    name = anesthetist.fullName,
                    crm = CrmAnesthetist(
                        number = anesthetistHp.council.number,
                        state = anesthetistHp.council.state
                    )
                )
            )
        )

        val mapOfTasks = mapOf<UUID?, List<HealthPlanTaskTransport>>(
            healthConditionId to response.caseRecordDetails?.first()?.healthPlanTasks!!
        )

        val result = AppointmentResponseConverter.convert(
            source = appointment,
            loggedStaffId = staffId,
            staffMap = staffMap,
            script = scriptNode,
            channelReference = channelReference,
            evolutions = evolutions,
            healthPlanTasks = mapOfTasks,
            referralsDetails = session
        )
        assertThat(result).isEqualTo(response)
    }

}
