package br.com.alice.api.ehr.converters

import br.com.alice.appointment.client.AnesthetistAppointmentRequest
import br.com.alice.appointment.client.AnesthetistStaffRequest
import br.com.alice.appointment.client.UpdateAppointmentDiscardedType
import br.com.alice.appointment.client.UpdateAppointmentRequest
import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentUpdateRequestConverterTest {

    private val staff = TestModelFactory.buildStaff()
    private val staffId = staff.id
    private val localDateTimeNow = LocalDateTime.now()

    @BeforeTest
    fun clearMockContext() = unmockkObject(RangeUUID)

    @Test
    fun `#convert returns appointment empty`() = runBlocking {
        val appointmentId = RangeUUID.generate()
        val appointment = Appointment(
            id = appointmentId,
            type = AppointmentType.DEFAULT,
            staffId = staff.id,
            ownerStaffIds = setOf(staff.id),
            personId = PersonId(),
            version = 5,
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow
        )

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            version = appointment.version,
            type = appointment.type
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow
            val result = AppointmentUpdateRequestConverter.convert(
                source = request,
                id = appointmentId,
                staff = staff
            )
            assertThat(result).isEqualTo(appointment)
        }
    }

    @Test
    fun `#convert returns appointment no show`() = runBlocking {
        val appointmentId = RangeUUID.generate()
        val appointment = Appointment(
            id = appointmentId,
            type = AppointmentType.DEFAULT,
            staffId = staff.id,
            ownerStaffIds = setOf(staff.id),
            personId = PersonId(),
            discardedType = AppointmentDiscardedType.NO_SHOW,
            version = 5,
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow
        )

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            version = appointment.version,
            type = appointment.type,
            discardedType = UpdateAppointmentDiscardedType.NO_SHOW
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow
            val result = AppointmentUpdateRequestConverter.convert(
                source = request,
                id = appointmentId,
                staff = staff
            )
            assertThat(result).isEqualTo(appointment)
        }
    }

    @Test
    fun `#convert returns appointment full mapping`() = runBlocking {
        val appointmentId = RangeUUID.generate()
        val scriptNodeId = RangeUUID.generate()
        val anesthetistId = RangeUUID.generate()

        val appointment = Appointment(
            staffId = staffId,
            personId = PersonId(),
            description = "description",
            guidance = "guidance",
            subjectiveCodes = listOf(Disease(Disease.Type.CIAP_2, "A03", "subjective - CIAP A03")),
            objectiveCodes = listOf(Disease(Disease.Type.CIAP_2, "A03", "objective - CIAP A03")),
            objective = "objective",
            subjective = "subjective",
            components = listOf(AppointmentComponent(AppointmentComponentType.SUBJECTIVE, 0)),
            plan = "plan",
            excuseNotes = listOf(
                ExcuseNote(
                    description = "presente no período da manhã para consulta",
                    id = "0c56bf1b-bfa6-4cff-964b-99473917a36e"
                )
            ),
            type = AppointmentType.DEFAULT,
            serviceScriptId = scriptNodeId,
            completedAt = localDateTimeNow,
            ownerStaffIds = setOf(staffId),
            appointmentDate = localDateTimeNow.toLocalDate(),
            endedAt = LocalDateTime.now(),
            attachments = listOf(
                Attachment(
                    id = RangeUUID.generate(),
                    type = "type",
                    fileName = "fileName",
                    thumbnailId = RangeUUID.generate()
                )
            ),
            specialty = AppointmentSpecialty("Test", RangeUUID.generate()),
            specialist = AppointmentSpecialist("Test", RangeUUID.generate()),
            referencedLinks = listOf(
                Appointment.ReferencedLink(id = RangeUUID.generate(), model = Appointment.ReferenceLinkModel.SCHEDULE),
                Appointment.ReferencedLink(RangeUUID.generate(), Appointment.ReferenceLinkModel.HEALTH_PLAN_TASK)
            ),
            status = AppointmentStatus.DRAFT,
            discardedType = null,
            discardedReason = null,
            finishType = null,
            clinicalEvaluation = "clinicalEvaluation",
            treatedBy = TreatedBy.PHYSICIAN,
            outcome = Outcome.ATTENDANCE_OR_DIGITAL_PRESCRIPTION,
            channelId = "channelId",
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    caseId = RangeUUID.generate(),
                    cipes = listOf(
                        DiseaseDetails(
                            type = Disease.Type.CIPE,
                            value = "1000000",
                            description = "Cipe 1000000",
                        )
                    ),
                    description = DiseaseDetails(
                        type = Disease.Type.CID_10,
                        value = "10",
                        description = "Unha Encravada",
                    ),
                    severity = CaseSeverity.ONGOING,
                )
            ),
            draftGroupStaffIds = listOf(staffId),
            event = null,
            name = "name",
            templateType = TemplateType.SOAP,
            staffRole = null,
            id = appointmentId,
            version = 0,
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow,
            updatedBy = null,
            emptyEventReason = "Reason",
            contractualRisks = listOf(
                AppointmentContractualRisk(
                    caseId = RangeUUID.generate(),
                    description = HealthConditionDescription(
                        type = Disease.Type.CID_10,
                        value = "MockValue",
                        id = RangeUUID.generate(),
                    ),
                    baseRiskRating = 1,
                    factor = 2,
                    finalRiskRating = 3,
                    reason = "Sinusite Crônica"
                )
            ),
            anesthetist = Anesthetist(
                type = AnesthetistType.INDIVIDUAL,
                staffId = anesthetistId,
            ),
        )

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            description = appointment.description,
            guidance = appointment.guidance,
            excuseNotes = appointment.excuseNotes,
            completed = appointment.completed,
            type = appointment.type,
            serviceScriptId = appointment.serviceScriptId,
            subjectiveCodes = appointment.subjectiveCodes,
            objectiveCodes = appointment.objectiveCodes,
            objective = appointment.objective,
            plan = appointment.plan,
            appointmentDate = appointment.appointmentDate,
            endedAt = appointment.endedAt,
            attachments = appointment.attachments,
            specialty = appointment.specialty,
            specialist = appointment.specialist,
            referencedLinks = appointment.referencedLinks,
            clinicalEvaluation = appointment.clinicalEvaluation,
            treatedBy = appointment.treatedBy,
            outcome = appointment.outcome,
            channelId = appointment.channelId,
            caseRecordDetails = appointment.caseRecordDetails,
            draftGroup = listOf(RelatedStaff(staff.fullName, staffId, staff.fullName)),
            subjective = appointment.subjective,
            event = appointment.event,
            name = appointment.name,
            components = appointment.components,
            templateType = appointment.templateType,
            version = appointment.version,
            emptyEventReason = "Reason",
            contractualRisks = appointment.contractualRisks,
            anesthetist = AnesthetistAppointmentRequest(
                type = appointment.anesthetist?.type!!,
                staff = AnesthetistStaffRequest(
                    id = appointment.anesthetist!!.staffId!!
                )
            )
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow
            val result = AppointmentUpdateRequestConverter.convert(
                source = request,
                id = appointmentId,
                staff = staff
            )
            assertThat(result).isEqualTo(appointment)
        }
    }

    @Test
    fun `#convert returns appointment with calculated type`() = runBlocking {
        val appointmentId = RangeUUID.generate()

        val appointment = Appointment(
            id = appointmentId,
            staffId = staffId,
            ownerStaffIds = setOf(staffId),
            personId = PersonId(),
            type = AppointmentType.DEFAULT,
            event = AppointmentEventDetail(
                name = "name",
                referenceModelId = "id",
                referenceModel = AppointmentEventReferenceModel.CHANNEL
            ),
            createdAt = localDateTimeNow,
            updatedAt = localDateTimeNow
        )

        val request = UpdateAppointmentRequest(
            personId = appointment.personId.toString(),
            event = appointment.event,
            version = 0
        )

        mockkObject(AppointmentTypeConverter) {
            coEvery { AppointmentTypeConverter.calculateType(staff, request.event) } returns appointment.type

            mockkStatic(LocalDateTime::class) {
                every { LocalDateTime.now() } returns localDateTimeNow
                val result = AppointmentUpdateRequestConverter.convert(
                    source = request,
                    id = appointmentId,
                    staff = staff
                )
                assertThat(result).isEqualTo(appointment)
            }
        }
    }
}
