package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.converters.AnesthetistConverter
import br.com.alice.api.ehr.converters.AnesthetistConverter.toCouncil
import br.com.alice.api.ehr.model.AnesthetistRequest
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.staff.client.HealthProfessionalFilters
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.models.StaffHpKey
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters

class AnesthetistController(
    val staffService: StaffService,
    val healthProfessionalService: HealthProfessionalService
) : Controller() {

    suspend fun addAnesthetist(request: AnesthetistRequest): Response = validHp(request)
        .let { validation ->
            if (validation.isValid.not())
                Response(HttpStatusCode.BadRequest, validation)
            else
                addAnesthetistToResponse(request).foldResponse()
        }

    suspend fun index(parameters: Parameters): Response =
        HealthProfessionalFilters(
            roles = listOf(Role.ANESTHETIST),
            types = listOf(StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL),
            searchTerm = parameters["q"],
            range = parseRange(parameters)
        ).let {
            healthProfessionalService.findBy(it)
        }.mapEach { AnesthetistConverter.hpToResponse(it) }
            .foldResponse()

    fun types(): Response =
        AnesthetistType.values()
            .map { AnesthetistTypeResponse(it.name, it.description) }
            .toResponse()

    private suspend fun validHp(request: AnesthetistRequest) =
        staffService.validHealthProfessional(
            StaffHpKey(
                request.email,
                request.crm.toCouncil()
            )
        ).get()

    private suspend fun addAnesthetistToResponse(request: AnesthetistRequest) =
        AnesthetistConverter.toModel(request)
            .let { (staff, hp) -> staffService.addAndPublishAnesthetist(staff, hp) }
            .map { AnesthetistConverter.requestToResponse(request, it.id) }
}

data class AnesthetistTypeResponse(
    val code: String,
    val value: String
)
