package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.metrics.Metrics
import br.com.alice.api.ehr.metrics.withMetric
import br.com.alice.api.ehr.services.internal.task.TaskReferralSessionsService
import br.com.alice.common.Response
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.asMap
import br.com.alice.common.core.extensions.containsAny
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.redis.Result
import br.com.alice.common.service.serialization.gsonCompleteSerializer
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.TaskSourceType
import br.com.alice.data.layer.models.TaskUpdatedBy
import br.com.alice.data.layer.models.copy
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.converters.HealthPlanTaskConverter
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import io.ktor.util.toMap
import java.util.UUID

class HealthPlanTaskController(
    private val healthPlanTaskService: HealthPlanTaskService,
    private val taskReferralSessionsService: TaskReferralSessionsService,
    staffService: StaffService
) : StaffController(staffService) {
    companion object {
        val MOOD_TITLE = "Orientações para Saúde Mental"
        val PHYSICAL_ACTIVITY_TITLE = "Orientações para Atividade Física"
        val EATING_TITLE = "Orientações para Alimentação"
        val SLEEP_TITLE = "Orientações para Sono"
        val OTHERS_TITLE = "Orientações para Cuidados Pessoais"
        val EMERGENCY_TITLE = "Encaminhamento para Pronto Socorro"
    }

    suspend fun changeStatus(taskId: UUID): Response =
        currentStaffId().let { healthPlanTaskService.markAsDoneUndone(taskId, it) }.foldResponse()

    suspend fun delete(taskId: UUID): Response =
        currentStaffId().let { healthPlanTaskService.delete(taskId, it) }.foldResponse()

    suspend fun update(taskJson: String): Response =
        withMetric(Metrics.UPDATE_HEALTH_PLAN_TASK) {
            currentStaff().let { staff ->
                val task = HealthPlanTaskConverter.convert(parseBody(taskJson), staff.id)
                task.log()
                healthPlanTaskService.update(task, staff.id, shouldValidateAdherence(task, staff))
            }.then {
                Metrics.incrementUpdateHealthPlanTask(Result.SUCCESS)
            }.thenError {
                logger.info(
                    "HealthPlanTaskController: invalid json",
                    "taskJson" to taskJson
                )
                Metrics.incrementUpdateHealthPlanTask(Result.FAILURE)
            }.foldResponse()
        }

    suspend fun create(taskJson: String): Response =
        withMetric(Metrics.CREATE_HEALTH_PLAN_TASK) {
            currentStaff().let { staff ->
                val healthPlanTask = HealthPlanTaskConverter.convert(parseBody(taskJson), staff.id)
                val task = healthPlanTask
                    .generalize()
                    .copy(
                        title = healthPlanTask.getTaskAutomaticTitle(staff),
                        description = healthPlanTask.getFixedDescription(staff),
                        createdBy = TaskUpdatedBy(staff.id, TaskSourceType.STAFF)
                    )
                task.log()
                healthPlanTaskService.create(task, staff.id, shouldValidateAdherence(task, staff))
            }.then {
                Metrics.incrementCreateHealthPlanTask(Result.SUCCESS)
            }.thenError {
                logger.info(
                    "HealthPlanTaskController: invalid json",
                    "taskJson" to taskJson
                )
                Metrics.incrementCreateHealthPlanTask(Result.FAILURE)
            }.foldResponse()
        }

    suspend fun getAll(personId: PersonId, parameters: Parameters): Response =
        healthPlanTaskService.getByPerson(personId, parameters.toMap(), true)
            .foldResponse()

    suspend fun getCounters(personId: PersonId, parameters: Parameters): Response =
        healthPlanTaskService.getCountsByPersonId(personId, parameters.toMap()).foldResponse()

    suspend fun getTaskReferralByPersonAndCurrentStaff(personId: PersonId): Response =
        healthPlanTaskService.getTaskReferralByPersonAndStaffId(personId, currentStaffId())
            .coFoldNotFound { false.success() }
            .foldResponse()

    suspend fun getTaskReferralSessionsCount(personId: PersonId, taskId: UUID) =
        taskReferralSessionsService.getTaskReferralSessionsCount(personId, taskId).foldResponse()

    /**
     * Need this method because our RoutesHandle + Gson doesn't work with interfaces as parameters
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T> parseBody(json: String): T {
        val encodedString = String(json.toByteArray(Charsets.UTF_8))
        val raw = gsonCompleteSerializer.fromJson(encodedString, Map::class.java) as Map<String, Any?>

        val className = raw.getValue("type").run { HealthPlanTaskType.valueOf(this as String).kClass.simpleName }
        val klass = Class.forName("br.com.alice.healthplan.models.${className}Transport") as Class<T>
        return gsonCompleteSerializer.fromJson(encodedString, klass)
    }

    private fun HealthPlanTask.log() {
        logger.info(
            "HealthPlanTask parsed in HealthPlanTaskController",
            "id" to id,
            "start" to start,
            "deadline" to deadline,
            "frequency" to frequency,
            "due_date" to dueDate,
            "type" to type,
            "content" to content
        )
    }

    private fun shouldValidateAdherence(healthPlanTask: HealthPlanTask, staff: Staff): Boolean =
        if (staff.type == StaffType.COMMUNITY_SPECIALIST) false
        else healthPlanTask.caseRecordDetails
            .takeIf { it.isNotEmpty() && enableValidationAdherence() && shouldBeValidated(it) }
            ?.let {
                when (healthPlanTask.type) {
                    HealthPlanTaskType.REFERRAL -> healthPlanTask.hasContentKey("specialty")
                    HealthPlanTaskType.TEST_REQUEST -> healthPlanTask.hasContentKey("code")
                    else -> false
                }
            } ?: false


    private fun HealthPlanTask.hasContentKey(key: String) =
        content?.takeIf { it.isNotEmpty() }?.containsKey(key) ?: false

    private fun shouldBeValidated(
        healthConditions: List<CaseRecordDetails>
    ) = !FeatureService.getList(
        namespace = FeatureNamespace.HEALTH_LOGICS,
        key = "ignored_health_conditions_ids",
        defaultValue = emptyList<UUID>()
    ).containsAny(healthConditions.mapNotNull { it.description.id?.toUUID() })

    private fun enableValidationAdherence() =
        FeatureService.get(
            FeatureNamespace.HEALTH_LOGICS,
            "enable_consumer_adherence_from_health_plan_task",
            false
        )

    private fun makeSurgeryPrescriptionTitle(name: String) =
        "Indicação de procedimento cirúrgico solicitado por $name"

    private fun HealthPlanTask.getTaskAutomaticTitle(staff: Staff) =
        when (type) {
            HealthPlanTaskType.REFERRAL -> getReferralTitle()
            HealthPlanTaskType.TEST_REQUEST -> getTestRequestTitle()
            HealthPlanTaskType.PRESCRIPTION -> getPrescriptionTitle()
            HealthPlanTaskType.MOOD -> MOOD_TITLE
            HealthPlanTaskType.SLEEP -> SLEEP_TITLE
            HealthPlanTaskType.PHYSICAL_ACTIVITY -> PHYSICAL_ACTIVITY_TITLE
            HealthPlanTaskType.EATING -> EATING_TITLE
            HealthPlanTaskType.OTHERS -> OTHERS_TITLE
            HealthPlanTaskType.EMERGENCY -> EMERGENCY_TITLE
            HealthPlanTaskType.FOLLOW_UP_REQUEST -> "Retorno com ${staff.fullName}"
            HealthPlanTaskType.SURGERY_PRESCRIPTION -> makeSurgeryPrescriptionTitle(staff.fullName)
            else -> title ?: "Tarefa"
        }

    private fun makeSurgeryPrescriptionDescription(name: String): String =
        """
            Olá! Somos do time de cirurgias aqui na Alice.

            Vimos que você tem uma indicação por $name para realizar uma cirurgia. A partir de agora, nosso time administrativo fará uma pré-análise de cobertura do procedimento.
            É importante destacar que, conforme a regulamentação da Agência Nacional de Saúde (ANS), o prazo para análise da cobertura do procedimento cirúrgico é de até 10 dias úteis. 
            Em breve, nosso time entrará em contato aqui no App dar uma devolutiva sobre a cobertura ou não da cirurgia e orientar quais são os próximos passos.
        """.trimIndent()
    
    private fun HealthPlanTask.getFixedDescription(staff: Staff) =
        when (type) {
            HealthPlanTaskType.SURGERY_PRESCRIPTION -> makeSurgeryPrescriptionDescription(staff.fullName)
            else -> description
        }

    private fun HealthPlanTask.getReferralTitle() =
        content?.get("suggestedSpecialist")?.asMap()?.get("name")?.let { specialistName ->
            content?.get("specialty")?.asMap()?.get("name")?.let { "Encaminhamento para ${it} com ${specialistName}" }
                ?: "Encaminhamento com ${specialistName}"
        } ?: content?.get("specialty")?.asMap()?.get("name")?.let { "Encaminhamento para ${it}" } ?: "Encaminhamento"

    private fun HealthPlanTask.getTestRequestTitle() =
        title ?: "Conjunto de exames"

    private fun HealthPlanTask.getPrescriptionTitle() =
        title ?: content?.get("medicine")?.asMap()?.get("name").toString()
}

