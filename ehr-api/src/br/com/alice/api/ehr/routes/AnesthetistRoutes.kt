package br.com.alice.api.ehr.routes

import br.com.alice.api.ehr.controllers.AnesthetistController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.anesthetistRoutes() {
    val anesthetistController by inject<AnesthetistController>()

    authenticate {
        route("/anesthetist") {
            get { coHandler(anesthetistController::index) }
            post { coHandler(anesthetistController::addAnesthetist) }
            get ("/types"){ coHandler(anesthetistController::types) }
        }
    }
}
