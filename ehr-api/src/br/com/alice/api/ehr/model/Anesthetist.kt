package br.com.alice.api.ehr.model

import br.com.alice.common.models.State
import java.util.UUID

data class AnesthetistRequest(
    val name: String,
    val crm: CrmAnesthetist,
    val email: String,
)

data class AnesthetistResponse(
    val name: String,
    val crm: CrmAnesthetist,
    val email: String,
    val id: UUID
) {
    val friendlyDescription = "$name - ${crm.number}-${crm.state}"
}

data class CrmAnesthetist(
    val number: String,
    val state: State,
)
