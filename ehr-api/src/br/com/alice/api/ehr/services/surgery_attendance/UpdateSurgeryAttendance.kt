package br.com.alice.api.ehr.services.surgery_attendance

import br.com.alice.api.ehr.controllers.model.SurgeryAttendanceRequest
import br.com.alice.api.ehr.converters.SurgeryAttendanceRequestConverter
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType.TIT_SURGERY
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import com.github.kittinunf.result.Result
import java.util.UUID

class UpdateSurgeryAttendance(
    private val service: TertiaryIntentionTouchPointService
) {
    suspend fun persist(
        personId: PersonId,
        tertiaryIntentionId: UUID,
        staffId: UUID,
        request: SurgeryAttendanceRequest
    ): Result<TertiaryIntentionTouchPoint, Throwable> {
        val tertiaryIntention = service.getByIdAndPersonId(tertiaryIntentionId, personId).get()

        val tertiaryIntentionRequest = SurgeryAttendanceRequestConverter.convert(request, personId, staffId)

        val newModelInstance = tertiaryIntentionRequest
            .copy(
                id = tertiaryIntention.id,
                version = tertiaryIntention.version,
                type = TIT_SURGERY,
                evolutions = (tertiaryIntention.evolutions + tertiaryIntentionRequest.evolutions)
            )

        return service.update(newModelInstance)
    }
}
