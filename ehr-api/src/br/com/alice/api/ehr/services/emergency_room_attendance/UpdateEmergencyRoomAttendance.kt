package br.com.alice.api.ehr.services.emergency_room_attendance

import br.com.alice.api.ehr.controllers.model.EmergencyRoomAttendanceRequest
import br.com.alice.api.ehr.converters.EmergencyRoomAttendanceRequestConverter
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import com.github.kittinunf.result.Result
import java.util.UUID

class UpdateEmergencyRoomAttendance(
    private val tertiaryIntentionService: TertiaryIntentionTouchPointService
) {
    suspend fun execute(
        personId: PersonId,
        tertiaryIntentionId: UUID,
        staffId: UUID,
        request: EmergencyRoomAttendanceRequest
    ): Result<TertiaryIntentionTouchPoint, Throwable> {
        val tertiaryIntentionPersisted =
            tertiaryIntentionService.getByIdAndPersonId(tertiaryIntentionId, personId).get()

        val newTertiaryIntention =
            EmergencyRoomAttendanceRequestConverter.convert(request, personId, staffId)

        return tertiaryIntentionService
            .update(
                newTertiaryIntention.copy(
                    id = tertiaryIntentionPersisted.id,
                    version = tertiaryIntentionPersisted.version,
                    healthEventId = tertiaryIntentionPersisted.healthEventId
                )
            )
            .then {
                logger.info(
                    "TertiaryIntentionTouchPoint Updated with success",
                    "tertiary_intention_id" to it.id,
                    "person_id" to it.personId
                )
            }
    }
}
