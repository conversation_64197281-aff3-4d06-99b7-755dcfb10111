package br.com.alice.api.ehr.services.person_health_info_summary

import br.com.alice.api.ehr.ServiceConfig
import br.com.alice.api.ehr.model.PersonHealthInfoSummaryAiChatRequest
import br.com.alice.api.ehr.model.ReviewPersonHealthInfoSummaryRequest
import br.com.alice.appointment.client.TimelineFilter
import br.com.alice.appointment.client.TimelineService
import br.com.alice.common.RangeUUID
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DataEventPayload
import br.com.alice.common.client.DataEventType
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.models.ClinicalBackground
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthMeasurement
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success
import java.util.UUID
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class PersonHealthInfoSummaryService(
    private val searchHealthMeasurementService: SearchHealthMeasurementService,
    private val searchTestResultService: SearchTestResultService,
    private val searchClinicalBackgroundService: SearchClinicalBackgroundService,
    private val chronicConditionService: SearchChronicConditionService,
    private val searchContinuousMedicationService: SearchContinuousMedicationService,
    private val searchSpecialistAppointmentService: SearchSpecialistAppointmentService,
    private val searchPrimaryAttentionAppointmentService: SearchPrimaryAttentionAppointmentService,
    private val searchAcuteCasesService: SearchAcuteCasesService,
    private val dataEventClient: DataEventClient,
    private val staffService: StaffService,
    private val timelineService: TimelineService,
    private val aiChatQuestionAnswerService: AiChatQuestionAnswerService,
) {

    suspend fun reviewSummary(
        personId: PersonId,
        staffId: UUID,
        review: ReviewPersonHealthInfoSummaryRequest
    ): Result<Boolean, Throwable> {
        dataEventClient.sendEventAsync(
            url = ServiceConfig.dataEventApiUrl,
            payload = DataEventPayload(
                memberId = personId.toString(),
                staffId = staffId.toString(),
                sessionId = review.sessionId ?: RangeUUID.generate().toString(),
                action = "review_summary",
                namespace = "health.person_health_info_summary",
                type = DataEventType.EVENT.description,
                properties = mapOf(
                    "score" to review.score.toString(),
                    "description" to review.description.orEmpty(),
                    "questionId" to review.questionId.toString(),
                ),
                version = "1.0"
            )
        )

        return true.success()
    }

    suspend fun findByPersonId(personId: PersonId, currentStaffId: UUID) =
        staffService.getActiveWithRole(currentStaffId, personHealthInfoAllowedRoles())
            .flatMap { getData(personId) }
            .coFoldNotFound { PersonHealthInfoSummaryTransport().success() }

    suspend fun getTimelineItemsByReferencedModelId(personId: PersonId, referencedModelIds: List<UUID>) =
        timelineService.findBy(
            TimelineFilter(
                personId = personId,
                referencedModelIds = referencedModelIds
            )
        )

    suspend fun getChatAnswer(personId: PersonId, staffId: UUID, request: PersonHealthInfoSummaryAiChatRequest) =
        aiChatQuestionAnswerService.getChatAnswer(personId, staffId, request)

    suspend fun checkChatAvailability(personId: PersonId) =
        aiChatQuestionAnswerService.checkChatAvailability(personId)


    private suspend fun getData(personId: PersonId) = coroutineScope {
        coResultOf<PersonHealthInfoSummaryTransport, Throwable> {
            val healthMeasurements = getHealthMeasurements(personId)
            val aliceTestResultBundle = getAliceTestResultBundle(personId)
            val clinicalBackgrounds = getClinicalBackgrounds(personId)
            val chronicConditions = getChronicConditions(personId)
            val prescriptions = getPrescriptions(personId)
            val specialistAppointments = getSpecialistAppointments(personId)
            val lastPrimaryAttentionAppointment = getLastPrimaryAttentionAppointment(personId)
            val acuteCases = getAcuteCases(personId)

            PersonHealthInfoSummaryTransport(
                healthMeasurements = healthMeasurements.await(),
                aliceTestResultBundle = aliceTestResultBundle.await(),
                clinicalBackgrounds = clinicalBackgrounds.await(),
                chronicConditions = chronicConditions.await(),
                prescriptions = prescriptions.await(),
                specialistAppointments = specialistAppointments.await(),
                lastPrimaryAttentionAppointment = lastPrimaryAttentionAppointment.await(),
                acuteCases = acuteCases.await()
            )
        }
    }

    private fun CoroutineScope.getSpecialistAppointments(personId: PersonId) = async {
        searchSpecialistAppointmentService.findByPersonId(personId).getOrElse { emptyList() }
    }

    private fun CoroutineScope.getPrescriptions(personId: PersonId) = async {
        searchContinuousMedicationService.findByPersonId(personId).getOrElse { emptyList() }
    }

    private fun CoroutineScope.getChronicConditions(personId: PersonId) = async {
        chronicConditionService.findByPersonId(personId).getOrElse { emptyList() }
    }

    private fun CoroutineScope.getHealthMeasurements(personId: PersonId) = async {
        searchHealthMeasurementService.findByPersonId(personId).getOrElse { emptyList() }
    }

    private fun CoroutineScope.getAliceTestResultBundle(personId: PersonId) = async {
        searchTestResultService.findGlycatedHemoglobinByPersonId(personId).getOrNull()
    }

    private fun CoroutineScope.getLastPrimaryAttentionAppointment(personId: PersonId) = async {
        searchPrimaryAttentionAppointmentService.findByPersonId(personId).getOrNull()
    }

    private fun CoroutineScope.getAcuteCases(personId: PersonId) = async {
        searchAcuteCasesService.findByPersonId(personId).getOrNull()
    }

    private fun CoroutineScope.getClinicalBackgrounds(personId: PersonId) = async {
        searchClinicalBackgroundService.findByPersonId(personId).getOrElse { emptyList() }
    }

    private fun personHealthInfoAllowedRoles() =
        FeatureService.getList(
            namespace = FeatureNamespace.EHR,
            key = "summary_roles",
            defaultValue = emptyList<String>()
        ).map{ Role.valueOf(it) }
}

data class PersonHealthInfoSummaryTransport(
    val healthMeasurements: List<HealthMeasurement> = emptyList(),
    val aliceTestResultBundle: AliceTestResultBundle? = null,
    val clinicalBackgrounds: List<ClinicalBackground> = emptyList(),
    val chronicConditions: List<ChronicConditionTransport> = emptyList(),
    val prescriptions: List<ContinuousPrescriptionTransport> = emptyList(),
    val specialistAppointments: List<SpecialistAppointmentTransport> = emptyList(),
    val lastPrimaryAttentionAppointment: PrimaryAttentionAppointmentTransport? = null,
    val acuteCases: AcuteCasesTransport? = null
)
