package br.com.alice.api.ehr.services.hospitalization_attendance

import br.com.alice.api.ehr.controllers.model.HospitalizationAttendanceRequest
import br.com.alice.api.ehr.converters.HospitalizationAttendanceRequestConverter
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType.TIT_HOSPITALIZATION
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import com.github.kittinunf.result.Result
import java.util.UUID

class UpdateHospitalizationAttendance(
    private val service: TertiaryIntentionTouchPointService
) {
    suspend fun persist(
        personId: PersonId,
        tertiaryIntentionId: UUID,
        staffId: UUID,
        request: HospitalizationAttendanceRequest
    ): Result<TertiaryIntentionTouchPoint, Throwable> {
        val tertiaryIntention = service.getByIdAndPersonId(tertiaryIntentionId, personId).get()

        val tertiaryIntentionRequest = HospitalizationAttendanceRequestConverter.convert(request, personId, staffId)

        val newModelInstance = tertiaryIntentionRequest.copy(
            id = tertiaryIntention.id,
            version = tertiaryIntention.version,
            type = TIT_HOSPITALIZATION,
            healthEventId = tertiaryIntention.healthEventId,
            evolutions = (tertiaryIntention.evolutions + tertiaryIntentionRequest.evolutions)
        )

        return service.update(newModelInstance)
    }
}
