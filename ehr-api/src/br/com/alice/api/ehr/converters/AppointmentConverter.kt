package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.controllers.model.AnesthetistAppointmentResponse
import br.com.alice.api.ehr.controllers.model.AnesthetistResponse
import br.com.alice.api.ehr.controllers.model.AppointmentResponse
import br.com.alice.api.ehr.controllers.model.AppointmentStaffResponse
import br.com.alice.api.ehr.controllers.model.CaseRecordDetailsAppointmentResponse
import br.com.alice.api.ehr.controllers.model.CouncilResponse
import br.com.alice.api.ehr.controllers.model.CreateAppointmentRequest
import br.com.alice.api.ehr.controllers.model.EvolutionResponse
import br.com.alice.api.ehr.controllers.model.OutcomeResponse
import br.com.alice.api.ehr.controllers.model.SessionResponse
import br.com.alice.api.ehr.model.CrmAnesthetist
import br.com.alice.api.ehr.services.internal.ProcedureResponse
import br.com.alice.appointment.client.ChannelReference
import br.com.alice.appointment.client.EvolutionCommenter
import br.com.alice.appointment.client.ExternalFileTransport
import br.com.alice.appointment.client.UpdateAppointmentRequest
import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.map
import br.com.alice.data.layer.models.Anesthetist
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.SCHEDULE
import br.com.alice.data.layer.models.Appointment.ReferencedLink
import br.com.alice.data.layer.models.AppointmentEvolution
import br.com.alice.data.layer.models.ExternalFile
import br.com.alice.data.layer.models.ExternalFileOrigin
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.RelatedStaff
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.Staff
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.secondary.attention.models.HealthPlanTaskSessionsCount
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import br.com.alice.staff.models.StaffWithHealthProfessional
import java.time.LocalDateTime
import java.util.UUID

object AppointmentRequestConverter :
    Converter<CreateAppointmentRequest, Appointment>(CreateAppointmentRequest::class, Appointment::class) {
    suspend fun convert(source: CreateAppointmentRequest, staff: Staff) =
        convert(
            source,
            map(Appointment::staffId) from staff.id,
            map(Appointment::ownerStaffIds) from setOf(staff.id),
            map(Appointment::referencedLinks) from buildReferencedLinks(source),
            map(Appointment::type) from (source.type ?: AppointmentTypeConverter.calculateType(staff, source.event)),
            map(Appointment::appointmentDate) from LocalDateTime.now().toSaoPauloTimeZone().toLocalDate(),
        )

    private fun buildReferencedLinks(request: CreateAppointmentRequest): List<ReferencedLink> =
        request.appointmentScheduleId?.let {
            listOf(ReferencedLink(id = it.toUUID(), model = SCHEDULE))
        } ?: emptyList()
}

object AppointmentResponseConverter : Converter<Appointment, AppointmentResponse>(
    Appointment::class, AppointmentResponse::class
) {
    fun convert(
        source: Appointment,
        loggedStaffId: UUID,
        staffMap: Map<UUID, StaffWithHealthProfessional>,
        script: ServiceScriptNode? = null,
        channelReference: ChannelReference? = null,
        evolutions: List<AppointmentEvolution> = emptyList(),
        healthPlanTasks: Map<UUID?, List<HealthPlanTaskTransport>>? = null,
        procedureExecuted: List<ProcedureResponse> = emptyList(),
        referralsDetails: HealthPlanTaskSessionsCount?
    ) =
        convert(
            source,
            map(AppointmentResponse::physician) from staffMap[source.staffId]!!.let {
                AppointmentStaffResponseConverter.convert(it.staff, it.healthProfessional)
            },
            map(AppointmentResponse::serviceScript) from script,
            map(AppointmentResponse::complaint) from source.content,
            map(AppointmentResponse::conduct) from source.guidance.orEmpty(),
            map(AppointmentResponse::completed) from (source.completedAt != null),
            map(AppointmentResponse::channelReference) from channelReference,
            map(AppointmentResponse::isEditable) from (source.staffId == loggedStaffId),
            map(AppointmentResponse::excuseNotesCount) from source.excuseNotes.size,
            map(AppointmentResponse::channel) from channelReference,
            map(AppointmentResponse::clinicalEvaluationCodes) from source.objectiveCodes,
            map(AppointmentResponse::emptyEventReason) from source.emptyEventReason,
            map(AppointmentResponse::evolutions) from evolutions.map {
                AppointmentEvolutionResponseConverter.convert(
                    it,
                    staffMap
                )
            },
            map(AppointmentResponse::outcomeResponse) from source.outcome?.let {
                OutcomeResponse(
                    it.name,
                    it.description,
                    it.severity
                )
            },
            map(AppointmentResponse::draftGroup) from (
                    source.draftGroupStaffIds?.mapNotNull { staffId ->
                        staffMap[staffId]?.let { staffHP ->
                            RelatedStaff(
                                name = staffHP.staff.fullName,
                                id = staffHP.staff.id,
                                fullName = staffHP.staff.fullName
                            )
                        }
                    } ?: emptyList()
                    ),
            map(AppointmentResponse::caseRecordDetails) from (source.caseRecordDetails?.map {
                CaseRecordDetailsAppointmentResponse(
                    caseId = it.caseId,
                    description = it.description,
                    cipes = it.cipes,
                    severity = it.severity,
                    follow = it.follow,
                    observation = it.observation,
                    channel = it.channel,
                    specialistOpinion = it.specialistOpinion,
                    seriousness = it.seriousness,
                    healthPlanTasks = healthPlanTasks?.get(it.description.id?.toUUID()),
                    dalyaRecommendation = it.dalyaRecommendation
                )
            }),
            map(AppointmentResponse::externalFiles) from (source.externalFiles?.map {
                ExternalFileTransport(
                    id = it.id.toString(),
                    store = it.store,
                    type = it.type
                )
            }),
            map(AppointmentResponse::proceduresExecuted) from procedureExecuted,
            map(AppointmentResponse::session) from referralsDetails?.let {
                SessionResponse(
                    totalQuantity = it.totalSessionsQuantity,
                    completedQuantity = it.completedSessionsQuantity,
                    availableQuantity = it.availableSessionsQuantity
                )
            },
            map(AppointmentResponse::anesthetist) from source.anesthetist?.let {
                AnesthetistAppointmentResponse(
                    staff = it.staffId?.let { id -> staffMap[id] }?.let { (staff, hp) ->
                        AnesthetistResponse(
                            name = staff.fullName,
                            crm = hp?.council?.let { council ->
                                CrmAnesthetist(
                                    number = council.number,
                                    state = council.state
                                )
                            },
                            id = staff.id
                        )
                    },
                    type = it.type,
                )
            }
        )
}

object AppointmentEvolutionResponseConverter : Converter<AppointmentEvolution, EvolutionResponse>(
    AppointmentEvolution::class,
    EvolutionResponse::class,
) {
    fun convert(source: AppointmentEvolution, ownerStaffs: Map<UUID, StaffWithHealthProfessional>) =
        convert(
            source,
            map(EvolutionResponse::commentedBy) from EvolutionCommenter(
                staffName = ownerStaffs[source.staffId]?.staff?.fullName ?: "Nome não disponível",
                staffId = source.staffId,
                profileImageUrl = ownerStaffs[source.staffId]?.staff?.profileImageUrl,
                councilSignature = ownerStaffs[source.staffId]?.healthProfessional?.councilSignature.orEmpty()
            )
        )
}

object AppointmentStaffResponseConverter : Converter<Staff, AppointmentStaffResponse>(
    Staff::class, AppointmentStaffResponse::class
) {
    fun convert(source: Staff, healthProfessional: HealthProfessional?) = convert(
        source,
        map(AppointmentStaffResponse::description) from StaffGenderDescriptionConverter.convert(source),
        map(AppointmentStaffResponse::council) from CouncilResponse(
            name = healthProfessional?.councilName.orEmpty(),
            number = healthProfessional?.council?.number.orEmpty(),
            state = healthProfessional?.council?.state?.name.orEmpty(),
        )
    )
}

object AppointmentUpdateRequestConverter : Converter<UpdateAppointmentRequest, Appointment>(
    UpdateAppointmentRequest::class, Appointment::class
) {
    suspend fun convert(source: UpdateAppointmentRequest, id: UUID, staff: Staff) = convert(
        source,
        map(Appointment::id) from id,
        map(Appointment::staffId) from staff.id,
        map(Appointment::personId) from source.personId.toPersonId(),
        map(Appointment::draftGroupStaffIds) from source.draftGroup?.map { it.id },
        map(Appointment::subjectiveCodes) from (source.subjectiveCodes ?: emptyList()),
        map(Appointment::objectiveCodes) from (source.objectiveCodes ?: emptyList()),
        map(Appointment::referencedLinks) from (source.referencedLinks ?: emptyList()),
        map(Appointment::attachments) from (source.attachments ?: emptyList()),
        map(Appointment::completedAt) from if (source.completed == true) LocalDateTime.now() else null,
        map(Appointment::event) from source.event,
        map(Appointment::ownerStaffIds) from setOf(staff.id),
        map(Appointment::specialty) from source.specialty,
        map(Appointment::specialist) from source.specialist,
        map(Appointment::emptyEventReason) from source.emptyEventReason,
        map(Appointment::anesthetist) from source.anesthetist?.let {
            Anesthetist(
                staffId = it.staff?.id,
                type = it.type
            )
        },
        map(Appointment::type) from (source.event?.let {
            AppointmentTypeConverter.calculateType(staff, source.event)
        }
            ?: source.type!!),
        map(Appointment::externalFiles) from (source.externalFiles?.map {
            ExternalFile(
                id = it.id.toUUID(),
                store = it.store,
                type = it.type,
                origin = ExternalFileOrigin.CHANNELS
            )
        })
    )
}
