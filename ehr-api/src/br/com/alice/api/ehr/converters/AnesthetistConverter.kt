package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.model.AnesthetistRequest
import br.com.alice.api.ehr.model.AnesthetistResponse
import br.com.alice.api.ehr.model.CrmAnesthetist
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.Staff
import java.util.UUID

object AnesthetistConverter {

    fun toModel(request: AnesthetistRequest): Pair<Staff, HealthProfessional> {
        val staff = Staff(
            firstName = getFirstName(request.name),
            lastName = getLastName(request.name),
            email = request.email,
            active = false,
            gender = Gender.NO_ANSWER,
            profileImageUrl = null,
            nationalId = null,
            role = Role.ANESTHETIST,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL
        )

        val healthProfessional = HealthProfessional(
            staffId = staff.id,
            council = request.crm.toCouncil(),
            role = staff.role,
            name = request.name,
            type = staff.type,
            email = request.email,
            status = SpecialistStatus.INACTIVE
        )

        return Pair(staff, healthProfessional)
    }

    fun requestToResponse(request: AnesthetistRequest, staffId: UUID): AnesthetistResponse {
        return AnesthetistResponse(
            name = request.name,
            crm = request.crm,
            email = request.email,
            id = staffId
        )
    }

    fun hpToResponse(healthProfessional: HealthProfessional): AnesthetistResponse {
        return AnesthetistResponse(
            name = healthProfessional.name,
            crm = CrmAnesthetist(
                number = healthProfessional.council.number,
                state = healthProfessional.council.state
            ),
            email = healthProfessional.email,
            id = healthProfessional.staffId
        )
    }

    fun CrmAnesthetist.toCouncil(): Council {
        return Council(
            number = this.number,
            state = this.state,
            type = CouncilType.CRM
        )
    }

    private fun getFirstName(name: String): String = name.split(" ").firstOrNull() ?: ""
    private fun getLastName(name: String): String = name.split(" ").drop(1).joinToString(" ") { it }
}
