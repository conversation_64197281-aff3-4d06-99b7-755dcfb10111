package br.com.alice.member.api.controllers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.authentication.currentUserId
import br.com.alice.authentication.currentUserType
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.beginningOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.endOfTheMonthWithSaoPauloTimezone
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.features.AppointmentScheduleWithStaff
import br.com.alice.common.featureaccess.features.AppointmentSchedulesListResource
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleCancelledByType
import br.com.alice.data.layer.models.AppointmentScheduleCheckInStatus
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.FeatureAccess
import br.com.alice.member.api.converters.AppointmentScheduleConverter
import br.com.alice.member.api.converters.AppointmentScheduleWithStaffConverter
import br.com.alice.member.api.models.FirstAppointmentScheduleResponse
import br.com.alice.member.api.models.HasCollidingSchedulesResponse
import br.com.alice.member.api.models.NextAppointmentSchedules
import br.com.alice.member.api.models.currentAppSessionId
import br.com.alice.member.api.models.currentAppTimeZoneOffset
import br.com.alice.member.api.models.scheduler.AppointmentScheduleDataResponse
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleCheckInService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentSchedulePreTriageService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.exceptions.AppointmentScheduleCollisionDetectedException
import br.com.alice.schedule.model.AppointmentScheduleEvent
import br.com.alice.schedule.model.AppointmentScheduleEvent.Companion.generateUniqueId
import br.com.alice.schedule.model.AppointmentScheduleRequest
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.opentelemetry.api.trace.Span
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import br.com.alice.member.api.services.AppointmentScheduleService as InternalAppointmentScheduleService

class AppointmentScheduleController(
    private val appointmentScheduleService: AppointmentScheduleService,
    private val internalAppointmentScheduleService: InternalAppointmentScheduleService,
    private val staffService: StaffService,
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val personCalendlyService: PersonCalendlyService,
    private val cache: GenericCache,
    private val appointmentSchedulePreTriageService: AppointmentSchedulePreTriageService,
    private val personService: PersonService,
    private val healthcareTeamService: HealthcareTeamService,
    private val appointmentScheduleCheckInService: AppointmentScheduleCheckInService,
    private val appointmentScheduleWithStaffConverter: AppointmentScheduleWithStaffConverter,
    private val actionPlanTaskService: ActionPlanTaskService,
) : Controller() {

    companion object {
        const val A_DAY_IN_SECONDS_EXPIRATION_TIME = 24 * 60 * 60L
        val EVENT_CATEGORIES_TO_CONSIDER_CONCURRENT_SCHEDULES = listOf(AppointmentScheduleType.HEALTHCARE_TEAM)
    }

    suspend fun getDetails(appointmentScheduleId: UUID): Response = coroutineScope {
        span("getDetails") { span ->

            val currentUserId = currentUid()
            span.setAttribute("appointment_schedule_id", appointmentScheduleId)
            span.setAttribute("current_person_id", currentUserId)

            val checkInStatusDeferred = async {
                appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentScheduleId)
                    .getOrNullIfNotFound()?.firstOrNull()?.status
            }

            appointmentScheduleService.get(appointmentScheduleId)
                .recordResult(span)
                .map {
                    val isAdvancedAccess = it.healthPlanTaskId
                        ?.let { taskId ->
                            span.setAttribute("health_plan_task_id", taskId)

                            actionPlanTaskService.getById(taskId)
                                .getOrNull()
                                ?.takeIf { task ->
                                    task.isReferral()
                                        .also { isReferral ->
                                            span.setAttribute("is_referral", isReferral)
                                        }
                                }
                                ?.specialize<ReferralNew>()?.isAdvancedAccess
                                .also { isAdvancedAccess ->
                                    span.setAttribute("is_advanced_access_task", isAdvancedAccess)
                                }
                        } ?: false

                    span.setAttribute("is_advanced_access_appointment", isAdvancedAccess)

                    val staff = it.staffId?.let { staffId -> staffService.get(staffId).getOrNullIfNotFound() }
                    val appointmentScheduleWithStaff = AppointmentScheduleWithStaff(
                        appointmentSchedule = it,
                        staff = staff,
                    )

                    appointmentScheduleWithStaffConverter.convert(
                        appointmentScheduleWithStaff,
                        false,
                        null,
                        checkInStatusDeferred.await(),
                        isAdvancedAccess,
                    )

                }
                .foldResponse()
        }
    }

    suspend fun getById(appointmentScheduleId: UUID): Response {
        logger.info(
            "AppointmentScheduleController::getById",
            "appointment_schedule_id" to appointmentScheduleId,
            "current_person_id" to currentUid()
        )

        return appointmentScheduleService.get(appointmentScheduleId)
            .thenError { exception ->
                logger.error(
                    "AppointmentScheduleController::getById error",
                    "error_message" to exception.message,
                    exception
                )
            }
            .map { AppointmentScheduleConverter.convert(it) }
            .foldResponse()
    }

    suspend fun getNextAppointmentSchedules(): Response {
        val currentUserId = currentUid()
        val personCalendly = personCalendlyService.getOrCreate(currentUserId.toPersonId()).get()

        logger.info(
            "AppointmentScheduleController::getNextAppointmentSchedules",
            "device_timezone_offset" to currentAppTimeZoneOffset(),
            "session_id" to currentAppSessionId(),
            "person_id" to currentUserId
        )

        return appointmentScheduleService.findWithStaffBy(
            AppointmentScheduleFilter(
                personId = currentUserId.toPersonId(),
                status = listOf(AppointmentScheduleStatus.SCHEDULED),
                endTimeGreater = LocalDateTime.now(),
                sortOrder = SortOrder.Ascending
            )
        ).map { appointmentSchedules ->
            FeatureAccess.filter(
                feature = Feature.APPOINTMENT_SCHEDULES_LIST,
                resource = AppointmentSchedulesListResource(
                    appointmentSchedulesWithStaff = appointmentSchedules.map {
                        it.convertTo(AppointmentScheduleWithStaff::class)
                    }
                )
            )
        }
            .map { featureAccessResult ->
                featureAccessResult.response?.appointmentSchedulesWithStaff ?: emptyList()
            }
            .map { appointmentSchedules ->
                logger.info(
                    "AppointmentScheduleServiceImpl::getNextAppointmentSchedules",
                    "appointment_schedules_with_staff_and_price" to appointmentSchedules.size
                )
                appointmentSchedules.filter { appointmentSchedule ->
                    appointmentSchedule.staff != null
                }
            }
            .flatMapPair { appointmentSchedulesWithStaff ->
                appointmentScheduleEventTypeService.getByIds(
                    appointmentSchedulesWithStaff.mapNotNull {
                        it.appointmentSchedule.appointmentScheduleEventTypeId
                    }
                )
            }
            .map { eventTypesAndAppointmentSchedules ->
                val eventTypes = eventTypesAndAppointmentSchedules.first.associateBy { it.id }
                val appointmentSchedules = eventTypesAndAppointmentSchedules.second

                appointmentSchedules.pmap {
                    val staffId = it.appointmentSchedule.staffId
                    val appointmentScheduleType = it.appointmentSchedule.type.name
                    logger.info(
                        "AppointmentScheduleServiceImpl::getNextAppointmentSchedules",
                        "staff_id" to staffId,
                        "appointment_schedule_type" to appointmentScheduleType
                    )
                    appointmentScheduleWithStaffConverter.convert(
                        it,
                        eventTypes[it.appointmentSchedule.appointmentScheduleEventTypeId]?.isMultiProfessionalReferral
                            ?: false,
                        personCalendly,
                    )
                }
            }
            .map { appointmentSchedules ->
                logger.info(
                    "AppointmentScheduleServiceImpl::getNextAppointmentSchedules final next appointments",
                    "appointment_schedules_with_staff_and_price" to appointmentSchedules.map { it.id }
                )
                NextAppointmentSchedules(appointmentSchedules = appointmentSchedules)
            }
            .foldResponse()
    }

    suspend fun createAppointmentSchedule(
        appointmentScheduleRequest: AppointmentScheduleRequest
    ): Response = span("createAppointmentSchedule") { span ->
        coroutineScope {
            val currentPersonId = currentUid()
            val staffId = appointmentScheduleRequest.staffId
            val startTime = appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone()
            val endTime = appointmentScheduleRequest.endTime?.fromSaoPauloToUTCTimeZone()
            span.setAppointmentScheduleCreateRequest(currentPersonId, appointmentScheduleRequest)
            val scheduleNavigationIdDeferred = async {
                appointmentSchedulePreTriageService.inferScreeningNavigation(currentPersonId.toPersonId())
                    .getOrNullIfNotFound()
            }

            staffService.get(staffId.toSafeUUID())
                .map { staff ->
                    val scheduleNavigationId = scheduleNavigationIdDeferred.await()
                    span.setAttribute("screening_navigation_id", scheduleNavigationId)

                    val appointmentScheduleEvent =
                        AppointmentScheduleEvent.build(
                            staff,
                            appointmentScheduleRequest,
                            startTime,
                            endTime,
                            userType = currentUserType(),
                            generateUniqueId(staff.email, currentPersonId, startTime.toString()),
                            screeningNavigationId = scheduleNavigationId
                        )

                    appointmentScheduleService.scheduleInternal(
                        currentPersonId.toPersonId(),
                        appointmentScheduleEvent,
                        appointmentScheduleRequest.numberOfSessions ?: 1
                    )
                        .coFoldError(
                            AppointmentScheduleCollisionDetectedException::class to {
                                true.success()
                            }
                        )
                }
                .recordResult(span)
                .foldResponse(HttpStatusCode.Created)
        }
    }

    suspend fun listScheduledByPersonAndType(queryParams: Parameters): Response =
        span("listScheduledByPersonAndType") { span ->
            val currentPersonId = currentUid()
            span.setAttribute("person_id", currentPersonId)

            val appointmentScheduleEventTypeId = queryParams["appointmentScheduleEventTypeId"]
                ?: run {
                    return@span Response(HttpStatusCode.BadRequest)
                }
            span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)

            val shouldByPassConcurrentScheduledAppointmentsValidation = FeatureService.inList(
                namespace = FeatureNamespace.SCHEDULE,
                key = "event_type_ids_by_pass_concurrent_scheduled_appointments_validation",
                testValue = appointmentScheduleEventTypeId,
                defaultReturn = false
            )
            if (shouldByPassConcurrentScheduledAppointmentsValidation) {
                span.setAttribute("by_pass_concurrent_schedule_appointments", true)
                return@span emptyList<String>().success().foldResponse()
            }

            val appointmentScheduleEventType = cache.get(
                key = "appointmentScheduleEventTypeById#$appointmentScheduleEventTypeId",
                type = AppointmentScheduleEventType::class,
                expirationTime = A_DAY_IN_SECONDS_EXPIRATION_TIME
            ) {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId.toUUID()).get()
            }

            listAppointmentSchedules(
                personId = currentPersonId.toPersonId(),
                appointmentScheduleEventType = appointmentScheduleEventType
            ).foldResponse()
        }

    suspend fun getStaffAvailabilityForStaffForPeriod(staffId: UUID, queryParams: Parameters): Response =
        span("getStaffAvailabilityForStaffForPeriod") { span ->
            coroutineScope {
                val localDateTimeNow = LocalDateTime.now()
                val fromDate = queryParams["fromDate"]?.toLocalDate()
                    ?: beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
                val toDate = queryParams["toDate"]?.toLocalDate()
                    ?: endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
                val appointmentScheduleEventTypeId = queryParams["appointmentScheduleEventTypeId"]
                    ?: run {
                        return@coroutineScope Response(HttpStatusCode.BadRequest)
                    }
                val providerUnitId = queryParams["providerUnitId"]

                span.setAvailabilityInfo(
                    staffId = staffId,
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId.toUUID(),
                    providerUnitIds = providerUnitId?.let { listOf(it.toUUID()) }
                )

                internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                    staffId = staffId,
                    personId = currentUid(),
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId.toUUID(),
                    providerUnitId = providerUnitId
                ).foldResponse()
            }
        }

    suspend fun getAdvancedAccessAvailability(staffId: UUID, queryParams: Parameters): Response =
        span("getAdvancedAccessAvailability") { span ->
            val personId = currentUserId().toPersonId()
            val providerUnitId = queryParams["providerUnitId"]

            val appointmentScheduleEventTypeId = queryParams["appointmentScheduleEventTypeId"]?.toUUID()
                ?: return@span Response(HttpStatusCode.BadRequest, "Missing appointment scheduleEventTypeId")

            val healthPlanTaskHashed = queryParams["healthPlanTaskId"]
                ?: return@span Response(HttpStatusCode.BadRequest, "Missing healthPlanTaskId")

            actionPlanTaskService.getTasksByFilters(
                filters = ActionPlanTaskFilters(
                    personId = personId,
                    statuses = ActionPlanTaskFilters.TO_DO_STATUSES,
                    types = listOf(ActionPlanTaskType.REFERRAL),
                )
            ).flatMap { tasks ->

                val actionPlanTask = tasks.firstOrNull { it.id.toString().toSha256() == healthPlanTaskHashed }
                    ?: return@flatMap IllegalArgumentException("health_plan_task_id is invalid").failure()

                val fromDate = LocalDate.now()

                val toDate = actionPlanTask.dueDate
                    ?: actionPlanTask.deadline?.date?.toLocalDate()
                    ?: return@flatMap IllegalArgumentException("invalid_due_date").failure()

                span.setAvailabilityInfo(
                    staffId = staffId,
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                    providerUnitIds = providerUnitId?.let { listOf(it.toUUID()) },
                    healthPlanTaskId = actionPlanTask.id
                )

                internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                    staffId = staffId,
                    personId = personId.toString(),
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                    providerUnitId = providerUnitId
                )
            }.foldResponse()
        }

    suspend fun getAvailabilityForAppointmentScheduleEventTypeForPeriod(
        appointmentScheduleEventTypeId: UUID,
        queryParams: Parameters
    ): Response = span("getAvailabilityForAppointmentScheduleEventTypeForPeriod") { span ->
        val localDateTimeNow = LocalDateTime.now()
        val fromDate = queryParams["fromDate"]?.toLocalDate()
            ?: beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val toDate = queryParams["toDate"]?.toLocalDate()
            ?: endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val providerUnitIds = queryParams["providerUnitId"]?.let { listOf(it.lowercase().trim().toSafeUUID()) }

        span.setAvailabilityInfo(
            staffId = null,
            fromDate = fromDate,
            toDate = toDate,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            providerUnitIds = providerUnitIds
        )

        internalAppointmentScheduleService.getAvailabilityForAppointmentScheduleEventTypeForPeriod(
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            personId = currentUid(),
            fromDate = fromDate,
            toDate = toDate,
            providerUnitIds = providerUnitIds
        ).foldResponse()
    }

    suspend fun cancelById(appointmentScheduleId: UUID): Response {
        logger.info(
            "AppointmentScheduleController::cancelById",
            "appointment_schedule_id" to appointmentScheduleId,
            "current_person_id" to currentUid()
        )

        return appointmentScheduleService.get(appointmentScheduleId)
            .then {
                appointmentScheduleService.cancel(appointmentScheduleId, AppointmentScheduleCancelledByType.MEMBER)
                    .fold(
                        { appointmentSchedule -> appointmentSchedule.success() },
                        { ex ->
                            when (ex) {
                                is NotFoundException -> true.success()
                                else -> ex.failure()
                            }
                        }
                    )
            }
            .thenError { exception ->
                logger.error(
                    "AppointmentScheduleController::cancelById error",
                    "error_message" to exception.message,
                    exception
                )
            }
            .map { it.id }
            .foldResponse()
    }

    suspend fun rescheduleAppointmentSchedule(
        appointmentScheduleId: UUID,
        appointmentScheduleRequest: AppointmentScheduleRequest
    ): Response = span("rescheduleAppointmentSchedule") { span ->
        val currentPersonId = currentUid()
        val staffId = appointmentScheduleRequest.staffId
        val startTime = appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone()
        val endTime = appointmentScheduleRequest.endTime?.fromSaoPauloToUTCTimeZone()
        span.setAppointmentScheduleCreateRequest(currentPersonId, appointmentScheduleRequest)

        appointmentScheduleService.get(appointmentScheduleId)
            .flatMap { staffService.get(staffId.toSafeUUID()) }
            .map { staff ->
                val appointmentScheduleEvent = AppointmentScheduleEvent.build(
                    staff = staff,
                    appointmentScheduleRequest = appointmentScheduleRequest,
                    startTime = startTime,
                    endTime = endTime,
                    userType = currentUserType(),
                    eventId = generateUniqueId(staff.email, currentPersonId, startTime.toString())
                )

                span.setAppointmentScheduleRequestedEventAttributes(currentPersonId, appointmentScheduleEvent)
                span.setAttribute("previous_appointment_schedule_id", appointmentScheduleId)
                appointmentScheduleService.reschedule(
                    currentPersonId.toPersonId(),
                    appointmentScheduleEvent,
                    appointmentScheduleId
                )
                    .coFoldError(
                        AppointmentScheduleCollisionDetectedException::class to {
                            true.success()
                        }
                    )
            }
            .recordResult(span)
            .foldResponse()
    }

    suspend fun hasCollidingSchedules(queryParams: Parameters): Response {
        val personId = currentUid()
        val startDateTime = queryParams["startDateTime"]?.toLocalDateTime() ?: run {
            logger.error("AppointmentScheduleController::getHasCollidingSchedules error while get start time")
            return Response(HttpStatusCode.BadRequest, "Missing start date time parameter")
        }
        val endDateTime = queryParams["endDateTime"]?.toLocalDateTime() ?: run {
            logger.error("AppointmentScheduleController::getHasCollidingSchedules error while get end time")
            return Response(HttpStatusCode.BadRequest, "Missing end date time parameter")
        }

        logger.info(
            "AppointmentScheduleController::getHasCollidingSchedules",
            "person_id" to personId,
            "start_date_time" to startDateTime,
            "end_date_time" to endDateTime
        )
        return appointmentScheduleService.hasCollidingForPerson(
            personId.toPersonId(),
            startDateTime.fromSaoPauloToUTCTimeZone(),
            endDateTime.fromSaoPauloToUTCTimeZone()
        )
            .map { HasCollidingSchedulesResponse(hasCollidingSchedules = it) }
            .foldResponse()

    }

    suspend fun getFirstAppointmentScheduleEventIfAvailable(appointmentScheduleEventTypeId: UUID): Response =
        span("getFirstAppointmentScheduleEventIfAvailable") { span ->
            val currentPersonId = currentUid().toPersonId()
            span.setAttribute("person_id", currentPersonId)
            span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)

            if (!useFirstAppointmentScheduleFlow()) {
                return@span FirstAppointmentScheduleResponse().success().foldResponse()
            }

            if (!allowedEventsIdsInFirstAppointmentFlow(appointmentScheduleEventTypeId)) {
                return@span FirstAppointmentScheduleResponse().success().foldResponse()
            }

            try {
                healthcareTeamService.getHealthcareTeamByPerson(currentPersonId)
                    .recordResult(span)
                    .fold(
                        { healthcareTeam ->
                            val allowedPhysicianIdsInFirstAppointmentFlow =
                                allowedPhysicianIdsInFirstAppointmentFlow(healthcareTeam.physicianStaffId)
                            val isFirstScheduleWithThisProfessional =
                                !existsAppointmentSchedule(currentPersonId, healthcareTeam.physicianStaffId)

                            when {
                                allowedPhysicianIdsInFirstAppointmentFlow && isFirstScheduleWithThisProfessional -> {
                                    personService.get(currentPersonId)
                                        .fold(
                                            { person ->
                                                val firstDigitalAppointmentScheduleEventTypeId =
                                                    if (person.isChild) firstDigitalAppointmentScheduleChildren() else firstDigitalAppointmentScheduleAdult()
                                                FirstAppointmentScheduleResponse(
                                                    firstDigitalAppointmentScheduleEventTypeId
                                                ).success()
                                            },
                                            { FirstAppointmentScheduleResponse().success() }
                                        )
                                }

                                else -> FirstAppointmentScheduleResponse().success()
                            }
                        },
                        { FirstAppointmentScheduleResponse().success() }
                    )
                    .foldResponse()
            } catch (e: Exception) {
                span.recordException(e)
                return@span FirstAppointmentScheduleResponse().success().foldResponse()
            }
        }

    suspend fun checkInAppointmentSchedule(
        appointmentScheduleId: UUID
    ): Response = span("checkInAppointmentSchedule") { span ->
        span.setAttribute("personId", currentUid())
        span.setAttribute("appointmentScheduleId", appointmentScheduleId)
        appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentScheduleId).mapEach {
            appointmentScheduleCheckInService.update(
                it.copy(
                    status = AppointmentScheduleCheckInStatus.CONFIRMED
                )
            )
        }.foldResponse()
    }

    private suspend fun existsAppointmentSchedule(personId: PersonId, physicianId: UUID) =
        appointmentScheduleService.existsBy(
            AppointmentScheduleFilter(
                personId = personId,
                status = listOf(AppointmentScheduleStatus.COMPLETED),
                staffId = physicianId
            )
        ).get()

    private fun allowedEventsIdsInFirstAppointmentFlow(eventId: UUID) =
        FeatureService.inList(
            namespace = FeatureNamespace.SCHEDULE,
            key = "allowed_event_id_in_first_appointment_flow",
            testValue = eventId.toString(),
            defaultReturn = false
        )

    private fun allowedPhysicianIdsInFirstAppointmentFlow(physicianId: UUID) =
        FeatureService.inList(
            namespace = FeatureNamespace.SCHEDULE,
            key = "allowed_physician_id_in_first_appointment_flow",
            testValue = physicianId.toString(),
            defaultReturn = false
        )

    private fun firstDigitalAppointmentScheduleAdult() =
        FeatureService.get(
            namespace = FeatureNamespace.SCHEDULE,
            key = "first_adult_digital_appointment_schedule_event_id",
            defaultValue = ""
        ).toUUID()

    private fun firstDigitalAppointmentScheduleChildren() =
        FeatureService.get(
            namespace = FeatureNamespace.SCHEDULE,
            key = "first_children_digital_appointment_schedule_event_id",
            defaultValue = ""
        ).toUUID()

    private fun useFirstAppointmentScheduleFlow() = FeatureService.get(
        namespace = FeatureNamespace.SCHEDULE,
        key = "use_first_appointment_schedule_flow",
        defaultValue = false
    )

    private suspend fun Span.setAppointmentScheduleCreateRequest(
        personId: String,
        appointmentScheduleRequest: AppointmentScheduleRequest
    ) {
        setAttribute("person_id", personId)
        setAttribute("session_id", currentAppSessionId().orEmpty())
        setAttribute("staff_id", appointmentScheduleRequest.staffId)
        setAttribute("health_plan_task_id", appointmentScheduleRequest.healthPlanTaskId.orEmpty())
        setAttribute("person_task_id", appointmentScheduleRequest.personTaskId.orEmpty())
        setAttribute("event_name", appointmentScheduleRequest.eventName.orEmpty())
        setAttribute("event_location", appointmentScheduleRequest.location.orEmpty())
        setAttribute("event_start_time", appointmentScheduleRequest.startTime)
        setAttribute("event_end_time", appointmentScheduleRequest.endTime)
        setAttribute("provider_unit_id", appointmentScheduleRequest.providerUnitId.orEmpty())
        setAttribute("device_timezone_offset", currentAppTimeZoneOffset().orEmpty())
    }

    private fun Span.setAppointmentScheduleRequestedEventAttributes(
        personId: String,
        appointmentScheduleEvent: AppointmentScheduleEvent
    ) {
        setAttribute("person_id", personId)
        setAttribute("event_id", appointmentScheduleEvent.eventId)
        setAttribute("event_name", appointmentScheduleEvent.eventName)
        setAttribute("current_user_type", appointmentScheduleEvent.currentUserType?.name.orEmpty())
        setAttribute("event_start_time", appointmentScheduleEvent.startTime.toString())
        setAttribute("event_end_time", appointmentScheduleEvent.endTime.toString())
    }

    private suspend fun listAppointmentSchedules(
        personId: PersonId,
        appointmentScheduleEventType: AppointmentScheduleEventType
    ) = span("listAppointmentSchedules") { span ->
        val shouldUseCategoryToCheckSchedulesConcurrency =
            EVENT_CATEGORIES_TO_CONSIDER_CONCURRENT_SCHEDULES.contains(appointmentScheduleEventType.category)
        span.setAttribute(
            "should_use_category_to_check_schedules_concurrency",
            shouldUseCategoryToCheckSchedulesConcurrency
        )
        span.setAttribute("appointment_schedule_event_type_category", appointmentScheduleEventType.category.name)

        findByFilters(shouldUseCategoryToCheckSchedulesConcurrency, personId, appointmentScheduleEventType)
            .map { appointmentSchedules ->
                if (appointmentSchedules.isEmpty()) return@map listOf()
                appointmentSchedules.map { appointmentSchedule ->
                    val staffId =
                        getHealthcareTeamPhysicianId(appointmentScheduleEventType, personId)
                            ?: appointmentSchedule.staffId

                    span.setAttribute("appointment_schedule_data_schedule_id", appointmentSchedule.id)
                    span.setAttribute("appointment_schedule_data_staff_id", staffId)

                    AppointmentScheduleDataResponse(
                        id = appointmentSchedule.id,
                        staffId = staffId,
                        startTime = appointmentSchedule.startTime.toSaoPauloTimeZone(),
                        endTime = appointmentSchedule.endTime?.toSaoPauloTimeZone(),
                        isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral,
                    )
                }
            }
    }

    private suspend fun findByFilters(
        shouldUseCategoryToCheckSchedulesConcurrency: Boolean,
        personId: PersonId,
        appointmentScheduleEventType: AppointmentScheduleEventType
    ): Result<List<AppointmentSchedule>, Throwable> {

        val filters = AppointmentScheduleFilter(
            personId = personId,
            status = listOf(AppointmentScheduleStatus.SCHEDULED),
            startDate = LocalDate.now()
        ).let {
            if (shouldUseCategoryToCheckSchedulesConcurrency)
                it.copy(types = listOf(appointmentScheduleEventType.category))
            else
                it.copy(appointmentScheduleEventTypeId = appointmentScheduleEventType.id)
        }

        return appointmentScheduleService.findBy(filters)
    }

    private suspend fun getHealthcareTeamPhysicianId(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        personId: PersonId
    ) =
        if (appointmentScheduleEventType.category == AppointmentScheduleType.HEALTHCARE_TEAM && !appointmentScheduleEventType.isMultiProfessionalReferral) {
            healthcareTeamService.getHealthcareTeamByPerson(personId).getOrNull()?.physicianStaffId
        } else null

    private suspend fun Span.setAvailabilityInfo(
        staffId: UUID? = null,
        fromDate: LocalDate,
        toDate: LocalDate,
        appointmentScheduleEventTypeId: UUID,
        providerUnitIds: List<UUID>?,
        healthPlanTaskId: UUID? = null,
    ) {
        setAttribute("device_timezone_offset", currentAppTimeZoneOffset().orEmpty())
        setAttribute("session_id", currentAppSessionId().orEmpty())
        setAttribute("person_id", currentUid())
        setAttribute("staff_id", staffId)
        setAttribute("from_date", fromDate.toString())
        setAttribute("to_date", toDate.toString())
        setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)
        setAttribute("provider_unit_ids", providerUnitIds?.joinToString { it.toString() }.orEmpty())
        setAttribute("health_plan_task_id", healthPlanTaskId)
    }

}
