package br.com.alice.member.api

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.authentication.currentUserType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.core.extensions.toUrlEncoded
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.FeatureAccessResult
import br.com.alice.common.featureaccess.features.AppointmentSchedulesListResource
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.AppointmentScheduleCancelledByType
import br.com.alice.data.layer.models.AppointmentScheduleCheckInStatus
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.member.api.builders.SchedulingUrlBuilder.Companion.asQueryParam
import br.com.alice.member.api.builders.SchedulingUrlBuilder.Companion.encodeContent
import br.com.alice.member.api.controllers.AppointmentScheduleController
import br.com.alice.member.api.controllers.AppointmentScheduleController.Companion.A_DAY_IN_SECONDS_EXPIRATION_TIME
import br.com.alice.member.api.converters.AppointmentScheduleConverter
import br.com.alice.member.api.converters.AppointmentScheduleWithStaffConverter
import br.com.alice.member.api.models.AppointmentScheduleResponse
import br.com.alice.member.api.models.FirstAppointmentScheduleResponse
import br.com.alice.member.api.models.HasCollidingSchedulesResponse
import br.com.alice.member.api.models.HealthProfessionalResponse
import br.com.alice.member.api.models.NextAppointmentSchedules
import br.com.alice.member.api.models.scheduler.AppointmentScheduleDataResponse
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleCheckInService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentSchedulePreTriageService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.exceptions.AppointmentScheduleCollisionDetectedException
import br.com.alice.schedule.model.AppointmentScheduleEvent
import br.com.alice.schedule.model.AppointmentScheduleRequest
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.StaffAvailabilityResponse
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import br.com.alice.common.featureaccess.features.AppointmentScheduleWithStaff as AppointmentScheduleWithStaffFeatureAccess
import br.com.alice.member.api.services.AppointmentScheduleService as InternalAppointmentScheduleService

class AppointmentScheduleRoutesTest : RoutesTestHelper() {

    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val internalAppointmentScheduleService: InternalAppointmentScheduleService = mockk()
    private val staffService: StaffService = mockk()
    private val personCalendlyService: PersonCalendlyService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()
    private val cache: GenericCache = mockk()
    private val appointmentSchedulePreTriageService: AppointmentSchedulePreTriageService = mockk()
    private val personService: PersonService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val appointmentScheduleCheckInService: AppointmentScheduleCheckInService = mockk()
    private val appointmentScheduleWithStaffConverter: AppointmentScheduleWithStaffConverter = mockk()
    private val actionPlanTaskService: ActionPlanTaskService = mockk()

    private val person = TestModelFactory.buildPerson()
    private val personCalendly = TestModelFactory.buildPersonCalendly(person.id)
    private val staff = TestModelFactory.buildStaff()

    private val utmTerm = gson.toJson(mapOf("person_calendly_id" to personCalendly.id)).toUrlEncoded()
    private val utmParam = "&utm_term=$utmTerm"

    private val token = RangeUUID.generate().toString()

    private val appointmentSchedule =
        TestModelFactory.buildAppointmentSchedule(personId = person.id, staffId = staff.id)
    private val expectedParams = mapOf(
        "health_plan_task_id" to appointmentSchedule.healthPlanTaskId.toString().toSha256(),
        "staff_id" to staff.id.toString(),
        "event_name" to appointmentSchedule.eventName,
        "appointment_schedule_id" to appointmentSchedule.id.toString(),
    ).encodeContent().asQueryParam().substring(1)

    private val appointmentScheduleResponse = AppointmentScheduleResponse(
        id = appointmentSchedule.id.toString(),
        healthProfessionals = listOf(
            HealthProfessionalResponse(
                id = staff.id.toString(),
                firstName = "José",
                lastName = "Silva",
                profileImageUrl = null,
                description = "Médico(a) do seu time de saúde"
            )
        ),
        name = "Primeira consulta com seu time de saúde",
        startTime = "2020-01-01T10:10",
        cancelUrl = "https://calendly.com/cancellations/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80&$expectedParams$utmParam",
        rescheduleUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/reschedulings/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80$utmParam",
        location = "Rua Rebouças, 3506",
        locationUrl = null
    )
    private val defaultExpectedResponse =
        NextAppointmentSchedules(
            appointmentSchedules = listOf(
                appointmentScheduleResponse
            )
        )

    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            AppointmentScheduleController(
                appointmentScheduleService,
                internalAppointmentScheduleService,
                staffService,
                appointmentScheduleEventTypeService,
                personCalendlyService,
                cache,
                appointmentSchedulePreTriageService,
                personService,
                healthcareTeamService,
                appointmentScheduleCheckInService,
                appointmentScheduleWithStaffConverter,
                actionPlanTaskService
            )
        }

        coEvery { staffService.get(staff.id) } returns staff
        coEvery {
            cache.get(
                key = match { it.startsWith("appointmentScheduleEventTypeById#") },
                type = AppointmentScheduleEventType::class,
                expirationTime = A_DAY_IN_SECONDS_EXPIRATION_TIME,
                putFunction = any()
            )
        } coAnswers {
            arg<suspend () -> AppointmentScheduleEventType>(4).invoke()
        }
    }

    @Test
    fun `#getNextAppointmentSchedules should return 200 OK with a list of schedules`() = mockLocalDateTime { date ->
        val scheduleWithStaff = AppointmentScheduleWithStaff(appointmentSchedule, staff)
        val appointmentScheduleWithStaff = AppointmentScheduleWithStaffFeatureAccess(
            appointmentSchedule,
            staff
        )
        coEvery {
            appointmentScheduleService.findWithStaffBy(
                AppointmentScheduleFilter(
                    personId = person.id,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    endTimeGreater = date,
                    sortOrder = SortOrder.Ascending
                )
            )
        } returns listOf(scheduleWithStaff)

        coEvery {
            appointmentScheduleEventTypeService.getByIds(emptyList())
        } returns emptyList()

        coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

        coEvery {
            appointmentScheduleWithStaffConverter.convert(
                appointmentScheduleWithStaff,
                false,
                personCalendly,
            )
        } returns appointmentScheduleResponse

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules") { response ->
                assertThat(response).isOK()

                val responseAsJson: NextAppointmentSchedules = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(defaultExpectedResponse)
            }
        }
    }

    @Test
    fun `#getNextAppointmentSchedules should return 200 OK with a list of schedules if user is non-member`() =
        mockLocalDateTime { date ->
            val scheduleWithStaff = AppointmentScheduleWithStaff(appointmentSchedule, staff)
            val appointmentScheduleWithStaff = AppointmentScheduleWithStaffFeatureAccess(
                appointmentSchedule,
                staff
            )
            coEvery {
                appointmentScheduleService.findWithStaffBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        endTimeGreater = date,
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(scheduleWithStaff)

            coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

            coEvery {
                appointmentScheduleEventTypeService.getByIds(emptyList())
            } returns emptyList()

            coEvery {
                appointmentScheduleWithStaffConverter.convert(
                    appointmentScheduleWithStaff,
                    false,
                    personCalendly,
                )
            } returns appointmentScheduleResponse

            val appointmentSchedulesListResource = AppointmentSchedulesListResource(
                appointmentSchedulesWithStaff = listOf(
                    AppointmentScheduleWithStaffFeatureAccess(
                        appointmentSchedule,
                        staff,
                    )
                )
            )

            val featureAccessResult = FeatureAccessResult(
                feature = Feature.APPOINTMENT_SCHEDULES_LIST,
                response = AppointmentSchedulesListResource(
                    appointmentSchedulesListResource.appointmentSchedulesWithStaff,
                )
            )

            mockkObject(FeatureAccess) {
                coEvery {
                    FeatureAccess.filter(
                        feature = Feature.APPOINTMENT_SCHEDULES_LIST,
                        resource = appointmentSchedulesListResource
                    )
                } returns featureAccessResult

                authenticatedAs(token, toTestPerson(person)) {
                    get("/appointment_schedules") { response ->
                        assertThat(response).isOK()

                        val expectedResponse =
                            defaultExpectedResponse.copy(
                                appointmentSchedules = defaultExpectedResponse.appointmentSchedules
                            )

                        val responseAsJson: NextAppointmentSchedules = response.bodyAsJson()
                        assertThat(responseAsJson).isEqualTo(expectedResponse)
                    }
                }
            }
        }

    @Test
    fun `#getNextAppointmentSchedules should return 200 OK with a list of schedules, discarding the case where staff == null`() =
        mockLocalDateTime { date ->
            val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(person.id, staffId = staff.id)
            val appointmentSchedule2 = TestModelFactory.buildAppointmentSchedule(person.id, staffId = staff.id)

            val scheduleWithStaff = AppointmentScheduleWithStaff(appointmentSchedule, staff)
            val scheduleWithoutStaff = AppointmentScheduleWithStaff(appointmentSchedule2, null)
            val appointmentScheduleWithStaff = AppointmentScheduleWithStaffFeatureAccess(
                appointmentSchedule,
                staff
            )
            val appointmentScheduleWithoutStaff = AppointmentScheduleWithStaffFeatureAccess(
                appointmentSchedule2,
                null
            )
            coEvery {
                appointmentScheduleService.findWithStaffBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        endTimeGreater = date,
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(scheduleWithStaff, scheduleWithoutStaff)

            coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

            coEvery {
                appointmentScheduleEventTypeService.getByIds(emptyList())
            } returns emptyList()

            val expectedParamsResponse01 = mapOf(
                "health_plan_task_id" to appointmentSchedule.healthPlanTaskId.toString().toSha256(),
                "staff_id" to staff.id.toString(),
                "event_name" to appointmentSchedule.eventName,
                "appointment_schedule_id" to appointmentSchedule.id.toString(),
            ).encodeContent().asQueryParam().substring(1)
            coEvery {
                appointmentScheduleWithStaffConverter.convert(
                    appointmentScheduleWithStaff,
                    false,
                    personCalendly,
                )
            } returns appointmentScheduleResponse.copy(
                id = appointmentScheduleWithStaff.appointmentSchedule.id.toString(),
                cancelUrl = "https://calendly.com/cancellations/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80&$expectedParamsResponse01$utmParam",
            )
            coEvery {
                appointmentScheduleWithStaffConverter.convert(
                    appointmentScheduleWithoutStaff,
                    false,
                    personCalendly,
                )
            } returns appointmentScheduleResponse.copy(id = appointmentScheduleWithoutStaff.appointmentSchedule.id.toString())

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules") { response ->
                    assertThat(response).isOK()


                    val expectedResponse = NextAppointmentSchedules(
                        appointmentSchedules = listOf(
                            AppointmentScheduleResponse(
                                id = appointmentSchedule.id.toString(),
                                healthProfessionals = listOf(
                                    HealthProfessionalResponse(
                                        id = staff.id.toString(),
                                        firstName = "José",
                                        lastName = "Silva",
                                        profileImageUrl = null,
                                        description = "Médico(a) do seu time de saúde"
                                    )
                                ),
                                name = "Primeira consulta com seu time de saúde",
                                startTime = "2020-01-01T10:10",
                                cancelUrl = "https://calendly.com/cancellations/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80&$expectedParamsResponse01$utmParam",
                                rescheduleUrl = "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/reschedulings/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80$utmParam",
                                location = "Rua Rebouças, 3506",
                                locationUrl = null
                            ),
                        )
                    )

                    val responseAsJson: NextAppointmentSchedules = response.bodyAsJson()
                    assertThat(responseAsJson).isEqualTo(expectedResponse)
                }
            }
        }

    @Test
    fun `#createAppointmentSchedule should successfully create sync  appointment schedule`() =
        runBlocking {
            val personId = person.id
            val appointmentScheduleRequest = AppointmentScheduleRequest(
                staffId = staff.id.toString(),
                eventName = "event_name",
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30)
            )

            val screeningNavigationId = RangeUUID.generate()

            val appointmentScheduleEvent = AppointmentScheduleEvent.build(
                staff,
                appointmentScheduleRequest,
                appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone(),
                appointmentScheduleRequest.endTime?.fromSaoPauloToUTCTimeZone(),
                userType = currentUserType(),
                eventId = staff.email.toSha256() + personId.toString()
                    .toSha256() + appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone().toString()
                    .toSha256(),
                screeningNavigationId = screeningNavigationId
            )

            val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                staffId = staff.id,
                personId = personId,
                startTime = appointmentScheduleRequest.startTime,
                endTime = appointmentScheduleRequest.endTime
            )

            coEvery {
                appointmentSchedulePreTriageService.inferScreeningNavigation(person.id)
            } returns screeningNavigationId

            coEvery {
                staffService.get(staff.id)
            } returns staff

            coEvery {
                appointmentScheduleService.scheduleInternal(personId, appointmentScheduleEvent, 1)
            } returns listOf(appointmentSchedule)

            authenticatedAs(token, toTestPerson(person)) {
                post("/appointment_schedules", appointmentScheduleRequest) { response ->
                    assertThat(response).isCreated()
                }
            }

            coVerifyOnce {
                appointmentSchedulePreTriageService.inferScreeningNavigation(any())
                staffService.get(any())
                appointmentScheduleService.scheduleInternal(any(), any(), any())
            }
        }

    @Test
    fun `#createAppointmentSchedule should return successfully create sync when has collision`() =
        runBlocking {
            val personId = person.id
            val appointmentScheduleRequest = AppointmentScheduleRequest(
                staffId = staff.id.toString(),
                eventName = "event_name",
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30)
            )

            val screeningNavigationId = RangeUUID.generate()

            val appointmentScheduleEvent = AppointmentScheduleEvent.build(
                staff,
                appointmentScheduleRequest,
                appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone(),
                appointmentScheduleRequest.endTime?.fromSaoPauloToUTCTimeZone(),
                userType = currentUserType(),
                eventId = staff.email.toSha256() + personId.toString()
                    .toSha256() + appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone().toString()
                    .toSha256(),
                screeningNavigationId = screeningNavigationId
            )

            coEvery {
                appointmentSchedulePreTriageService.inferScreeningNavigation(person.id)
            } returns screeningNavigationId

            coEvery {
                staffService.get(staff.id)
            } returns staff

            coEvery {
                appointmentScheduleService.scheduleInternal(personId, appointmentScheduleEvent, 1)
            } returns AppointmentScheduleCollisionDetectedException("")

            authenticatedAs(token, toTestPerson(person)) {
                post("/appointment_schedules", appointmentScheduleRequest) { response ->
                    assertThat(response).isCreated()
                }
            }

            coVerifyOnce {
                appointmentSchedulePreTriageService.inferScreeningNavigation(any())
                staffService.get(any())
                appointmentScheduleService.scheduleInternal(any(), any(), any())
            }
        }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod without toDate should return slots from current month`() {
        val token = RangeUUID.generate().toString()
        val localDateTimeNow = LocalDateTime.now()
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val slots = listOf(
            SlotForSpecificDuration(
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30),
                durationInMinutes = 30,
            )
        )

        coEvery {
            internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                staffId = staff.id,
                personId = staff.id.toString(),
                fromDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow),
                toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow),
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                providerUnitId = null
            )
        } returns StaffAvailabilityResponse(slots)

        val expectedResponse = StaffAvailabilityResponse(slots)
        authenticatedAs(token, toTestStaff(staff)) {
            get("/staff_availability/${staff.id}?appointmentScheduleEventTypeId=${appointmentScheduleEventTypeId}") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
                coVerifyOnce {
                    internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }
    }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod get staff availability for period`() {
        val token = RangeUUID.generate().toString()
        val localDateTimeNow = LocalDateTime.now()
        val toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val slots = listOf(
            SlotForSpecificDuration(
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30),
                durationInMinutes = 30,
            )
        )

        coEvery {
            internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                staffId = staff.id,
                personId = staff.id.toString(),
                fromDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow),
                toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow),
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                providerUnitId = null
            )
        } returns StaffAvailabilityResponse(slots)

        val expectedResponse = StaffAvailabilityResponse(slots)
        authenticatedAs(token, toTestStaff(staff)) {
            get("/staff_availability/${staff.id}?toDate=$toDate&appointmentScheduleEventTypeId=${appointmentScheduleEventTypeId}") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
                coVerifyOnce {
                    internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }
    }

    @Test
    fun `#getAvailabilityForAppointmentScheduleEventTypeForPeriod get staff availability for period`() {
        val token = RangeUUID.generate().toString()
        val localDateTimeNow = LocalDateTime.now()
        val toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val appointmentScheduleEventTypeId = RangeUUID.generate()
        val slots = listOf(
            SlotForSpecificDuration(
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now().plusMinutes(30),
                durationInMinutes = 30,
            )
        )

        coEvery {
            internalAppointmentScheduleService.getAvailabilityForAppointmentScheduleEventTypeForPeriod(
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                personId = person.id.toString(),
                fromDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow),
                toDate = toDate,
                providerUnitIds = null
            )
        } returns StaffAvailabilityResponse(slots)

        val expectedResponse = StaffAvailabilityResponse(slots)
        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedule_event_type_availability/${appointmentScheduleEventTypeId}?toDate=$toDate") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
                coVerifyOnce {
                    internalAppointmentScheduleService.getAvailabilityForAppointmentScheduleEventTypeForPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }
    }

    @Test
    fun `#getAvailabilityForAppointmentScheduleEventTypeForPeriod get empty availability for period when event type is not active`() {
        val token = RangeUUID.generate().toString()
        val localDateTimeNow = LocalDateTime.now()
        val toDate = endOfTheMonthWithSaoPauloTimezone(localDateTimeNow)
        val appointmentScheduleEventTypeId = RangeUUID.generate()

        coEvery {
            internalAppointmentScheduleService.getAvailabilityForAppointmentScheduleEventTypeForPeriod(
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                personId = person.id.toString(),
                fromDate = beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow),
                toDate = toDate,
                providerUnitIds = null
            )
        } returns StaffAvailabilityResponse(emptyList())

        val expectedResponse = StaffAvailabilityResponse(emptyList())
        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedule_event_type_availability/${appointmentScheduleEventTypeId}?toDate=$toDate") { response ->
                val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expectedResponse)
                coVerifyOnce {
                    internalAppointmentScheduleService.getAvailabilityForAppointmentScheduleEventTypeForPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }
    }

    @Test
    fun `#getDetails should get appointment schedule with checkIn status`() = runBlocking {
        val appointmentScheduleWithStaff = AppointmentScheduleWithStaffFeatureAccess(
            appointmentSchedule,
            staff
        )
        val appointmentScheduleCheckIn = TestModelFactory.buildAppointmentScheduleCheckIn()
        coEvery {
            appointmentScheduleWithStaffConverter.convert(
                appointmentScheduleWithStaff,
                false,
                null,
                appointmentScheduleCheckIn.status
            )
        } returns appointmentScheduleResponse
        val expected = appointmentScheduleWithStaffConverter.convert(
            appointmentScheduleWithStaff,
            false,
            null,
            appointmentScheduleCheckIn.status
        )

        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule
        coEvery { staffService.get(staff.id) } returns staff
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns listOf(
            appointmentScheduleCheckIn
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/${appointmentSchedule.id}/details") { response ->
                val responseAsJson: AppointmentScheduleResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expected)
            }
        }

        coVerifyOnce {
            appointmentScheduleService.get(any())
            staffService.get(any())
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
        }
    }

    @Test
    fun `#getDetails should get appointment schedule when checkIn status is NULL`() = runBlocking {
        val appointmentScheduleWithStaff = AppointmentScheduleWithStaffFeatureAccess(
            appointmentSchedule,
            staff
        )
        coEvery {
            appointmentScheduleWithStaffConverter.convert(
                appointmentScheduleWithStaff,
                false,
                null,
                null
            )
        } returns appointmentScheduleResponse
        val expected = appointmentScheduleWithStaffConverter.convert(appointmentScheduleWithStaff, false, null, null)

        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule
        coEvery { staffService.get(staff.id) } returns staff
        coEvery { appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentSchedule.id) } returns NotFoundException(
            ""
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/${appointmentSchedule.id}/details") { response ->
                val responseAsJson: AppointmentScheduleResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expected)
            }
        }

        coVerifyOnce {
            appointmentScheduleService.get(any())
            staffService.get(any())
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
        }
    }

    @Test
    fun `#getById should get appointment schedule`() {
        val expected = AppointmentScheduleConverter.convert(appointmentSchedule)

        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/${appointmentSchedule.id}") { response ->
                val responseAsJson: AppointmentScheduleResponse = response.bodyAsJson()
                assertThat(responseAsJson).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getById should return error while getting appointment schedule`() {
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns Exception()

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/${appointmentSchedule.id}") { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `#cancelById should cancel appointment schedule sync`() = runBlocking {
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule
        coEvery {
            appointmentScheduleService.cancel(
                appointmentSchedule.id,
                AppointmentScheduleCancelledByType.MEMBER
            )
        } returns appointmentSchedule

        authenticatedAs(token, toTestPerson(person)) {
            post("/appointment_schedules/${appointmentSchedule.id}/cancel") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            appointmentScheduleService.get(any())
            appointmentScheduleService.cancel(any(), any())
        }
    }

    @Test
    fun `#cancelById should return success to cancel appointment schedule when not found`() = runBlocking {
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule
        coEvery {
            appointmentScheduleService.cancel(
                appointmentSchedule.id,
                AppointmentScheduleCancelledByType.MEMBER
            )
        } returns NotFoundException("")

        authenticatedAs(token, toTestPerson(person)) {
            post("/appointment_schedules/${appointmentSchedule.id}/cancel") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            appointmentScheduleService.get(any())
            appointmentScheduleService.cancel(any(), any())
        }
    }

    @Test
    fun `#rescheduleAppointmentSchedule should create appointment schedule`() = runBlocking {
        val newAppointmentSchedule = TestModelFactory.buildAppointmentSchedule(staffId = staff.id, personId = person.id)

        val appointmentScheduleRequest = AppointmentScheduleRequest(
            staffId = staff.id.toString(),
            eventName = "event_name",
            startTime = appointmentSchedule.startTime,
            endTime = appointmentSchedule.endTime
        )

        val appointmentScheduleEvent = AppointmentScheduleEvent.build(
            staff,
            appointmentScheduleRequest,
            appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone(),
            appointmentScheduleRequest.endTime?.fromSaoPauloToUTCTimeZone(),
            userType = currentUserType(),
            eventId = staff.email.toSha256() + person.id.toString()
                .toSha256() + appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone().toString().toSha256(),
        )

        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule

        coEvery {
            appointmentScheduleService.reschedule(person.id, appointmentScheduleEvent, appointmentSchedule.id)
        } returns listOf(newAppointmentSchedule)

        authenticatedAs(token, toTestPerson(person)) {
            post(
                "/appointment_schedules/${appointmentSchedule.id}/reschedule",
                appointmentScheduleRequest
            ) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            appointmentScheduleService.get(any())
            appointmentScheduleService.reschedule(any(), any(), any())
        }
    }

    @Test
    fun `#rescheduleAppointmentSchedule returns created appointment schedule sync when has collision`() =
        runBlocking {
            val appointmentScheduleRequest = AppointmentScheduleRequest(
                staffId = staff.id.toString(),
                eventName = "event_name",
                startTime = appointmentSchedule.startTime,
                endTime = appointmentSchedule.endTime
            )

            val appointmentScheduleEvent = AppointmentScheduleEvent.build(
                staff,
                appointmentScheduleRequest,
                appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone(),
                appointmentScheduleRequest.endTime?.fromSaoPauloToUTCTimeZone(),
                userType = currentUserType(),
                eventId = staff.email.toSha256() + person.id.toString()
                    .toSha256() + appointmentScheduleRequest.startTime.fromSaoPauloToUTCTimeZone().toString()
                    .toSha256(),
            )

            coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule

            coEvery {
                appointmentScheduleService.reschedule(person.id, appointmentScheduleEvent, appointmentSchedule.id)
            } returns AppointmentScheduleCollisionDetectedException("")

            authenticatedAs(token, toTestPerson(person)) {
                post(
                    "/appointment_schedules/${appointmentSchedule.id}/reschedule",
                    appointmentScheduleRequest
                ) { response ->
                    assertThat(response).isOK()
                }
            }

            coVerifyOnce {
                appointmentScheduleService.get(any())
                appointmentScheduleService.reschedule(any(), any(), any())
            }
        }

    @Test
    fun `#rescheduleAppointmentSchedule should not do anything if appointment schedule for reschedule is not found`() {
        val appointmentScheduleRequest = AppointmentScheduleRequest(
            staffId = staff.id.toString(),
            eventName = "event_name",
            startTime = LocalDateTime.now(),
            endTime = LocalDateTime.now().plusMinutes(30)
        )
        coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns NotFoundException("not found")

        authenticatedAs(token, toTestPerson(person)) {
            post(
                "/appointment_schedules/${appointmentSchedule.id}/reschedule",
                appointmentScheduleRequest
            ) { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { appointmentScheduleService.get(any()) }
    }

    @Test
    fun `#listScheduledByPersonAndType should get appointment schedule data by person id and event type and different staffId if healthcare team changed`() =
        mockLocalDate { localDate ->
            val appointmentScheduleEventType =
                appointmentScheduleEventType.copy(category = AppointmentScheduleType.HEALTHCARE_TEAM)
            val healthcareTeam = healthcareTeam.copy(physicianStaffId = UUID.randomUUID())

            val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                personId = person.id,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                startTime = LocalDateTime.of(2020, 1, 1, 10, 10),
                endTime = LocalDateTime.of(2020, 1, 1, 11, 10),
                staffId = UUID.randomUUID()
            )
            val expected = listOf(
                AppointmentScheduleDataResponse(
                    staffId = healthcareTeam.physicianStaffId,
                    id = appointmentSchedule.id,
                    startTime = LocalDateTime.of(2020, 1, 1, 7, 10),
                    endTime = LocalDateTime.of(2020, 1, 1, 8, 10),
                    isMultiProfessionalReferral = false
                )
            )

            coEvery {
                cache.get(
                    key = "appointmentScheduleEventTypeById#${appointmentScheduleEventType.id}",
                    type = AppointmentScheduleEventType::class,
                    expirationTime = A_DAY_IN_SECONDS_EXPIRATION_TIME,
                    putFunction = any()
                )
            } returns appointmentScheduleEventType

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        types = listOf(AppointmentScheduleType.HEALTHCARE_TEAM),
                        startDate = localDate
                    )
                )
            } returns listOf(appointmentSchedule)

            coEvery {
                healthcareTeamService.getHealthcareTeamByPerson(person.id)
            } returns healthcareTeam

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/list_scheduled_by_person_and_type/?appointmentScheduleEventTypeId=${appointmentScheduleEventType.id}") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }

            coVerify { appointmentScheduleEventTypeService wasNot called }
        }

    @Test
    fun `#listScheduledByPersonAndType should get appointment schedule data by person id and event type with same staffId from healthcare team`() =
        mockLocalDate { localDate ->
            val appointmentScheduleEventType =
                appointmentScheduleEventType.copy(category = AppointmentScheduleType.HEALTHCARE_TEAM)
            val healthcareTeam = healthcareTeam.copy(physicianStaffId = UUID.randomUUID())

            val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                personId = person.id,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                startTime = LocalDateTime.of(2020, 1, 1, 10, 10),
                endTime = LocalDateTime.of(2020, 1, 1, 11, 10),
                staffId = healthcareTeam.physicianStaffId
            )
            val expected = listOf(
                AppointmentScheduleDataResponse(
                    staffId = appointmentSchedule.staffId,
                    id = appointmentSchedule.id,
                    startTime = LocalDateTime.of(2020, 1, 1, 7, 10),
                    endTime = LocalDateTime.of(2020, 1, 1, 8, 10),
                    isMultiProfessionalReferral = false
                )
            )

            coEvery {
                cache.get(
                    key = "appointmentScheduleEventTypeById#${appointmentScheduleEventType.id}",
                    type = AppointmentScheduleEventType::class,
                    expirationTime = A_DAY_IN_SECONDS_EXPIRATION_TIME,
                    putFunction = any()
                )
            } returns appointmentScheduleEventType

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        types = listOf(AppointmentScheduleType.HEALTHCARE_TEAM),
                        startDate = localDate
                    )
                )
            } returns listOf(appointmentSchedule)

            coEvery {
                healthcareTeamService.getHealthcareTeamByPerson(person.id)
            } returns healthcareTeam

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/list_scheduled_by_person_and_type/?appointmentScheduleEventTypeId=${appointmentScheduleEventType.id}") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }

            coVerify { appointmentScheduleEventTypeService wasNot called }
        }


    @Test
    fun `#listScheduledByPersonAndType should get appointment schedule data by person id and event type`() =
        mockLocalDate { localDate ->
            val staffId = UUID.randomUUID()

            val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
                personId = person.id,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                startTime = LocalDateTime.of(2020, 1, 1, 10, 10),
                endTime = LocalDateTime.of(2020, 1, 1, 11, 10),
                staffId = staffId
            )
            val expected = listOf(
                AppointmentScheduleDataResponse(
                    staffId = staffId,
                    id = appointmentSchedule.id,
                    startTime = LocalDateTime.of(2020, 1, 1, 7, 10),
                    endTime = LocalDateTime.of(2020, 1, 1, 8, 10),
                    isMultiProfessionalReferral = false
                )
            )

            coEvery {
                cache.get(
                    key = "appointmentScheduleEventTypeById#${appointmentScheduleEventType.id}",
                    type = AppointmentScheduleEventType::class,
                    expirationTime = A_DAY_IN_SECONDS_EXPIRATION_TIME,
                    putFunction = any()
                )
            } returns appointmentScheduleEventType

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                        startDate = localDate
                    )
                )
            } returns listOf(appointmentSchedule)

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/list_scheduled_by_person_and_type/?appointmentScheduleEventTypeId=${appointmentScheduleEventType.id}") { response ->
                    assertThat(response).isOK()
                    assertThat(response).isOKWithData(expected)
                }
            }

            coVerify { appointmentScheduleEventTypeService wasNot called }
        }

    @Test
    fun `#listScheduledByPersonAndType should return empty appointment schedules data`() = mockLocalDate { localDate ->
        coEvery { appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id) } returns appointmentScheduleEventType
        coEvery {
            appointmentScheduleService.findBy(
                AppointmentScheduleFilter(
                    personId = person.id,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    startDate = localDate
                )
            )
        } returns emptyList()

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/list_scheduled_by_person_and_type/?appointmentScheduleEventTypeId=${appointmentScheduleEventType.id}") { response ->
                assertThat(response).isOK()
                assertThat(response).isOKWithData(emptyList<String>())
            }
        }
    }

    @Test
    fun `#listScheduledByPersonAndType should return empty list when validation flag is active`() = runBlocking {
        val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()

        withFeatureFlag(
            FeatureNamespace.SCHEDULE,
            "event_type_ids_by_pass_concurrent_scheduled_appointments_validation",
            listOf(appointmentScheduleEventType.id.toString())
        ) {
            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/list_scheduled_by_person_and_type/?appointmentScheduleEventTypeId=${appointmentScheduleEventType.id}") { response ->
                    assertThat(response).isOK()
                    assertThat(response).isOKWithData(emptyList<String>())
                }
            }
        }

        coVerify(exactly = 0) { appointmentScheduleService.findBy(any()) }
    }

    @Test
    fun `#hasCollidingSchedules should be false when hour and day is not colliding`() {
        val startDateTime = LocalDateTime.of(2023, 7, 1, 10, 0, 0)
        val endDateTime = LocalDateTime.of(2023, 7, 1, 10, 30, 0)

        coEvery {
            appointmentScheduleService.hasCollidingForPerson(
                personId = person.id,
                startDateTime = startDateTime.fromSaoPauloToUTCTimeZone(),
                endDateTime = endDateTime.fromSaoPauloToUTCTimeZone()
            )
        } returns false

        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/has_colliding_schedules/?startDateTime=$startDateTime&endDateTime=$endDateTime") { response ->
                assertThat(response).isOKWithData(HasCollidingSchedulesResponse(hasCollidingSchedules = false))
            }
        }
    }

    @Test
    fun `#hasCollidingSchedules returns not found response when do not have start time request parameter`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/has_colliding_schedules/?endDateTime=2023-07-01T10:30:00") { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyNone { appointmentScheduleService.hasCollidingForPerson(any(), any(), any()) }
    }

    @Test
    fun `#hasCollidingSchedules returns not found response when do not have end time request parameter`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/appointment_schedules/has_colliding_schedules/?startDateTime=2023-07-01T10:00:00") { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyNone { appointmentScheduleService.hasCollidingForPerson(any(), any(), any()) }
    }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns first event appointment schedule for adult`() =
        runBlocking {
            val eventId = RangeUUID.generate()
            val eventChildrenId = RangeUUID.generate()
            val originalEventId = RangeUUID.generate()
            val expected = FirstAppointmentScheduleResponse(eventId)
            val featureFlagsMap = mapOf(
                "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
                "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
                "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
                "use_first_appointment_schedule_flow" to true,
                "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
            )

            withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
                coEvery {
                    healthcareTeamService.getHealthcareTeamByPerson(person.id)
                } returns healthcareTeam

                coEvery {
                    personService.get(person.id)
                } returns person.copy(dateOfBirth = LocalDateTime.of(2001, 1, 1, 10, 10))

                coEvery {
                    appointmentScheduleService.existsBy(
                        AppointmentScheduleFilter(
                            personId = person.id,
                            status = listOf(AppointmentScheduleStatus.COMPLETED),
                            staffId = healthcareTeam.physicianStaffId
                        )
                    )
                } returns false

                authenticatedAs(token, toTestPerson(person)) {
                    get("/appointment_schedules/check_first/$originalEventId") { response ->
                        assertThat(response).isOKWithData(expected)
                    }
                }
            }

            coVerifyOnce {
                healthcareTeamService.getHealthcareTeamByPerson(any())
                personService.get(any())
                appointmentScheduleService.existsBy(any())
            }
        }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns first event appointment schedule for minor`() =
        runBlocking {
            val eventId = RangeUUID.generate()
            val eventChildrenId = RangeUUID.generate()
            val originalEventId = RangeUUID.generate()
            val expected = FirstAppointmentScheduleResponse(eventId)
            val featureFlagsMap = mapOf(
                "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
                "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
                "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
                "use_first_appointment_schedule_flow" to true,
                "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
            )

            withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
                coEvery {
                    healthcareTeamService.getHealthcareTeamByPerson(person.id)
                } returns healthcareTeam

                coEvery {
                    personService.get(person.id)
                } returns person.copy(dateOfBirth = LocalDateTime.now().minusYears(15))

                coEvery {
                    appointmentScheduleService.existsBy(
                        AppointmentScheduleFilter(
                            personId = person.id,
                            status = listOf(AppointmentScheduleStatus.COMPLETED),
                            staffId = healthcareTeam.physicianStaffId
                        )
                    )
                } returns false

                authenticatedAs(token, toTestPerson(person)) {
                    get("/appointment_schedules/check_first/$originalEventId") { response ->
                        assertThat(response).isOKWithData(expected)
                    }
                }
            }

            coVerifyOnce {
                healthcareTeamService.getHealthcareTeamByPerson(any())
                personService.get(any())
                appointmentScheduleService.existsBy(any())
            }
        }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when eventId is blank on FF`() = runBlocking {
        val expected = FirstAppointmentScheduleResponse()
        val eventId = RangeUUID.generate()
        val featureFlagsMap = mapOf(
            "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
            "first_adult_digital_appointment_schedule_event_id" to "",
            "first_children_digital_appointment_schedule_event_id" to "",
            "use_first_appointment_schedule_flow" to true,
            "allowed_event_id_in_first_appointment_flow" to listOf(eventId)
        )

        withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
            coEvery {
                healthcareTeamService.getHealthcareTeamByPerson(person.id)
            } returns healthcareTeam

            coEvery {
                personService.get(person.id)
            } returns person

            coEvery {
                appointmentScheduleService.existsBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.COMPLETED),
                        staffId = healthcareTeam.physicianStaffId
                    )
                )
            } returns false

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/check_first/$eventId") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }

        coVerifyOnce {
            healthcareTeamService.getHealthcareTeamByPerson(any())
            personService.get(any())
            appointmentScheduleService.existsBy(any())
        }
    }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns first event appointment schedule for children`() =
        runBlocking {
            val eventId = RangeUUID.generate()
            val eventChildrenId = RangeUUID.generate()
            val originalEventId = RangeUUID.generate()
            val expected = FirstAppointmentScheduleResponse(eventChildrenId)
            val featureFlagsMap = mapOf(
                "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
                "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
                "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
                "use_first_appointment_schedule_flow" to true,
                "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
            )

            withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
                coEvery {
                    healthcareTeamService.getHealthcareTeamByPerson(person.id)
                } returns healthcareTeam

                coEvery {
                    personService.get(person.id)
                } returns person.copy(dateOfBirth = LocalDateTime.now().minusYears(2))

                coEvery {
                    appointmentScheduleService.existsBy(
                        AppointmentScheduleFilter(
                            personId = person.id,
                            status = listOf(AppointmentScheduleStatus.COMPLETED),
                            staffId = healthcareTeam.physicianStaffId
                        )
                    )
                } returns false

                authenticatedAs(token, toTestPerson(person)) {
                    get("/appointment_schedules/check_first/$originalEventId") { response ->
                        assertThat(response).isOKWithData(expected)
                    }
                }
            }

            coVerifyOnce {
                healthcareTeamService.getHealthcareTeamByPerson(any())
                personService.get(any())
                appointmentScheduleService.existsBy(any())
            }
        }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when physicianId is not in the list`() = runBlocking {
        val eventId = RangeUUID.generate()
        val eventChildrenId = RangeUUID.generate()
        val originalEventId = RangeUUID.generate()
        val expected = FirstAppointmentScheduleResponse()
        val featureFlagsMap = mapOf(
            "allowed_physician_id_in_first_appointment_flow" to emptyList<UUID>(),
            "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
            "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
            "use_first_appointment_schedule_flow" to true,
            "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
        )

        withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
            coEvery {
                healthcareTeamService.getHealthcareTeamByPerson(person.id)
            } returns healthcareTeam

            coEvery {
                appointmentScheduleService.existsBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.COMPLETED),
                        staffId = healthcareTeam.physicianStaffId
                    )
                )
            } returns false

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/check_first/$originalEventId") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }

        coVerifyOnce {
            healthcareTeamService.getHealthcareTeamByPerson(any())
            appointmentScheduleService.existsBy(any())
        }
    }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when exists appointment schedule completed before`() =
        runBlocking {
            val eventId = RangeUUID.generate()
            val eventChildrenId = RangeUUID.generate()
            val originalEventId = RangeUUID.generate()
            val expected = FirstAppointmentScheduleResponse()
            val featureFlagsMap = mapOf(
                "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
                "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
                "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
                "use_first_appointment_schedule_flow" to true,
                "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
            )

            withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
                coEvery {
                    healthcareTeamService.getHealthcareTeamByPerson(person.id)
                } returns healthcareTeam

                coEvery {
                    appointmentScheduleService.existsBy(
                        AppointmentScheduleFilter(
                            personId = person.id,
                            status = listOf(AppointmentScheduleStatus.COMPLETED),
                            staffId = healthcareTeam.physicianStaffId
                        )
                    )
                } returns true

                authenticatedAs(token, toTestPerson(person)) {
                    get("/appointment_schedules/check_first/$originalEventId") { response ->
                        assertThat(response).isOKWithData(expected)
                    }
                }
            }

            coVerifyOnce {
                healthcareTeamService.getHealthcareTeamByPerson(any())
                appointmentScheduleService.existsBy(any())
            }
        }


    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when getHealthcareTeamByPerson returns error`() =
        runBlocking {
            val eventId = RangeUUID.generate()
            val eventChildrenId = RangeUUID.generate()
            val originalEventId = RangeUUID.generate()
            val expected = FirstAppointmentScheduleResponse()
            val featureFlagsMap = mapOf(
                "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
                "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
                "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
                "use_first_appointment_schedule_flow" to true,
                "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
            )

            withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
                coEvery {
                    healthcareTeamService.getHealthcareTeamByPerson(person.id)
                } returns NotFoundException()

                authenticatedAs(token, toTestPerson(person)) {
                    get("/appointment_schedules/check_first/$originalEventId") { response ->
                        assertThat(response).isOKWithData(expected)
                    }
                }
            }

            coVerifyOnce {
                healthcareTeamService.getHealthcareTeamByPerson(any())
            }
        }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when person get returns error`() = runBlocking {
        val eventId = RangeUUID.generate()
        val eventChildrenId = RangeUUID.generate()
        val originalEventId = RangeUUID.generate()
        val expected = FirstAppointmentScheduleResponse()
        val featureFlagsMap = mapOf(
            "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
            "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
            "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
            "use_first_appointment_schedule_flow" to true,
            "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
        )

        withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
            coEvery {
                healthcareTeamService.getHealthcareTeamByPerson(person.id)
            } returns healthcareTeam

            coEvery {
                personService.get(person.id)
            } returns NotFoundException()

            coEvery {
                appointmentScheduleService.existsBy(
                    AppointmentScheduleFilter(
                        personId = person.id,
                        status = listOf(AppointmentScheduleStatus.COMPLETED),
                        staffId = healthcareTeam.physicianStaffId
                    )
                )
            } returns false

            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/check_first/$originalEventId") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }

        coVerifyOnce {
            healthcareTeamService.getHealthcareTeamByPerson(any())
            personService.get(any())
            appointmentScheduleService.existsBy(any())
        }
    }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when flow FF is false`() = runBlocking {
        val eventId = RangeUUID.generate()
        val eventChildrenId = RangeUUID.generate()
        val originalEventId = RangeUUID.generate()
        val expected = FirstAppointmentScheduleResponse()
        val featureFlagsMap = mapOf(
            "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
            "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
            "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
            "use_first_appointment_schedule_flow" to false,
            "allowed_event_id_in_first_appointment_flow" to listOf(originalEventId)
        )

        withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/check_first/${RangeUUID.generate()}") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `getFirstAppointmentScheduleEventIfAvailable returns null when flow event not allowed in flow`() = runBlocking {
        val eventId = RangeUUID.generate()
        val eventChildrenId = RangeUUID.generate()

        val expected = FirstAppointmentScheduleResponse()
        val featureFlagsMap = mapOf(
            "allowed_physician_id_in_first_appointment_flow" to listOf(healthcareTeam.physicianStaffId),
            "first_adult_digital_appointment_schedule_event_id" to eventId.toString(),
            "first_children_digital_appointment_schedule_event_id" to eventChildrenId.toString(),
            "use_first_appointment_schedule_flow" to true,
            "allowed_event_id_in_first_appointment_flow" to emptyList<UUID>()
        )

        withFeatureFlags(FeatureNamespace.SCHEDULE, featureFlagsMap) {
            authenticatedAs(token, toTestPerson(person)) {
                get("/appointment_schedules/check_first/${RangeUUID.generate()}") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `checkInAppointmentSchedule update appointment schedule check in status for confirmed`() = runBlocking {
        val appointmentScheduleCheckIn = TestModelFactory.buildAppointmentScheduleCheckIn()
        val confirmedAppointmentScheduleCheckIn =
            appointmentScheduleCheckIn.copy(status = AppointmentScheduleCheckInStatus.CONFIRMED)

        coEvery {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(appointmentScheduleCheckIn.appointmentScheduleId)
        } returns listOf(appointmentScheduleCheckIn)

        coEvery {
            appointmentScheduleCheckInService.update(confirmedAppointmentScheduleCheckIn)
        } returns confirmedAppointmentScheduleCheckIn

        authenticatedAs(token, toTestPerson(person)) {
            post("/appointment_schedules/${appointmentScheduleCheckIn.appointmentScheduleId}/check_in") { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            appointmentScheduleCheckInService.findByAppointmentScheduleId(any())
            appointmentScheduleCheckInService.update(any())
        }
    }

    @Test
    fun `#getAdvancedAccessAvailability get staff availability for advanced access referral task`() =
        mockLocalDate { now ->
            val token = person.id.toString()
            val appointmentScheduleEventTypeId = RangeUUID.generate()
            val healthPlanTaskId = RangeUUID.generate()
            val healthPlanTaskIdHashed = healthPlanTaskId.toString().toSha256()
            val providerUnitId = RangeUUID.generate()
            val dueDate = now.plusDays(2)
            val actionPlanTask = TestModelFactory.buildActionPlanTask(id = healthPlanTaskId, dueDate = dueDate)

            val slots = listOf(
                SlotForSpecificDuration(
                    startTime = LocalDateTime.now(),
                    endTime = LocalDateTime.now().plusMinutes(30),
                    durationInMinutes = 30,
                )
            )

            coEvery {
                actionPlanTaskService.getTasksByFilters(
                    ActionPlanTaskFilters(
                        personId = person.id,
                        types = listOf(ActionPlanTaskType.REFERRAL),
                        statuses = ActionPlanTaskFilters.TO_DO_STATUSES,
                    )
                )
            } returns listOf(actionPlanTask)

            coEvery {
                internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                    staffId = staff.id,
                    personId = person.id.toString(),
                    fromDate = now,
                    toDate = dueDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                    providerUnitId = providerUnitId.toString()
                )
            } returns StaffAvailabilityResponse(slots)

            val expectedResponse = StaffAvailabilityResponse(slots)
            authenticatedAs(token, toTestPerson(person)) {
                get(
                    "/staff_availability/${staff.id}/advanced_access?" +
                            "appointmentScheduleEventTypeId=${appointmentScheduleEventTypeId}" +
                            "&healthPlanTaskId=$healthPlanTaskIdHashed" +
                            "&providerUnitId=$providerUnitId"
                ) { response ->
                    val responseAsJson: StaffAvailabilityResponse = response.bodyAsJson()
                    assertThat(responseAsJson).isEqualTo(expectedResponse)
                    coVerifyOnce {
                        internalAppointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                            any(),
                            any(),
                            any(),
                            any(),
                            any(),
                            any()
                        )
                    }
                }
            }
        }

    private fun beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow: LocalDateTime) =
        localDateTimeNow.toSaoPauloTimeZone().toLocalDate().atBeginningOfTheMonth()

    private fun endOfTheMonthWithSaoPauloTimezone(localDateTimeNow: LocalDateTime) =
        localDateTimeNow.toSaoPauloTimeZone().toLocalDate().atEndOfTheMonth()


}
