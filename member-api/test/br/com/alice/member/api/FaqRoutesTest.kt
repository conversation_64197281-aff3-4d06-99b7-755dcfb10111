package br.com.alice.member.api

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.Origin
import br.com.alice.channel.models.SendMessageRequest
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.FeatureAccessResult
import br.com.alice.common.featureaccess.FeatureNavigation
import br.com.alice.common.featureaccess.NavigationRouting
import br.com.alice.common.featureaccess.features.FaqFeatureResource
import br.com.alice.common.featureaccess.features.FaqFeedbackFeatureResource
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.FaqContent
import br.com.alice.data.layer.models.FaqFeedback
import br.com.alice.data.layer.models.FaqGroup
import br.com.alice.data.layer.models.FaqGroupType
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.PrimaryAttentionType
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TierType
import br.com.alice.member.api.controllers.FaqController
import br.com.alice.member.api.models.FaqContentResponse
import br.com.alice.member.api.models.FaqFeedbackRequest
import br.com.alice.member.api.models.FaqFeedbackResponse
import br.com.alice.member.api.models.FaqFeedbackTextRequest
import br.com.alice.member.api.models.FaqGroupResponse
import br.com.alice.member.api.models.ListFaqGroupResponse
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.membership.client.FaqContentService
import br.com.alice.membership.client.FaqFeedbackService
import br.com.alice.membership.client.FaqGroupService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.slot
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class FaqRoutesTest : RoutesTestHelper() {

    private val faqGroupService: FaqGroupService = mockk()
    private val faqContentService: FaqContentService = mockk()
    private val faqFeedbackService: FaqFeedbackService = mockk()
    private val channelService: ChannelService = mockk()
    private val personService: PersonService = mockk()

    private val person = TestModelFactory.buildPerson().copy(productInfo =
        ProductInfo(
            brand = Brand.ALICE,
            primaryAttention = PrimaryAttentionType.ALICE,
            tier = TierType.TIER_1,
            coPayment = CoPaymentType.NONE,
            healthcareModelType = HealthcareModelType.V3,
            refund = RefundType.NONE
        ))
    private val token = person.id.toString()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { FaqController(
            faqGroupService,
            faqContentService,
            faqFeedbackService,
            channelService,
            personService,
        ) }
    }

    @Test
    fun `#listFaqGroups should list Faq Groups as expected when SuperApp FF is on`() {
        val member = TestModelFactory.buildMember(brand = Brand.ALICE)
        val faqGroup1 = TestModelFactory.buildRandom(FaqGroup::class)
        val faqGroup2 = TestModelFactory.buildRandom(FaqGroup::class)
        val faqGroup3 = TestModelFactory.buildRandom(FaqGroup::class)

        val faqGroups = listOf(faqGroup1, faqGroup2, faqGroup3)

        val faqContent1 = TestModelFactory.buildRandom(FaqContent::class).copy(groupIds = listOf(faqGroup1.id))
        val faqContent2 = TestModelFactory.buildRandom(FaqContent::class).copy(groupIds = listOf(faqGroup2.id))

        val faqContents = listOf(faqContent1, faqContent2)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { faqGroupService.findByType(FaqGroupType.TIER_1) } returns faqGroups.success()
        coEvery { faqContentService.findActive() } returns faqContents.success()

        mockkObject(FeatureAccess) {
            val featureAccessResult = FeatureAccessResult(
                feature = Feature.FAQ,
                response = FaqFeatureResource(
                    faqContents = faqContents.subList(0, 1),
                    faqGroups = faqGroups.subList(0, 1),
                ),
                featureNavigation = FeatureNavigation(
                    routing = NavigationRouting.FAQ,
                ),
            )

            coEvery {
                FeatureAccess.filter(
                    feature = Feature.FAQ,
                    resource = FaqFeatureResource(
                        faqContents = faqContents,
                        faqGroups = faqGroups,
                    )
                )
            } returns featureAccessResult

            val expectedResponse = ListFaqGroupResponse(
                groupResponse = listOf(
                    FaqGroupResponse(faqGroup1.title, faqGroup1.description, faqGroup1.featured,
                        listOf(faqContent1.convertTo(FaqContentResponse::class))
                    ),
                )
            )

            authenticatedAs(token, toTestPerson(person)) {
                get("/faq") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }

                coVerifyOnce {
                    personService.get(any())
                    faqGroupService.findByType(any())
                    faqContentService.findActive()
                }
            }
        }
    }

    @Test
    fun `#listFaqGroups should list Faq Groups as expected when SuperApp FF is off`() {
        val member = TestModelFactory.buildMember(brand = Brand.DUQUESA)

        val faqGroup1 = TestModelFactory.buildRandom(FaqGroup::class)
        val faqGroup2 = TestModelFactory.buildRandom(FaqGroup::class)
        val faqGroup3 = TestModelFactory.buildRandom(FaqGroup::class)

        val faqGroups = listOf(faqGroup1, faqGroup2, faqGroup3)

        val faqContent1 = TestModelFactory.buildRandom(FaqContent::class).copy(groupIds = listOf(faqGroup1.id))
        val faqContent2 = TestModelFactory.buildRandom(FaqContent::class).copy(groupIds = listOf(faqGroup2.id))

        val faqContents = listOf(faqContent1, faqContent2)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { faqGroupService.findByType(FaqGroupType.TIER_1) } returns faqGroups.success()
        coEvery { faqContentService.findActive() } returns faqContents.success()

        val expectedResponse = ListFaqGroupResponse(
            groupResponse = listOf(
                FaqGroupResponse(faqGroup1.title, faqGroup1.description, faqGroup1.featured,
                    listOf(faqContent1.convertTo(FaqContentResponse::class))
                ),
                FaqGroupResponse(faqGroup2.title, faqGroup2.description, faqGroup2.featured,
                    listOf(faqContent2.convertTo(FaqContentResponse::class))
                )
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/faq") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#saveFaqFeedback should call service and add feedback`() {
        val faqFeedback = TestModelFactory.buildRandom(FaqFeedback::class).copy(personId = person.id)
        val request = FaqFeedbackRequest(faqFeedback.faqContentId, faqFeedback.useful, false)

        coEvery { personService.get(person.id) } returns person.success()

        coEvery { faqFeedbackService.add(match { it.faqContentId == request.faqContentId
                && it.useful == request.useful && it.personId == person.id
        }) } returns faqFeedback.success()

        val mockedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.EMERGENCY_CARE
            ),
            response = FaqFeedbackFeatureResource(
                useful = request.useful,
                feedbackMessage = "Que pena! Mas ainda podemos te ajudar, basta mandar um <NAME_EMAIL>"
            )
        )

        val expectedResponse = FaqFeedbackResponse(
            faqContentId = faqFeedback.faqContentId,
            useful = faqFeedback.useful,
            showUserInput = true,
            id = faqFeedback.id,
            feedback = faqFeedback.feedback,
            feedbackMessage = mockedResult.response?.feedbackMessage,
        )

        mockkObject(FeatureAccess) {
            coEvery {
                FeatureAccess.filter(
                    feature = Feature.FAQ_FEEDBACK,
                    resource = FaqFeedbackFeatureResource(
                        useful = request.useful
                    ),
                )
            } returns mockedResult

            authenticatedAs(token, toTestPerson(person)) {
                post("/faq", request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#saveFaqFeedback should call service and add feedback for person, but cannot show user input after it`() {
        val faqFeedback = TestModelFactory.buildRandom(FaqFeedback::class).copy(personId = person.id)
        val request = FaqFeedbackRequest(faqFeedback.faqContentId, faqFeedback.useful, false)
        val member = TestModelFactory.buildMember(brand = Brand.ALICE)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { faqFeedbackService.add(match { it.faqContentId == request.faqContentId
                && it.useful == request.useful && it.personId == person.id
        }) } returns faqFeedback.success()

        val mockedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.NONE
            ),
            response = FaqFeedbackFeatureResource(
                useful = request.useful,
                feedbackMessage = "Que pena! Mas ainda podemos te ajudar, basta mandar um <NAME_EMAIL>"
            )
        )

        val expectedResponse = FaqFeedbackResponse(
            faqContentId = faqFeedback.faqContentId,
            useful = faqFeedback.useful,
            showUserInput = false,
            id = faqFeedback.id,
            feedback = faqFeedback.feedback,
            feedbackMessage = mockedResult.response?.feedbackMessage,
        )

        mockkObject(FeatureAccess) {
            coEvery {
                FeatureAccess.filter(
                    feature = Feature.FAQ_FEEDBACK,
                    resource = FaqFeedbackFeatureResource(
                        useful = request.useful
                    ),
                )
            } returns mockedResult

            authenticatedAs(token, toTestPerson(person)) {
                post("/faq", request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#saveFaqFeedback should call service and add feedback anonymously`() {
        val faqFeedback = TestModelFactory.buildRandom(FaqFeedback::class).copy(personId = null)
        val request = FaqFeedbackRequest(faqFeedback.faqContentId, faqFeedback.useful, true)
        val member = TestModelFactory.buildMember(brand = Brand.ALICE)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { faqFeedbackService.add(match { it.faqContentId == request.faqContentId
                && it.useful == request.useful && it.personId == null
        }) } returns faqFeedback.success()

        val mockedResult = FeatureAccessResult(
            feature = Feature.FAQ_FEEDBACK,
            featureNavigation = FeatureNavigation(
                routing = NavigationRouting.EMERGENCY_CARE
            ),
            response = FaqFeedbackFeatureResource(
                useful = request.useful,
                feedbackMessage = "Que pena! Mas ainda podemos te ajudar, basta mandar um <NAME_EMAIL>"
            )
        )

        val expectedResponse = FaqFeedbackResponse(
            faqContentId = faqFeedback.faqContentId,
            useful = faqFeedback.useful,
            showUserInput = true,
            id = faqFeedback.id,
            feedback = faqFeedback.feedback,
            feedbackMessage = mockedResult.response?.feedbackMessage,
        )

        mockkObject(FeatureAccess) {
            coEvery {
                FeatureAccess.filter(
                    feature = Feature.FAQ_FEEDBACK,
                    resource = FaqFeedbackFeatureResource(
                        useful = request.useful
                    ),
                )
            } returns mockedResult

            authenticatedAs(token, toTestPerson(person)) {
                post("/faq", request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#saveFaqFeedbackText should call service and update feedback`() {
        val request = FaqFeedbackTextRequest("texto do feedback")
        val faqContent = TestModelFactory.buildRandom(FaqContent::class)

        val feedback = TestModelFactory.buildRandom(FaqFeedback::class).copy(
            useful = false,
            feedback = request.feedback,
            faqContentId = faqContent.id,
            personId = person.id
        )

        val slot = slot<FaqFeedback>()
        val chatMessage = slot<SendMessageRequest>()
        val channelId = RangeUUID.generate().toString()

        coEvery { faqContentService.get(faqContent.id) } returns faqContent.success()
        coEvery { channelService.sendMessage(person.id, capture(chatMessage)) } returns channelId.success()
        coEvery { faqFeedbackService.add(capture(slot)) } returns feedback.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/faq/${faqContent.id}/feedbacks", request) { response ->
                val actualChatMessage = chatMessage.captured

                val expectedResponse = NavigationResponse(
                    mobileRoute = MobileRouting.CHANNEL,
                    properties = mapOf("channel_id" to channelId)
                )

                val expectedMessage = SendMessageRequest(
                    chatName = faqContent.title,
                    type = MessageType.TEXT,
                    content = request.feedback,
                    origin = Origin.HELP_CENTER,
                    appVersion = "ANDROID - 1.0.0"
                )

                assertThat(actualChatMessage).isEqualTo(expectedMessage)
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

}
