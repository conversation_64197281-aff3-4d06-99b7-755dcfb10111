package br.com.alice.api.event.routes

import br.com.alice.api.event.controllers.ChannelsCloudFunctionController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.channelsRoutes() {
    authenticate {
        val channelsCloudFunction by inject<ChannelsCloudFunctionController>()

        route("/cloud_function/") {
            route("/channel/{channelId}") {
                post("/") {
                    coHandler("channelId", channelsCloudFunction::channelEvent)
                }
                post("/message/{messageId}") {
                    coHandler("channelId", "messageId", channelsCloudFunction::messageEvent)
                }
                post("/private_message/{messageId}") {
                    coHandler("channelId", "messageId", channelsCloudFunction::privateMessageEvent)
                }
            }
        }
    }
}
