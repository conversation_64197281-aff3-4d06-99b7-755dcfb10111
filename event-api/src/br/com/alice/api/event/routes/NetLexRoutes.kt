package br.com.alice.api.event.routes

import br.com.alice.api.event.controllers.NetLexAuthController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post

fun Route.netLexRoutes() {
    val netLexTokenAuthController by inject<NetLexAuthController>()
    post("/auth/netlex") {
        coHandler(netLexTokenAuthController::authorize)
    }

    authenticate("auth-netlex") {
        get("/auth/test") {
            call.respond(HttpStatusCode.OK, mapOf("message" to "Autenticação OK!"))
        }
    }
}
