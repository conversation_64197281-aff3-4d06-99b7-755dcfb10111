package br.com.alice.api.event

import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.utils.io.core.toByteArray

object ServiceConfig: BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {
    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    object NetLex {
        fun clientId() =
            config.property("${environment().value.lowercase()}.netLexAuth.clientId").getString()

        fun clientSecret() =
            config.property("${environment().value.lowercase()}.netLexAuth.clientSecret").getString()
    }

    fun secretKey(): ByteArray {
        var defaultKey = config.property("${environment().value.lowercase()}.authSecretKey").getString()
        while (defaultKey.length < 48) {
            defaultKey += defaultKey
        }
        return defaultKey.toByteArray()
    }

    val ALICE_SECRET_KEY = config("aliceSecretKey")
}
