package br.com.alice.api.event

import br.com.alice.api.event.controllers.NetLexAuthController
import br.com.alice.api.event.controllers.ChannelsCloudFunctionController
import br.com.alice.api.event.providers.aliceAuth
import br.com.alice.api.event.providers.netLexAuth
import br.com.alice.api.event.routes.apiRoutes
import br.com.alice.api.event.services.NetLexAuthService
import br.com.alice.common.application.plugin.RateLimitPlugin
import br.com.alice.common.application.setupBffApi
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import com.google.gson.FieldNamingPolicy
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.serialization.gson.gson
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module
import java.time.Duration
import org.koin.ktor.ext.get

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        KafkaProducerModule,
        FeatureConfigDomainClientModule,

        module {
            // Configuration
            single { config }
            single {
                DefaultHttpClient({ install(ContentNegotiation) { gson {} } }, timeoutInMillis = 15_000)
            }

            // Controllers
            single { ChannelsCloudFunctionController(get()) }
            single { NetLexAuthController(get()) }

            // Service
            single { NetLexAuthService() }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules
) {
    setupBffApi(
        dependencyInjectionModules,
        fieldNamingPolicy = FieldNamingPolicy.IDENTITY,
    ) {
        install(CORS) {
            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Delete)
            allowMethod(HttpMethod.Options)
            allowHeader(HttpHeaders.Authorization)
            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.ContentRange)
            allowHeadersPrefixed("X-Datadog-")
            allowHeader("traceparent")
            exposeHeader(HttpHeaders.ContentRange)
            // TODO: be more restrict on hosts
            anyHost()
            maxAgeInSeconds = Duration.ofDays(7).seconds
        }

        install(Authentication) {
            aliceAuth()
            netLexAuth("auth-netlex", <EMAIL>())
        }

        routing {
            apiRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.CHANNELS)
        install(RateLimitPlugin)
    }
}
