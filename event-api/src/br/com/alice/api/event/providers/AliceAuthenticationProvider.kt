package br.com.alice.api.event.providers

import br.com.alice.api.event.ServiceConfig
import br.com.alice.api.event.common.SystemAccessTokenPrincipal
import br.com.alice.api.event.common.respondUnauthorized
import io.ktor.server.auth.AuthenticationConfig
import io.ktor.server.auth.AuthenticationProvider

class AliceAuthenticationProvider(
    configuration: Config,
    private val secretKey: String = ServiceConfig.ALICE_SECRET_KEY
) : AuthenticationProvider(configuration) {
    class Configuration internal constructor(name: String?) : Config(name)


    override suspend fun onAuthenticate(context: io.ktor.server.auth.AuthenticationContext) =
        context.call.request.headers["Authorization"]
            ?.replace("Bearer ", "")
            ?.takeIf { it == secretKey }
            ?.let { context.principal(SystemAccessTokenPrincipal(it)) }
            ?: respondUnauthorized(context)
}

fun AuthenticationConfig.aliceAuth(
    name: String? = null,
) {
    register(AliceAuthenticationProvider(AliceAuthenticationProvider.Configuration(name)))
}
