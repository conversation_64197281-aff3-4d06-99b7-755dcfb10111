package br.com.alice.api.event.providers

import br.com.alice.api.event.common.SystemAccessTokenPrincipal
import br.com.alice.api.event.services.NetLexAuthService
import br.com.alice.api.event.common.respondUnauthorized
import io.ktor.server.auth.AuthenticationConfig
import io.ktor.server.auth.AuthenticationContext
import io.ktor.server.auth.AuthenticationProvider

class NetLexAuthenticationProvider internal constructor(
    configuration: Config,
    private val authService: NetLexAuthService
) : AuthenticationProvider(configuration) {

    class Configuration internal constructor(name: String?) : Config(name)

    override suspend fun onAuthenticate(context: AuthenticationContext) =
        context.call.request.headers["Authorization"]
            ?.replace("Bearer ", "")
            ?.takeIf { authService.verifyToken(it) }
            ?.let { context.principal(SystemAccessTokenPrincipal(it)) }
            ?: respondUnauthorized(context)
}

fun AuthenticationConfig.netLexAuth(
    name: String? = null,
    authService: NetLexAuthService
) {
    register(NetLexAuthenticationProvider(NetLexAuthenticationProvider.Configuration(name), authService))
}
