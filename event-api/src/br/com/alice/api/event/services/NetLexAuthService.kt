package br.com.alice.api.event.services

import br.com.alice.api.event.ServiceConfig
import br.com.alice.common.core.extensions.toDate
import br.com.alice.common.logging.logger
import io.jsonwebtoken.JwtException
import io.jsonwebtoken.JwtParser
import java.time.LocalDateTime
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import io.jsonwebtoken.io.Encoders
import io.jsonwebtoken.security.Keys
import javax.crypto.SecretKey

const val TOKEN_ISSUER = "alice"

class NetLexAuthService {
    private lateinit var parser: JwtParser
    private lateinit var key: SecretKey

    fun authorize(clientId: String, clientSecret: String): String? =
        if (ServiceConfig.NetLex.clientId() == clientId &&
            ServiceConfig.NetLex.clientSecret() == clientSecret
        ) {
            logger.info("Authentication successful", "clientId" to clientId)
            generateToken(clientId)
        } else {
            logger.warn("Authentication failed, invalid credentials", "clientId" to clientId)
            null
        }


    fun verifyToken(jwtToken: String) =
        try {
            getJwtParser().parseClaimsJws(jwtToken)
            true
        } catch (e: JwtException) {
            false
        }

    private fun generateToken(id: String): String {
        val now = LocalDateTime.now()

        return Jwts.builder()
            .setId(id)
            .setIssuer(TOKEN_ISSUER)
            .setIssuedAt(now.toDate())
            .setExpiration(now.plusHours(1).toDate())
            .signWith(getKey(), SignatureAlgorithm.HS256)
            .compact()
    }

    private fun getKey(): SecretKey {
        if (!this::key.isInitialized) {
            key = Keys.hmacShaKeyFor(
                Encoders.BASE64URL.encode(ServiceConfig.secretKey()).toByteArray()
            )
        }

        return key
    }

    private fun getJwtParser(): JwtParser {
        if (!this::parser.isInitialized) {
            parser = Jwts.parserBuilder().setSigningKey(getKey()).build()
        }

        return parser
    }
}
