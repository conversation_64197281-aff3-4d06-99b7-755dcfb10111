package br.com.alice.api.event.controllers

import br.com.alice.api.event.services.NetLexAuthService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import io.ktor.http.HttpStatusCode

class NetLexAuthController(
    private val authService: NetLexAuthService
) : Controller() {
    fun authorize(request: AuthRequest): Response =
        authService.authorize(request.clientId, request.clientSecret)
            ?.let { Response(HttpStatusCode.OK, AuthResponse(it)) }
            ?: Response(HttpStatusCode.Unauthorized)
}

data class AuthRequest(
    val clientId: String,
    val clientSecret: String
)

data class AuthResponse(
    val token: String
)
