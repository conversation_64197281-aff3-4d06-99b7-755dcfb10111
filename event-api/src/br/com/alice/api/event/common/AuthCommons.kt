package br.com.alice.api.event.common

import io.ktor.server.auth.AuthenticationContext
import io.ktor.server.auth.AuthenticationFailedCause
import io.ktor.server.auth.Principal
import io.ktor.server.auth.UnauthorizedResponse
import io.ktor.server.response.respond

data class SystemAccessTokenPrincipal(val token: String) : Principal

fun respondUnauthorized(context: AuthenticationContext) {
    context.challenge("EventApi", AuthenticationFailedCause.NoCredentials) { challenge, call ->
        call.respond(UnauthorizedResponse())
        challenge.complete()
    }
}
