ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [ br.com.alice.api.event.ApplicationKt.module ]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
    aliceSecretKey = "api_key"
    netLexAuth {
        clientId = "clientId"
        clientSecret = "clientSecret"
    }
    authSecretKey = "secretKey"
}

test {
    aliceSecretKey = "api_key"
    netLexAuth {
        clientId = "clientId"
        clientSecret = "clientSecret"
    }
    authSecretKey = "secretKey"
}

production {
    aliceSecretKey = ""
    aliceSecretKey = ${?ALICE_SECRET_KEY}
    netLexAuth {
        clientId = "netLexClientId"
        clientId = ${?NETLEX_CLIENT_ID}
        clientSecret = "netLexAuthClientSecret"
        clientSecret = ${?NETLEX_CLIENT_SECRET}
    }
    authSecretKey = "authSecretKey"
    authSecretKey = ${?EVENT_API_SECRET_KEY}
}
