package br.com.alice.api.event.providers

import br.com.alice.api.event.common.SystemAccessTokenPrincipal
import br.com.alice.api.event.services.NetLexAuthService
import io.ktor.http.Headers
import io.ktor.http.headersOf
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.AuthenticationContext
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class NetLexAuthenticationProviderTest {
    private val authService: NetLexAuthService = mockk()
    private val config = NetLexAuthenticationProvider.Configuration("test")

    @Test
    fun `should authenticate and set principal when token is valid`() = runBlocking {
        val provider = NetLexAuthenticationProvider(config, authService)
        val context: AuthenticationContext = mockk(relaxed = true)
        val call: ApplicationCall = mockk(relaxed = true)
        val headers = headersOf("Authorization", listOf("Bearer valid-token"))

        every { context.call } returns call
        every { call.request.headers } returns Headers.build { appendAll(headers) }
        coEvery { authService.verifyToken("valid-token") } returns true

        provider.onAuthenticate(context)

        verify(exactly = 1) { context.principal(SystemAccessTokenPrincipal("valid-token")) }
    }


    @Test
    fun `should respond unauthorized when token is invalid`() = runBlocking {
        val provider = NetLexAuthenticationProvider(config, authService)
        val context: AuthenticationContext = mockk(relaxed = true)
        val call: ApplicationCall = mockk(relaxed = true)
        val headers = headersOf("Authorization", listOf("Bearer invalid-token"))

        every { context.call } returns call
        every { call.request.headers } returns Headers.build { appendAll(headers) }
        coEvery { authService.verifyToken("invalid-token") } returns false

        provider.onAuthenticate(context)

        verify(exactly = 0) { context.principal(any()) }
    }
}
