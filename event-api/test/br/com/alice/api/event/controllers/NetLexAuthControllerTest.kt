package br.com.alice.api.event.controllers

import br.com.alice.api.event.ControllerTestHelper
import br.com.alice.api.event.services.NetLexAuthService
import com.google.gson.Gson
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.mockk.coVerify
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class NetLexAuthControllerTest : ControllerTestHelper() {
    private val authService: NetLexAuthService = mockk()
    private val authController = NetLexAuthController(authService)

    private val clientId = "ba74859d-964b-4acb-9a62-ec5c83c48555"
    private val clientSecret = "d2c7db28-d57c-4060-b055-2e4dc52fee13"

    private val request = AuthRequest(
        clientId = clientId,
        clientSecret = clientSecret
    )
    private val bodyString = Gson().toJson(request)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { authController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(authService)

    @Test
    fun `#authorize should return 200 with token if valid credentials`() {
        val expected = AuthResponse(token = token)

        coEvery { authService.authorize(clientId, clientSecret) } returns token

        post("/auth/netlex", body = bodyString) { response ->
            assertEquals(HttpStatusCode.OK, response.status)
            val json = response.bodyAsText()
            val parsed = Gson().fromJson(json, AuthResponse::class.java)
            assertEquals(expected, parsed)
        }

        coVerify(exactly = 1) { authService.authorize(any(), any()) }
    }

    @Test
    fun `#authorize should return 401 if not authorized`() {
        coEvery { authService.authorize(clientId, clientSecret) } returns null

        post("/auth/netlex", body = bodyString) { response ->
            assertEquals(HttpStatusCode.Unauthorized, response.status)
        }

        coVerify(exactly = 1) { authService.authorize(any(), any()) }
    }
}
