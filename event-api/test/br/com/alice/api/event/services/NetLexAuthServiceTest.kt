package br.com.alice.api.event.services

import br.com.alice.api.event.ServiceConfig
import br.com.alice.common.RangeUUID
import io.jsonwebtoken.JwtBuilder
import io.jsonwebtoken.JwtException
import io.jsonwebtoken.JwtParser
import io.jsonwebtoken.JwtParserBuilder
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import io.jsonwebtoken.io.Encoders
import io.jsonwebtoken.security.Keys
import io.mockk.clearAllMocks
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import java.security.Key
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.Date
import kotlin.test.BeforeTest
import kotlin.test.assertEquals

class NetLexAuthServiceTest {
    private val authService = NetLexAuthService()

    private val clientId = "clientId"
    private val clientSecret = "clientSecret"
    private val token = "token"

    companion object {
        @JvmStatic
        @BeforeAll
        fun setup() {
            mockkStatic(LocalDateTime::class)
            mockkStatic(Jwts::class)
        }

        @JvmStatic
        @AfterAll
        fun tearDown() {
            unmockkStatic(LocalDateTime::class)
            unmockkStatic(Jwts::class)
        }
    }

    @BeforeTest
    fun setupEach() = clearAllMocks()

    @Test
    fun `#authorize returns token if valid credentials`(): Unit = runBlocking {
        val now = LocalDateTime.now()
        val issuedAt = Date.from(now.atZone(ZoneOffset.systemDefault()).toInstant())
        val expiration = Date.from(now.plusHours(1).atZone(ZoneOffset.systemDefault()).toInstant())

        val jwtBuilder: JwtBuilder = mockk()

        every { LocalDateTime.now() } returns now
        every { Jwts.builder() } returns jwtBuilder

        every { jwtBuilder.setId(clientId) } returns jwtBuilder
        every { jwtBuilder.setIssuedAt(issuedAt) } returns jwtBuilder
        every { jwtBuilder.setIssuer(TOKEN_ISSUER) } returns jwtBuilder
        every { jwtBuilder.setExpiration(expiration) } returns jwtBuilder
        every { jwtBuilder.signWith(any(), SignatureAlgorithm.HS256) } returns jwtBuilder
        every { jwtBuilder.compact() } returns token

        val result = authService.authorize(clientId, clientSecret)
        assertEquals(result, token)

        coVerify(exactly = 1) { Jwts.builder() }
        coVerify(exactly = 1) { jwtBuilder.setId(clientId) }
        coVerify(exactly = 1) { jwtBuilder.setIssuedAt(issuedAt) }
        coVerify(exactly = 1) { jwtBuilder.setIssuer(TOKEN_ISSUER) }
        coVerify(exactly = 1) { jwtBuilder.setExpiration(expiration) }
        coVerify(exactly = 1) { jwtBuilder.signWith(any<Key>(), SignatureAlgorithm.HS256) }
        coVerify(exactly = 1) { jwtBuilder.compact() }
    }

    @Test
    fun `#authorize returns null when provider access have other client secret`(): Unit = runBlocking {
        val wrongClientSecret = RangeUUID.generate().toString()

        val result = authService.authorize(clientId, wrongClientSecret)
        assertEquals(result, null)
    }


    @Test
    fun `#verifyToken returns true when token is valid`() {
        val key = Keys.hmacShaKeyFor(Encoders.BASE64URL.encode(ServiceConfig.secretKey()).toByteArray())

        val jwtParserBuilder: JwtParserBuilder = mockk()
        val jwtParser: JwtParser = mockk()

        every { Jwts.parserBuilder() } returns jwtParserBuilder
        every { jwtParserBuilder.setSigningKey(key) } returns jwtParserBuilder
        every { jwtParserBuilder.build() } returns jwtParser
        every { jwtParser.parseClaimsJws(token) } returns mockk()

        val result = authService.verifyToken(token)
        assertEquals(result, true)

        coVerify(exactly = 1) { Jwts.parserBuilder() }
        coVerify(exactly = 1) { jwtParserBuilder.setSigningKey(any<Key>()) }
        coVerify(exactly = 1) { jwtParserBuilder.build() }
        coVerify(exactly = 1) { jwtParser.parseClaimsJws(any()) }
    }

    @Test
    fun `#verifyToken returns false when token is invalid`() {
        val key = Keys.hmacShaKeyFor(Encoders.BASE64URL.encode(ServiceConfig.secretKey()).toByteArray())

        val jwtParserBuilder: JwtParserBuilder = mockk()
        val jwtParser: JwtParser = mockk()

        every { Jwts.parserBuilder() } returns jwtParserBuilder
        every { jwtParserBuilder.setSigningKey(key) } returns jwtParserBuilder
        every { jwtParserBuilder.build() } returns jwtParser
        every { jwtParser.parseClaimsJws(token) } throws JwtException("error")

        val result = authService.verifyToken(token)
        assertEquals(result, false)

        coVerify(exactly = 1) { Jwts.parserBuilder() }
        coVerify(exactly = 1) { jwtParserBuilder.setSigningKey(any<Key>()) }
        coVerify(exactly = 1) { jwtParserBuilder.build() }
        coVerify(exactly = 1) { jwtParser.parseClaimsJws(any()) }
    }

    @Test
    fun `#verifyToken initialize jwtParse only once`() {
        val key = Keys.hmacShaKeyFor(Encoders.BASE64URL.encode(ServiceConfig.secretKey()).toByteArray())

        val jwtParserBuilder: JwtParserBuilder = mockk()
        val jwtParser: JwtParser = mockk()

        every { Jwts.parserBuilder() } returns jwtParserBuilder
        every { jwtParserBuilder.setSigningKey(key) } returns jwtParserBuilder
        every { jwtParserBuilder.build() } returns jwtParser
        every { jwtParser.parseClaimsJws(token) } returns mockk()

        val result1 = authService.verifyToken(token)
        assertEquals(result1, true)

        val result2 = authService.verifyToken(token)
        assertEquals(result2, true)

        coVerify(exactly = 1) { Jwts.parserBuilder() }
        coVerify(exactly = 1) { jwtParserBuilder.setSigningKey(any<Key>()) }
        coVerify(exactly = 1) { jwtParserBuilder.build() }
        coVerify(exactly = 2) { jwtParser.parseClaimsJws(any()) }
    }
}
