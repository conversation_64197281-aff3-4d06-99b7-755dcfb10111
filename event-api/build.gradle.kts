plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.event-api"
version = aliceEventApiVersion
val jwtVersion = "0.11.2"

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:event-api")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation("br.com.alice.alice-common:common:0.0.4")
    implementation("br.com.alice.alice-common-core:common-core:0.0.4")
    implementation("br.com.alice.alice-common-service:common-service:0.0.4")
    implementation("br.com.alice.alice-common-logging:common-logging:0.0.4")
    implementation("br.com.alice.alice-common-kafka:common-kafka:0.0.4")
    implementation("br.com.alice.feature-config-domain-client:feature-config-domain-client:0.0.4")
    implementation("br.com.alice.data.packs.feature-config-domain-service:feature-config-domain-service-data-package:0.0.1")
    implementation("io.ktor:ktor-server-cors:$ktor2Version")

    implementation("io.jsonwebtoken:jjwt-api:$jwtVersion")
    implementation("io.jsonwebtoken:jjwt-impl:$jwtVersion")
    implementation("io.jsonwebtoken:jjwt-gson:$jwtVersion")

    ktor2Dependencies()
    test2Dependencies()
}
