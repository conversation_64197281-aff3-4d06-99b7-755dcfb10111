package br.com.alice.membership.client

import br.com.alice.common.Brand
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.FaqContent
import br.com.alice.data.layer.models.FaqGroup
import br.com.alice.data.layer.models.FaqGroupType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FaqGroupService: Service,
    Getter<FaqGroup>,
    Updater<FaqGroup>,
    Adder<FaqGroup>,
    Finder<FaqGroupService.FieldOptions, FaqGroupService.OrderingOptions, FaqGroup>,
    Counter<FaqGroupService.FieldOptions, FaqGroupService.OrderingOptions, FaqGroup> {

    override val namespace get() = "membership"
    override val serviceName get() = "faq_group"

    class Active : Field.BooleanField(FaqContent::active)

    class IdField: Field.UUIDField(FaqContent::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Title: Field.TextField(FaqContent::title) {
        fun eq(value: String) = Predicate.eq(this, value)
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class CreatedAt: Field.DateTimeField(FaqContent::createdAt)

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    class FieldOptions {
        val title = Title()
        val active = Active()
        val id = IdField()
    }

    class OrderingOptions {
        val title = Title()
        val createdAt = CreatedAt()
    }

    override suspend fun get(id: UUID): Result<FaqGroup, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<FaqGroup>, Throwable>

    override suspend fun countByQuery(query: Query): Result<Int, Throwable>

    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>

    override suspend fun update(model: FaqGroup): Result<FaqGroup, Throwable>

    override suspend fun add(model: FaqGroup): Result<FaqGroup, Throwable>

    suspend fun findActive(brand: Brand): Result<List<FaqGroup>, Throwable>

    suspend fun findByType(type: FaqGroupType): Result<List<FaqGroup>, Throwable>

}
