package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.AddBeneficiaryBatchErrorsResponse
import br.com.alice.bff.business.models.v2.AddBeneficiaryBatchResponse
import br.com.alice.bff.business.models.v2.AddBeneficiaryBatchSuccessResponse
import br.com.alice.bff.business.models.v2.BeneficiaryBatchRequest
import br.com.alice.bff.business.models.v2.BeneficiaryBatchValidationErrorItemResponse
import br.com.alice.bff.business.models.v2.BeneficiaryBatchValidationErrorResponse
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import java.util.UUID

fun BeneficiaryBatchRequest.toHrBeneficiaryBatchTransport() = BeneficiaryBatchTransport(
    uploadId = this.uploadId,
    items = this.items.map {
        BeneficiaryBatchItemTransport(
            index = it.index,
            nationalId = it.nationalId,
            fullName = it.fullName,
            cnpj = it.cnpj,
            subContractTitle = it.subContractTitle,
            sex = it.sex,
            dateOfBirth = it.dateOfBirth,
            mothersName = it.mothersName,
            email = it.email,
            phoneNumber = it.phoneNumber,
            addressPostalCode = it.addressPostalCode,
            addressNumber = it.addressNumber,
            addressComplement = it.addressComplement,
            productTitle = it.productTitle,
            activatedAt = it.activatedAt,
            beneficiaryContractType = it.beneficiaryContractType,
            hiredAt = it.hiredAt,
            parentNationalId = it.parentNationalId,
            parentBeneficiaryRelationType = it.parentBeneficiaryRelationType,
            relationExceeds30Days = it.relationExceeds30Days,
            ownership = it.ownership,
        )
    }
)

fun BeneficiaryBatchValidation.toAddBeneficiaryBatchResponse(uploadId: UUID) = AddBeneficiaryBatchResponse(
    uploadId = uploadId,
    success = AddBeneficiaryBatchSuccessResponse(
        count = this.success.count(),
        items = this.success
    ),
    errors = AddBeneficiaryBatchErrorsResponse(
        count = this.errors.count(),
        items = this.errors.map { error ->
            BeneficiaryBatchValidationErrorResponse(
                index = error.index,
                error = error.error.map { item ->
                    BeneficiaryBatchValidationErrorItemResponse(
                        field = item.field,
                        message = item.message,
                    )
                }
            )
        }
    )
)
