package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.BeneficiaryBatchItemRequest
import br.com.alice.bff.business.models.v2.BeneficiaryBatchRequest
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import kotlinx.coroutines.runBlocking
import java.util.UUID
import kotlin.test.Test

class BeneficiaryBatchConverterTest {
    private val request = BeneficiaryBatchRequest(
        uploadId = UUID.randomUUID(),
        items = listOf(
            BeneficiaryBatchItemRequest(
                index = 1,
                nationalId = "***********",
                fullName = "Calango Zokas",
                cnpj = "12345678000195",
                subContractTitle = "Matriz",
                phoneNumber = "(11) 91234-5678",
                mothersName = "Jane Doe",
                email = "<EMAIL>",
                addressNumber = "222",
                addressComplement = null,
                productTitle = "Plano de Saúde Bonzao",
                activatedAt = "2023-01-01T00:00:00Z",
                beneficiaryContractType = "CLT",
                hiredAt = "2023-01-01T00:00:00Z",
                parentNationalId = null,
                parentBeneficiaryRelationType = null,
                relationExceeds30Days = "nao",
                ownership = "nao",
            )
        )
    )
    private val transport = BeneficiaryBatchTransport(
        uploadId = request.uploadId,
        items = request.items.map {
            BeneficiaryBatchItemTransport(
                index = it.index,
                nationalId = it.nationalId,
                fullName = it.fullName,
                cnpj = it.cnpj,
                subContractTitle = it.subContractTitle,
                phoneNumber = it.phoneNumber,
                mothersName = it.mothersName,
                email = it.email,
                addressNumber = it.addressNumber,
                addressComplement = it.addressComplement,
                productTitle = it.productTitle,
                activatedAt = it.activatedAt,
                beneficiaryContractType = it.beneficiaryContractType,
                hiredAt = it.hiredAt,
                parentNationalId = it.parentNationalId,
                parentBeneficiaryRelationType = it.parentBeneficiaryRelationType,
                relationExceeds30Days = it.relationExceeds30Days,
                ownership = it.ownership,
            )
        }
    )
    private val validation = BeneficiaryBatchValidation(
        success = listOf(1),
        errors = listOf(
            BeneficiaryBatchValidationError(
                index = 1,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "nationalId",
                        message = "Invalid national ID"
                    )
                )
            )
        )
    )

    @Test
    fun `#toHrBeneficiaryBatchTransport should return correct response`() = runBlocking {
        val response = request.toHrBeneficiaryBatchTransport()

        assert(response.items.isNotEmpty())
        assert(response.items.size == transport.items.size)

        val itemFirst = response.items.first()
        val transportFirst = transport.items.first()

        assert(itemFirst.index == transportFirst.index)
        assert(itemFirst.nationalId == transportFirst.nationalId)
        assert(itemFirst.fullName == transportFirst.fullName)
        assert(itemFirst.cnpj == transportFirst.cnpj)
        assert(itemFirst.subContractTitle == transportFirst.subContractTitle)
        assert(itemFirst.phoneNumber == transportFirst.phoneNumber)
        assert(itemFirst.mothersName == transportFirst.mothersName)
        assert(itemFirst.email == transportFirst.email)
        assert(itemFirst.addressNumber == transportFirst.addressNumber)
        assert(itemFirst.addressComplement == transportFirst.addressComplement)
        assert(itemFirst.productTitle == transportFirst.productTitle)
        assert(itemFirst.activatedAt == transportFirst.activatedAt)
        assert(itemFirst.beneficiaryContractType == transportFirst.beneficiaryContractType)
        assert(itemFirst.hiredAt == transportFirst.hiredAt)
        assert(itemFirst.parentNationalId == transportFirst.parentNationalId)
        assert(itemFirst.parentBeneficiaryRelationType == transportFirst.parentBeneficiaryRelationType)
        assert(itemFirst.relationExceeds30Days == transportFirst.relationExceeds30Days)
        assert(itemFirst.ownership == transportFirst.ownership)
    }

    @Test
    fun `#toAddBeneficiaryBatchResponse should return correct response`() = runBlocking {
        val response = validation.toAddBeneficiaryBatchResponse(request.uploadId)

        assert(response.success.items == validation.success)
        assert(response.errors.items.isNotEmpty())
        assert(response.errors.count == validation.errors.size)

        val errorFirst = response.errors.items.first()
        val validationFirst = validation.errors.first()

        assert(errorFirst.index == validationFirst.index)
        assert(errorFirst.error.isNotEmpty())
        assert(errorFirst.error.size == validationFirst.error.size)

        val itemFirst = errorFirst.error.first()
        val validationItemFirst = validationFirst.error.first()

        assert(itemFirst.field == validationItemFirst.field)
        assert(itemFirst.message == validationItemFirst.message)
    }
}
