package br.com.alice.bff.business.controllers.v2

import br.com.alice.bff.business.ControllerTestHelper
import br.com.alice.bff.business.converters.v2.toBeneficiaryTransport
import br.com.alice.bff.business.converters.v2.toHrBeneficiaryBatchTransport
import br.com.alice.bff.business.converters.v2.toHrDependentTransport
import br.com.alice.bff.business.converters.v2.toSummaryResponse
import br.com.alice.bff.business.models.v2.AddBeneficiaryBatchErrorsResponse
import br.com.alice.bff.business.models.v2.AddBeneficiaryBatchResponse
import br.com.alice.bff.business.models.v2.AddBeneficiaryBatchSuccessResponse
import br.com.alice.bff.business.models.v2.BeneficiaryBatchItemRequest
import br.com.alice.bff.business.models.v2.BeneficiaryBatchRequest
import br.com.alice.bff.business.models.v2.BeneficiaryDependentRequest
import br.com.alice.bff.business.models.v2.BeneficiaryDetailResponse
import br.com.alice.bff.business.models.v2.BeneficiaryIsCancelableResponse
import br.com.alice.bff.business.models.v2.MultiPartResponse
import br.com.alice.bff.business.models.v2.ParentBeneficiaryRelationTypeOption
import br.com.alice.bff.business.models.v2.ProductChangeResponse
import br.com.alice.bff.business.models.v2.RelationTypeResponse
import br.com.alice.bff.business.services.ContractService
import br.com.alice.bff.business.services.ProductCachedService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanyStaffService
import br.com.alice.business.client.MailerService
import br.com.alice.business.exceptions.AlreadyAliceMemberException
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.MemberProductChangeSchedule
import br.com.alice.data.layer.models.MemberProductChangeScheduleStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.PersonVaultDeleteByteArray
import br.com.alice.filevault.models.PersonVaultUploadByteArray
import br.com.alice.hr.core.client.HrBeneficiaryService
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.TemplateSheetResult
import br.com.alice.membership.client.MemberProductChangeScheduleService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpMethod
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryControllerTest : ControllerTestHelper() {
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyStaffService: CompanyStaffService = mockk()
    private val productService: ProductCachedService = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val memberProductChangeScheduleService: MemberProductChangeScheduleService = mockk()
    private val companyService: CompanyService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val contractService: ContractService = mockk()
    private val mailerService: MailerService = mockk()
    private val hrBeneficiaryService: HrBeneficiaryService = mockk()

    private val product = TestModelFactory.buildProduct()
    private val person = TestModelFactory.buildPerson()
    private val member = TestModelFactory.buildMember()
    private val beneficiary = TestModelFactory.buildBeneficiary(
        companyId = companyStaff.companyId,
        memberId = member.id,
        personId = person.id,
    )

    private val controller = BeneficiaryController(
        companyStaffService,
        beneficiaryService,
        memberService,
        personService,
        productService,
        memberProductChangeScheduleService,
        fileVaultActionService,
        contractService,
        mailerService,
        hrBeneficiaryService
    )

    private val emailReceipt = EmailReceipt(id = RangeUUID.generate().toString())


    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
        coEvery { companyStaffService.getLatestByEmail(staff.email) } returns companyStaff
    }

    @AfterTest
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `#get should return holder data with dependents`() = runBlocking {
        val dependent1 = TestModelFactory.buildPerson()
        val dependent2 = TestModelFactory.buildPerson()
        val dependents = listOf(
            TestModelFactory.buildBeneficiary(personId = dependent1.id),
            TestModelFactory.buildBeneficiary(personId = dependent2.id),
        )

        val dependentMembers = dependents.map { TestModelFactory.buildMember(personId = it.personId, id = it.memberId) }

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { beneficiaryService.withOnboardingAndDependents(beneficiary) } returns beneficiary.copy(
            dependents = dependents,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding()
        ).success()

        coEvery { productService.getProductSummary(member.productId) } returns product.toSummaryResponse()
        coEvery {
            memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
                member.id,
                MemberProductChangeScheduleStatus.REQUESTED
            )
        } returns emptyList<MemberProductChangeSchedule>().success()
        coEvery { memberService.getCurrent(beneficiary.personId) } returns member.success()
        coEvery { memberService.getCurrentsByIds(dependents.map { it.memberId }) } returns dependentMembers.success()
        coEvery {
            personService.findByIds(dependents.map { it.personId.toString() } + person.id.toString())
        } returns listOf(person, dependent1, dependent2).success()

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#get should return holder data with dependents without the canceled ones`() = runBlocking {
        val dependent1 = TestModelFactory.buildPerson()
        val dependent2 = TestModelFactory.buildPerson()
        val dependents = listOf(
            TestModelFactory.buildBeneficiary(personId = dependent1.id),
            TestModelFactory
                .buildBeneficiary(personId = dependent2.id)
                .copy(
                    canceledAt = LocalDateTime.now(),
                    canceledReason = BeneficiaryCancelationReason.ANOTHER,
                ),
        )

        val dependentMembers = dependents
            .filter { it.canceledAt == null }
            .map { TestModelFactory.buildMember(personId = it.personId, id = it.memberId) }

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { beneficiaryService.withOnboardingAndDependents(beneficiary) } returns beneficiary.copy(
            dependents = dependents,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding()
        ).success()

        coEvery { productService.getProductSummary(member.productId) } returns product.toSummaryResponse()
        coEvery {
            memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
                member.id,
                MemberProductChangeScheduleStatus.REQUESTED
            )
        } returns emptyList<MemberProductChangeSchedule>().success()
        coEvery { memberService.getCurrent(beneficiary.personId) } returns member.success()
        coEvery { memberService.getCurrentsByIds(dependentMembers.map { it.id }) } returns dependentMembers.success()
        coEvery {
            personService.findByIds(listOf(dependents.first().personId.toString()) + beneficiary.personId.toString())
        } returns listOf(person, dependent1).success()

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#get should return holder data with dependents filtering by personId to avoid duplicated dependents`() = runBlocking {
        val dependent1 = TestModelFactory.buildPerson()
        val dependent2 = TestModelFactory.buildPerson()
        val dependents = listOf(
            TestModelFactory.buildBeneficiary(personId = dependent1.id, parentBeneficiary = beneficiary.id, memberStatus = MemberStatus.ACTIVE),
            TestModelFactory.buildBeneficiary(personId = dependent1.id, parentBeneficiary = beneficiary.id, memberStatus = MemberStatus.CANCELED),
            TestModelFactory.buildBeneficiary(personId = dependent2.id, parentBeneficiary = beneficiary.id, memberStatus = MemberStatus.ACTIVE),
            TestModelFactory.buildBeneficiary(personId = dependent2.id, parentBeneficiary = beneficiary.id, memberStatus = MemberStatus.CANCELED),
        )

        val dependentMembers = dependents
            .filter { it.memberStatus == MemberStatus.ACTIVE }
            .map { TestModelFactory.buildMember(personId = it.personId, id = it.memberId) }

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { beneficiaryService.withOnboardingAndDependents(beneficiary) } returns beneficiary.copy(
            dependents = dependents,
            onboarding = TestModelFactory.buildBeneficiaryOnboarding()
        ).success()

        coEvery { productService.getProductSummary(member.productId) } returns product.toSummaryResponse()
        coEvery {
            memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
                member.id,
                MemberProductChangeScheduleStatus.REQUESTED
            )
        } returns emptyList<MemberProductChangeSchedule>().success()
        coEvery { memberService.getCurrent(beneficiary.personId) } returns member.success()
        coEvery { memberService.getCurrentsByIds(dependents.map { it.memberId }) } returns dependentMembers.success()
        coEvery {
            personService.findByIds(dependentMembers.map { it.personId.toString() } + person.id.toString())
        } returns listOf(person, dependent1, dependent2).success()

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}") { response ->
                val content = gson.fromJson<BeneficiaryDetailResponse>(response.bodyAsText())
                val uniqueDependents = content.dependents.distinctBy { it.personId }

                assert(uniqueDependents.size == content.dependents.size)
            }
        }
    }

    @Test
    fun `#get should return dependent with holder`() = runBlocking {
        val holderPerson = TestModelFactory.buildPerson()
        val holder = TestModelFactory.buildBeneficiary(personId = holderPerson.id)

        val dependentBeneficiary = beneficiary.copy(parentBeneficiary = holder.id)

        coEvery { productService.getProductSummary(member.productId) } returns product.toSummaryResponse()
        coEvery { beneficiaryService.get(dependentBeneficiary.id) } returns dependentBeneficiary
        coEvery { beneficiaryService.get(dependentBeneficiary.parentBeneficiary!!) } returns holder
        coEvery {
            memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
                member.id,
                MemberProductChangeScheduleStatus.REQUESTED
            )
        } returns emptyList<MemberProductChangeSchedule>().success()
        coEvery { beneficiaryService.withOnboardingAndDependents(dependentBeneficiary) } returns dependentBeneficiary.copy(
            dependents = emptyList(),
            onboarding = TestModelFactory.buildBeneficiaryOnboarding()
        ).success()

        coEvery { memberService.getCurrent(dependentBeneficiary.personId) } returns member.success()
        coEvery { personService.findByIds(any()) } returns listOf(holderPerson, person).success()

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#getBeneficiary should return error when beneficiary does not belong to staff company`() = runBlocking {
        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary.copy(companyId = UUID.randomUUID())
            .success()

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}") { response ->
                ResponseAssert.assertThat(response).isForbidden()
            }
        }
    }

    @Test
    fun `#changeProduct change product should return error when beneficiary does not belong to staff company`() =
        runBlocking {
            coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary.copy(companyId = UUID.randomUUID())

            authenticatedAs(idToken, staffTest) {
                post(
                    "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
                    mapOf("product_id" to product.id)
                ) { response ->
                    ResponseAssert.assertThat(response).isForbidden()
                }
            }
        }

    @Test
    fun `#changeProduct should schedule product change to the first day of next month`() = runBlocking {
        val contract = TestModelFactory.buildCompanyContract(groupCompany = "0001")
        val now = LocalDateTime.now()
        val schedule = TestModelFactory.buildMemberProductChangeSchedule(
            productId = product.id,
            memberId = member.id,
            personId = beneficiary.personId,
            applyAt = now
        )
        val firstDayOfNextMonth = now
            .withDayOfMonth(1)
            .atBeginningOfTheDay()
            .plusMonths(1)

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { productService.getProductSummary(product.id) } returns product.toSummaryResponse()
        coEvery { companyService.get(any()) } returns company.copy(contractIds = listOf(contract.id))
        coEvery { companyContractService.get(any()) } returns contract
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns firstDayOfNextMonth
        coEvery { mailerService.sendChangePlanEmail(any(), any(), any(), any()) } returns emailReceipt

        coEvery {
            memberProductChangeScheduleService.create(
                match {
                    it.memberId == member.id &&
                            it.productId == product.id &&
                            it.personId == beneficiary.personId &&
                            it.applyAt == firstDayOfNextMonth
                }
            )
        } returns schedule

        authenticatedAs(idToken, staffTest) {
            post(
                "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
                mapOf("product_id" to product.id)
            ) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce {
            memberProductChangeScheduleService.create(
                match {
                    it.memberId == member.id &&
                            it.productId == product.id &&
                            it.personId == beneficiary.personId &&
                            it.applyAt == firstDayOfNextMonth
                }
            )
        }
    }

    @Test
    fun `#changeProduct should return 400 if contractId is null`() = runBlocking {
        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { productService.getProductSummary(product.id) } returns product.toSummaryResponse()
        coEvery { companyService.get(any()) } returns company
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns null


        authenticatedAs(idToken, staffTest) {
            post(
                "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
                mapOf("product_id" to product.id)
            ) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#changeProduct should return 400 if groupCompany is null`() = runBlocking {
        val contract = TestModelFactory.buildCompanyContract()

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { productService.getProductSummary(product.id) } returns product.toSummaryResponse()
        coEvery { companyService.get(any()) } returns company.copy(contractIds = listOf(contract.id))
        coEvery { companyContractService.get(any()) } returns contract
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns null

        authenticatedAs(idToken, staffTest) {
            post(
                "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
                mapOf("product_id" to product.id)
            ) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#changeProduct should schedule product change to the contract startedAt of next month`() = runBlocking {
        val startedAt = LocalDate.of(2023, 1, 15)
        val applyAt = LocalDateTime.now().plusMonths(1).withDayOfMonth(startedAt.dayOfMonth).atBeginningOfTheDay()

        val contract = TestModelFactory.buildCompanyContract(groupCompany = "0018", startedAt = startedAt)
        val schedule = TestModelFactory.buildMemberProductChangeSchedule(
            productId = product.id,
            memberId = member.id,
            personId = beneficiary.personId,
            applyAt = applyAt
        )

        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery { productService.getProductSummary(product.id) } returns product.toSummaryResponse()
        coEvery { companyService.get(any()) } returns company.copy(contractIds = listOf(contract.id))
        coEvery { companyContractService.get(any()) } returns contract
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns applyAt
        coEvery { mailerService.sendChangePlanEmail(any(), any(), any(), any()) } returns emailReceipt


        coEvery {
            memberProductChangeScheduleService.create(
                match {
                    it.memberId == member.id &&
                            it.productId == product.id &&
                            it.personId == beneficiary.personId &&
                            it.applyAt == applyAt
                }
            )
        } returns schedule

        authenticatedAs(idToken, staffTest) {
            post(
                "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
                mapOf("product_id" to product.id)
            ) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce {
            memberProductChangeScheduleService.create(
                match {
                    it.memberId == member.id &&
                            it.productId == product.id &&
                            it.personId == beneficiary.personId &&
                            it.applyAt == applyAt
                }
            )
        }
    }


    @Test
    fun `#cancelProductChange should return error when there is no scheduled change`() = runBlocking {
        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery {
            memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
                member.id,
                MemberProductChangeScheduleStatus.REQUESTED
            )
        } returns emptyList<MemberProductChangeSchedule>()

        authenticatedAs(idToken, staffTest) {
            delete(
                "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
            ) { response ->
                ResponseAssert.assertThat(response).isNotFound()
            }
        }

        coVerifyNone { memberProductChangeScheduleService.cancel(any()) }
    }

    @Test
    fun `#cancelProductChange should cancel product change scheduling`() = runBlocking {
        val schedule = TestModelFactory.buildMemberProductChangeSchedule(
            productId = product.id,
            memberId = member.id,
            personId = beneficiary.personId,
            applyAt = LocalDateTime.now()
        )


        coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary
        coEvery {
            memberProductChangeScheduleService.getSchedulesByMemberAndStatus(
                member.id,
                MemberProductChangeScheduleStatus.REQUESTED
            )
        } returns listOf(schedule)
        coEvery { memberProductChangeScheduleService.cancel(schedule.id) } returns schedule.cancel()
        coEvery { mailerService.sendChangePlanEmail(any(), any(), any(), any()) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            delete(
                "/v2/beneficiaries/${beneficiary.id}/product_change_requests",
            ) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { memberProductChangeScheduleService.cancel(any()) }
    }

    @Test
    fun `#uploadFile should save file to fileVault and return id`() = runBlocking {
        val personId = beneficiary.personId
        val personVaultUploadByteArray = PersonVaultUploadByteArray(
            domain = BeneficiaryController.BUSINESS_FILE_VAULT_STORAGE_DOMAIN,
            namespace = BeneficiaryController.BUSINESS_FILE_VAULT_STORAGE_NAMESPACE,
            originalFileName = "test.jpg",
            fileContent = ByteArray(0),
            fileType = FileType.fromExtension("jpg")!!,
            personId = personId,
            fileSize = 0L
        )
        val fileVault = TestModelFactory.buildFileVault(
            personId = personId,
            domain = personVaultUploadByteArray.domain,
            namespace = personVaultUploadByteArray.namespace,
            originalFileName = personVaultUploadByteArray.originalFileName!!,
            fileType = personVaultUploadByteArray.fileType.toString(),
            fileSize = personVaultUploadByteArray.fileSize
        )

        val expected = MultiPartResponse(
            id = fileVault.id,
            fileName = "${fileVault.originalFileName}.${fileVault.fileType}",
            originalFileName = fileVault.originalFileName,
        )

        coEvery { fileVaultActionService.uploadFile(any()) } returns fileVault

        authenticatedAs(idToken, staffTest) {
            multipart(
                HttpMethod.Post,
                "/v2/persons/${personId}/file",
                fileName = "test.jpg"
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#deleteFile should delete file from fileVault`() = runBlocking {
        val fileId = UUID.randomUUID()
        val personId = beneficiary.personId
        val personVaultUploadByteArray = PersonVaultDeleteByteArray(
            domain = BeneficiaryController.BUSINESS_FILE_VAULT_STORAGE_DOMAIN,
            namespace = BeneficiaryController.BUSINESS_FILE_VAULT_STORAGE_NAMESPACE,
            fileName = fileId.toString(),
            personId = personId,
        )

        coEvery {
            fileVaultActionService.deleteFile(personVaultUploadByteArray)
        } returns true.success()

        authenticatedAs(idToken, staff) {
            delete("/v2/persons/${personId}/file/${fileId}") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#getProductChangeNextDate should return date correctly when flexible`() = runBlocking {
        val startAt = LocalDate.of(2023, 1, 15)
        val contract = TestModelFactory.buildCompanyContract(groupCompany = "0018", startedAt = startAt)

        val date = LocalDateTime.now().withDayOfMonth(startAt.dayOfMonth).plusMonths(1).atBeginningOfTheDay()
        val expected = ProductChangeResponse(
            date = date.toString()
        )

        coEvery { companyService.get(company.id) } returns company.copy(contractIds = listOf(contract.id))
        coEvery { companyContractService.get(contract.id) } returns contract
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns date

        authenticatedAs(idToken, staff) {
            get("/v2/companies/${company.id}/change_product_next_date") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

            }
        }
    }

    @Test
    fun `#getProductChangeNextDate should return date correctly pre paid or pos paid`() = runBlocking {
        val contract =
            TestModelFactory.buildCompanyContract(groupCompany = "0005", startedAt = LocalDate.of(2023, 1, 15))

        val date = LocalDateTime.now().withDayOfMonth(1).plusMonths(1).atBeginningOfTheDay()
        val expected = ProductChangeResponse(
            date = date.toString()
        )

        coEvery { companyService.get(company.id) } returns company.copy(contractIds = listOf(contract.id))
        coEvery { companyContractService.get(contract.id) } returns contract
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns date

        authenticatedAs(idToken, staff) {
            get("/v2/companies/${company.id}/change_product_next_date") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `#getProductChangeNextDate should return 400 if contract id does not exist`() = runBlocking {

        coEvery { companyService.get(company.id) } returns company
        coEvery { contractService.getApplyDateBasedOnGroupCompany(company.id) } returns null

        authenticatedAs(idToken, staff) {
            get("/v2/companies/${company.id}/change_product_next_date") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }


    @Test
    fun `#isCancelable - should return true when beneficiary is holder but not the last`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.ACTIVE, type = BeneficiaryType.EMPLOYEE)
        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
        coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any(), any()) } returns 50

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}/is_cancelable") { response ->
                ResponseAssert.assertThat(response).isOKWithData(BeneficiaryIsCancelableResponse(true))
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyOnce { beneficiaryService.countActiveWithNoPendingCancellation(beneficiary.companyId, BeneficiaryType.EMPLOYEE) }
    }

    @Test
    fun `#isCancelable - should return false when beneficiary is holder and the last`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.ACTIVE, type = BeneficiaryType.EMPLOYEE)
        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary
        coEvery { beneficiaryService.countActiveWithNoPendingCancellation(any(), any()) } returns 1

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}/is_cancelable") { response ->
                ResponseAssert.assertThat(response).isOKWithData(BeneficiaryIsCancelableResponse(false))
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyOnce { beneficiaryService.countActiveWithNoPendingCancellation(beneficiary.companyId, BeneficiaryType.EMPLOYEE) }
    }

    @Test
    fun `#isCancelable - should return true when beneficiary is dependent`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.ACTIVE, type = BeneficiaryType.DEPENDENT)
        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}/is_cancelable") { response ->
                ResponseAssert.assertThat(response).isOKWithData(BeneficiaryIsCancelableResponse(true))
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyNone { beneficiaryService.countActiveWithNoPendingCancellation(beneficiary.companyId) }
    }

    @Test
    fun `#isCancelable - should return true when beneficiary is status pending`() {
        val beneficiary = TestModelFactory.buildBeneficiary(memberStatus = MemberStatus.PENDING)
        coEvery { beneficiaryService.get(beneficiary.id, any()) } returns beneficiary

        authenticatedAs(idToken, staffTest) {
            get("/v2/beneficiaries/${beneficiary.id}/is_cancelable") { response ->
                ResponseAssert.assertThat(response).isOKWithData(BeneficiaryIsCancelableResponse(true))
            }
        }

        coVerifyOnce { beneficiaryService.get(beneficiary.id, any()) }
        coVerifyNone { beneficiaryService.countActiveWithNoPendingCancellation(beneficiary.companyId) }
    }

    @Test
    fun `#getRelationTypes - should return relation types for a given company and subcontract`() {
        val relationTypes = listOf(
            ParentBeneficiaryRelationType.SPOUSE,
            ParentBeneficiaryRelationType.CHILD,
            ParentBeneficiaryRelationType.PARTNER
        )

        val subContract = subContracts.first()

        coEvery { hrBeneficiaryService.getRelationTypes(company.id, subContract.id) } returns relationTypes

        val expectedResponse = RelationTypeResponse(
            options = relationTypes.map { ParentBeneficiaryRelationTypeOption(it.name, it.description) }
        )

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/subcontracts/${subContract.id}/relations") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#addBeneficiaryDependent should return 201 when request is successful`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()

        val request = BeneficiaryDependentRequest(
            firstName = "firstName",
            lastName = "lastName",
            mothersName = "mothersName",
            nationalId = "nationalId",
            email = "email",
            sex = Sex.MALE,
            birthDate = LocalDateTime.now(),
            phoneNumber = "phoneNumber",
            activatedAt = LocalDateTime.now(),
            address = Address(
                street = "Rua Teste",
                number = "123",
                city = "São Paulo",
                state = State.SP,
            ),
            productId = product.id,
            parentBeneficiary = beneficiary.id,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.now(),
            cnpj = "1234567890",
            subcontractId = UUID.randomUUID()
        )

        val dependentTransport = request.toHrDependentTransport()


        coEvery { hrBeneficiaryService.createDependent(dependentTransport, company.id) } returns beneficiary.success()
        coEvery { mailerService.sendInclusionEmail(any(), dependentTransport.toBeneficiaryTransport(company.id)) } returns emailReceipt

        authenticatedAs(idToken, staffTest) {
            post("/v2/companies/${company.id}/beneficiaries/dependents", body = request) { response ->
                ResponseAssert.assertThat(response).isCreated()
            }
        }

        coVerifyOnce { hrBeneficiaryService.createDependent(any(), any()) }
        coVerifyOnce { mailerService.sendInclusionEmail(any(), any()) }
    }

    @Test
    fun `#addBeneficiaryDependent should return 400 when request is invalid`() = runBlocking {
        val beneficiary = TestModelFactory.buildBeneficiary()

        val request = BeneficiaryDependentRequest(
            firstName = "firstName",
            lastName = "lastName",
            mothersName = "mothersName",
            nationalId = "nationalId",
            email = "email",
            sex = Sex.MALE,
            birthDate = LocalDateTime.now(),
            phoneNumber = "phoneNumber",
            activatedAt = LocalDateTime.now(),
            address = Address(
                street = "Rua Teste",
                number = "123",
                city = "São Paulo",
                state = State.SP,
            ),
            productId = product.id,
            parentBeneficiary = beneficiary.id,
            parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
            parentBeneficiaryRelatedAt = LocalDateTime.now(),
            cnpj = "1234567890",
            subcontractId = UUID.randomUUID()
        )

        val dependentTransport = request.toHrDependentTransport()

        coEvery { hrBeneficiaryService.createDependent(dependentTransport, company.id) } returns AlreadyAliceMemberException(personName = "").failure()

        authenticatedAs(idToken, staffTest) {
            post("/v2/companies/${company.id}/beneficiaries/dependents", body = request) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#downloadBeneficiarySheetTemplate should return 200 when request is successful`() = runBlocking {
        val templateSheetResult = TemplateSheetResult(
            fileByteContent = byteArrayOf(1, 2, 3),
            fileName = "test.xlsx"
        )

        coEvery { hrBeneficiaryService.getBeneficiarySheetTemplate(company.id) } returns templateSheetResult.success()

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/beneficiaries/sheet/template") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#downloadBeneficiarySheetTemplate should return 500 when request fails`() = runBlocking {
        coEvery { hrBeneficiaryService.getBeneficiarySheetTemplate(company.id) } returns InternalServiceErrorException("Error getting beneficiary sheet template").failure()

        authenticatedAs(idToken, staffTest) {
            get("/v2/companies/${company.id}/beneficiaries/sheet/template") { response ->
                ResponseAssert.assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `#uploadBeneficiariesBatch should call the service successfully`() = runBlocking {
        val body = BeneficiaryBatchRequest(
            uploadId = UUID.randomUUID(),
            items = listOf(
                BeneficiaryBatchItemRequest(
                    index = 1,
                    nationalId = "12345678901",
                    fullName = "Calango Zokas",
                    cnpj = "12345678000195",
                    subContractTitle = "Matriz",
                    phoneNumber = "(11) 91234-5678",
                    mothersName = "Jane Doe",
                    email = "<EMAIL>",
                    addressNumber = "222",
                    addressComplement = null,
                    productTitle = "Plano de Saúde Bonzao",
                    activatedAt = "2023-01-01T00:00:00Z",
                    beneficiaryContractType = "CLT",
                    hiredAt = "2023-01-01T00:00:00Z",
                    parentNationalId = null,
                    parentBeneficiaryRelationType = null,
                    relationExceeds30Days = "nao",
                    ownership = "nao",
                )
            )
        )

        coEvery {
            hrBeneficiaryService.addBeneficiariesBatch(body.uploadId, company.id, companyStaff.id, body.toHrBeneficiaryBatchTransport())
        } returns BeneficiaryBatchValidation(
            success = listOf(1),
            errors = emptyList(),
        ).success()

        authenticatedAs(idToken, staffTest) {
            post("/v2/companies/${company.id}/beneficiaries/batch", body) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    AddBeneficiaryBatchResponse(
                        uploadId = body.uploadId,
                        success = AddBeneficiaryBatchSuccessResponse(
                            count = 1,
                            items = listOf(1)
                        ),
                        errors = AddBeneficiaryBatchErrorsResponse(
                            count = 0,
                            items = emptyList()
                        )
                    )
                )
            }
        }
        coVerifyOnce { hrBeneficiaryService.addBeneficiariesBatch(any(), any(), any(), any()) }
    }

    @Test
    fun `#uploadBeneficiariesBatch should return an exception when service gives an error`() = runBlocking {
        val body = BeneficiaryBatchRequest(
            uploadId = RangeUUID.generate(),
            items = listOf(
                BeneficiaryBatchItemRequest(
                    index = 1,
                    nationalId = "12345678901",
                    fullName = "Calango Zokas",
                    cnpj = "12345678000195",
                    subContractTitle = "Matriz",
                    phoneNumber = "(11) 91234-5678",
                    mothersName = "Jane Doe",
                    email = "<EMAIL>",
                    addressNumber = "222",
                    addressComplement = null,
                    productTitle = "Plano de Saúde Bonzao",
                    activatedAt = "2023-01-01T00:00:00Z",
                    beneficiaryContractType = "CLT",
                    hiredAt = "2023-01-01T00:00:00Z",
                    parentNationalId = null,
                    parentBeneficiaryRelationType = null,
                    relationExceeds30Days = "nao",
                    ownership = "nao",
                )
            )
        )

        coEvery {
            hrBeneficiaryService.addBeneficiariesBatch(body.uploadId, company.id, companyStaff.id, body.toHrBeneficiaryBatchTransport())
        } returns InternalServiceErrorException("Error", "Error").failure()

        authenticatedAs(idToken, staffTest) {
            post("/v2/companies/${company.id}/beneficiaries/batch", body) { response ->
                ResponseAssert.assertThat(response).isInternalServerError()
            }
        }
        coVerifyOnce { hrBeneficiaryService.addBeneficiariesBatch(any(), any(), any(), any()) }
    }
}
