package br.com.alice.nullvs.models.payment

import br.com.alice.nullvs.models.Meta
import java.time.LocalDate
import java.util.UUID

class NullvsFirstPaymentRequest(
    val meta: Meta,
    val payload: Payload
) {
    data class Payload(
        val aliceId: UUID,
        val schedule: LocalDate,
        val prePayment: PrePayment,
        val additional: List<Additional>,
        val groupCompany: String,
        val contractNumber: String,
        val subContractNumber: String,
    ) {
        data class PrePayment(
            val prefix: String,
            val number: String,
            val installment: String,
            val type: String,
        )

        data class Additional(
            val level: Level,
            val cpfCnpj: String,
            val code: String,
            val valueType: ValueType,
            val value: Double,
            val observation: String,
            val idAddBilling: UUID,
        ) {
            enum class Level(val description: String) {
                MEMBER("membro"),
                SUBCONTRACT("subcontrato")
            }

            enum class ValueType(val description: String) {
                PERCENTAGE("0"),
                ABSOLUTE("1")
            }
        }
    }
}
