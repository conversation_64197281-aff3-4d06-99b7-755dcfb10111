package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.nullvs.SERVICE_NAME

class NullvsFirstPaymentFailedEvent(payload: Payload
) : NotificationEvent<NullvsFirstPaymentFailedEvent.Payload>(
producer = SERVICE_NAME,
name = name,
payload = payload,
) {
    companion object {
        const val name = "nullvs-first-payment-failed"
    }

    data class Payload(
        val firstPaymentScheduleId: String,
        val errorMessage: String,
    )
}
