package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.nullvs.SERVICE_NAME
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class NullvsFirstPaymentCreatedEvent(
    payload: Payload
) : NotificationEvent<NullvsFirstPaymentCreatedEvent.Payload>(
    producer = SERVICE_NAME,
    name = name,
    payload = payload,
) {
    companion object {
        const val name = "nullvs-first-payment-created"
    }

    data class Payload(
        val firstPaymentScheduleId: String,
        val discount: BigDecimal,
        val externalId: String,
        val billingAccountablePartyId: UUID,
        val dueDate: LocalDate,
        val value: BigDecimal,
        val paidAt: LocalDate,
        val gloablItems: List<InvoiceDetailItem>,
        val memberDetails: List<MemberInvoiceDetail>,
        val referenceDate: LocalDate,
        val groupCompany: String,
        val companyContractNumber: String,
        val companySubContractNumber: String,
    ) {
        data class MemberInvoiceDetail(
            val totalAmount: BigDecimal,
            val memberId: UUID,
            val referenceDate: LocalDate,
            val dueDate: LocalDate,
            val items: List<InvoiceDetailItem>
        )

        data class InvoiceDetailItem(
            val referenceDate: LocalDate,
            val operation: InvoiceItemOperation,
            val type: InvoiceItemType,
            val value: BigDecimal,
            val notes: String,
        )
    }
}

