package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.nullvs.SERVICE_NAME
import java.util.UUID

class NullvsScheduleFirstPaymentResponseEvent(firstPaymentScheduleId: UUID, externalId: String?, error: String?) :
    NotificationEvent<NullvsScheduleFirstPaymentResponseEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(firstPaymentScheduleId, externalId, error),
    ) {
    companion object {
        const val name = "nullvs-schedule-first-payment-response"
    }

    data class Payload(
        val firstPaymentScheduleId: UUID,
        val externalId: String? = null,
        val error: String? = null,
    )
}
