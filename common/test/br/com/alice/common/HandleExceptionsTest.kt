package br.com.alice.common

import br.com.alice.common.core.exceptions.AliceException
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.service.serialization.gsonBFF
import br.com.alice.common.service.serialization.gsonSnakeCase
import com.google.gson.JsonParseException
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode.Companion.BadRequest
import io.ktor.server.application.install
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.post
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HandleExceptionsTest {

    private val controller = TestController()

    @Test
    fun `Should return custom message when exception is mapped custom exception`(): Unit = testApplication {
        application {
            install(ContentNegotiation) {
                gsonSnakeCase()
            }
            routing {
                post("/") { coHandler(controller::postExceptionCode) }
            }

            handleExceptions()
        }

        val stringBody = """{"code":"mapped_custom_exception"}"""

        client.post("/") {
            header("Accept", "application/json")
            header("Content-Type", "application/json")
            setBody(stringBody)
        }.let { response ->
            assertThat(response.status).isEqualTo(BadRequest)

            val content: ErrorResponse = gsonBFF.fromJson(response.bodyAsText())
            assertThat(content.code).isEqualTo("mapped_custom_exception")
            assertThat(content.message).isEqualTo("mapped custom exception message")
        }
    }

    @Test
    fun `Should return generic message when exception is unmapped custom exception and withOriginalErrorMessage is false`(): Unit =
        testApplication {
            application {
                install(ContentNegotiation) {
                    gsonSnakeCase()
                }
                routing {
                    post("/") { coHandler(controller::postExceptionCode) }
                }

                handleExceptions(withOriginalErrorMessage = false)
            }

            val stringBody = """{"code":"unmapped_custom_exception"}"""

            client.post("/") {
                header("Accept", "application/json")
                header("Content-Type", "application/json")
                setBody(stringBody)
            }.let { response ->
                assertThat(response.status).isEqualTo(BadRequest)

                val content: ErrorResponse = gsonBFF.fromJson(response.bodyAsText())
                assertThat(content.code).isEqualTo("unmapped_custom_exception")
                assertThat(content.message).isIn(genericErrorMessages)
            }
        }


    @Test
    fun `Should return exception message when exception is unmapped custom exception and withOriginalErrorMessage is true`(): Unit =
        testApplication {
            application {
                install(ContentNegotiation) {
                    gsonSnakeCase()
                }
                routing {
                    post("/") { coHandler(controller::postExceptionCode) }
                }

                handleExceptions(withOriginalErrorMessage = true)
            }

            val stringBody = """{"code":"unmapped_custom_exception"}"""

            client.post("/") {
                header("Accept", "application/json")
                header("Content-Type", "application/json")
                setBody(stringBody)
            }.let { response ->
                assertThat(response.status).isEqualTo(BadRequest)

                val content: ErrorResponse = gsonBFF.fromJson(response.bodyAsText())
                assertThat(content.code).isEqualTo("unmapped_custom_exception")
                assertThat(content.message).isEqualTo("default exception message")
            }
        }

    @Test
    fun `Should return exception message when exception is BadRequestException`(): Unit =
        testApplication {
            application {
                install(ContentNegotiation) {
                    gsonSnakeCase()
                }
                routing {
                    post("/") { coHandler(controller::badRequestException) }
                }

                handleExceptions(withOriginalErrorMessage = true)
            }

            client.post("/") {
                header("Accept", "application/json")
                header("Content-Type", "application/json")
            }.let { response ->
                assertThat(response.status).isEqualTo(BadRequest)

                val content: ErrorResponse = gsonBFF.fromJson(response.bodyAsText())
                assertThat(content.code).isEqualTo("bad_request")
                assertThat(content.message).isEqualTo("bad choice bro")
            }
        }

    @Test
    fun `Should return exception message when exception is JsonParseException`(): Unit =
        testApplication {
            application {
                install(ContentNegotiation) {
                    gsonSnakeCase()
                }
                routing {
                    post("/") { coHandler(controller::jsonParseException) }
                }

                handleExceptions(withOriginalErrorMessage = true)
            }

            client.post("/") {
                header("Accept", "application/json")
                header("Content-Type", "application/json")
            }.let { response ->
                assertThat(response.status).isEqualTo(BadRequest)

                val content: ErrorResponse = gsonBFF.fromJson(response.bodyAsText())
                assertThat(content.code).isEqualTo("invalid_json")
                assertThat(content.message).isEqualTo("json parse exception")
            }
        }
}

class TestController {
    fun postExceptionCode(body: ExceptionCodePayload): Response {
        throw AliceException(code = body.code, message = "default exception message")
    }
    fun badRequestException(): Response {
        throw BadRequestException("bad choice bro")
    }
    fun jsonParseException(): Response {
        throw BadRequestException("bad choice bro", cause = JsonParseException("json parse exception"))
    }
}

data class ExceptionCodePayload(
    val code: String
)
