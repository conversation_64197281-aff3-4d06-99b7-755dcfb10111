package br.com.alice.common

import br.com.alice.common.application.plugin.ExceptionHandler
import br.com.alice.common.core.exceptions.AliceException
import br.com.alice.common.core.exceptions.RfcException
import br.com.alice.common.logging.logger
import com.google.gson.JsonParseException
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.application.install
import io.ktor.server.plugins.BadRequestException
import java.util.Locale
import java.util.ResourceBundle

val customExceptionMessages = runCatching { ResourceBundle.getBundle("custom-exception-messages", Locale.ROOT) }.getOrNull()

fun Application.handleExceptions(withOriginalErrorMessage: Boolean = false) {
    install(ExceptionHandler) {
        exception<MultipartRequestRequiredException> { ex ->
            logger.warn("Handled MultipartRequestRequiredException", ex)
            call.respondBadRequest("multipart_required", ex, withOriginalErrorMessage)
        }
        exception<IllegalArgumentException> { ex ->
            logger.warn("Handled IllegalArgumentException", ex)
            call.respondBadRequest("illegal_argument", ex, withOriginalErrorMessage)
        }
        exception<JsonParseException> { cause ->
            logger.warn("Handled JsonParseException", cause)
            call.respondBadRequest("invalid_json", cause, withOriginalErrorMessage)
        }
        exception<BadRequestException> { cause ->
            if (cause.cause is JsonParseException) { // https://youtrack.jetbrains.com/issue/KTOR-373
                logger.warn("Handled JsonParseException", cause.cause)
                call.respondBadRequest("invalid_json", cause.cause as JsonParseException, withOriginalErrorMessage)
            } else {
                logger.warn("Handled BadRequestException", cause)
                call.respondBadRequest("bad_request", cause, withOriginalErrorMessage)
            }
        }
        exception<RfcException> { ex ->
            logger.warn("Handled RfcException", ex)
            call.respondRfcException(ex, withOriginalErrorMessage)
        }
        exception<AliceException> { ex ->
            logger.warn("Handled AliceException", ex)
            call.respondBadRequest(ex.code, ex, withOriginalErrorMessage)
        }
        exception<Exception> { ex ->
            logger.error("Unexpected error", ex)
            respond(call, Response(HttpStatusCode.InternalServerError), ex)
        }
    }
}

suspend inline fun ApplicationCall.respondBadRequest(
    code: String,
    ex: Exception,
    withOriginalErrorMessage: Boolean,
) =
    respond(
        this,
        Response(HttpStatusCode.BadRequest, ErrorResponse(code, ex.getErrorMessage(code, withOriginalErrorMessage))),
        ex
    )

suspend inline fun ApplicationCall.respondRfcException(ex: RfcException, withOriginalErrorMessage: Boolean) =
    respond(
        this,
        Response(ex.statusCode, ErrorResponse(ex.code, ex.getErrorMessage(ex.code, withOriginalErrorMessage))),
        ex
    )

fun Exception.getErrorMessage(code: String, withOriginalErrorMessage: Boolean): String {
    return when {
        customExceptionMessages != null && customExceptionMessages.containsKey(code) ->
            customExceptionMessages.getString(code)

        withOriginalErrorMessage -> this.message ?: ""
        else -> getGenericErrorMessage()
    }
}

fun Exception.toStatusCode() =  when (this) {
    is RfcException -> this.statusCode
    is IllegalArgumentException -> HttpStatusCode.BadRequest
    is JsonParseException -> HttpStatusCode.BadRequest
    is AliceException -> HttpStatusCode.BadRequest
    else -> HttpStatusCode.InternalServerError
}
