ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [br.com.alice.business.ApplicationKt.module]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}
subEnv = ${?SUB_ENV}

development {
    AWS_ACCESS_KEY_ID = "ACCESS_KEY_HERE"
    AWS_SECRET_ACCESS_KEY = "SECRET_ACESS_KEY"
    AWS_SESSION_TOKEN = "AWS_SESSION_TOKEN"

    crm {
        hubspot {
            apiKey = ""
            baseUrl = "https://api.hubapi.com",
            accessToken = ""
            dealPipeline = "65166618"
            dealStage = "126082640"

            staging {
                dealPipeline = "65166618"
                dealStage = "126082640"
            }
        }
    }

    cassi {
        clientId = ""
        secret = ""
        userKey =  ""
        userKeyV1 =  ""
        authUrl = "https://rhsso.paas.cassi.com.br/auth/realms/Colaborador/protocol/openid-connect/token"
        baseUrl = "https://cartao-participante.api.paas.cassi.com.br/api/v1"
        baseUrlV1 = "https://convenio.api.paas.cassi.com.br/api/v1/cartao/listarCartaoReciprocidade"
    }

    netlex {
        token = "token"
        baseUrl = "https://hom-alice.netlex.com.br"
        b2bContractTemplateId = "12944"
        script = "3095"
    }

    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
        noReplySenderName = "Alice"
        noReplySenderEmail = "<EMAIL>"
        bccRecipientName = "Alice"
        bccRecipientEmails = "<EMAIL>,<EMAIL>"
    }

    ply {
        baseUrl = ${?PLY_COGNITO_URL}
    }

    escrybe {
        baseUrl = ""
        baseUrl = ${?ESCRYBE_BASE_URL}
        authToken = ""
        authToken = ${?ESCRYBE_TOKEN}
    }

    clicksign {
        baseUrl = ""
        baseUrl = ${?CLICKSIGN_BASE_URL}

        accessToken = ""
        accessToken = ${?CLICKSIGN_ACCESS_TOKEN}

        responsibleSigner = ""
        responsibleSigner = ${?CLICKSIGN_RESPONSIBLE_SIGNER}

        responsibleCertificate = ""
        responsibleCertificate = ${?CLICKSIGN_RESPONSIBLE_CERTIFICATE}

        witnessSigner = ""
        witnessSigner = ${?CLICKSIGN_WITNESS_SIGNER}

        witnessCertificate = ""
        witnessCertificate = ${?CLICKSIGN_WITNESS_CERTIFICATE}
    }

    receitaws {
        authToken = ""
        authToken = ${?RECEITAWS_AUTH}
    }
}

test {
    AWS_ACCESS_KEY_ID = "ACCESS_KEY_HERE"
    AWS_SECRET_ACCESS_KEY = "SECRET_ACESS_KEY"
    AWS_SESSION_TOKEN = "AWS_SESSION_TOKEN"

    crm {
         hubspot {
             apiKey = ""
             baseUrl = "https://api.hubapi.com"
             accessToken = ""
             dealPipeline = "65166618"
             dealStage = "126082640"

             staging {
                 dealPipeline = "65166618"
                 dealStage = "126082640"
             }
         }
    }

    cassi {
        clientId = ""
        secret = ""
        userKey =  ""
        userKeyV1 =  ""
        authUrl = "https://rhsso.paas.cassi.com.br/auth/realms/Colaborador/protocol/openid-connect/token"
        baseUrl = "https://cartao-participante.api.paas.cassi.com.br/api/v1"
        baseUrlV1 = "https://convenio.api.paas.cassi.com.br/api/v1/cartao/listarCartaoReciprocidade"
    }

    netlex {
        token = "token"
        baseUrl = "https://hom-alice.netlex.com.br"
        b2bContractTemplateId = "12944"
        script = "3095"
    }

    mailer {
        senderName = "Alice"
        senderEmail = "<EMAIL>"
        noReplySenderName = "Alice"
        noReplySenderEmail = "<EMAIL>"
        bccRecipientName = "Alice"
        bccRecipientEmails = "<EMAIL>,<EMAIL>"
    }

    ply {
        baseUrl = "http://localhost"
        baseUrl = ${?PLY_COGNITO_URL}
    }

    escrybe {
        baseUrl = "http://localhost"
        baseUrl = ${?ESCRYBE_BASE_URL}
        authToken = "TOKEN"
        authToken = ${?ESCRYBE_TOKEN}
    }

    clicksign {
        baseUrl = "https://sandbox.clicksign.com"
        accessToken = "token"
        responsibleSigner = "{\"signer\":{\"key\":\"db25dce7-e7f6-4228-bd72-245c37767914\",\"email\":\"<EMAIL>\",\"auths\":[\"api\"],\"delivery\":\"email\",\"name\":\"Testemunha da Silva\",\"documentation\":\"000.000.000-00\",\"selfie_enabled\":false,\"handwritten_enabled\":false,\"birthday\":\"1983-03-31\",\"phone_number\":\"11999999999\",\"has_documentation\":true,\"created_at\":\"2024-08-26T15:28:42.248-03:00\",\"updated_at\":\"2024-08-26T15:28:42.248-03:00\",\"official_document_enabled\":false,\"liveness_enabled\":false,\"facial_biometrics_enabled\":false,\"communicate_by\":\"email\",\"location_required_enabled\":false}}"
        responsibleCertificate = "responsibleCertificate"
        witnessSigner = "{\"signer\":{\"key\":\"6ba567f5-e959-461b-968c-3744b1f06623\",\"email\":\"<EMAIL>\",\"auths\":[\"api\"],\"delivery\":\"email\",\"name\":\"Testemunha da Silva Junior\",\"documentation\":\"000.000.000-01\",\"selfie_enabled\":false,\"handwritten_enabled\":false,\"birthday\":\"1983-03-31\",\"phone_number\":\"11999999998\",\"has_documentation\":true,\"created_at\":\"2024-08-26T15:28:42.248-03:00\",\"updated_at\":\"2024-08-26T15:28:42.248-03:00\",\"official_document_enabled\":false,\"liveness_enabled\":false,\"facial_biometrics_enabled\":false,\"communicate_by\":\"email\",\"location_required_enabled\":false}}"
        witnessCertificate = "witnessCertificate"
    }

    receitaws {
        authToken = ""
    }
}

production {
    AWS_ACCESS_KEY_ID = ${?AWS_ACCESS_KEY_ID}
    AWS_SECRET_ACCESS_KEY = ${?AWS_ACCESS_KEY_ID}
    AWS_SESSION_TOKEN = ${?AWS_SESSION_TOKEN}

    crm {
        hubspot {
            apiKey = ""
            apiKey = ${?HUBSPOT_API_KEY}

            baseUrl = "https://api.hubapi.com"
            baseUrl = ${?HUBSPOT_BASE_URL}

            accessToken = ""
            accessToken = ${?HUBSPOT_ACCESS_TOKEN}

            dealPipeline = "65166618"
            dealPipeline = ${?HUBSPOT_DEAL_PIPELINE}

            dealStage = ${?HUBSPOT_DEAL_STAGE}
            dealStage = "126082640"

            staging {
                dealPipeline = "65166618"
                dealPipeline = ${?HUBSPOT_DEAL_PIPELINE}

                dealStage = "126082640"
                dealStage = ${?HUBSPOT_DEAL_STAGE}
            }
        }
    }

    cassi {
        authUrl = "https://rhsso.paas.cassi.com.br/auth/realms/Colaborador/protocol/openid-connect/token"
        baseUrl = "https://cartao-participante.api.paas.cassi.com.br/api/v1"
        baseUrlV1 = "https://convenio.api.paas.cassi.com.br/api/v1/cartao/listarCartaoReciprocidade"

        clientId = ""
        clientId = ${?CASSI_CLIENT_ID}

        secret = ""
        secret = ${?CASSI_SECRET}

        userKey =  ""
        userKey = ${?CASSI_USER_KEY}
        userKeyV1 =  ""
        userKeyV1 = ${?CASSI_USER_KEY_V1}
    }

    netlex {
        token = ""
        token = ${?NETLEX_TOKEN}

        baseUrl = ""
        baseUrl = ${?NETLEX_BASE_URL}

        script = ""
        script = ${?NETLEX_SCRIPT}

        b2bContractTemplateId = ""
        b2bContractTemplateId = ${?NETLEX_B2B_CONTRACT_TEMPLATE_ID}
    }

    mailer {
        senderName = "Alice"
        senderName = ${?DEFAULT_EMAIL_SENDER_NAME}

        senderEmail = "<EMAIL>"
        senderEmail = ${?DEFAULT_EMAIL_SENDER_ADDRESS}

        noReplySenderName = "Alice"
        noReplySenderName = ${?NO_REPLY_EMAIL_SENDER_NAME}

        noReplySenderEmail = "<EMAIL>"
        noReplySenderEmail = ${?NO_REPLY_EMAIL_SENDER_ADDRESS}

        bccRecipientName = "Alice"
        bccRecipientName = ${?DEFAULT_BCC_RECIPIENT_NAME}

        bccRecipientEmails = "<EMAIL>"
        bccRecipientEmails = ${?DEFAULT_BCC_RECIPIENT_EMAILS}
    }

    ply {
        baseUrl = ${?PLY_COGNITO_URL}
    }

    escrybe {
        baseUrl = ""
        baseUrl = ${?ESCRYBE_BASE_URL}
        authToken = ""
        authToken = ${?ESCRYBE_TOKEN}
    }

    clicksign {

        baseUrl = ""
        baseUrl = ${?CLICKSIGN_BASE_URL}

        accessToken = ""
        accessToken = ${?CLICKSIGN_ACCESS_TOKEN}

        responsibleSigner = ""
        responsibleSigner = ${?CLICKSIGN_RESPONSIBLE_SIGNER}

        responsibleCertificate = ""
        responsibleCertificate = ${?CLICKSIGN_RESPONSIBLE_CERTIFICATE}

        witnessSigner = ""
        witnessSigner = ${?CLICKSIGN_WITNESS_SIGNER}

        witnessCertificate = ""
        witnessCertificate = ${?CLICKSIGN_WITNESS_CERTIFICATE}
    }

    receitaws {
        authToken = ""
        authToken = ${?RECEITAWS_AUTH}
    }
}
