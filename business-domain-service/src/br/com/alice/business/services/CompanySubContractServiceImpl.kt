package br.com.alice.business.services

import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.CompanySubContractCreatedEvent
import br.com.alice.business.events.CompanySubContractUpdatedEvent
import br.com.alice.business.exceptions.SubContractInvalidDefaultProductIdException
import br.com.alice.business.exceptions.SubContractInvalidProductAvailableList
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.services.CompanySubContractModelDataService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class CompanySubContractServiceImpl(
    private val companySubContractDataService: CompanySubContractModelDataService,
    private val productService: ProductService,
    private val kafkaProducerService: KafkaProducerService,
) : CompanySubContractService {

    private suspend fun <T : Any> doAndLog(
        action: String,
        vararg inputField: Any,
        block: suspend () -> Result<T, Throwable>
    ): Result<T, Throwable> {
        logger.info("CompanySubContractService::$action started", "input" to inputField)
        return block()
            .then { logger.info("CompanySubContractService::$action finished") }
            .thenError { logger.error("CompanySubContractService::$action exception thrown", "ex" to it) }
    }

    override suspend fun add(companySubContract: CompanySubContract, sendEvent: Boolean) = doAndLog("add", companySubContract) {
        companySubContract.validateDefaultProductId()
            .flatMap {
                val integrationStatus =
                    if (sendEvent) CompanySubContractIntegrationStatus.PROCESSING else companySubContract.integrationStatus
                companySubContractDataService.add(it.toModel().copy(integrationStatus = integrationStatus))
            }
            .map { it.toTransport() }
            .then { if (sendEvent) kafkaProducerService.produce(CompanySubContractCreatedEvent(it)) }
    }

    override suspend fun update(
        companySubContract: CompanySubContract,
        sendEvent: Boolean,
        shouldUpdateTotvsMemberPriceListing: Boolean?
    ) = doAndLog("update", companySubContract) {
        companySubContract.validateDefaultProductId()
            .flatMap {
                val integrationStatus = if (sendEvent) {
                    CompanySubContractIntegrationStatus.PROCESSING
                } else {
                    companySubContract.integrationStatus
                }

                companySubContractDataService.update(companySubContract.toModel().copy(integrationStatus = integrationStatus))
            }
            .map { it.toTransport() }
            .then {
                if (sendEvent) kafkaProducerService.produce(
                    CompanySubContractUpdatedEvent(
                        it,
                        shouldUpdateTotvsMemberPriceListing
                    )
                )
            }
    }

    private suspend fun CompanySubContract.validateDefaultProductId() =
        if (availableProducts != null && defaultProductId != null) {
            if (!availableProducts!!.contains(defaultProductId)) SubContractInvalidDefaultProductIdException(
                defaultProductId!!,
                availableProducts!!
            ).failure()
            else validateAvailableProducts()
        } else {
            logger.info("#validateDefaultProductId - ignore validate since availableProducts and/or defaultProductId were not informed in the request")

            this.success()
        }

    private suspend fun CompanySubContract.validateAvailableProducts() =
        productService.findByIds(availableProducts!!, ProductService.FindOptions(false))
            .flatMap { products ->
                val productIds = products.map { it.id }
                if (!productIds.containsAll(availableProducts!!) or !availableProducts!!.containsAll(productIds)) {
                    SubContractInvalidProductAvailableList(availableProducts!!).failure()
                } else {
                    this.success()
                }
            }

    override suspend fun get(id: UUID) = doAndLog("get", id) {
        companySubContractDataService.get(id)
    }.map { it.toTransport() }

    override suspend fun findByTitle(title: String) = doAndLog("findByTitle", title) {
        companySubContractDataService.find { where { this.title.like(title) } }.pmapEach { it.toTransport() }
    }

    override suspend fun findByTitles(titles: List<String>) = doAndLog("findByTitles", titles) {
        companySubContractDataService.find { where { this.title.inList(titles) } }.pmapEach { it.toTransport() }
    }

    override suspend fun findByCompanyId(companyId: UUID) = doAndLog("findByCompanyId", companyId) {
        companySubContractDataService.find { where { this.companyId.eq(companyId) } }.pmapEach { it.toTransport() }
    }

    override suspend fun findByCompanyIds(companyIds: List<UUID>) = doAndLog("findByCompanyIds", companyIds) {
        companySubContractDataService.find { where { this.companyId.inList(companyIds) } }.pmapEach { it.toTransport() }
    }

    override suspend fun findByIds(ids: List<UUID>): Result<List<CompanySubContract>, Throwable> =
        doAndLog("findByIds", ids) {
            companySubContractDataService.find { where { this.id.inList(ids) } }
        }.pmapEach { it.toTransport() }

    override suspend fun findByContractId(contractId: UUID) = doAndLog("findByContractId", contractId) {
        companySubContractDataService.find { where { this.contractId.eq(contractId) } }.pmapEach { it.toTransport() }
    }

    override suspend fun findByContractIds(contractIds: List<UUID>) = doAndLog("findByContractIds", contractIds) {
        companySubContractDataService.find { where { this.contractId.inList(contractIds) } }
            .pmapEach { it.toTransport() }
    }

    override suspend fun findByBillingAccountablePartyIds(billingAccountablePartyIds: List<UUID>) =
        doAndLog("findByBillingAccountablePartyIds", billingAccountablePartyIds) {
            companySubContractDataService.find {
                where {
                    this.billingAccountablePartyId.inList(
                        billingAccountablePartyIds
                    )
                }
            }
        }.pmapEach { it.toTransport() }

    override suspend fun findByContractIdAndExternalId(contractId: UUID, externalId: String) =
        doAndLog("findByContractIdAndExternalId", contractId, externalId) {
            companySubContractDataService.findOne {
                where {
                    this.contractId.eq(contractId) and this.externalId.eq(
                        externalId
                    )
                }
            }
        }.map { it.toTransport() }

    @OptIn(QueryAllUsage::class)
    override suspend fun findByRange(range: IntRange) = doAndLog("findByRange", range) {
        companySubContractDataService.find { all().offset { range.first }.limit { range.count() } }
            .pmapEach { it.toTransport() }
    }

    override suspend fun delete(companySubContract: CompanySubContract): Result<Boolean, Throwable> =
        companySubContractDataService.delete(companySubContract.toModel())


}
