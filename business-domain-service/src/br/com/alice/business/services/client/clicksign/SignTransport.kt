package br.com.alice.business.services.client.clicksign

import com.google.gson.annotations.SerializedName

data class ClicksignConfiguration(
    val baseUrl: String,
    val accessToken: String,
    val responsibleSigner: String,
    val responsibleCertificate: String,
    val witnessSigner: String,
    val witnessCertificate: String,
)

data class ClicksignSigner(
    val signer: ClicksignSignerData,
)

data class ClicksignSignerData(
    val key: String,
    val email: String,
    val auths: List<String>,
    val delivery: String,
    val name: String,
    val documentation: String,
    val selfie_enabled: Boolean,
    val handwritten_enabled: Boolean,
    val birthday: String,
    val phone_number: String,
    val has_documentation: Boolean,
    val created_at: String,
    val updated_at: String,
    val official_document_enabled: Boolean,
    val liveness_enabled: Boolean,
    val facial_biometrics_enabled: Boolean,
    val communicate_by: String,
    val location_required_enabled: Boolean,
)

data class AssignSignerToDocumentRequest(
    val list: AssignSignerToDocumentRequestData,
)

data class AssignSignerToDocumentRequestData(
    val document_key: String,
    val signer_key: String,
    val sign_as: String = "sign",
    val refusable: Boolean = true,
)

data class AssignSignerToDocumentResponse(
    val list: AssignSignerToDocumentResponseData,
)

data class AssignSignerToDocumentResponseData(
    val key: String,
    val request_signature_key: String,
    val document_key: String,
    val signer_key: String,
    val sign_as: String,
    val refusable: Boolean,
    val created_at: String,
    val updated_at: String,
    val url: String,
)

data class SignDocumentRequest(
    val request_signature_key: String,
    val secret_hmac_sha256: String,
)

data class ClicksignResponseData(
    val document: Document,
)

data class Document(
    val key: String,
    val path: String,
    val status: DocumentStatus,
    val signers: List<Signer>? = null,
)

data class Signer(
    val key: String,
    @SerializedName("request_signature_key")
    val requestSignatureKey: String,
    val email: String,
    val name: String,
    @SerializedName("has_documentation")
    val hasDocumentation: Boolean,
    val documentation: String,
    val birthday: String,
    @SerializedName("phone_number")
    val phoneNumber: String,
    @SerializedName("sign_as")
    val signAs: String,
    @SerializedName("created_at")
    val createdAt: String,
    val signature: Signature? = null
)

data class Signature(
    val name: String,
    val email: String,
    val birthday: String,
    val documentation: String,
    val validation: Validation,
    @SerializedName("signed_at")
    val signedAt: String?
)

data class Validation(
    val status: String,
    val name: String
)

enum class DocumentStatus() {
    @SerializedName("running")
    RUNNING,

    @SerializedName("closed")
    CLOSED,

    @SerializedName("canceled")
    CANCELED;
}

fun DocumentStatus.isFinished(): Boolean =
    this == DocumentStatus.CLOSED || this == DocumentStatus.CANCELED

data class SignerInfoWithCertificate(
    val signer: ClicksignSignerData,
    val certificate: String
)
