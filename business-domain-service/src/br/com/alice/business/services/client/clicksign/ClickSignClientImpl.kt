package br.com.alice.business.services.client.clicksign

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import io.ktor.client.HttpClient
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import org.apache.commons.codec.binary.Hex
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec


class ClicksignClientImpl(
    private val config: ClicksignConfiguration,
    private val client: HttpClient,
) : ClicksignClient {

    override suspend fun assignSignerToDocument(
        documentKey: String,
        signerKey: String
    ): AssignSignerToDocumentResponseData {
        logger.info("ClicksignClientImpl::assignSignerToDocument")

        try {
            "${config.baseUrl}/api/v1/lists?access_token=${config.accessToken}".let { url ->
                client.post(url) {
                    contentType(ContentType.Application.Json)
                    setBody(
                        AssignSignerToDocumentRequest(
                            AssignSignerToDocumentRequestData(
                                document_key = documentKey,
                                signer_key = signerKey,
                            )
                        )
                    )
                }.let { response ->
                    val expectedResponseStatusCodes = listOf(HttpStatusCode.OK, HttpStatusCode.Created)

                    if (!expectedResponseStatusCodes.contains(response.status)) {
                        logger.error(
                            "Error assigning signer to document",
                            "status" to response.status,
                        )
                        throw Exception("Error assigning signer to document")
                    }

                    val body = response.bodyAsText()
                    val assignSignerToDocumentResponse = gson.fromJson<AssignSignerToDocumentResponse>(body)

                    logger.info(
                        "Signer assigned to document",
                        "document_key" to documentKey,

                        )

                    return assignSignerToDocumentResponse.list
                }
            }
        } catch (ex: Exception) {
            logger.error(
                "Error assigning signer to document",
                "message" to ex.message,
            )

            throw ex
        }
    }

    override suspend fun signDocument(assignResponse: AssignSignerToDocumentResponseData, certificate: String) {
        logger.info("ClicksignClientImpl::signDocument")

        try {
            "${config.baseUrl}/api/v1/sign?access_token=${config.accessToken}".let { url ->
                client.post(url) {
                    contentType(ContentType.Application.Json)
                    setBody(
                        SignDocumentRequest(
                            request_signature_key = assignResponse.request_signature_key,
                            secret_hmac_sha256 = generateHMACSha256(certificate, assignResponse.request_signature_key),
                        )
                    )
                }.let { response ->
                    if (response.status != HttpStatusCode.OK) {
                        logger.error("Error signing document", "status" to response.status)
                        throw Exception("Error signing document")
                    }

                    logger.info(
                        "Document signed successfully",
                        "document_key" to assignResponse.document_key,
                    )
                }
            }
        } catch (ex: Exception) {
            logger.error(
                "Error signing document",
                "message" to ex.message,
            )

            throw ex
        }
    }

    override suspend fun getDocument(documentKey: String): ClicksignResponseData {
        logger.info("ClicksignClientImpl::getDocument", "documentKey" to documentKey)

        return try {
            "${config.baseUrl}/api/v1/documents/${documentKey}?access_token=${config.accessToken}".let { url ->
                client.get(url) {
                }.let { response ->
                    logger.info("Document retrieved successfully", "document_key" to documentKey)

                    gson.fromJson<ClicksignResponseData>(response.bodyAsText())
                }
            }
        } catch (ex: Exception) {
            logger.error(
                "Error retrieving document",
                "message" to ex.message,
            )

            throw ex
        }
    }

    private fun generateHMACSha256(key: String, data: String): String {
        val sha256HMAC = Mac.getInstance("HmacSHA256")
        val secretKey = SecretKeySpec(key.toByteArray(charset("UTF-8")), "HmacSHA256")
        sha256HMAC.init(secretKey)

        return Hex.encodeHexString(sha256HMAC.doFinal(data.toByteArray(charset("UTF-8"))))
    }
}
