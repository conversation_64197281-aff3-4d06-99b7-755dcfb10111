package br.com.alice.business.services

import br.com.alice.business.ServiceConfig.Clicksign.signerResponsible
import br.com.alice.business.ServiceConfig.Clicksign.signerWitness
import br.com.alice.business.client.SignerAddedToContractService
import br.com.alice.business.exceptions.AlreadyFinishedDocumentException
import br.com.alice.business.services.client.clicksign.AssignSignerToDocumentResponseData
import br.com.alice.business.services.client.clicksign.ClicksignClient
import br.com.alice.business.services.client.clicksign.ClicksignResponseData
import br.com.alice.business.services.client.clicksign.SignerInfoWithCertificate
import br.com.alice.business.services.client.clicksign.isFinished
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.logging.logger
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class SignerAddedToContractServiceImpl(
    private val client: ClicksignClient
) : SignerAddedToContractService {

    override suspend fun signDocument(documentKey: String): Result<Boolean, Throwable> = catchResult {
        logger.info("Signing document", "documentKey" to documentKey)

        val documentResponse = client.getDocument(documentKey)

        logger.info(
            "Document response received",
            "document_key" to documentKey,
            "status" to documentResponse.document.status,
            "signers_name" to documentResponse.document.signers?.map { it.name }
        )

        if (documentResponse.document.status.isFinished()) {
            throw AlreadyFinishedDocumentException(
                "Document with key $documentKey is already finished."
            )
        }

        val responsibleAssigned = assignSigner(documentKey, signerResponsible)
        val witnessAssigned = assignSigner(documentKey, signerWitness)

        checkIfResponsibleAlreadySignedOrSign(signerResponsible, documentResponse, responsibleAssigned).also {
            checkIfWitnessAlreadySignedOrSign(signerWitness, documentResponse, witnessAssigned)
        }

        logger.info("Document signed", "documentKey" to documentKey)
        true.success()
    }

    private suspend fun checkIfResponsibleAlreadySignedOrSign(
        responsibleSigner: SignerInfoWithCertificate,
        response: ClicksignResponseData,
        signerAssignedDocumentResponse: AssignSignerToDocumentResponseData
    ) {
        val signer = response.document.signers?.firstOrNull { it.email == responsibleSigner.signer.email }

        if (signer?.signature?.signedAt != null) {
            logger.info("Responsible signer already signed the document")

            return
        }

        signWithSigner(responsibleSigner, signerAssignedDocumentResponse)
    }

    private suspend fun checkIfWitnessAlreadySignedOrSign(
        witnessSigner: SignerInfoWithCertificate,
        response: ClicksignResponseData,
        signerAssignedDocumentResponse: AssignSignerToDocumentResponseData
    ) {
        val signer = response.document.signers?.firstOrNull { it.email == witnessSigner.signer.email }

        if (signer?.signature?.signedAt != null) {
            logger.info("Witness signer already signed the document")

            return
        }

        signWithSigner(witnessSigner, signerAssignedDocumentResponse)
    }

    private suspend fun assignSigner(
        documentKey: String,
        signerInfoWithCertificate: SignerInfoWithCertificate
    ) =
        client.assignSignerToDocument(documentKey, signerInfoWithCertificate.signer.key)

    private suspend fun signWithSigner(
        signerInfoWithCertificate: SignerInfoWithCertificate,
        assignSignerResponseData: AssignSignerToDocumentResponseData
    ) =
        client.signDocument(assignSignerResponseData, signerInfoWithCertificate.certificate)
}


