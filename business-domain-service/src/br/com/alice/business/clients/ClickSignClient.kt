package br.com.alice.business.clients

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.service.serialization.gsonSnakeCase
import io.ktor.client.engine.apache5.Apache5
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation

fun clicksignClient() = DefaultHttpClient(Apache5.create(), {
    install(ContentNegotiation) {
        gsonSnakeCase()
    }
}, timeoutInMillis = 15_000)
