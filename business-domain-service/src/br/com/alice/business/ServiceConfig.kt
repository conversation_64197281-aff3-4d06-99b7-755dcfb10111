package br.com.alice.business

import br.com.alice.business.services.client.cassi.CassiConfiguration
import br.com.alice.business.services.client.clicksign.ClicksignConfiguration
import br.com.alice.business.services.client.clicksign.ClicksignSigner
import br.com.alice.business.services.client.clicksign.SignerInfoWithCertificate
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.serialization.gson
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotConfiguration
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig : BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {
    object Crm {
        fun hubspotConfig(): HubspotConfiguration {
            val apiBaseUrl = config("crm.hubspot.baseUrl")
            val apiKey = config("crm.hubspot.apiKey")
            val accessToken = config("crm.hubspot.accessToken")
            val dealPipeline = config("crm.hubspot.dealPipeline")
            val dealStage = config("crm.hubspot.dealStage")

            return HubspotConfiguration(apiKey, apiBaseUrl, accessToken, dealPipeline, dealStage)
        }

        fun hubspotConfigStaging(): HubspotConfiguration {
            val apiBaseUrl = config("crm.hubspot.baseUrl")
            val apiKey = config("crm.hubspot.apiKey")
            val accessToken = config("crm.hubspot.accessToken")
            val dealPipeline = config("crm.hubspot.staging.dealPipeline")
            val dealStage = config("crm.hubspot.staging.dealStage")

            return HubspotConfiguration(apiKey, apiBaseUrl, accessToken, dealPipeline, dealStage)
        }
    }

    object Mailer {
        val defaultSenderName = config("mailer.senderName")
        val defaultSenderEmail = config("mailer.senderEmail")
    }

    object SubEnv {
        val current = config.propertyOrNull("subEnv")?.getString()
    }

    object Cassi {
        private val authUrl = config("cassi.authUrl")
        private val baseUrl = config("cassi.baseUrl")
        private val baseUrlV1 = config("cassi.baseUrlV1")
        private val clientId = config("cassi.clientId")
        private val secret = config("cassi.secret")
        private val userKey = config("cassi.userKey")
        private val userKeyV1 = config("cassi.userKeyV1")

        fun config(): CassiConfiguration {
            return CassiConfiguration(
                baseUrl = baseUrl,
                baseUrlV1 = baseUrlV1,
                authUrl = authUrl,
                clientId = clientId,
                secret = secret,
                userKey = userKey,
                userKeyV1 = userKeyV1,
            )
        }
    }

    object Clicksign {
        fun config(): ClicksignConfiguration = ClicksignConfiguration(
            baseUrl = config("clicksign.baseUrl"),
            accessToken = config("clicksign.accessToken"),
            responsibleSigner = config("clicksign.responsibleSigner"),
            responsibleCertificate = config("clicksign.responsibleCertificate"),
            witnessSigner = config("clicksign.witnessSigner"),
            witnessCertificate = config("clicksign.witnessCertificate"),
        )

        val signerResponsible = SignerInfoWithCertificate(
            gson.fromJson<ClicksignSigner>(config().responsibleSigner).signer,
            certificate = config().responsibleCertificate
        )

        val signerWitness = SignerInfoWithCertificate(
            gson.fromJson<ClicksignSigner>(config().witnessSigner).signer,
            certificate = config().witnessCertificate
        )
    }

    object Receitaws {
        val authToken = config("receitaws.authToken")
    }

}
