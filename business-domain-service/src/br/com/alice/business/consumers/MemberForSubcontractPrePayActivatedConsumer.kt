package br.com.alice.business.consumers

import br.com.alice.business.events.MemberActivatedOnPrePaidSubcontractEvent
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.logging.logger
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.PreActivationPaymentExternalIdNullException
import br.com.alice.moneyin.client.PreActivationPaymentService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class MemberForSubcontractPrePayActivatedConsumer(
    private val preActivationPaymentService: PreActivationPaymentService,
    private val firstPaymentScheduleService: FirstPaymentScheduleService,
) : AutoRetryableConsumer(PreActivationPaymentExternalIdNullException::class) {

    suspend fun createFirstPaymentSchedule(event: MemberActivatedOnPrePaidSubcontractEvent) =
        withSubscribersEnvironment {
            val payload = event.payload
            val companyContract = payload.companyContract
            val companySubContract = payload.companySubcontract
            val company = payload.company

            logger.info(
                "MemberForSubcontractPrePayActivatedConsumer::createFirstPaymentSchedule - Creating first payment schedule",
                "company_id" to company.id,
                "company_subcontract_id" to companySubContract.id,
                "company_contract_id" to companyContract.id,
            )

            preActivationPaymentService.listByCompanyId(companySubContract.companyId)
                .flatMap { preActivationPayments ->
                    val paidPAP = preActivationPayments.firstOrNull { it.isPaid } ?: run {
                        logger.info("No paid pre-activation payment found for companyId: ${company.id}")

                        return@withSubscribersEnvironment false.success()
                    }

                    firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                        companyId = company.id,
                        companySubcontractId = companySubContract.id,
                        preActivationPaymentId = paidPAP.id
                    ).coFoldNotFound {
                        firstPaymentScheduleService.create(
                            FirstPaymentScheduleService.CreatePayload(
                                company = company,
                                companySubContract = companySubContract,
                                companyContract = companyContract,
                                preActivationPayment = paidPAP
                            )
                        )
                    }
                }.map { true }
        }
}
