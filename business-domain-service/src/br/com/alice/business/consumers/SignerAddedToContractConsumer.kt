package br.com.alice.business.consumers

import br.com.alice.business.client.SignerAddedToContractService
import br.com.alice.business.exceptions.AlreadyFinishedDocumentException
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.sales_channel.events.SignerAddedToContractEvent
import com.github.kittinunf.result.success
import io.ktor.client.plugins.HttpRequestTimeoutException

class SignerAddedToContractConsumer(
    private val signerAddedToContractService: SignerAddedToContractService,
) : AutoRetryableConsumer(HttpRequestTimeoutException::class) {

    suspend fun handler(event: SignerAddedToContractEvent) = withSubscribersEnvironment {
        val documentKey = event.payload.document.key

        signerAddedToContractService.signDocument(documentKey).then {
            logger.info("Contract has been signed by <PERSON> Signers", "document_key" to documentKey)
        }.coFoldError(AlreadyFinishedDocumentException::class to {
            logger.info("Document is already finished", "document_key" to documentKey)

            true.success()
        }).thenError {
            logger.error("Error signing document", "document_key" to documentKey, "error" to it)
        }
    }
}
