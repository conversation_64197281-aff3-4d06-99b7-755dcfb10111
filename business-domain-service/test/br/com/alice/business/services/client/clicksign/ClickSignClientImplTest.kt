package br.com.alice.business.services.client.clicksign

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.readFile
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class ClickSignClientImplTest {
    private val config: ClicksignConfiguration = mockk()

    @Test
    fun `assignSignerToDocument should return correct response for responsible signer`() = runBlocking {
        val documentKey = "docKey"
        val expectedResponse = AssignSignerToDocumentResponseData(
            "responseKey",
            "requestSignatureKey",
            "document_key",
            "signer_key",
            "sign_as",
            false,
            "created_at",
            "updated_at",
            "url",
        )

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"


        val client = ClicksignClientImpl(
            config,
            httpClientMock("{\"list\":{\"key\":\"responseKey\",\"request_signature_key\":\"requestSignatureKey\",\"document_key\":\"document_key\",\"signer_key\":\"signer_key\",\"sign_as\":\"sign_as\",\"refusable\":\"False\",\"created_at\":\"created_at\",\"updated_at\":\"updated_at\",\"url\":\"url\"}}")
        )

        val response = client.assignSignerToDocument(documentKey, "signer_key")

        assertEquals(expectedResponse, response)

        coVerifyOnce { config.baseUrl }
        coVerifyOnce { config.accessToken }
    }

    @Test
    fun `assignSignerToDocument should throw HttpTimeout when timeout happens`(): Unit = runBlocking {
        val documentKey = "docKey"

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"

        val httpClientMock = HttpClient(MockEngine) {
            expectSuccess = true
            install(HttpTimeout) {
                socketTimeoutMillis = 10
                requestTimeoutMillis = 10
            }
            install(ContentNegotiation) { gsonSnakeCase() }
            engine {
                addHandler { request ->
                    Thread.sleep(100)
                    respond(
                        "{\"list\":{\"key\":\"responseKey\",\"request_signature_key\":\"requestSignatureKey\",\"document_key\":\"document_key\",\"signer_key\":\"signer_key\",\"sign_as\":\"sign_as\",\"refusable\":\"False\",\"created_at\":\"created_at\",\"updated_at\":\"updated_at\",\"url\":\"url\"}}",
                        HttpStatusCode.OK
                    )
                }
            }
        }

        val client = ClicksignClientImpl(config, httpClientMock)

        assertFailsWith<HttpRequestTimeoutException> {
            client.assignSignerToDocument(documentKey, "signer_key")
        }
    }

    @Test
    fun `signDocument should return success on responsible sign`() = runBlocking {
        val assignResponse = AssignSignerToDocumentResponseData(
            "responseKey",
            "requestSignatureKey",
            "document_key",
            "signer_key",
            "sign_as",
            false,
            "created_at",
            "updated_at",
            "url",
        )

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"

        val client = ClicksignClientImpl(config, httpClientMock(""))

        client.signDocument(assignResponse, "testCertificate")

        coVerifyOnce { config.baseUrl }
        coVerifyOnce { config.accessToken }
    }

    @Test
    fun `signDocument should throw exception when error occurs`(): Unit = runBlocking {
        val assignResponse = AssignSignerToDocumentResponseData(
            "responseKey",
            "requestSignatureKey",
            "document_key",
            "signer_key",
            "sign_as",
            false,
            "created_at",
            "updated_at",
            "url",
        )

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"

        val client = ClicksignClientImpl(config, httpClientMock("", HttpStatusCode.InternalServerError))

        assertFailsWith<Exception> {
            client.signDocument(assignResponse, "testCertificate")
        }
    }

    @Test
    fun `#getDocument should retrieve document data successfully`() = runBlocking {
        val expectedResponseString = readFile("testResources/clicksign/clicksignResponseExample.json")
        val expectedResponse = gson.fromJson<ClicksignResponseData>(expectedResponseString)
        val documentKey = "55fea7c2-0e5b-40dc-bacf-50fb8529f866"
        val client = ClicksignClientImpl(
            config,
            httpClientMock(expectedResponseString)
        )

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"

        val result = client.getDocument(documentKey)

        assertEquals(result, expectedResponse)
        assertEquals(result.document.status, DocumentStatus.RUNNING)
        assertEquals(result.document.signers!!.first().signature!!.signedAt, "2025-06-10T13:25:24.864Z")

        coVerifyOnce { config.baseUrl }
        coVerifyOnce { config.accessToken }
    }

    @Test
    fun `#getDocument should retrieve document data successfully without signers`() = runBlocking {
        val expectedResponseString = readFile("testResources/clicksign/clicksignResponseExampleWithoutSigners.json")
        val expectedResponse = gson.fromJson<ClicksignResponseData>(expectedResponseString)
        val documentKey = "55fea7c2-0e5b-40dc-bacf-50fb8529f866"
        val client = ClicksignClientImpl(
            config,
            httpClientMock(expectedResponseString)
        )

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"

        val result = client.getDocument(documentKey)

        assertEquals(result, expectedResponse)
        assertEquals(result.document.signers, null)

        coVerifyOnce { config.baseUrl }
        coVerifyOnce { config.accessToken }
    }

    @Test
    fun `#getDocument should retrieve document data successfully without signatures`() = runBlocking {
        val expectedResponseString = readFile("testResources/clicksign/clicksignResponseExampleWithoutSignatures.json")
        val expectedResponse = gson.fromJson<ClicksignResponseData>(expectedResponseString)
        val documentKey = "55fea7c2-0e5b-40dc-bacf-50fb8529f866"
        val client = ClicksignClientImpl(
            config,
            httpClientMock(expectedResponseString)
        )

        coEvery { config.baseUrl } returns "https://sandbox.clicksign.com"
        coEvery { config.accessToken } returns "accessToken"

        val result = client.getDocument(documentKey)

        assertEquals(result, expectedResponse)
        assertEquals(result.document.signers!!.first().signature, null)

        coVerifyOnce { config.baseUrl }
        coVerifyOnce { config.accessToken }
    }
}

private fun httpClientMock(
    responseBody: String,
    statusCode: HttpStatusCode = HttpStatusCode.OK
): HttpClient = HttpClient(MockEngine) {
    expectSuccess = true
    install(ContentNegotiation) { gsonSnakeCase() }
    engine {
        addHandler { request ->
            respond(
                responseBody,
                statusCode
            )
        }
    }
}
