package br.com.alice.business.services

import br.com.alice.business.converters.model.toModel
import br.com.alice.business.converters.model.toTransport
import br.com.alice.business.events.CompanySubContractCreatedEvent
import br.com.alice.business.events.CompanySubContractUpdatedEvent
import br.com.alice.business.exceptions.SubContractInvalidDefaultProductIdException
import br.com.alice.business.exceptions.SubContractInvalidProductAvailableList
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.services.CompanySubContractModelDataService
import br.com.alice.product.client.ProductService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import kotlin.test.AfterTest
import kotlin.test.Test

class CompanySubContractServiceImplTest {
    private val companySubContractDataService: CompanySubContractModelDataService = mockk()
    private val productService: ProductService = mockk()
    private val companySubContractService =
        CompanySubContractServiceImpl(companySubContractDataService, productService, LocalProducer)

    private val companySubContract = TestModelFactory.buildCompanySubContract(externalId = "1234").toModel()

    @AfterTest
    fun clear() {
        LocalProducer.clearMessages()
        clearAllMocks()
    }

    @Nested
    inner class Add {

        @Test
        fun `#should add CompanySubContract with integration status PROCESSING when sendEvent is true`(): Unit = runBlocking {
            val product = TestModelFactory.buildProduct()
            val productId = product.id

            val companySubContract = companySubContract.copy(
                availableProducts = listOf(productId),
                defaultProductId = productId,
            )

            coEvery {
                productService.findByIds(
                    listOf(productId),
                    ProductService.FindOptions(false)
                )
            } returns listOf(product)

            coEvery { companySubContractDataService.add(any()) } returns companySubContract

            companySubContractService.add(companySubContract.toTransport(), true)

            coVerifyOnce {
                productService.findByIds(
                    listOf(productId),
                    ProductService.FindOptions(false),
                )
            }
            coVerifyOnce { companySubContractDataService.add(companySubContract.copy(integrationStatus = CompanySubContractIntegrationStatus.PROCESSING)) }
            Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractCreatedEvent.name)).isTrue()
        }

        @Test
        fun `#should add CompanySubContract with status DRAFT when sendEvent is false`(): Unit = runBlocking {
            val product = TestModelFactory.buildProduct()
            val productId = product.id

            val companySubContract = companySubContract.copy(
                availableProducts = listOf(productId),
                defaultProductId = productId,
            )

            coEvery {
                productService.findByIds(
                    listOf(productId),
                    ProductService.FindOptions(false)
                )
            } returns listOf(product)

            coEvery { companySubContractDataService.add(any()) } returns companySubContract

            companySubContractService.add(companySubContract.toTransport(), false)

            coVerifyOnce {
                productService.findByIds(
                    listOf(productId),
                    ProductService.FindOptions(false),
                )
            }
            coVerifyOnce { companySubContractDataService.add(companySubContract) }
            Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractCreatedEvent.name)).isFalse()
        }

        @Test
        fun `#shouldnt add CompanySubContract when default Id is not in product available ids list`(): Unit =
            runBlocking {
                val companySubContract = companySubContract.copy(
                    availableProducts = listOf(RangeUUID.generate()),
                    defaultProductId = RangeUUID.generate(),
                )

                val result = companySubContractService.add(companySubContract.toTransport())
                ResultAssert.assertThat(result).isFailureOfType(SubContractInvalidDefaultProductIdException::class)

                coVerifyNone { productService.findByIds(any(), any()) }
                coVerifyNone { companySubContractDataService.add(companySubContract) }
                Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractCreatedEvent.name)).isFalse()
            }

        @Test
        fun `#shouldnt add CompanySubContract when some product in product available ids list is invalid`(): Unit =
            runBlocking {
                val invalidProduct = TestModelFactory.buildProduct()
                val productId = invalidProduct.id

                val companySubContract = companySubContract.copy(
                    availableProducts = listOf(productId),
                    defaultProductId = productId,
                )

                coEvery {
                    productService.findByIds(
                        listOf(productId),
                        ProductService.FindOptions(false),
                    )
                } returns emptyList()

                val result = companySubContractService.add(companySubContract.toTransport())
                ResultAssert.assertThat(result).isFailureOfType(SubContractInvalidProductAvailableList::class)

                coVerifyOnce {
                    productService.findByIds(
                        listOf(productId),
                        ProductService.FindOptions(false),
                    )
                }
                coVerifyNone { companySubContractDataService.add(companySubContract) }
                Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractCreatedEvent.name)).isFalse()
            }
    }

    @Nested
    inner class Update {
        @Test
        fun `#should update CompanySubContract`(): Unit = runBlocking {
            val product = TestModelFactory.buildProduct()
            val productId = product.id

            val companySubContract = companySubContract.copy(
                availableProducts = listOf(productId),
                defaultProductId = productId,
            )

            val companySubContractUpdated = companySubContract.copy(
                title = "Updated SubContract",
                integrationStatus = CompanySubContractIntegrationStatus.PROCESSING,
            )

            coEvery { companySubContractDataService.update(any()) } returns companySubContractUpdated
            coEvery {
                productService.findByIds(
                    listOf(productId),
                    ProductService.FindOptions(false)
                )
            } returns listOf(product)


            val result = companySubContractService.update(companySubContractUpdated.toTransport())
            ResultAssert.assertThat(result).isSuccessWithData(companySubContractUpdated.toTransport())

            coVerifyOnce {
                productService.findByIds(
                    listOf(productId),
                    ProductService.FindOptions(false),
                )
            }
            coVerifyOnce { companySubContractDataService.update(companySubContractUpdated) }
            Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractUpdatedEvent.name)).isTrue()
        }

        @Test
        fun `#update should update CompanySubContract and do not produce an event when the send event arg is false`(): Unit =
            runBlocking {
                val product = TestModelFactory.buildProduct()
                val productId = product.id

                val companySubContract = companySubContract.copy(
                    availableProducts = listOf(productId),
                    defaultProductId = productId,
                )

                val companySubContractUpdated = companySubContract.copy(title = "Updated SubContract")

                coEvery { companySubContractDataService.update(any()) } returns companySubContractUpdated
                coEvery {
                    productService.findByIds(
                        listOf(productId),
                        ProductService.FindOptions(false)
                    )
                } returns listOf(product)


                val result = companySubContractService.update(companySubContractUpdated.toTransport(), false)
                ResultAssert.assertThat(result).isSuccessWithData(companySubContractUpdated.toTransport())

                coVerifyOnce {
                    productService.findByIds(
                        listOf(productId),
                        ProductService.FindOptions(false),
                    )
                }
                coVerifyOnce { companySubContractDataService.update(companySubContractUpdated) }
                Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractUpdatedEvent.name)).isFalse()
            }

        @Test
        fun `#shouldnt update CompanySubContract when default Id is not in product available ids list`(): Unit =
            runBlocking {
                val companySubContractUpdated = companySubContract.copy(
                    availableProducts = listOf(RangeUUID.generate()),
                    defaultProductId = RangeUUID.generate(),
                )

                val result = companySubContractService.update(companySubContractUpdated.toTransport())
                ResultAssert.assertThat(result).isFailureOfType(SubContractInvalidDefaultProductIdException::class)

                coVerifyNone { companySubContractDataService.update(companySubContractUpdated) }
                Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractUpdatedEvent.name)).isFalse()
            }

        @Test
        fun `#shouldnt update CompanySubContract when some product in product available ids list is invalid`(): Unit =
            runBlocking {
                val invalidProduct = TestModelFactory.buildProduct()
                val productId = invalidProduct.id

                val companySubContractUpdated = companySubContract.copy(
                    availableProducts = listOf(productId),
                    defaultProductId = productId,
                )

                coEvery {
                    productService.findByIds(
                        listOf(productId),
                        ProductService.FindOptions(false),
                    )
                } returns emptyList()

                val result = companySubContractService.update(companySubContractUpdated.toTransport())
                ResultAssert.assertThat(result).isFailureOfType(SubContractInvalidProductAvailableList::class)

                coVerifyOnce {
                    productService.findByIds(
                        listOf(productId),
                        ProductService.FindOptions(false),
                    )
                }
                coVerifyNone { companySubContractDataService.add(companySubContractUpdated) }
                Assertions.assertThat(LocalProducer.hasEvent(CompanySubContractUpdatedEvent.name)).isFalse()
            }
    }

    @Test
    fun `#should get CompanyContract`(): Unit = runBlocking {
        coEvery { companySubContractDataService.get(any()) } returns companySubContract

        companySubContractService.get(companySubContract.id)

        coVerifyOnce { companySubContractDataService.get(companySubContract.id) }
    }

    @Test
    fun `#should find by title CompanySubContract`(): Unit = runBlocking {
        val companySubContract = companySubContract.copy(title = "SubContract1")
        val companySubContract2 = companySubContract.copy(id = RangeUUID.generate(), title = "SubContract1")

        coEvery { companySubContractDataService.find(any()) } returns listOf(companySubContract, companySubContract2)

        val result = companySubContractService.findByTitle("SubContract")

        ResultAssert.assertThat(result).isSuccessWithData(
            listOf(companySubContract, companySubContract2).map { it.toTransport() }
        )

        coVerifyOnce { companySubContractDataService.find(queryEq { where { this.title.like("SubContract") } }) }
    }

    @Test
    fun `#should find CompanySubContract by a list of titles`(): Unit = runBlocking {
        val companySubContract = companySubContract.copy(title = "SubContract1")
        val companySubContract3 = companySubContract.copy(id = RangeUUID.generate(), title = "SubContract1")

        coEvery { companySubContractDataService.find(any()) } returns listOf(companySubContract, companySubContract3)

        val result = companySubContractService.findByTitles(listOf("SubContract1"))

        ResultAssert.assertThat(result).isSuccessWithData(
            listOf(companySubContract, companySubContract3).map { it.toTransport() }
        )

        coVerifyOnce { companySubContractDataService.find(queryEq { where { this.title.inList(listOf("SubContract1")) } }) }
    }

    @Test
    fun `#should find by companyId CompanySubContract`(): Unit = runBlocking {
        val companyId = RangeUUID.generate()
        val companySubContract = companySubContract.copy(companyId = companyId)
        val companySubContract2 = companySubContract.copy(id = RangeUUID.generate(), companyId = companyId)

        coEvery { companySubContractDataService.find(any()) } returns listOf(companySubContract, companySubContract2)

        val result = companySubContractService.findByCompanyId(companyId)

        ResultAssert.assertThat(result).isSuccessWithData(
            listOf(companySubContract, companySubContract2).map { it.toTransport() }
        )

        coVerifyOnce { companySubContractDataService.find(queryEq { where { this.companyId.eq(companyId) } }) }
    }

    @Test
    fun `#should find by companyIds`(): Unit = runBlocking {
        val companyId = RangeUUID.generate()
        val companySubContract = companySubContract.copy(companyId = companyId)
        val companySubContract2 = companySubContract.copy(id = RangeUUID.generate(), companyId = companyId)

        coEvery { companySubContractDataService.find(any()) } returns listOf(companySubContract, companySubContract2)

        val result = companySubContractService.findByCompanyIds(listOf(companyId))

        ResultAssert.assertThat(result).isSuccessWithData(
            listOf(companySubContract, companySubContract2).map { it.toTransport() }
        )

        coVerifyOnce { companySubContractDataService.find(queryEq { where { this.companyId.inList(listOf(companyId)) } }) }
    }

    @Test
    fun `#should find by contractId CompanySubContract`(): Unit = runBlocking {
        val contractId = RangeUUID.generate()
        val companySubContract = companySubContract.copy(contractId = contractId)
        val companySubContract2 = companySubContract.copy(id = RangeUUID.generate(), contractId = contractId)

        coEvery { companySubContractDataService.find(any()) } returns listOf(companySubContract, companySubContract2)

        val result = companySubContractService.findByContractId(contractId)

        ResultAssert.assertThat(result).isSuccessWithData(
            listOf(companySubContract, companySubContract2).map { it.toTransport() }
        )

        coVerifyOnce { companySubContractDataService.find(queryEq { where { this.contractId.eq(contractId) } }) }
    }

    @Test
    fun `#should find by ids CompanySubContract`(): Unit = runBlocking {
        val subcontractId = RangeUUID.generate()
        val companySubContract = companySubContract.copy(id = subcontractId)
        val companySubContract2 = companySubContract.copy(id = subcontractId)

        coEvery { companySubContractDataService.find(any()) } returns listOf(companySubContract, companySubContract2)

        val result = companySubContractService.findByIds(listOf(companySubContract.id, companySubContract2.id))

        ResultAssert.assertThat(result).isSuccessWithData(
            listOf(companySubContract, companySubContract2).map { it.toTransport() }
        )

        coVerifyOnce {
            companySubContractDataService.find(queryEq {
                where {
                    this.id.inList(
                        listOf(
                            companySubContract.id,
                            companySubContract2.id
                        )
                    )
                }
            })
        }
    }
}
