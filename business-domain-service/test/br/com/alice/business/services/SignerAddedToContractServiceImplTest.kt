package br.com.alice.business.services

import br.com.alice.business.exceptions.AlreadyFinishedDocumentException
import br.com.alice.business.services.client.clicksign.AssignSignerToDocumentResponseData
import br.com.alice.business.services.client.clicksign.ClicksignClient
import br.com.alice.business.services.client.clicksign.ClicksignResponseData
import br.com.alice.business.services.client.clicksign.ClicksignSigner
import br.com.alice.business.services.client.clicksign.ClicksignSignerData
import br.com.alice.business.services.client.clicksign.Document
import br.com.alice.business.services.client.clicksign.DocumentStatus
import br.com.alice.business.services.client.clicksign.Signature
import br.com.alice.business.services.client.clicksign.Signer
import br.com.alice.business.services.client.clicksign.Validation
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class SignerAddedToContractServiceImplTest : MockedTestHelper() {

    private val clicksignClient: ClicksignClient = mockk()
    private val signerAddedToContractService = SignerAddedToContractServiceImpl(clicksignClient)

    @Test
    fun `signDocument should return true when document is already finished`() = runBlocking {
        val documentKey = "documentKey"

        val documentResponse = ClicksignResponseData(
            document = Document(
                key = documentKey,
                path = "path",
                status = DocumentStatus.CLOSED,
                signers = emptyList()
            ),
        )

        coEvery { clicksignClient.getDocument(documentKey) } returns documentResponse

        val result = signerAddedToContractService.signDocument(documentKey)

        assertThat(result).isFailureOfType(AlreadyFinishedDocumentException::class)

        coVerifyOnce { clicksignClient.getDocument(any()) }
    }

    @Test
    fun `signDocument should return true and sign document for both signers when document is not signed by responsible and witness`() =
        runBlocking {
            val documentKey = "documentKey"
            val responsibleSignerData = ClicksignSignerData(
                key = "db25dce7-e7f6-4228-bd72-245c37767914",
                email = "<EMAIL>",
                auths = listOf("api"),
                delivery = "email",
                name = "Testemunha da Silva",
                documentation = "000.000.000-00",
                selfie_enabled = false,
                handwritten_enabled = false,
                birthday = "1983-03-31",
                phone_number = "11999999999",
                has_documentation = true,
                created_at = "2024-08-26T15:28:42.248-03:00",
                updated_at = "2024-08-26T15:28:42.248-03:00",
                official_document_enabled = false,
                liveness_enabled = false,
                facial_biometrics_enabled = false,
                communicate_by = "email",
                location_required_enabled = false
            )

            val responsibleSigner = ClicksignSigner(
                signer = responsibleSignerData
            )

            val witnessSignerData = ClicksignSignerData(
                key = "6ba567f5-e959-461b-968c-3744b1f06623",
                email = "<EMAIL>",
                auths = listOf("api"),
                delivery = "email",
                name = "Testemunha da Silva Junior",
                documentation = "000.000.000-01",
                selfie_enabled = false,
                handwritten_enabled = false,
                birthday = "1983-03-31",
                phone_number = "11999999998",
                has_documentation = true,
                created_at = "2024-08-26T15:28:42.248-03:00",
                updated_at = "2024-08-26T15:28:42.248-03:00",
                official_document_enabled = false,
                liveness_enabled = false,
                facial_biometrics_enabled = false,
                communicate_by = "email",
                location_required_enabled = false
            )

            val witnessSigner = ClicksignSigner(
                signer = witnessSignerData
            )

            val responsibleResponseData = AssignSignerToDocumentResponseData(
                responsibleSigner.signer.key,
                "requestSignatureKey1",
                documentKey,
                responsibleSigner.signer.key,
                "",
                true,
                "",
                "",
                ""
            )

            val witnessResponseData = AssignSignerToDocumentResponseData(
                witnessSigner.signer.key,
                "requestSignatureKey2",
                documentKey,
                witnessSigner.signer.key,
                "",
                true,
                "",
                "",
                ""
            )

            val documentResponse = ClicksignResponseData(
                document = Document(
                    key = documentKey,
                    path = "path",
                    status = DocumentStatus.RUNNING,
                    signers = emptyList()
                ),
            )

            coEvery { clicksignClient.getDocument(documentKey) } returns documentResponse
            coEvery {
                clicksignClient.assignSignerToDocument(
                    documentKey,
                    responsibleSigner.signer.key
                )
            } returns responsibleResponseData
            coEvery { clicksignClient.signDocument(responsibleResponseData, "responsibleCertificate") } returns Unit
            coEvery {
                clicksignClient.assignSignerToDocument(
                    documentKey,
                    witnessSigner.signer.key
                )
            } returns witnessResponseData
            coEvery { clicksignClient.signDocument(witnessResponseData, "witnessCertificate") } returns Unit

            val result = signerAddedToContractService.signDocument(documentKey)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { clicksignClient.getDocument(any()) }
            coVerify(exactly = 2) { clicksignClient.assignSignerToDocument(any(), any()) }
            coVerify(exactly = 2) { clicksignClient.signDocument(any(), any()) }
        }

    @Test
    fun `signDocument should return true and sign document for witness only when document is already signed by responsible`() =
        runBlocking {
            val documentKey = "documentKey"
            val responsibleSignerData = ClicksignSignerData(
                key = "db25dce7-e7f6-4228-bd72-245c37767914",
                email = "<EMAIL>",
                auths = listOf("api"),
                delivery = "email",
                name = "Testemunha da Silva",
                documentation = "000.000.000-00",
                selfie_enabled = false,
                handwritten_enabled = false,
                birthday = "1983-03-31",
                phone_number = "11999999999",
                has_documentation = true,
                created_at = "2024-08-26T15:28:42.248-03:00",
                updated_at = "2024-08-26T15:28:42.248-03:00",
                official_document_enabled = false,
                liveness_enabled = false,
                facial_biometrics_enabled = false,
                communicate_by = "email",
                location_required_enabled = false
            )

            val responsibleSigner = ClicksignSigner(
                signer = responsibleSignerData
            )

            val witnessSignerData = ClicksignSignerData(
                key = "6ba567f5-e959-461b-968c-3744b1f06623",
                email = "<EMAIL>",
                auths = listOf("api"),
                delivery = "email",
                name = "Testemunha da Silva Junior",
                documentation = "000.000.000-01",
                selfie_enabled = false,
                handwritten_enabled = false,
                birthday = "1983-03-31",
                phone_number = "11999999998",
                has_documentation = true,
                created_at = "2024-08-26T15:28:42.248-03:00",
                updated_at = "2024-08-26T15:28:42.248-03:00",
                official_document_enabled = false,
                liveness_enabled = false,
                facial_biometrics_enabled = false,
                communicate_by = "email",
                location_required_enabled = false
            )

            val witnessSigner = ClicksignSigner(
                signer = witnessSignerData
            )

            val responsibleResponseData = AssignSignerToDocumentResponseData(
                responsibleSigner.signer.key,
                "requestSignatureKey1",
                documentKey,
                responsibleSigner.signer.key,
                "",
                true,
                responsibleSigner.signer.created_at,
                responsibleSigner.signer.updated_at,
                ""
            )

            val witnessResponseData = AssignSignerToDocumentResponseData(
                witnessSigner.signer.key,
                "requestSignatureKey2",
                documentKey,
                witnessSigner.signer.key,
                "",
                true,
                witnessSigner.signer.created_at,
                witnessSigner.signer.updated_at,
                ""
            )

            val documentResponse = ClicksignResponseData(
                document = Document(
                    key = documentKey,
                    path = "path",
                    status = DocumentStatus.RUNNING,
                    signers = listOf(
                        Signer(
                            key = responsibleSigner.signer.key,
                            requestSignatureKey = responsibleResponseData.request_signature_key,
                            email = responsibleSigner.signer.email,
                            name = responsibleSigner.signer.name,
                            hasDocumentation = false,
                            documentation = responsibleSigner.signer.documentation,
                            birthday = responsibleSigner.signer.birthday,
                            phoneNumber = responsibleSigner.signer.phone_number,
                            signAs = "sign",
                            createdAt = responsibleSigner.signer.created_at,
                            signature = Signature(
                                name = responsibleSigner.signer.name,
                                email = responsibleSigner.signer.email,
                                birthday = responsibleSigner.signer.birthday,
                                documentation = responsibleSigner.signer.documentation,
                                validation = Validation(
                                    status = "conferred",
                                    name = responsibleSigner.signer.name
                                ),
                                signedAt = "2024-08-26T15:28:42.248-03:00"
                            )
                        )
                    )
                ),
            )

            coEvery { clicksignClient.getDocument(documentKey) } returns documentResponse

            coEvery {
                clicksignClient.assignSignerToDocument(
                    documentKey,
                    responsibleSigner.signer.key
                )
            } returns witnessResponseData
            coEvery {
                clicksignClient.assignSignerToDocument(
                    documentKey,
                    witnessSigner.signer.key
                )
            } returns witnessResponseData
            coEvery { clicksignClient.signDocument(witnessResponseData, "witnessCertificate") } returns Unit

            val result = signerAddedToContractService.signDocument(documentKey)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { clicksignClient.getDocument(any()) }
            coVerify(exactly = 2) { clicksignClient.assignSignerToDocument(any(), any()) }
            coVerifyOnce { clicksignClient.signDocument(any(), any()) }
        }

    @Test
    fun `signDocument should return true and sign document for responsible only when document is already signed by witness`() =
        runBlocking {
            val documentKey = "documentKey"
            val responsibleSigner = ClicksignSigner(
                signer = ClicksignSignerData(
                    key = "db25dce7-e7f6-4228-bd72-245c37767914",
                    email = "<EMAIL>",
                    auths = listOf("api"),
                    delivery = "email",
                    name = "Testemunha da Silva",
                    documentation = "000.000.000-00",
                    selfie_enabled = false,
                    handwritten_enabled = false,
                    birthday = "1983-03-31",
                    phone_number = "11999999999",
                    has_documentation = true,
                    created_at = "2024-08-26T15:28:42.248-03:00",
                    updated_at = "2024-08-26T15:28:42.248-03:00",
                    official_document_enabled = false,
                    liveness_enabled = false,
                    facial_biometrics_enabled = false,
                    communicate_by = "email",
                    location_required_enabled = false
                )
            )

            val witnessSigner = ClicksignSigner(
                signer = ClicksignSignerData(
                    key = "6ba567f5-e959-461b-968c-3744b1f06623",
                    email = "<EMAIL>",
                    auths = listOf("api"),
                    delivery = "email",
                    name = "Testemunha da Silva Junior",
                    documentation = "000.000.000-01",
                    selfie_enabled = false,
                    handwritten_enabled = false,
                    birthday = "1983-03-31",
                    phone_number = "11999999998",
                    has_documentation = true,
                    created_at = "2024-08-26T15:28:42.248-03:00",
                    updated_at = "2024-08-26T15:28:42.248-03:00",
                    official_document_enabled = false,
                    liveness_enabled = false,
                    facial_biometrics_enabled = false,
                    communicate_by = "email",
                    location_required_enabled = false
                )
            )

            val responsibleResponseData = AssignSignerToDocumentResponseData(
                responsibleSigner.signer.key,
                "requestSignatureKey1",
                documentKey,
                responsibleSigner.signer.key,
                "",
                true,
                "",
                "",
                ""
            )

            val witnessResponseData = AssignSignerToDocumentResponseData(
                witnessSigner.signer.key,
                "requestSignatureKey2",
                documentKey,
                witnessSigner.signer.key,
                "",
                true,
                "",
                "",
                ""
            )

            val documentResponse = ClicksignResponseData(
                document = Document(
                    key = documentKey,
                    path = "path",
                    status = DocumentStatus.RUNNING,
                    signers = listOf(
                        Signer(
                            key = witnessSigner.signer.key,
                            requestSignatureKey = witnessResponseData.request_signature_key,
                            email = witnessSigner.signer.email,
                            name = witnessSigner.signer.name,
                            hasDocumentation = false,
                            documentation = witnessSigner.signer.documentation,
                            birthday = witnessSigner.signer.birthday,
                            phoneNumber = witnessSigner.signer.phone_number,
                            signAs = "sign",
                            createdAt = witnessSigner.signer.created_at,
                            signature = Signature(
                                name = witnessSigner.signer.name,
                                email = witnessSigner.signer.email,
                                birthday = witnessSigner.signer.birthday,
                                documentation = witnessSigner.signer.documentation,
                                validation = Validation(
                                    status = "conferred",
                                    name = witnessSigner.signer.name
                                ),
                                signedAt = "2024-08-26T15:28:42.248-03:00"
                            )
                        )
                    )
                ),
            )

            coEvery { clicksignClient.getDocument(documentKey) } returns documentResponse
            coEvery {
                clicksignClient.assignSignerToDocument(
                    documentKey,
                    responsibleSigner.signer.key
                )
            } returns responsibleResponseData
            coEvery {
                clicksignClient.assignSignerToDocument(
                    documentKey,
                    witnessSigner.signer.key
                )
            } returns witnessResponseData
            coEvery { clicksignClient.signDocument(responsibleResponseData, "responsibleCertificate") } returns Unit

            val result = signerAddedToContractService.signDocument(documentKey)

            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { clicksignClient.getDocument(any()) }
            coVerify(exactly = 2) { clicksignClient.assignSignerToDocument(any(), any()) }
            coVerifyOnce { clicksignClient.signDocument(any(), any()) }
        }
}
