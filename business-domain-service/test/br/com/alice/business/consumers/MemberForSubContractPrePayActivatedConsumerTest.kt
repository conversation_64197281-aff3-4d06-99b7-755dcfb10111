package br.com.alice.business.consumers

import br.com.alice.business.events.MemberActivatedOnPrePaidSubcontractEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.client.PreActivationPaymentExternalIdNullException
import br.com.alice.moneyin.client.PreActivationPaymentService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class MemberForSubContractPrePayActivatedConsumerTest : ConsumerTest() {
    private val preActivationPaymentService: PreActivationPaymentService = mockk()
    private val firstPaymentScheduleService: FirstPaymentScheduleService = mockk()

    private val consumer = MemberForSubcontractPrePayActivatedConsumer(
        preActivationPaymentService = preActivationPaymentService,
        firstPaymentScheduleService = firstPaymentScheduleService,
    )
    val id = RangeUUID.generate()
    val localDateTime = LocalDateTime.now()

    @Test
    fun `#createFirstPaymentSchedule should create first payment schedule when is not found`() =
        runBlocking {
            val company = TestModelFactory.buildCompany()
            val companyContract = TestModelFactory.buildCompanyContract(
                paymentType = PaymentModel.PRE_PAY
            )
            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                contractId = companyContract.id
            )

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                companyId = company.id,
                companySubContractId = companySubContract.id,
                status = PreActivationPaymentStatus.PAID
            )

            val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule(
                id = id,
                preActivationPaymentId = preActivationPayment.id,
                companyId = company.id,
                companySubcontractId = companySubContract.id,
                createdAt = localDateTime,
                updatedAt = localDateTime
            )

            val event = MemberActivatedOnPrePaidSubcontractEvent(
                companyContract = companyContract,
                companySubcontract = companySubContract,
                company = company
            )

            coEvery { preActivationPaymentService.listByCompanyId(companySubContract.companyId) } returns listOf(
                preActivationPayment
            )

            coEvery {
                firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                    company.id,
                    companySubContract.id,
                    preActivationPayment.id
                )
            } returns NotFoundException()

            coEvery {
                firstPaymentScheduleService.create(
                    FirstPaymentScheduleService.CreatePayload(
                        company = company,
                        companyContract = companyContract,
                        companySubContract = companySubContract,
                        preActivationPayment = preActivationPayment,
                    )
                )
            } returns firstPaymentSchedule

            val result = consumer.createFirstPaymentSchedule(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                preActivationPaymentService.listByCompanyId(any())
                firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                    any(),
                    any(),
                    any()
                )
                firstPaymentScheduleService.create(any())
            }
        }

    @Test
    fun `#createFirstPaymentSchedule should return false when pap is not paid`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val companyContract = TestModelFactory.buildCompanyContract(
            paymentType = PaymentModel.PRE_PAY
        )
        val companySubContract = TestModelFactory.buildCompanySubContract(
            companyId = company.id,
            contractId = companyContract.id
        )

        val preActivationPayment = TestModelFactory.buildPreActivationPayment(
            companyId = company.id,
            companySubContractId = companySubContract.id,
            status = PreActivationPaymentStatus.PROCESSING
        )

        val event = MemberActivatedOnPrePaidSubcontractEvent(
            companyContract = companyContract,
            companySubcontract = companySubContract,
            company = company
        )

        coEvery { preActivationPaymentService.listByCompanyId(companySubContract.companyId) } returns listOf(
            preActivationPayment
        )


        val result = consumer.createFirstPaymentSchedule(event)

        ResultAssert.assertThat(result).isSuccessWithData(false)

        coVerifyOnce { preActivationPaymentService.listByCompanyId(any()) }
        coVerifyNone {
            firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                any(),
                any(),
                any()
            )
        }
        coVerifyNone { firstPaymentScheduleService.create(any()) }
    }

    @Test
    fun `#createFirstPaymentSchedule should return first payment schedule when it exists and should not create a new one`() =
        runBlocking {
            val company = TestModelFactory.buildCompany()
            val companyContract = TestModelFactory.buildCompanyContract(
                paymentType = PaymentModel.PRE_PAY
            )
            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                contractId = companyContract.id
            )

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                companyId = company.id,
                companySubContractId = companySubContract.id,
                status = PreActivationPaymentStatus.PAID
            )

            val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule(
                preActivationPaymentId = preActivationPayment.id,
                companyId = company.id,
                companySubcontractId = companySubContract.id,
            )

            val event = MemberActivatedOnPrePaidSubcontractEvent(
                companyContract = companyContract,
                companySubcontract = companySubContract,
                company = company
            )

            coEvery { preActivationPaymentService.listByCompanyId(companySubContract.companyId) } returns listOf(
                preActivationPayment
            )

            coEvery {
                firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                    company.id,
                    companySubContract.id,
                    preActivationPayment.id
                )
            } returns firstPaymentSchedule

            val result = consumer.createFirstPaymentSchedule(event)

            ResultAssert.assertThat(result).isSuccessWithData(true)

            coVerifyOnce { preActivationPaymentService.listByCompanyId(any()) }
            coVerifyOnce {
                firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                    any(),
                    any(),
                    any()
                )
            }
        }

    @Test
    fun `#createFirstPaymentSchedule should return AutoRetryableException when PreActivationPaymentExternalIdNullException is thrown`() =
        runBlocking {
            val company = TestModelFactory.buildCompany()
            val companyContract = TestModelFactory.buildCompanyContract(
                paymentType = PaymentModel.PRE_PAY
            )
            val companySubContract = TestModelFactory.buildCompanySubContract(
                companyId = company.id,
                contractId = companyContract.id
            )

            val preActivationPayment = TestModelFactory.buildPreActivationPayment(
                companyId = company.id,
                companySubContractId = companySubContract.id,
                status = PreActivationPaymentStatus.PAID,
                externalId = null
            )

            val event = MemberActivatedOnPrePaidSubcontractEvent(
                companyContract = companyContract,
                companySubcontract = companySubContract,
                company = company
            )

            coEvery { preActivationPaymentService.listByCompanyId(companySubContract.companyId) } returns listOf(
                preActivationPayment
            )

            coEvery {
                firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                    company.id,
                    companySubContract.id,
                    preActivationPayment.id
                )
            } returns NotFoundException()

            coEvery {
                firstPaymentScheduleService.create(
                    FirstPaymentScheduleService.CreatePayload(
                        company = company,
                        companyContract = companyContract,
                        companySubContract = companySubContract,
                        preActivationPayment = preActivationPayment,
                    )
                )
            } returns PreActivationPaymentExternalIdNullException(preActivationPayment.id)

            val result = consumer.createFirstPaymentSchedule(event)

            ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)

            coVerifyOnce {
                preActivationPaymentService.listByCompanyId(companySubContract.companyId)
                firstPaymentScheduleService.findByCompanyIdSubContractIdAndPreActivationPaymentId(
                    company.id,
                    companySubContract.id,
                    preActivationPayment.id
                )
                firstPaymentScheduleService.create(
                    FirstPaymentScheduleService.CreatePayload(
                        company = company,
                        companyContract = companyContract,
                        companySubContract = companySubContract,
                        preActivationPayment = preActivationPayment,
                    )
                )
            }
        }
}


