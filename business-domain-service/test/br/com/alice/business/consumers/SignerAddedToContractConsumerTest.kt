package br.com.alice.business.consumers

import br.com.alice.business.client.SignerAddedToContractService
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.sales_channel.events.ClicksignAccount
import br.com.alice.sales_channel.events.ClicksignDocument
import br.com.alice.sales_channel.events.ClicksignDocumentDownloads
import br.com.alice.sales_channel.events.ClicksignEvent
import br.com.alice.sales_channel.events.ClicksignEventData
import br.com.alice.sales_channel.events.ClicksignLinks
import br.com.alice.sales_channel.events.ClicksignPayload
import br.com.alice.sales_channel.events.ClicksignSigner
import br.com.alice.sales_channel.events.ClicksignUser
import br.com.alice.sales_channel.events.SignerAddedToContractEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class SignerAddedToContractConsumerTest : ConsumerTest() {

    private val signerAddedToContractService: SignerAddedToContractService = mockk()
    private val signerAddedToContractConsumer = SignerAddedToContractConsumer(signerAddedToContractService)

    @Test
    fun `handler should call signDocument from signerAddedToContractService and return true`() =
        mockRangeUuidAndDateTime { uuid, localDateTime ->
            runBlocking {
                val documentKey = uuid.toString()
                val event = SignerAddedToContractEvent(
                    clicksignPayload = ClicksignPayload(
                        event = ClicksignEvent(
                            name = "upload",
                            data = ClicksignEventData(
                                user = ClicksignUser(
                                    email = "<EMAIL>",
                                    name = "José Teste"
                                ),
                                account = ClicksignAccount(
                                    key = uuid.toString()
                                ),
                                signers = listOf(
                                    ClicksignSigner(
                                        signAs = "sign",
                                        key = uuid.toString(),
                                        email = "<EMAIL>",
                                        name = "Test",
                                        birthday = null,
                                        documentation = null,
                                        hasDocumentation = false,
                                        createdAt = localDateTime.toString(),
                                        auths = null,
                                        selfieEnabled = false,
                                        handwrittenEnabled = false,
                                        officialDocumentEnabled = false,
                                        livenessEnabled = false,
                                        facialBiometricsEnabled = false,
                                        communicateBy = null,
                                        listKey = null,
                                        url = null,
                                        readReceipts = null
                                    )
                                ),
                            ),
                            occurredAt = localDateTime.toString()
                        ),
                        document = ClicksignDocument(
                            key = uuid.toString(),
                            accountKey = uuid.toString(),
                            path = "/Contrato de Prestação de Serviços-321.pdf",
                            filename = "Contrato de Prestação de Serviços-321.pdf",
                            uploadedAt = "2024-08-27T19:48:53.740Z",
                            updatedAt = "2024-08-27T19:48:53.750Z",
                            finishedAt = null,
                            deadlineAt = "2024-09-05T14:30:59.000-03:00",
                            status = "running",
                            autoClose = true,
                            locale = "pt-BR",
                            metadata = emptyMap(),
                            sequenceEnabled = false,
                            signableGroup = 0,
                            remindInterval = null,
                            blockAfterRefusal = true,
                            preview = true,
                            downloads = ClicksignDocumentDownloads(
                                originalFileUrl = "fileUrl"
                            ),
                            template = null,
                            signers = emptyList(),
                            events = listOf(
                                ClicksignEvent(
                                    name = "upload",
                                    data = ClicksignEventData(
                                        user = ClicksignUser(
                                            email = "<EMAIL>",
                                            name = "José Teste"
                                        ),
                                        account = ClicksignAccount(
                                            key = uuid.toString()
                                        ),
                                        signers = emptyList(),
                                    ),
                                    occurredAt = "2024-08-27T19:48:53.780Z"
                                )
                            ),
                            attachments = emptyList(),
                            links = ClicksignLinks(
                                self = "link"
                            )
                        )

                    )
                )

                coEvery { signerAddedToContractService.signDocument(documentKey) } returns true.success()

                val result = signerAddedToContractConsumer.handler(event)

                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { signerAddedToContractService.signDocument(documentKey) }
            }
        }

    @Test
    fun `handler should call signDocument and throw AutoRetryableException when HttpRequestTimeoutException happens`() =
        mockRangeUuidAndDateTime { uuid, localDateTime ->
            runBlocking {
                val documentKey = uuid.toString()
                val event = SignerAddedToContractEvent(
                    clicksignPayload = ClicksignPayload(
                        event = ClicksignEvent(
                            name = "upload",
                            data = ClicksignEventData(
                                user = ClicksignUser(
                                    email = "<EMAIL>",
                                    name = "José Teste"
                                ),
                                account = ClicksignAccount(
                                    key = uuid.toString()
                                ),
                                signers = listOf(
                                    ClicksignSigner(
                                        signAs = "sign",
                                        key = uuid.toString(),
                                        email = "<EMAIL>",
                                        name = "Test",
                                        birthday = null,
                                        documentation = null,
                                        hasDocumentation = false,
                                        createdAt = localDateTime.toString(),
                                        auths = null,
                                        selfieEnabled = false,
                                        handwrittenEnabled = false,
                                        officialDocumentEnabled = false,
                                        livenessEnabled = false,
                                        facialBiometricsEnabled = false,
                                        communicateBy = null,
                                        listKey = null,
                                        url = null,
                                        readReceipts = null
                                    )
                                ),
                            ),
                            occurredAt = localDateTime.toString()
                        ),
                        document = ClicksignDocument(
                            key = uuid.toString(),
                            accountKey = uuid.toString(),
                            path = "/Contrato de Prestação de Serviços-321.pdf",
                            filename = "Contrato de Prestação de Serviços-321.pdf",
                            uploadedAt = "2024-08-27T19:48:53.740Z",
                            updatedAt = "2024-08-27T19:48:53.750Z",
                            finishedAt = null,
                            deadlineAt = "2024-09-05T14:30:59.000-03:00",
                            status = "running",
                            autoClose = true,
                            locale = "pt-BR",
                            metadata = emptyMap(),
                            sequenceEnabled = false,
                            signableGroup = 0,
                            remindInterval = null,
                            blockAfterRefusal = true,
                            preview = true,
                            downloads = ClicksignDocumentDownloads(
                                originalFileUrl = "fileUrl"
                            ),
                            template = null,
                            signers = emptyList(),
                            events = listOf(
                                ClicksignEvent(
                                    name = "upload",
                                    data = ClicksignEventData(
                                        user = ClicksignUser(
                                            email = "<EMAIL>",
                                            name = "José Teste"
                                        ),
                                        account = ClicksignAccount(
                                            key = uuid.toString()
                                        ),
                                        signers = emptyList(),
                                    ),
                                    occurredAt = "2024-08-27T19:48:53.780Z"
                                )
                            ),
                            attachments = emptyList(),
                            links = ClicksignLinks(
                                self = "link"
                            )
                        )

                    )
                )

                coEvery { signerAddedToContractService.signDocument(documentKey) } returns HttpRequestTimeoutException(
                    "url",
                    null
                ).failure()

                val result = signerAddedToContractConsumer.handler(event)

                assertThat(result).fails().withCause(HttpRequestTimeoutException::class)
                    .ofType(AutoRetryableException::class)

                coVerifyOnce { signerAddedToContractService.signDocument(documentKey) }
            }
        }
}
