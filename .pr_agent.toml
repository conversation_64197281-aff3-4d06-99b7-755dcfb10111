[pr_reviewer]
extra_instructions="""
Project Languages / Libraries / Frameworks

- Kotlin 1.7 on Java 17
- Ktor 2.3 for web application development
- Koin 3.4 for dependency injection
- Google gson for json serde
- Junit 5, AssertJ and Mockk for unit tests
- AWS java client for cloud integrations

Standard Project Structure

- commons: contains common libraries used by all modules, not deployed
- data-packages: contains the data definitions and data access code for all domains, not deployed
- data-layer: contains the code for the application that interacts directly with the database, is deployed as a standalone service
- domain-clients / integration-clients: contains the client interfaces for the domain services, not deployed
- domain-services / integration-services: contains the implementations of the interfaces specified in domain-clients, is deployed as a standalone service
- bffs / apis: orchestrates calls to domain services and exposes a single API to the front-end, is deployed as a standalone service

Reviewing new code

- point out whenever the new code includes terms that minorities may find offensive,
such as "blacklist / whitelist", "master / slave" and so on, and suggest better alternatives.
- avoid logging sensitive or classified information whenever possible.
- reinforce the project structure, discouraging the creation of code elements outside their expected packages.
- avoid suggesting the use of libraries alternative to those already in the project (for example, jackson's objectmapper instead of the project's gson)

Reviewing tests

- Most of the tests depend heavily on mocking to work. Make sure that stubbing (every / coEvery) matches specific arguments whenever possible (i.e. do not use any())
- On the other hand, verifying (verify / coVerify) may check for specific or generic arguments, but generic (i.e. any()) is preferred.
- Make sure tests that need static mocking (mockkObject / mockkStatic) unmock the objects by the end of the test class.
- Prefer to create new UUID instances with the RangeUUID.generate() method instead of UUID.randomUUID()

Refer to Kotlin and Ktor documentation for best practices and security considerations.
"""

[pr_code_suggestions]
extra_instructions="""
Project Languages / Libraries / Frameworks

- Kotlin 1.7 on Java 17
- Ktor 2.3 for web application development
- Koin 3.4 for dependency injection
- Google gson for json serde
- Junit 5, AssertJ and Mockk for unit tests
- AWS java client for cloud integrations

Standard Project Structure

- commons: contains common libraries used by all modules, not deployed
- data-packages: contains the data definitions and data access code for all domains, not deployed
- data-layer: contains the code for the application that interacts directly with the database, is deployed as a standalone service
- domain-clients / integration-clients: contains the client interfaces for the domain services, not deployed
- domain-services / integration-services: contains the implementations of the interfaces specified in domain-clients, is deployed as a standalone service
- bffs / apis: orchestrates calls to domain services and exposes a single API to the front-end, is deployed as a standalone service

Reviewing new code

- point out whenever the new code includes terms that minorities may find offensive,
such as "blacklist / whitelist", "master / slave" and so on, and suggest better alternatives.
- avoid logging sensitive or classified information whenever possible.
- reinforce the project structure, discouraging the creation of code elements outside their expected packages.
- avoid suggesting the use of libraries alternative to those already in the project (for example, jackson's objectmapper instead of the project's gson)

Reviewing tests

- Most of the tests depend heavily on mocking to work. Make sure that stubbing (every / coEvery) matches specific arguments whenever possible (i.e. do not use any())
- On the other hand, verifying (verify / coVerify) may check for specific or generic arguments, but generic (i.e. any()) is preferred.
- Make sure tests that need static mocking (mockkObject / mockkStatic) unmock the objects by the end of the test class.
- Prefer to create new UUID instances with the RangeUUID.generate() method instead of UUID.randomUUID()

Refer to Kotlin and Ktor documentation for best practices and security considerations.
"""

