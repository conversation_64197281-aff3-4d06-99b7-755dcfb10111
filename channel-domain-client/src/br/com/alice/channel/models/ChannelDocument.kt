package br.com.alice.channel.models

import br.com.alice.authentication.UserType
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.models.AdministrativeDetails
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelCategory.ASSISTANCE
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelKind.CHANNEL
import br.com.alice.data.layer.models.ChannelScreeningNavigation
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategory.ACUTE
import br.com.alice.data.layer.models.ChannelSubCategory.LONGITUDINAL
import br.com.alice.data.layer.models.ChannelSubCategory.MULTI
import br.com.alice.data.layer.models.ChannelSubCategory.SCREENING
import br.com.alice.data.layer.models.ChannelSubCategory.VIRTUAL_CLINIC
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier.AI
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier.HEALTH_TEAM
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier.PHYSICAL_PREPARATION
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.ChannelType.ADMINISTRATIVE
import br.com.alice.data.layer.models.ChannelType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.ChannelType.CHAT
import br.com.alice.data.layer.models.ChannelType.HEALTH_PLAN
import br.com.alice.data.layer.models.ChannelType.NUTRITION
import br.com.alice.data.layer.models.ChannelType.PHYSICAL_ACTIVITY
import br.com.alice.data.layer.models.Demand
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.RejectVideoCallReason
import br.com.alice.data.layer.models.Screening
import br.com.alice.data.layer.models.ScreeningStatus
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StarredAction
import br.com.alice.data.layer.models.VideoCallStatus
import br.com.alice.data.layer.models.VirtualClinicStatusControl
import br.com.alice.staff.converters.StaffGenderDescriptionConverter
import com.google.cloud.Timestamp
import com.google.cloud.firestore.annotation.Exclude
import java.util.UUID

const val acuteImmediateTag = "agudo_imediato"
const val emergencyNotAlarmSignal = "emergencia_sem_sinal_de_alarme"
const val budTriageTag = "bud_triage"
const val followUpTag = "reacionamento"
const val inProgressTag = "em_andamento"
const val redirectTag = "redirect"
const val criticalTag = "crítico"
const val redirectedToN2ChannelTag = "redirecionado_n2"
const val waitingForN2ChannelTag = "aguardando_n2"
const val healthPlanChannelName = "Time de Saúde"
const val nutritionChannelName = "Nutrição & você"
const val physicalActivityChannelName = "Preparação física & você"
const val psychologistChannelName = "Psicologia & você"
const val administrativeChatName = "Atendimento Administrativo"
const val firstAccess = "primeiro_acesso"

const val modalContentImagePrefix = "https://alice-member-app-assets.s3.amazonaws.com/channels/"

val modalContent = listOf(
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_selfie.svg",
        "Dê preferência a um ambiente silencioso"
    ),
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_alice_agora.svg",
        "O profissional está conferindo suas informações"
    ),
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_plan.svg",
        "Estamos preparando o seu atendimento"
    )
)

val modalContent2 = listOf(
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_0.svg",
        "Dê preferência a um ambiente reservado e silencioso"
    ),
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_1.svg",
        "O tempo de espera é estimado e já já sua consulta vai começar"
    ),
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_2.svg",
        "Evite entrar na consulta sem camisa ou com trajes íntimos"
    ),
    DynamicContent(
        "https://alice-member-app-assets.s3.amazonaws.com/channels/queue_experience_3.svg",
        "Estamos preparando o seu atendimento"
    )
)

data class ChannelDocument(
    @get:Exclude
    override val id: String? = null,
    val channelPersonId: String,
    val personId: String,
    val name: String? = null,
    val type: ChannelType? = null,
    val status: ChannelStatus = ChannelStatus.ACTIVE,
    val staff: MutableMap<String, ChannelStaffInfo> = mutableMapOf(),
    val staffHistory: MutableMap<String, ChannelStaffInfo> = mutableMapOf(),
    val staffIds: List<String>? = staff.keys.toList(),
    val tags: List<String> = emptyList(),
    val timeLastMessage: Timestamp? = null,
    val isArchived: Boolean = false,
    val archivedAt: Timestamp? = null,
    val archivedReason: ChannelArchivedReason? = null,
    val canBeArchived: Boolean? = true,
    val createdAt: Timestamp = Timestamp.now(),
    val updatedAt: Timestamp? = Timestamp.now(),
    val lastSync: Timestamp? = Timestamp.now(),
    val origin: String? = null,
    val mergedWith: String? = null,
    val appVersion: String? = null,
    val unreadMessages: Long? = 0,
    val becameAsyncAt: Timestamp? = null,
    val channelPersonType: UserType? = null,
    val inProgress: Boolean? = null,
    val privateTyping: List<String>? = emptyList(),
    val typing: List<String>? = emptyList(),
    val waitingSince: Timestamp? = null,
    val followUp: Map<String, Any>? = null,
    val lastPreviewableMessage: MessageDocument? = null,
    val starred: StarredAction? = null,
    val screening: Screening? = null,
    val onCallAction: String? = null,
    val isOnCall: Boolean = false,
    val hasAppointment: Boolean = false,
    val hideMemberInput: Boolean = false,
    val hasQuestionnaireInProgress: Boolean = false,
    val isWaiting: Boolean? = false,
    val kind: ChannelKind? = null,
    val category: ChannelCategory? = null,
    val subCategory: ChannelSubCategory? = null,
    val subCategoryClassifier: ChannelSubCategoryClassifier? = null,
    val demands: List<Demand> = emptyList(),
    val appointmentIds: List<UUID> = emptyList(),
    val routingTo: List<String> = emptyList(),
    val csatUrl: String? = null,
    val csatAnswered: Boolean = false,
    val videoCall: VideoCall? = null,
    val screeningStatus: ScreeningStatus? = null,
    val screeningFinishedAt: Timestamp? = null,
    val screeningNavigation: ChannelScreeningNavigation? = null,
    val discharge: RedirectTo? = null,
    val virtualClinicStatusControl: VirtualClinicStatusControl? = null,
    val segment: ChannelSegment? = null,
    val inactiveAt: Timestamp? = null,
    val digitalCareQueueModalInfo: ModalInfo? = null,
    val zendeskTicketId: String? = null,
    val aiExternalId: String? = null,
    val administrativeDetails: AdministrativeDetails? = null,
    val aiInstructions: String? = null,
    val protocolAns: String? = null,
    val routedAt: Timestamp? = null
) : FirestoreDocument {
    @Exclude
    fun getOwner() = staff.values.firstOrNull { it.owner }?.id?.toUUID()

    @Exclude
    fun isChat() = kind == ChannelKind.CHAT || type == CHAT

    @Exclude
    fun hasDemands() = demands.isNotEmpty()

    @Exclude
    fun isAssistance() = category == ASSISTANCE

    fun isAdministrative() = category == ChannelCategory.ADMINISTRATIVE

    fun isVirtualClinic() = subCategory == VIRTUAL_CLINIC

    fun isScreening() = subCategory == SCREENING

    fun isAI() = subCategoryClassifier == AI

    @Exclude
    fun staffInHistory(staffId: String) = staffHistory[staffId] != null

    @Exclude
    fun getAllStaffIds() = (staffHistory.keys + staff.keys).toList()

    @Exclude
    fun formattedAppVersion() = appVersion?.let {
        Regex("\\d{1,2}\\.\\d{1,2}\\.\\d{1,2}").find(it)?.groupValues?.first()
    } ?: "0.0.0"

    @Exclude
    fun semanticAppVersion() = SemanticVersion(formattedAppVersion())

    fun cannotBeArchived() = canBeArchived != true

    fun getIsWaiting(): Boolean = isWaiting == true

    fun id() = id!!

    fun isActive() = status == ChannelStatus.ACTIVE

    fun memberIsTyping() = typing?.contains(channelPersonId) ?: false

    @Exclude
    fun populateTypes(): ChannelDocument {
        if (kind != null && type != null) return this.copy(type = null).populateTypes()
        if (kind == null && type == null) throw IllegalArgumentException("Channel not have any classification, type or kind")

        return when (type) {
            null -> {
                when {
                    kind == ChannelKind.CHAT -> copy(type = CHAT)
                    subCategoryClassifier == HEALTH_TEAM -> copy(type = HEALTH_PLAN)
                    subCategoryClassifier == ChannelSubCategoryClassifier.NUTRITION -> copy(type = NUTRITION)
                    subCategoryClassifier == PHYSICAL_PREPARATION -> copy(type = PHYSICAL_ACTIVITY)
                    category == ChannelCategory.ADMINISTRATIVE -> copy(type = ADMINISTRATIVE)
                    category == ASSISTANCE -> copy(type = ASSISTANCE_CARE)
                    else -> copy(type = ASSISTANCE_CARE).also {
                        logger.warn(
                            "ChannelDocument::populateTypes: possible classification inconsistency",
                            "channel_id" to it.id,
                            "type" to it.type,
                            "kind" to it.kind,
                            "category" to it.category,
                            "sub_category" to it.subCategory,
                            "sub_category_classifier" to it.subCategoryClassifier
                        )
                    }
                }
            }

            CHAT -> copy(kind = ChannelKind.CHAT)
            ASSISTANCE_CARE -> copy(kind = CHANNEL, category = ASSISTANCE, subCategory = ACUTE)
            ADMINISTRATIVE -> copy(kind = CHANNEL, category = ChannelCategory.ADMINISTRATIVE)
            HEALTH_PLAN -> copy(
                kind = CHANNEL,
                category = ASSISTANCE,
                subCategory = LONGITUDINAL,
                subCategoryClassifier = HEALTH_TEAM
            )

            NUTRITION -> copy(
                kind = CHANNEL,
                category = ASSISTANCE,
                subCategory = MULTI,
                subCategoryClassifier = ChannelSubCategoryClassifier.NUTRITION
            )

            PHYSICAL_ACTIVITY -> copy(
                kind = CHANNEL,
                category = ASSISTANCE,
                subCategory = MULTI,
                subCategoryClassifier = PHYSICAL_PREPARATION
            )
        }
    }

    fun plusDemand(demand: Demand) =
        copy(demands = this.demands.filter { it.caseId != demand.caseId }.plus(demand))

    fun plusDemands(demands: List<Demand>) =
        demands.map { it.caseId }.let { demandIds ->
            copy(demands = this.demands.filter { !demandIds.contains(it.caseId) }.plus(demands))
        }

    fun plusAppointmentId(appointmentId: UUID) =
        copy(appointmentIds = this.appointmentIds.plus(appointmentId).distinct())

    fun plusAppointmentIds(appointmentIds: List<UUID>) =
        copy(appointmentIds = this.appointmentIds.plus(appointmentIds).distinct())
}

data class ChannelStaffInfo(
    val id: String,
    val name: String? = null,
    val firstName: String,
    val lastName: String,
    val description: String,
    val profileImageUrl: String,
    val owner: Boolean = false,
    val lastSync: Timestamp = Timestamp.now(),
    val unreadMessages: Long? = 0,
    val privateUnreadMessages: Long? = 0,
    val privateUnreadMentions: Long? = 0,
    val highlight: Boolean = false,
    val onBackground: Boolean = false,
    @get:Exclude
    val roles: List<Role> = emptyList()
) {

    companion object {
        fun from(staff: Staff) =
            ChannelStaffInfo(
                id = staff.id.toString(),
                name = staff.firstName,
                firstName = staff.firstName,
                lastName = staff.lastName,
                profileImageUrl = staff.profileImageUrl.orEmpty(),
                description = StaffGenderDescriptionConverter.convert(staff)
            )
    }

    fun totalUnreadMessages() = (unreadMessages ?: 0) + (privateUnreadMessages ?: 0)

    fun fullName() = "${this.firstName} ${this.lastName}"
}

data class VideoCall(
    val showButton: Boolean? = false,
    val amazonChime: VideoCallInfo? = null,
    val startedAt: Timestamp? = null,
    val finishedAt: Timestamp? = null,
    val rejectVideoCallReason: RejectVideoCallReason? = null
)

enum class MemberProductType {
    B2B,
    B2C
}

data class VideoCallInfo(
    val status: String,
    val url: String? = null,
    val id: String? = null
) {
    fun isWaitingMember() = status == VideoCallStatus.WAITING_MEMBER.name
}

enum class ChannelSegment {
    ADULT,
    PEDIATRIC;

    companion object {
        fun fromTeamSegment(segment: HealthcareTeam.Segment) =
            when (segment) {
                HealthcareTeam.Segment.DEFAULT -> ADULT
                HealthcareTeam.Segment.PEDIATRIC -> PEDIATRIC
            }
    }
}

data class ModalInfo(
    val content: List<DynamicContent> = modalContent,
    val medicalCareAt: Timestamp,
    val positionInQueue: Int? = null,
)

data class DynamicContent(
    val imageUrl: String,
    val description: String
)
