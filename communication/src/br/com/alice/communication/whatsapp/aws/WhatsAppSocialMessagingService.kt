package br.com.alice.communication.whatsapp.aws

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.thenLogOnFailure
import br.com.alice.communication.whatsapp.aws.transport.SocialMessagingRequestTransport
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.future.await
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.socialmessaging.SocialMessagingAsyncClient
import software.amazon.awssdk.services.socialmessaging.model.SendWhatsAppMessageRequest
import java.nio.charset.Charset

class WhatsAppSocialMessagingService(
    private val asyncClient: SocialMessagingAsyncClient,
    private val gson: Gson = defaultGson,
) {

    suspend fun sendMessage(request: SocialMessagingRequestTransport) = coResultOf<Unit, Throwable> {
        val bytes = SdkBytes.fromString(
            gson.toJson(request.whatsAppMessageRequest).toString(),
            Charset.defaultCharset()
        )

        val messageRequest = SendWhatsAppMessageRequest.builder()
            .message(bytes)
            .originationPhoneNumberId(request.fromPhoneNumberId)
            .metaApiVersion(request.metaApiVersion)
            .build()

        asyncClient.sendWhatsAppMessage(messageRequest)
            .await()

    }.thenLogOnFailure(
        "from_number" to request.fromPhoneNumberId,
        "message_type" to request.whatsAppMessageRequest.type
    ) { "Error sending WhatsApp message." }

    private companion object {
        val defaultGson: Gson = GsonBuilder()
            .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
            .create()
    }
}
