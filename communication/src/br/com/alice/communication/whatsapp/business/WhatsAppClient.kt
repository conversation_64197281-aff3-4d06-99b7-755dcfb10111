package br.com.alice.communication.whatsapp.business

import br.com.alice.common.extensions.TResult
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.communication.ServiceConfig
import br.com.alice.communication.whatsapp.transport.WhatsAppMessage
import br.com.alice.communication.whatsapp.transport.WhatsAppMessageResponse
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.call.body
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType

val recommendClientEngine: HttpClientConfig<*>.() -> Unit = {
    install(ContentNegotiation) { gsonSnakeCase() }
    install(HttpRequestRetry) {
        exponentialDelay(maxDelayMs = ServiceConfig.WhatsAppBusinessApi.Retry.maxDelayMsOnRetry)
        retryOnExceptionOrServerErrors(ServiceConfig.WhatsAppBusinessApi.Retry.maxRetries)
    }
}

class WhatsAppClient(private val engine: HttpClient) {

    suspend fun sendMessage(
        messageRequest: WhatsAppMessage,
        phoneNumberId: String? = null,
    ): TResult<WhatsAppMessageResponse> = coResultOf {
        val endpoint = with(ServiceConfig.WhatsAppBusinessApi) {
            "$baseUrl/${phoneNumberId ?: this.defaultPhoneNumberId}/messages"
        }

        engine.post(urlString = endpoint) {
            header("Authorization", "Bearer ${ServiceConfig.WhatsAppBusinessApi.token}")
            contentType(ContentType.Application.Json)
            setBody(messageRequest)
        }.body()
    }
}
