package br.com.alice.communication.whatsapp.transport.message.interactive

data class WhatsAppInteractiveMessageAction(
    val button: String? = null,
    val buttons: List<WhatsAppInteractiveMessageButton>? = null,
    val sections: List<WhatsAppInteractiveMessageSection>? = null,
) {

    init {
        if (button != null) require(button.length <= 20) { "button must be <= 20 characters" }
        if (buttons != null) require(buttons.size in 1..3) { "buttons must be >= 1 and <= 3" }
    }
}
