package br.com.alice.communication.whatsapp.transport.message.template

import com.google.gson.annotations.SerializedName

data class WhatsAppMessageTemplateComponent(
    val type: Type,
    val subType: SubType? = null,
    val index: String? = null,
    val parameters: List<WhatsAppMessageTemplateComponentParameter>,
) {

    init {
        if (type == Type.BUTTON) {
            require(subType != null) { "subType must be filled for button type" }
            require(index != null) { "index must be filled for button type" }
        }
    }

    enum class Type {
        @SerializedName("body")
        BODY,

        @SerializedName("header")
        HEADER,

        @SerializedName("footer")
        FOOTER,

        @SerializedName("button")
        BUTTON
    }

    enum class SubType {

        @SerializedName("quick_reply")
        QUICK_REPLY,

        @SerializedName("url")
        URL
    }
}
