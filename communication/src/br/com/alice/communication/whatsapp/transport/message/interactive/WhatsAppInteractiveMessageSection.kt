package br.com.alice.communication.whatsapp.transport.message.interactive

data class WhatsAppInteractiveMessageSection(
    val title: String? = null,
    val rows: List<WhatsAppInteractiveMessageSectionRow>,
) {

    init {
        require(rows.isNotEmpty()) { "rows must be filled" }
        require(rows.size <= 10) { "rows must be <= 10" }
        if (title != null) require(title.length <= 24) { "title must be <= 24 characters" }
    }
}
