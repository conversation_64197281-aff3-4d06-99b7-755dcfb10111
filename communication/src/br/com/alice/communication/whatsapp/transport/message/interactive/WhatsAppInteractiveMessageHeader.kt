package br.com.alice.communication.whatsapp.transport.message.interactive

import br.com.alice.communication.whatsapp.transport.WhatsAppMidia
import com.google.gson.annotations.SerializedName

data class WhatsAppInteractiveMessageHeader(
    val type: MessageHeaderType,
    val document: WhatsAppMidia? = null,
    val image: WhatsAppMidia? = null,
    val text: String? = null,
    val subText: String? = null,
    val video: WhatsAppMidia? = null,
) {

    init {
        if (type == MessageHeaderType.DOCUMENT) require(document != null) { "document must be filled" }
        if (type == MessageHeaderType.IMAGE) require(image != null) { "image must be filled" }
        if (type == MessageHeaderType.TEXT) require(text != null) { "text must be filled" }
        if (type == MessageHeaderType.VIDEO) require(video != null) { "video must be filled" }
    }

    enum class MessageHeaderType {
        @SerializedName("document")
        DOCUMENT,

        @SerializedName("image")
        IMAGE,

        @SerializedName("text")
        TEXT,

        @SerializedName("video")
        VIDEO
    }
}
