package br.com.alice.communication.whatsapp.transport

import br.com.alice.communication.whatsapp.transport.message.interactive.WhatsAppInteractiveMessage
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplate
import br.com.alice.communication.whatsapp.transport.message.text.WhatsAppTextMessage
import com.google.gson.annotations.SerializedName

data class WhatsAppMessage(
    val messagingProduct: MessagingProduct = MessagingProduct.WHATSAPP,
    val recipientType: RecipientType = RecipientType.INDIVIDUAL,
    val to: String,
    val type: MessageType = MessageType.TEMPLATE,
    val text: WhatsAppTextMessage? = null,
    val template: WhatsAppMessageTemplate? = null,
    val interactive: WhatsAppInteractiveMessage? = null,
) {
    init {
        if (type == MessageType.TEXT) require(text != null) { "text property must be filled" }
        if (type == MessageType.TEMPLATE) require(template != null) { "template property must be filled" }
        if (type == MessageType.INTERACTIVE) require(interactive != null) { "interactive property must be filled" }
    }

    enum class MessageType {
        @SerializedName("text")
        TEXT,

        @SerializedName("template")
        TEMPLATE,

        @SerializedName("interactive")
        INTERACTIVE
    }

    enum class MessagingProduct {
        @SerializedName("whatsapp")
        WHATSAPP
    }

    enum class RecipientType {
        @SerializedName("individual")
        INDIVIDUAL
    }
}
