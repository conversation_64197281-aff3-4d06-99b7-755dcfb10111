package br.com.alice.communication.whatsapp.transport.message.template

import br.com.alice.communication.whatsapp.transport.WhatsAppMidia
import com.google.gson.annotations.SerializedName

data class WhatsAppMessageTemplateComponentParameter(
    val type: Type,
    val currency: WhatsAppMessageTemplateCurrency? = null,
    val dateTime: WhatsAppMessageTemplateDateTime? = null,
    val document: WhatsAppMidia? = null,
    val image: WhatsAppMidia? = null,
    val text: String? = null,
    val video: WhatsAppMidia? = null,
) {

    init {
        if (type == Type.CURRENCY) require(currency != null) { "currency must be filled" }
        if (type == Type.DATE_TIME) require(dateTime != null) { "dateTime must be filled" }
        if (type == Type.DOCUMENT) require(document != null) { "document must be filled" }
        if (type == Type.IMAGE) require(image != null) { "image must be filled" }
        if (type == Type.TEXT) require(text != null) { "text must be filled" }
        if (type == Type.VIDEO) require(video != null) { "video must be filled" }
    }

    enum class Type {
        @SerializedName("currency")
        CURRENCY,

        @SerializedName("date_time")
        DATE_TIME,

        @SerializedName("document")
        DOCUMENT,

        @SerializedName("image")
        IMAGE,

        @SerializedName("text")
        TEXT,

        @SerializedName("video")
        VIDEO
    }
}
