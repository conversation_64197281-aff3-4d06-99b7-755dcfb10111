package br.com.alice.communication.whatsapp.transport.message.template

data class WhatsAppMessageTemplateCurrency(
    val fallbackValue: String,
    val code: String,
    val amount1000: Int,
) {

    init {
        require(fallbackValue.isNotBlank()) { "fallbackValue must be filled" }
        require(code.isNotBlank()) { "code must be filled" }
        require(amount1000 >= 0) { "amount1000 must be >= 0" }
    }
}
