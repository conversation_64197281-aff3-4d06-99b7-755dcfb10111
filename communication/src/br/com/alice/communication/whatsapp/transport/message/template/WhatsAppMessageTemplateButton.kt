package br.com.alice.communication.whatsapp.transport.message.template

import com.google.gson.annotations.SerializedName

data class WhatsAppMessageTemplateButton(
    val type: Type,
    val payload: String? = null,
    val text: String? = null,
) {

    init {
        if (type == Type.URL) require(text != null) { "text must be filled for URL type" }
        if (type == Type.QUICK_REPLY) require(payload != null) { "text must be filled for QUICK_REPLY type" }
    }

    enum class Type {
        @SerializedName("url")
        URL,

        @SerializedName("quick_reply")
        QUICK_REPLY
    }
}
