package br.com.alice.communication.whatsapp.transport.message.interactive

import com.google.gson.annotations.SerializedName

data class WhatsAppInteractiveMessage(
    val action: WhatsAppInteractiveMessageAction,
    val body: WhatsAppInteractiveMessageBody? = null,
    val footer: WhatsAppInteractiveMessageFooter? = null,
    val header: WhatsAppInteractiveMessageHeader? = null,
    val type: InteractiveMessageType,
) {

    init {
        if (type == InteractiveMessageType.LIST) {
            require(action.sections != null) { "action.sections must be filled for list type" }
        }

        if (type == InteractiveMessageType.BUTTON) {
            require(action.buttons != null) { "action.buttons must be filled for button type" }
            require(body != null) { "body must be filled for button type" }
        }
    }

    enum class InteractiveMessageType {
        @SerializedName("button")
        BUTTON,

        @SerializedName("list")
        LIST
    }
}
