package br.com.alice.communication.whatsapp.transport.message.interactive

data class WhatsAppInteractiveMessageButtonReply(
    val id: String,
    val title: String,
) {

    init {
        require(id.isNotBlank()) { "id must be filled" }
        require(id.length <= 256) { "id must be <= 256 characters" }
        require(title.isNotBlank()) { "title must be filled" }
        require(title.length <= 20) { "title must be <= 20 characters" }
    }
}
