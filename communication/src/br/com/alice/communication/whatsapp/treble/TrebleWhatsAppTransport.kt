package br.com.alice.communication.whatsapp.treble


data class TrebleDeploymentRequest(
    val users: List<TrebleDeploymentRequestUser> = emptyList()
)

data class TrebleDeploymentRequestUser(
    val cellphone: String,
    val countryCode: String,
    val userSessionKeys: List<TrebleDeploymentRequestUserSessionKey> = emptyList(),
    val deploymentEta: Int? = null,
)

data class TrebleDeploymentRequestUserSessionKey(
    val key: String,
    val value: String,
)

data class TrebleDeploymentResponse(
    val message: String,
    val id: String,
    val batchId: String,
    val newLastScheduledDeployment: String,
    val conversationsId: List<String>,
)
