package br.com.alice.communication.whatsapp.treble

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.serialization.gson
import io.ktor.client.HttpClient
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType

class TrebleWhatsAppClient(
    private val trebleWhatsAppConfiguration: TrebleWhatsAppConfiguration,
    private val httpClient: HttpClient
) {

    suspend fun deployConversation(pollId: String, users: List<TrebleDeploymentRequestUser>): TrebleDeploymentResponse =
        httpClient.post("${trebleWhatsAppConfiguration.apiUrl}/deployment/api/poll/$pollId") {
            header(HttpHeaders.Authorization, trebleWhatsAppConfiguration.apiKey)
            contentType(ContentType.Application.Json)
            setBody(TrebleDeploymentRequest(users = users))
        }.bodyAsText()
            .let { gson.fromJson<TrebleDeploymentResponse>(it) }
}

data class TrebleWhatsAppConfiguration(
    val apiUrl: String,
    val apiKey: String
)


