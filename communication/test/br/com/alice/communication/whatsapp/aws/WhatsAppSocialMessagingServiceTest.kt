package br.com.alice.communication.whatsapp.aws

import br.com.alice.communication.whatsapp.aws.transport.SocialMessagingRequestTransport
import br.com.alice.communication.whatsapp.transport.WhatsAppMessage
import br.com.alice.communication.whatsapp.transport.message.text.WhatsAppTextMessage
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.socialmessaging.SocialMessagingAsyncClient
import software.amazon.awssdk.services.socialmessaging.model.SendWhatsAppMessageRequest
import java.util.concurrent.CompletableFuture
import kotlin.test.Test

class WhatsAppSocialMessagingServiceTest {

    private val asyncClient: SocialMessagingAsyncClient = mockk()

    @Test
    fun `given an whatsapp message when send message is called then it should send the message successfully`() {
        val service = WhatsAppSocialMessagingService(asyncClient)

        val request = SocialMessagingRequestTransport(
            fromPhoneNumberId = "1234567890",
            whatsAppMessageRequest = WhatsAppMessage(
                to = "1234567809",
                type = WhatsAppMessage.MessageType.TEXT,
                text = WhatsAppTextMessage(
                    body = "Hello, this is a test message!"
                )
            )
        )

        val expectedRequest = SendWhatsAppMessageRequest.builder()
            .message(
                SdkBytes.fromString(
                    """{"messaging_product":"whatsapp","recipient_type":"individual","to":"1234567809","type":"text","text":{"preview_url":true,"body":"Hello, this is a test message!"}}""",
                    Charsets.UTF_8
                )
            )
            .originationPhoneNumberId("1234567890")
            .metaApiVersion("v21.0")
            .build()

        every {
            asyncClient.sendWhatsAppMessage(any<SendWhatsAppMessageRequest>())
        } returns CompletableFuture.completedFuture(mockk())

        runBlocking { service.sendMessage(request) }

        verify { asyncClient.sendWhatsAppMessage(expectedRequest) }
    }
}
