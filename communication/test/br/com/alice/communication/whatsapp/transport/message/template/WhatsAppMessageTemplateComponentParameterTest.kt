package br.com.alice.communication.whatsapp.transport.message.template

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppMessageTemplateComponentParameterTest {

    @Test
    fun `given an currency type parameter when create with no currency then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponentParameter(
                type = WhatsAppMessageTemplateComponentParameter.Type.CURRENCY,
                currency = null
            )
        }
        assertEquals("currency must be filled", exception.message)
    }

    @Test
    fun `given a date_time type parameter when create with no dateTime then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponentParameter(
                type = WhatsAppMessageTemplateComponentParameter.Type.DATE_TIME,
                dateTime = null
            )
        }
        assertEquals("dateTime must be filled", exception.message)
    }

    @Test
    fun `given a document type parameter when create with no document then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponentParameter(
                type = WhatsAppMessageTemplateComponentParameter.Type.DOCUMENT,
                document = null
            )
        }
        assertEquals("document must be filled", exception.message)
    }

    @Test
    fun `given an image type parameter when create with no image then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponentParameter(
                type = WhatsAppMessageTemplateComponentParameter.Type.IMAGE,
                image = null
            )
        }
        assertEquals("image must be filled", exception.message)
    }

    @Test
    fun `given a text type parameter when create with no text then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponentParameter(
                type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                text = null
            )
        }
        assertEquals("text must be filled", exception.message)
    }

    @Test
    fun `given a video type parameter when create with no video then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponentParameter(
                type = WhatsAppMessageTemplateComponentParameter.Type.VIDEO,
                video = null
            )
        }
        assertEquals("video must be filled", exception.message)
    }
}
