package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppInteractiveMessageSectionTest {

    @Test
    fun `given empty rows, when create section, then throw exception`() {
        assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageSection(
                title = "Test Section",
                rows = emptyList()
            )
        }
    }

    @Test
    fun `given more than 10 rows, when create section, then throw exception`() {
        val manyRows = List(11) { WhatsAppInteractiveMessageSectionRow("id$it", "Row $it") }
        assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageSection(
                title = "Test Section",
                rows = manyRows
            )
        }
    }
}
