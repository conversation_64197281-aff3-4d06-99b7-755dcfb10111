package br.com.alice.communication.whatsapp.transport.message.template


import org.junit.jupiter.api.assertThrows
import kotlin.test.Test
import kotlin.test.assertEquals

class WhatsAppMessageTemplateButtonTest {

    @Test
    fun `given an empty text, when creating a WhatsAppMessageTemplateButton, then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateButton(text = "", type = WhatsAppMessageTemplateButton.Type.QUICK_REPLY)
        }
        assertEquals("text must be filled for QUICK_REPLY type", exception.message)
    }

    @Test
    fun `given an type URL and no text, when creating a WhatsAppMessageTemplateButton, then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateButton(type = WhatsAppMessageTemplateButton.Type.URL)
        }
        assertEquals("text must be filled for URL type", exception.message)
    }
}
