package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppInteractiveMessageTest {

    @Test
    fun `given a list interactive message with no section, when created, then trows exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessage(
                action = WhatsAppInteractiveMessageAction(),
                type = WhatsAppInteractiveMessage.InteractiveMessageType.LIST
            )
        }
        assertEquals("action.sections must be filled for list type", exception.message)
    }

    @Test
    fun `given a button interactive message with no buttons, when created, then trows exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessage(
                action = WhatsAppInteractiveMessageAction(),
                body = WhatsAppInteractiveMessageBody("Test"),
                type = WhatsAppInteractiveMessage.InteractiveMessageType.BUTTON
            )
        }
        assertEquals("action.buttons must be filled for button type", exception.message)
    }

}
