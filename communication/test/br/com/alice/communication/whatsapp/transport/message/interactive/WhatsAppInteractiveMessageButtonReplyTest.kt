package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test

class WhatsAppInteractiveMessageButtonReplyTest {

    @Test
    fun `given an invalid id, when create button reply, then it should throw exception`() {
        assertThrows(IllegalArgumentException::class.java) {
            WhatsAppInteractiveMessageButtonReply("", "Valid Title")
        }
    }

    @Test
    fun `given an invalid title, when create button reply, then it should throw exception`() {
        assertThrows(IllegalArgumentException::class.java) {
            WhatsAppInteractiveMessageButtonReply("valid_id", "")
        }
    }

    @Test
    fun `given an id longer than 256 characters, when create button reply, then it should throw exception`() {
        val longId = "a".repeat(257)
        assertThrows(IllegalArgumentException::class.java) {
            WhatsAppInteractiveMessageButtonReply(longId, "Valid Title")
        }
    }

    @Test
    fun `given a title longer than 20 characters, when create button reply, then it should throw exception`() {
        val longTitle = "a".repeat(21)
        assertThrows(IllegalArgumentException::class.java) {
            WhatsAppInteractiveMessageButtonReply("valid_id", longTitle)
        }
    }

}
