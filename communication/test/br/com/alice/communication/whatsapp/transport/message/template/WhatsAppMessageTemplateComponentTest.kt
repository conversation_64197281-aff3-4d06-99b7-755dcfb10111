package br.com.alice.communication.whatsapp.transport.message.template

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppMessageTemplateComponentTest {

    @Test
    fun `given an invalid subType on button component when created then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponent(
                type = WhatsAppMessageTemplateComponent.Type.BUTTON,
                subType = null,
                index = null,
                parameters = emptyList()
            )
        }
        assertEquals("subType must be filled for button type", exception.message)
    }

    @Test
    fun `given an invalid index on button component when created then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessageTemplateComponent(
                type = WhatsAppMessageTemplateComponent.Type.BUTTON,
                subType = WhatsAppMessageTemplateComponent.SubType.QUICK_REPLY,
                parameters = emptyList()
            )
        }
        assertEquals("index must be filled for button type", exception.message)
    }
}
