package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppInteractiveMessageBodyTest {

    @Test
    fun `given a WhatsAppInteractiveMessageBody with empty text, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageBody(text = "")
        }
        assertEquals("text must be filled", exception.message)
    }

    @Test
    fun `given a WhatsAppInteractiveMessageBody with text longer than 1024 characters, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageBody(text = "a".repeat(1025))
        }
        assertEquals("text must be less than or equal to 1024 characters", exception.message)
    }
}
