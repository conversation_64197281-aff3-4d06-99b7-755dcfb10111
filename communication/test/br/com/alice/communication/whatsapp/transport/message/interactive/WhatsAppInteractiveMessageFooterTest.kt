package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.Assertions.*
import kotlin.test.Test

class WhatsAppInteractiveMessageFooterTest {

    @Test
    fun `given an empty footer text, when creating footer, then it should throw exception`() {
        assertThrows(IllegalArgumentException::class.java) {
            WhatsAppInteractiveMessageFooter("")
        }
    }

    @Test
    fun `given a footer text longer than 60 characters, when creating footer, then it should throw exception`() {
        val longText = "a".repeat(61)
        assertThrows(IllegalArgumentException::class.java) {
            WhatsAppInteractiveMessageFooter(longText)
        }
    }
}
