package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppInteractiveMessageHeaderTest {

    @Test
    fun `given a document type, when create header with null document, then throw exception`() {
        assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageHeader(
                type = WhatsAppInteractiveMessageHeader.MessageHeaderType.DOCUMENT,
                document = null
            )
        }
    }

    @Test
    fun `given an image type, when create header with null image, then throw exception`() {
        assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageHeader(
                type = WhatsAppInteractiveMessageHeader.MessageHeaderType.IMAGE,
                image = null
            )
        }
    }

    @Test
    fun `given a text type, when create header with null text, then throw exception`() {
        assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageHeader(
                type = WhatsAppInteractiveMessageHeader.MessageHeaderType.TEXT,
                text = null
            )
        }
    }

    @Test
    fun `given a video type, when create header with null video, then throw exception`() {
        assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageHeader(
                type = WhatsAppInteractiveMessageHeader.MessageHeaderType.VIDEO,
                video = null
            )
        }
    }
}
