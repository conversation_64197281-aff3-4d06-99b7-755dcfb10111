package br.com.alice.communication.whatsapp.transport.message.text

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppTextMessageTest {

    @Test
    fun `given an empty body, when creating a WhatsAppTextMessage, then it should throw an exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppTextMessage(body = "")
        }
        assertEquals("body must not be blank", exception.message)
    }

    @Test
    fun `given a body longer than 4096 characters, when creating a WhatsAppTextMessage, then it should throw an exception`() {
        val longBody = "a".repeat(4097)
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppTextMessage(body = longBody)
        }
        assertEquals("body must be <= 1024 characters", exception.message)
    }
}
