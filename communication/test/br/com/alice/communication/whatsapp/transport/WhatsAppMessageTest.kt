package br.com.alice.communication.whatsapp.transport

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppMessageTest {

    @Test
    fun `given a text WhatsAppMessage without text, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessage(
                to = "1234567890",
                type = WhatsAppMessage.MessageType.TEXT
            )
        }
        assertEquals("text property must be filled", exception.message)
    }

    @Test
    fun `given a template WhatsAppMessage without template, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessage(
                to = "1234567890",
                type = WhatsAppMessage.MessageType.TEMPLATE
            )
        }
        assertEquals("template property must be filled", exception.message)
    }

    @Test
    fun `given an interactive WhatsAppMessage without interactive, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppMessage(
                to = "1234567890",
                type = WhatsAppMessage.MessageType.INTERACTIVE
            )
        }
        assertEquals("interactive property must be filled", exception.message)
    }

}
