package br.com.alice.communication.whatsapp.transport.message.interactive

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test

class WhatsAppInteractiveMessageActionTest {

    @Test
    fun `given a button action with more than 20 characters, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageAction(button = "This button text is way too long")
        }
        assertEquals("button must be <= 20 characters", exception.message)
    }

    @Test
    fun `given a buttons action with more than 3 buttons, when created, then throws exception`() {
        val exception = assertThrows<IllegalArgumentException> {
            WhatsAppInteractiveMessageAction(
                buttons = listOf(
                    WhatsAppInteractiveMessageButton(
                        type = WhatsAppInteractiveMessageButton.ButtonType.REPLY,
                        reply = WhatsAppInteractiveMessageButtonReply("1", "Button 1")
                    ),
                    WhatsAppInteractiveMessageButton(
                        type = WhatsAppInteractiveMessageButton.ButtonType.REPLY,
                        reply = WhatsAppInteractiveMessageButtonReply("2", "Button 2")
                    ),
                    WhatsAppInteractiveMessageButton(
                        type = WhatsAppInteractiveMessageButton.ButtonType.REPLY,
                        reply = WhatsAppInteractiveMessageButtonReply("3", "Button 3")
                    ),
                    WhatsAppInteractiveMessageButton(
                        type = WhatsAppInteractiveMessageButton.ButtonType.REPLY,
                        reply = WhatsAppInteractiveMessageButtonReply("4", "Button 4")
                    )
                )
            )
        }
        assertEquals("buttons must be >= 1 and <= 3", exception.message)
    }
}
