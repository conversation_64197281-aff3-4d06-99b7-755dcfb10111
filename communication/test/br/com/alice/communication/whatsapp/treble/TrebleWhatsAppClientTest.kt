package br.com.alice.communication.whatsapp.treble

import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.HttpRequestData
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class TrebleWhatsAppClientTest {

    private val trebleWhatsAppConfiguration = TrebleWhatsAppConfiguration(
        apiUrl = "https://apiUrl",
        apiKey = "apiKey"
    )

    @Test
    fun `#deployConversation returns TrebleDeploymentResponse`() = runBlocking<Unit> {
        val pollId = "pollId"
        val users = listOf(
            TrebleDeploymentRequestUser(
                cellphone = "cellphone",
                countryCode = "countryCode",
                userSessionKeys = listOf(
                    TrebleDeploymentRequestUserSessionKey(
                        key = "key",
                        value = "value"
                    )
                ),
                deploymentEta = 10
            )
        )

        val expectedBody = TrebleDeploymentRequest(users = users)

        val expectedResponse = TrebleDeploymentResponse(
            message = "message",
            id = "id",
            batchId = "batchId",
            newLastScheduledDeployment = "newLastScheduledDeployment",
            conversationsId = listOf("conversationsId")
        )

        val httpClientMock = httpClientMock(gson.toJson(expectedResponse)) { request ->
            request.method == HttpMethod.Post &&
                    request.url.toString() == "${trebleWhatsAppConfiguration.apiUrl}/deployment/api/poll/$pollId" &&
                    request.headers[HttpHeaders.Authorization] == trebleWhatsAppConfiguration.apiKey &&
                    request.body.contentType == ContentType.Application.Json &&
                    String(request.body.toByteArray()) == gson.toJson(expectedBody)
        }

        val trebleWhatsAppClient = TrebleWhatsAppClient(trebleWhatsAppConfiguration, httpClientMock)

        val result = trebleWhatsAppClient.deployConversation(pollId, users)
        assertThat(result).isEqualTo(expectedResponse)
    }


    private fun httpClientMock(
        responseContent: String,
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        requestMatcher: suspend (request: HttpRequestData) -> Boolean,
    ): HttpClient =
        HttpClient(MockEngine) {
            expectSuccess = true
            install(ContentNegotiation) {
                gsonSnakeCase()
            }
            engine {
                addHandler { request ->
                    if (requestMatcher(request)) respond(responseContent, statusCode)
                    else error("unknown request")
                }
            }
        }
}
