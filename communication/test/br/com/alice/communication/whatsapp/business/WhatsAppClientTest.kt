package br.com.alice.communication.whatsapp.business

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.loadFixture
import br.com.alice.communication.whatsapp.transport.WhatsAppMessage
import br.com.alice.communication.whatsapp.transport.WhatsAppMessageResponse
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplate
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplateComponent
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplateComponentParameter
import br.com.alice.communication.whatsapp.transport.message.text.WhatsAppTextMessage
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.client.request.HttpRequestData
import io.ktor.client.request.HttpResponseData
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class WhatsAppClientTest {

    @Test
    fun `given a text message when sending it then it should be sent successfully`() {
        val messageRequest = WhatsAppMessage(
            to = "16505555555",
            type = WhatsAppMessage.MessageType.TEXT,
            text = WhatsAppTextMessage(body = "Hello, World!")
        )

        val expectedResponse = WhatsAppMessageResponse(
            messagingProduct = "whatsapp",
            contacts = listOf(WhatsAppMessageResponse.Contact(input = "16505555555", waId = "16505555555")),
            messages = listOf(
                WhatsAppMessageResponse.Message(
                    id = "wamid.HBgLMTY1MDUwNzY1MjAVAgARGBI5QTNDQTVCM0Q0Q0Q2RTY3RTcA",
                    messageStatus = "accepted"
                )
            )
        )

        val client = WhatsAppClient(buildMockedClient { request ->
            when (request.url.encodedPath) {
                "/v23.0/693630227161340/messages" -> {
                    respond("send_message_response_success.json")
                }

                else -> error("Unhandled ${request.url.encodedPath}")
            }
        })

        val response = runBlocking { client.sendMessage(messageRequest) }
        assertThat(response).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `given a template message when sending it then throw error`() {
        val messageRequest = WhatsAppMessage(
            to = "16505555555",
            template = WhatsAppMessageTemplate(
                name = "template_name",
                components = listOf(
                    WhatsAppMessageTemplateComponent(
                        type = WhatsAppMessageTemplateComponent.Type.BODY,
                        parameters = listOf(
                            WhatsAppMessageTemplateComponentParameter(
                                type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                                text = "Fulano de Ciclano"
                            ),
                            WhatsAppMessageTemplateComponentParameter(
                                type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                                text = "enviado"
                            )
                        )
                    )
                )
            )
        )

        val client = WhatsAppClient(buildMockedClient { request ->
            when (request.url.encodedPath) {
                "/v23.0/693630227161340/messages" -> {
                    respond(
                        statusCode = HttpStatusCode.InternalServerError,
                        fixture = "send_message_response_error.json"
                    )
                }

                else -> error("Unhandled ${request.url.encodedPath}")
            }
        })

        val response = runBlocking { client.sendMessage(messageRequest) }
        assertThat(response).isFailure()
    }

    private fun MockRequestHandleScope.respond(fixture: String, statusCode: HttpStatusCode = HttpStatusCode.OK) =
        respond(
            content = loadFixture("whatsapp/$fixture", this::class.java),
            status = statusCode,
            headers = headersOf("Content-Type" to listOf(ContentType.Application.Json.toString()))
        )

    private fun buildMockedClient(block: suspend MockRequestHandleScope.(request: HttpRequestData) -> HttpResponseData) =
        HttpClient(MockEngine { block(it) }) {
            expectSuccess = true
            recommendClientEngine()
        }
}
