plugins {
    kotlin
    id("org.sonarqube")
}

group = "br.com.alice.alice-notification"
version = aliceNotificationVersion
val jwtVersion = "0.11.5"

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:communication")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

dependencies {
    ktor2Dependencies()
    implementation("io.ktor:ktor-client-content-negotiation:$ktor2Version")
    implementation("com.google.firebase:firebase-admin:$firebaseAdminVersion")
    implementation("javax.mail:mail:$javaxMailVersion")

    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("software.amazon.awssdk:pinpoint")
    implementation("software.amazon.awssdk:ses")
    implementation("software.amazon.awssdk:socialmessaging")

    implementation("io.jsonwebtoken:jjwt-api:$jwtVersion")
    implementation("io.jsonwebtoken:jjwt-impl:$jwtVersion")
    implementation("io.jsonwebtoken:jjwt-gson:$jwtVersion")
    implementation("io.ktor:ktor-client-java:$ktor2Version")

    implementation(project(":common"))
    implementation(project(":common-core"))
    implementation(project(":common-service"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:business-domain-service-data-package"))
	implementation(project(":data-packages:marauders-map-domain-service-data-package"))

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
