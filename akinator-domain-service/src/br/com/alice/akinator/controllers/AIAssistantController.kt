package br.com.alice.akinator.controllers

import br.com.alice.akinator.ServiceConfig
import br.com.alice.akinator.controllers.transfer.AIAssistantDTO
import br.com.alice.akinator.controllers.transfer.merge
import br.com.alice.akinator.controllers.transfer.toDTO
import br.com.alice.akinator.services.AIAssistantService
import br.com.alice.common.coFoldResponse
import br.com.alice.common.foldResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import java.util.UUID

class AIAssistantController(
    private val aiAssistantService: AIAssistantService
) {
    suspend fun getAssistantById(assistantId: UUID) = aiAssistantService.getAssistantById(assistantId).foldResponse({ it.toDTO() })

    suspend fun getAllAssistants() = aiAssistantService.getAllAssistants().foldResponse({ it.map { it.toDTO() } })

    suspend fun createAIAssistant(aiAssistantDTO: AIAssistantDTO) =
        aiAssistantDTO.validateApiKey().flatMap {
            aiAssistantService.addAssistant(it.toModel())
        }.coFoldResponse({ it.toDTO() })

    suspend fun updateAIAssistant(assistantId: UUID, aiAssistantDTO: AIAssistantDTO) =
        aiAssistantService.getAssistantById(assistantId).flatMap { ass ->
            aiAssistantDTO.validateApiKey().flatMap {
                aiAssistantService.updateAssistant(ass.merge(it))
            }
        }.coFoldResponse({ it.toDTO() })

    suspend fun deleteAIAssistant(assistantId: UUID) =
        aiAssistantService.getAssistantById(assistantId).flatMap {
            aiAssistantService.deleteAssistant(it)
        }.foldResponse()

    private fun AIAssistantDTO.validateApiKey(): Result<AIAssistantDTO, Throwable> =
        if (apiKeyEnvVar.isNullOrBlank())
            IllegalArgumentException("apiKeyEnvVar is required").failure()
        else if (isDefaultKey())
            IllegalArgumentException("apiKey value cannot be the default").failure()
        else
            Result.success(this)

    private fun AIAssistantDTO.isDefaultKey(): Boolean {
        val modelKey = ServiceConfig.getEnv(this.apiKeyEnvVar)
        return when (this.provider) {
            "openai" ->
                ServiceConfig.chatGptCredentials.basicAuthToken == modelKey
            "edenai" ->
                ServiceConfig.edenAIConfig.apiKey == modelKey
            else -> false
        }
    }
}
