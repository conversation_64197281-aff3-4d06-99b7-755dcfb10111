package br.com.alice.akinator.controllers

import br.com.alice.akinator.ChatGptCredentials
import br.com.alice.akinator.ServiceConfig
import br.com.alice.akinator.controllers.transfer.toDTO
import br.com.alice.akinator.module
import br.com.alice.akinator.services.AIAssistantService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AIAssistant
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.server.application.Application
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module

class AIAssistantControllerTest : RoutesTestHelper() {

    private val aiAssistantService: AIAssistantService = mockk()
    private val aiAssistantController = AIAssistantController(aiAssistantService)
    
    private val testStaff = TestModelFactory.buildStaff()
    private val testAssistant = AIAssistant(
        name = "Test Assistant",
        assistantId = "asst_test123",
        provider = "openai",
        apiKeyEnvVar = "OPENAI_API_KEY"
    )
    
    private val testAssistantDTO = testAssistant.toDTO()

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module,
                module(createdAtStart = true) { single { aiAssistantController } }
            )
        )
    }

    @Test
    fun `#getAssistantById should return assistant by ID`() {
        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns testAssistant.success()

        authenticatedAs("TOLKIEN", testStaff) {
            get("/assistants/${testAssistant.id}") {
                assertThat(it).isOKWithData(testAssistantDTO)
            }
        }

        coVerifyOnce { aiAssistantService.getAssistantById(any()) }
    }

    @Test
    fun `#getAssistantById should return 404 when assistant not found`() {
        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns NotFoundException("Assistant not found").failure()

        authenticatedAs("TOLKIEN", testStaff) {
            get("/assistants/${testAssistant.id}") { response ->
                assertThat(response).isNotFound()
                assertEquals("{\"code\":\"resource_not_found\",\"message\":\"Assistant not found\"}", response.bodyAsText())
            }
        }

        coVerifyOnce { aiAssistantService.getAssistantById(any()) }
    }

    @Test
    fun `#getAllAssistants should return all assistants`() {
        val assistants = listOf(testAssistant)
        coEvery { aiAssistantService.getAllAssistants() } returns assistants.success()

        authenticatedAs("TOLKIEN", testStaff) {
            get("/assistants") {
                assertThat(it).isOKWithData(assistants.map { it.toDTO() })
            }
        }

        coVerifyOnce { aiAssistantService.getAllAssistants() }
    }

    @Test
    fun `#createAIAssistant should create a new assistant`() {
        coEvery { aiAssistantService.addAssistant(match {
            it.name == testAssistant.name &&
            it.assistantId == testAssistant.assistantId &&
            it.provider == testAssistant.provider &&
            it.apiKeyEnvVar == testAssistant.apiKeyEnvVar
        }) } returns testAssistant.success()

        authenticatedAs("TOLKIEN", testStaff) {
            post("/assistants", gson.toJson(testAssistantDTO)) {
                assertThat(it).isOKWithData(testAssistantDTO)
            }
        }

        coVerifyOnce { aiAssistantService.addAssistant(any()) }
    }

    @Test
    fun `#createAIAssistant should return 500 on persistence failure`() {
        coEvery { aiAssistantService.addAssistant(match {
            it.name == testAssistant.name &&
                    it.assistantId == testAssistant.assistantId &&
                    it.provider == testAssistant.provider &&
                    it.apiKeyEnvVar == testAssistant.apiKeyEnvVar
        }) } returns RuntimeException("Database error").failure()

        authenticatedAs("TOLKIEN", testStaff) {
            post("/assistants", gson.toJson(testAssistantDTO)) { response ->
                assertThat(response).isInternalServerError()
            }
        }

        coVerifyOnce { aiAssistantService.addAssistant(any()) }
    }

    @Test
    fun `#updateAIAssistant should update an existing assistant`() {
        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns testAssistant.success()
        coEvery { aiAssistantService.updateAssistant(match {
            it.id == testAssistant.id &&
            it.name == testAssistant.name &&
                    it.assistantId == testAssistant.assistantId &&
                    it.provider == testAssistant.provider &&
                    it.apiKeyEnvVar == testAssistant.apiKeyEnvVar
        }) } returns testAssistant.success()

        authenticatedAs("TOLKIEN", testStaff) {
            put("/assistants/${testAssistant.id}", gson.toJson(testAssistantDTO)) {
                assertThat(it).isOKWithData(testAssistantDTO)
            }
        }

        coVerifyOnce { aiAssistantService.getAssistantById(any()) }
        coVerifyOnce { aiAssistantService.updateAssistant(any()) }
    }

    @Test
    fun `#updateAIAssistant should return 404 when assistant not found`() {
        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns NotFoundException("Assistant not found").failure()

        authenticatedAs("TOLKIEN", testStaff) {
            put("/assistants/${testAssistant.id}", gson.toJson(testAssistantDTO)) { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { aiAssistantService.getAssistantById(any()) }
    }

    @Test
    fun `#createAIAssistant should return 400 when apiKeyEnvVar is missing`() {
        val invalidAssistantDTO = testAssistantDTO.copy(apiKeyEnvVar = "")

        authenticatedAs("TOLKIEN", testStaff) {
            post("/assistants", gson.toJson(invalidAssistantDTO)) { response ->
                assertThat(response).isBadRequest()
                assertEquals("{\"code\":\"illegal_argument\",\"message\":\"apiKeyEnvVar is required\"}", response.bodyAsText())
            }
        }
    }

    @Test
    fun `#createAIAssistant should return 400 when apiKeyEnvVar is default`() {
        val invalidAssistantDTO = testAssistantDTO.copy(apiKeyEnvVar = "DEFAULT_API_KEY")

        mockkObject(ServiceConfig) {
            coEvery { ServiceConfig.getEnv("DEFAULT_API_KEY") } returns "default_api_key"
            coEvery { ServiceConfig.chatGptCredentials } returns ChatGptCredentials(
                basicAuthToken = "default_api_key",
                baseUrl = "whatever"
            )

            authenticatedAs("TOLKIEN", testStaff) {
                post("/assistants", gson.toJson(invalidAssistantDTO)) { response ->
                    assertThat(response).isBadRequest()
                    assertEquals("{\"code\":\"illegal_argument\",\"message\":\"apiKey value cannot be the default\"}", response.bodyAsText())
                }
            }
        }
    }

    @Test
    fun `#updateAIAssistant should return 400 when apiKeyEnvVar is missing`() {
        val invalidAssistantDTO = testAssistantDTO.copy(apiKeyEnvVar = "")

        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns testAssistant.success()

        authenticatedAs("TOLKIEN", testStaff) {
            put("/assistants/${testAssistant.id}", gson.toJson(invalidAssistantDTO)) { response ->
                assertThat(response).isBadRequest()
                assertEquals("{\"code\":\"illegal_argument\",\"message\":\"apiKeyEnvVar is required\"}", response.bodyAsText())
            }
        }
    }

    @Test
    fun `#updateAIAssistant should return 400 when apiKeyEnvVar is default`() {
        val invalidAssistantDTO = testAssistantDTO.copy(apiKeyEnvVar = "DEFAULT_API_KEY")

        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns testAssistant.success()

        mockkObject(ServiceConfig) {
            coEvery { ServiceConfig.getEnv("DEFAULT_API_KEY") } returns "default_api_key"
            coEvery { ServiceConfig.chatGptCredentials } returns ChatGptCredentials(
                basicAuthToken = "default_api_key",
                baseUrl = "whatever"
            )

            authenticatedAs("TOLKIEN", testStaff) {
                put("/assistants/${testAssistant.id}", gson.toJson(invalidAssistantDTO)) { response ->
                    assertThat(response).isBadRequest()
                    assertEquals(
                        "{\"code\":\"illegal_argument\",\"message\":\"apiKey value cannot be the default\"}",
                        response.bodyAsText()
                    )
                }
            }
        }
    }

    @Test
    fun `#deleteAIAssistant should delete an existing assistant`() {
        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns testAssistant.success()
        coEvery { aiAssistantService.deleteAssistant(testAssistant) } returns true.success()

        authenticatedAs("TOLKIEN", testStaff) {
            delete("/assistants/${testAssistant.id}") {
                assertThat(it).isOKWithData(true)
            }
        }

        coVerifyOnce { aiAssistantService.getAssistantById(any()) }
        coVerifyOnce { aiAssistantService.deleteAssistant(any()) }
    }

    @Test
    fun `#deleteAIAssistant should return 404 when assistant not found`() {
        coEvery { aiAssistantService.getAssistantById(testAssistant.id) } returns NotFoundException("Assistant not found").failure()

        authenticatedAs("TOLKIEN", testStaff) {
            delete("/assistants/${testAssistant.id}") { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { aiAssistantService.getAssistantById(any()) }
    }
} 
