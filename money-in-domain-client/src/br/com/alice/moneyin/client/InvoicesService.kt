package br.com.alice.moneyin.client

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoicePriceType
import br.com.alice.data.layer.models.MemberInvoiceType
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface InvoicesService : Service {

    override val namespace get() = "money_in"
    override val serviceName get() = "invoices"

    suspend fun get(id: UUID, withPayments: Boolean = false): Result<MemberInvoice, Throwable>

    suspend fun listInvoices(memberId: UUID, withPayments: Boolean = false): Result<List<MemberInvoice>, Throwable>

    suspend fun listInvoiceByPerson(
        personId: PersonId,
        withPayments: Boolean = false
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun getFromLastFewMonthsByPersonId(
        personId: PersonId,
        limit: Int,
        withPayments: Boolean = false
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listInvoiceByPersons(personIds: List<PersonId>): Result<List<MemberInvoice>, Throwable>

    suspend fun listByPersonAndStatuses(
        personId: PersonId,
        statuses: List<InvoiceStatus>,
        withPayments: Boolean = false
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listByMemberInvoiceGroupId(
        memberInvoiceGroupId: UUID,
        withPayments: Boolean = false,
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listByMemberInvoiceGroupIds(
        memberInvoiceGroupIds: List<UUID>,
        withPayments: Boolean = false,
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listByMemberInvoiceGroupIdsPaginated(
        memberInvoiceGroupIds: List<UUID>,
        withPayments: Boolean = false,
        offset: Int = 0,
        limit: Int = 200,
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listByPreActivationPaymentId(
        preActivationPaymentId: UUID,
        withPayments: Boolean = false,
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listByPreActivationPaymentIds(
        preActivationPaymentIds: List<UUID>,
        withPayments: Boolean = false,
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listByPreActivationPaymentIdsPaginated(
        preActivationPaymentIds: List<UUID>,
        withPayments: Boolean = false,
        offset: Int = 0,
        limit: Int = 200,
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun listNearOverdueInvoices(
        nearOverdueDate: LocalDateTime,
        initialDate: LocalDateTime,
        memberInvoiceType: MemberInvoiceType?
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun findInvoicesByIds(
        memberInvoiceIds: List<UUID>,
        withPayments: Boolean = false
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun createInvoice(
        memberInvoice: MemberInvoice,
        skipValidation: Boolean = false
    ): Result<MemberInvoice, Throwable>

    suspend fun createInvoices(
        memberInvoices: List<MemberInvoice>
    ): Result<List<MemberInvoice>, Throwable>

    suspend fun addList(memberInvoices: List<MemberInvoice>): Result<List<MemberInvoice>, Throwable>

    suspend fun update(memberInvoice: MemberInvoice): Result<MemberInvoice, Throwable>

    suspend fun validateInvoices(memberInvoices: List<MemberInvoice>): Result<List<MemberInvoice>, Throwable>

    suspend fun issueInvoice(
        memberId: UUID,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paymentMethod: PaymentMethod = PaymentMethod.BOLETO,
        withPayment: Boolean = true,
        type: MemberInvoiceType = MemberInvoiceType.REGULAR_PAYMENT,
    ): Result<MemberInvoice, Throwable>

    suspend fun markAsPaid(memberInvoiceId: UUID, paidAt: LocalDateTime? = null): Result<MemberInvoice, Throwable>

    suspend fun markInvoicesAsPaidByLiquidation(memberInvoiceIds: List<UUID>): Result<List<MemberInvoice>, Throwable>

    suspend fun cancel(
        invoiceId: UUID,
        cancellationReason: CancellationReason,
        forceCancellation: Boolean = false
    ): Result<MemberInvoice, Throwable>

    suspend fun issueFirstInvoice(member: Member): Result<Member, Throwable>

    suspend fun findInvoiceAndPaymentsByPersonId(
        personId: PersonId,
        billingAccountablePartyId: UUID
    ): Result<Pair<List<InvoicePayment?>, MemberInvoice?>, Throwable>

    suspend fun generate(
        member: Member,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paymentMethod: PaymentMethod,
        type: MemberInvoiceType,
        priceType: MemberInvoicePriceType
    ): Result<MemberInvoice, Throwable>

    suspend fun generateForB2B(
        subcontract: CompanySubContract,
        members: List<Member>,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paymentMethod: PaymentMethod,
        type: MemberInvoiceType,
        priceType: MemberInvoicePriceType
    ): Result<List<MemberInvoice>, Throwable>
}

class InvalidAmountException(
    message: String,
    code: String = "invalid_amount",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class OverdueInvoiceException(
    message: String,
    code: String = "invoice_overdue",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class InvoiceAlreadyPaidException(
    message: String,
    code: String = "invoice_already_paid",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class DuplicatedInvoiceException(
    message: String,
    code: String = "duplicated_invoice",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class MemberInvoiceNotFoundException(
    message: String,
    code: String = "member_invoice_not_found",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class InvoicePaymentHasNoMemberInvoice(
    message: String,
    code: String = "invoice_payment_has_no_member_invoice",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class MemberInvoiceNotOpenedException(
    message: String,
    code: String = "member_invoice_not_opened",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)
