package br.com.alice.moneyin.client

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FirstPaymentSchedule
import br.com.alice.data.layer.models.PreActivationPayment
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FirstPaymentScheduleService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "first_payment_schedule"

    suspend fun create(
        payload: CreatePayload,
    ): Result<FirstPaymentSchedule, Throwable>

    suspend fun get(id: UUID): Result<FirstPaymentSchedule, Throwable>

    suspend fun getByExternalId(externalId: String): Result<FirstPaymentSchedule, Throwable>

    suspend fun update(firstPaymentSchedule: FirstPaymentSchedule): Result<FirstPaymentSchedule, Throwable>

    suspend fun findByCompanyIdSubContractIdAndPreActivationPaymentId(
        companyId: UUID,
        companySubcontractId: UUID,
        preActivationPaymentId: UUID
    ): Result<FirstPaymentSchedule, Throwable>

    data class CreatePayload(
        val company: Company,
        val companyContract: CompanyContract,
        val companySubContract: CompanySubContract,
        val preActivationPayment: PreActivationPayment,
    )

    suspend fun findOlderThanOneMonthFromNow(limit: Int): Result<List<FirstPaymentSchedule>, Throwable>

    suspend fun cancelList(firstPaymentSchedules: List<FirstPaymentSchedule>): Result<List<FirstPaymentSchedule>, Throwable>
}

class PreActivationPaymentExternalIdNullException(
    message: String,
    code: String = "pre_activation_payment_external_id_null",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(
        message = "PreActivationPayment does not have an externalId. id: $id",
    )
}
