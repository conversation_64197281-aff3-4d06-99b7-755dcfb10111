package br.com.alice.api.scheduler.converters

import br.com.alice.api.scheduler.controllers.model.RoleResponse
import br.com.alice.api.scheduler.controllers.model.SchedulerPermissions
import br.com.alice.api.scheduler.controllers.model.SimpleStaffResponse
import br.com.alice.common.Converter
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.containsAny
import br.com.alice.common.map
import br.com.alice.data.layer.models.Staff

object SimpleStaffResponseConverter : Converter<Staff, SimpleStaffResponse>(
    Staff::class, SimpleStaffResponse::class
) {
    private val rolesThatCanEditSchedulingInformationButCannotBeScheduled = listOf(
        Role.B2B_OPS,
        Role.CARE_COORD_NURSE,
        Role.CHIEF_DIGITAL_CARE_NURSE,
        Role.CHIEF_NAVIGATOR,
        Role.CHIEF_NAVIGATOR_OPS,
        Role.CX_OPS,
        Role.HEALTH_OPS,
        Role.HEALTH_OPS_LEAD,
        Role.HEALTH_OPS_MULTI,
        Role.NAVIGATOR,
        Role.NAVIGATOR_OPS,
        Role.OPS,
        Role.PRODUCT_TECH,
        Role.PRODUCT_TECH_HEALTH,
        Role.PRODUCT_TECH_STAFF_EDITOR,
        Role.PRODUCT_TECH_HEALTH_STAFF_EDITOR
    )

    private val rolesThatCanViewSchedulingInformationButCannotBeScheduled = listOf(
        Role.DIGITAL_CARE_NURSE,
        Role.DIGITAL_SCREENING_NURSE,
    )

    private val rolesThatCanEditFullSchedulingInformationButCannotBeScheduled = listOf(
        Role.CHIEF_NAVIGATOR,
        Role.CHIEF_NAVIGATOR_OPS,
        Role.CX_OPS,
        Role.NAVIGATOR,
        Role.NAVIGATOR_OPS,
        Role.HEALTH_OPS_LEAD
    )

    private val rolesThatCanDebugSchedulingScreen = listOf(
        Role.CX_OPS,
        Role.HEALTH_OPS,
        Role.HEALTH_OPS_LEAD,
        Role.HEALTH_OPS_MULTI,
        Role.PRODUCT_TECH,
        Role.PRODUCT_TECH_HEALTH,
        Role.PRODUCT_TECH_STAFF_EDITOR,
        Role.PRODUCT_TECH_HEALTH_STAFF_EDITOR,
        Role.CHIEF_NAVIGATOR,
        Role.CHIEF_NAVIGATOR_OPS
    )

    fun convert(source: Staff): SimpleStaffResponse {
        return convert(
            source,
            map(SimpleStaffResponse::roles) from source.allRoles().map { RoleResponse.fromRole(it) },
            map(SimpleStaffResponse::fullName) from source.fullName,
            map(SimpleStaffResponse::permissions) from SchedulerPermissions(
                canEdit = source.canEditOtherProfessionalsSchedulingInformation(),
                canEditFull = source.canEditFullProfessionalsSchedulingInformation(),
                canView = source.canViewOtherProfessionalsSchedulingInformation(),
                canBeScheduled = source.canBeScheduled(),
                canAccessDebugScreen = source.canAccessDebugScreen()
            )
        )
    }

    fun Staff.canEditOtherProfessionalsSchedulingInformation(staffList: List<String> = emptyList()): Boolean {
        val containRole = allRoles().containsAny(rolesThatCanEditSchedulingInformationButCannotBeScheduled)
        return containRole || staffList.contains(this.id.toString())
    }

    fun Staff.canEditFullProfessionalsSchedulingInformation(staffList: List<String> = emptyList()): Boolean {
        val containRole = allRoles().containsAny(rolesThatCanEditFullSchedulingInformationButCannotBeScheduled)
        return containRole || staffList.contains(this.id.toString())
    }

    fun Staff.canViewOtherProfessionalsSchedulingInformation() = isAliceHealthProfessionalOrNavigator()

    fun Staff.canBeScheduled() = allRoles().containsAny(
        Role.values().toList().minus(
            (rolesThatCanViewSchedulingInformationButCannotBeScheduled +
                    rolesThatCanEditSchedulingInformationButCannotBeScheduled).toSet()
        )
    )

    fun Staff.canAccessDebugScreen() = allRoles().containsAny(
        rolesThatCanDebugSchedulingScreen
    )
}


