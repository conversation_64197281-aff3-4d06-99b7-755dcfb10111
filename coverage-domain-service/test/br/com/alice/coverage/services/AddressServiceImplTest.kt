package br.com.alice.coverage.services

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.data.layer.services.StructuredAddressDataService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AddressServiceImplTest {

    @AfterTest
    fun setup() = clearAllMocks()

    private val addressDataService: StructuredAddressDataService = mockk()

    private val addressService = AddressServiceImpl(
        addressDataService,
    )

    private val providerUnit = TestModelFactory.buildProviderUnit()
    private val structuredAddress = TestModelFactory.buildStructuredAddress(
        referencedModelId = providerUnit.id,
        referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
    )

    private val healthProfessional = TestModelFactory.buildHealthProfessional()
    private val healthCommunitySpecialistStructuredAddress = TestModelFactory.buildStructuredAddress(
        referencedModelId = healthProfessional.id,
        referencedModelClass = StructuredAddressReferenceModel.HEALTH_PROFESSIONAL
    )

    private val cassiSpecialist = TestModelFactory.buildCassiSpecialist()
    private val cassiSpecialistStructuredAddress = TestModelFactory.buildStructuredAddress(
        referencedModelId = cassiSpecialist.id,
        referencedModelClass = StructuredAddressReferenceModel.CASSI_SPECIALIST
    )

    @Test
    fun `#findById should return single StructuredAddress`() = runBlocking {
        coEvery { addressDataService.findOne(any()) } returns structuredAddress.success()

        val result = addressService.findById(structuredAddress.id)
        ResultAssert.assertThat(result).isSuccessWithData(structuredAddress)

        coVerify(exactly = 1) { addressDataService.findOne(queryEq { where { this.id.eq(structuredAddress.id) } }) }
    }

    @Test
    fun `#findByIds should return a list of StructuredAddresses`() = runBlocking {
        val expectedReturn = listOf(structuredAddress)
        val structuredAddressesIdList = listOf(structuredAddress.id)
        coEvery { addressDataService.find(any()) } returns expectedReturn.success()

        val result = addressService.findByIds(structuredAddressesIdList)
        ResultAssert.assertThat(result).isSuccessWithData(expectedReturn)

        coVerify(exactly = 1) { addressDataService.find(queryEq { where { this.id.inList(structuredAddressesIdList) } }) }
    }

    @Test
    fun `#findByReferencedObject should return StructuredAddress`() = runBlocking {
        coEvery {
            addressDataService.findOne(queryEq {
                where {
                    this.referencedModelClass.eq(structuredAddress.referencedModelClass!!) and this.referencedModelId.eq(
                        structuredAddress.referencedModelId!!
                    )
                }
            })
        } returns structuredAddress.success()

        val result =
            addressService.findByReferencedObject(providerUnit.id, StructuredAddressReferenceModel.PROVIDER_UNIT)
        ResultAssert.assertThat(result).isSuccessWithData(structuredAddress)

        coVerify(exactly = 1) {
            addressDataService.findOne(queryEq {
                where {
                    this.referencedModelClass.eq(
                        structuredAddress.referencedModelClass!!
                    ) and this.referencedModelId.eq(structuredAddress.referencedModelId!!)
                }
            })
        }
    }

    @Test
    fun `#findListByReferencedObject should return list of StructuredAddresses`() = runBlocking {
        val secondStructuredAddress = TestModelFactory.buildStructuredAddress(
            number = "1816",
            referencedModelId = healthProfessional.id,
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_COMMUNITY_SPECIALIST
        )

        val structuredAddressList = listOf(healthCommunitySpecialistStructuredAddress, secondStructuredAddress)

        coEvery {
            addressDataService.find(queryEq {
                where {
                    this.active.eq(true) and this.referencedModelClass.eq(structuredAddress.referencedModelClass!!) and this.referencedModelId.eq(
                        structuredAddress.referencedModelId!!
                    )
                }.orderBy { this.createdAt }
            })
        } returns structuredAddressList.success()

        val result =
            addressService.findListByReferencedObject(providerUnit.id, StructuredAddressReferenceModel.PROVIDER_UNIT)
        ResultAssert.assertThat(result).isSuccessWithData(structuredAddressList)

        coVerify(exactly = 1) {
            addressDataService.find(queryEq {
                where {
                    this.active.eq(true) and this.referencedModelClass.eq(structuredAddress.referencedModelClass!!) and this.referencedModelId.eq(
                        structuredAddress.referencedModelId!!
                    )
                }.orderBy { this.createdAt }
            })
        }
    }

    @Test
    fun `#findActiveListByReferencedAndTypes should return list of StructuredAddresses`() = runBlocking {
        val types = listOf(
            StructuredAddressReferenceModel.HEALTH_PROFESSIONAL,
            StructuredAddressReferenceModel.HEALTH_COMMUNITY_SPECIALIST
        )
        val secondStructuredAddress = TestModelFactory.buildStructuredAddress(
            number = "1816",
            referencedModelId = healthProfessional.id,
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_PROFESSIONAL
        )

        val structuredAddressList = listOf(healthCommunitySpecialistStructuredAddress, secondStructuredAddress)

        coEvery {
            addressDataService.find(queryEq {
                where {
                    this.active.eq(true) and this.referencedModelClass.inList(types) and this.referencedModelId.eq(
                        healthProfessional.id
                    )
                }.orderBy { this.createdAt }
            })
        } returns structuredAddressList.success()

        val result =
            addressService.findActiveListByReferencedAndTypes(
                healthProfessional.id,
                types
            )
        ResultAssert.assertThat(result).isSuccessWithData(structuredAddressList)

        coVerifyOnce {
            addressDataService.find(queryEq {
                where {
                    this.active.eq(true) and this.referencedModelClass.inList(types) and this.referencedModelId.eq(
                        healthProfessional.id
                    )
                }.orderBy { this.createdAt }
            })
        }
    }

    @Test
    fun `#upsert updates StructuredAddress from ProviderUnit`() = runBlocking {
        coEvery {
            addressDataService.findOneOrNull(
                queryEq {
                    where {
                        this.referencedModelClass.eq(structuredAddress.referencedModelClass!!) and this.referencedModelId.eq(
                            structuredAddress.referencedModelId!!
                        )
                    }
                }
            )
        } returns structuredAddress

        val structuredAddressUpdated = TestModelFactory.buildStructuredAddress(
            street = "Rua Quatorze de Maio",
            referencedModelId = providerUnit.id,
            referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
        )

        coEvery {
            addressDataService.update(match {
                it.street == structuredAddressUpdated.street
                        && it.number == structuredAddressUpdated.number
                        && it.complement == structuredAddressUpdated.complement
                        && it.neighborhood == structuredAddressUpdated.neighborhood
                        && it.city == structuredAddressUpdated.city
                        && it.state == structuredAddressUpdated.state
                        && it.zipcode == structuredAddressUpdated.zipcode
            })
        } returns structuredAddressUpdated.success()


        val result = addressService.upsert(structuredAddressUpdated)

        ResultAssert.assertThat(result).isSuccessWithData(structuredAddressUpdated)

        coVerify(exactly = 1) { addressDataService.update(any()) }
        coVerify(exactly = 0) { addressDataService.add(any()) }
    }

    @Test
    fun `#upsert adds StructuredAddress from ProviderUnit`() = runBlocking {
        coEvery {
            addressDataService.findOneOrNull(
                queryEq {
                    where {
                        this.referencedModelClass.eq(structuredAddress.referencedModelClass!!) and this.referencedModelId.eq(
                            structuredAddress.referencedModelId!!
                        )
                    }
                }
            )
        } returns null

        coEvery {
            addressDataService.add(match {
                it.street == structuredAddress.street &&
                        it.number == structuredAddress.number &&
                        it.complement == structuredAddress.complement &&
                        it.neighborhood == structuredAddress.neighborhood &&
                        it.city == structuredAddress.city &&
                        it.state == structuredAddress.state &&
                        it.zipcode == structuredAddress.zipcode
            })
        } returns structuredAddress.success()


        val result = addressService.upsert(structuredAddress)

        ResultAssert.assertThat(result).isSuccessWithData(structuredAddress)

        coVerify(exactly = 0) { addressDataService.update(any()) }
        coVerify(exactly = 1) { addressDataService.add(any()) }
    }

    @Test
    fun `#upsert updates StructuredAddress from CassiSpecialist`() = runBlocking {
        val structuredAddressUpdated = TestModelFactory.buildStructuredAddress(
            street = "Rua Quatorze de Maio",
            referencedModelId = cassiSpecialist.id,
            referencedModelClass = StructuredAddressReferenceModel.CASSI_SPECIALIST
        )

        coEvery {
            addressDataService.findOneOrNull(
                queryEq {
                    where { this.id.eq(structuredAddressUpdated.id) }
                }
            )
        } returns structuredAddressUpdated

        coEvery {
            addressDataService.update(match {
                it.street == structuredAddressUpdated.street
                        && it.number == structuredAddressUpdated.number
                        && it.complement == structuredAddressUpdated.complement
                        && it.neighborhood == structuredAddressUpdated.neighborhood
                        && it.city == structuredAddressUpdated.city
                        && it.state == structuredAddressUpdated.state
                        && it.zipcode == structuredAddressUpdated.zipcode
            })
        } returns structuredAddressUpdated.success()


        val result = addressService.upsert(structuredAddressUpdated)

        ResultAssert.assertThat(result).isSuccessWithData(structuredAddressUpdated)

        coVerifyOnce { addressDataService.update(any()) }
        coVerifyNone { addressDataService.add(any()) }
    }

    @Test
    fun `#upsert adds StructuredAddress from CassiSpecialist`() = runBlocking {
        coEvery {
            addressDataService.findOneOrNull(
                queryEq {
                    where { this.id.eq(cassiSpecialistStructuredAddress.id) }
                }
            )
        } returns null

        coEvery {
            addressDataService.add(match {
                it.street == cassiSpecialistStructuredAddress.street &&
                        it.number == cassiSpecialistStructuredAddress.number &&
                        it.complement == cassiSpecialistStructuredAddress.complement &&
                        it.neighborhood == cassiSpecialistStructuredAddress.neighborhood &&
                        it.city == cassiSpecialistStructuredAddress.city &&
                        it.state == cassiSpecialistStructuredAddress.state &&
                        it.zipcode == cassiSpecialistStructuredAddress.zipcode
            })
        } returns cassiSpecialistStructuredAddress.success()

        val result = addressService.upsert(cassiSpecialistStructuredAddress)

        ResultAssert.assertThat(result).isSuccessWithData(cassiSpecialistStructuredAddress)

        coVerifyNone { addressDataService.update(any()) }
        coVerifyOnce { addressDataService.add(any()) }
    }

    @Test
    fun `#inactiveById should inactive by id`() = runBlocking {
        coEvery { addressDataService.get(structuredAddress.id) } returns structuredAddress.success()
        coEvery { addressDataService.update(structuredAddress.copy(active = false)) } returns structuredAddress.success()

        val result = addressService.inactiveById(structuredAddress.id)
        ResultAssert.assertThat(result).isSuccessWithData(true)

        coVerifyOnce { addressDataService.get(any()) }
        coVerifyOnce { addressDataService.update(any()) }
    }

}
