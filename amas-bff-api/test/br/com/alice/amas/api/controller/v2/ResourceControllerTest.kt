package br.com.alice.amas.api.controller.v2

import br.com.alice.amas.api.controller.ControllerTestHelper
import br.com.alice.amas.api.models.v2.ResourceAutocompleteResponse
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.client.specialistResources.SpecialistResourceService
import br.com.alice.exec.indicator.models.PaginatedList
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import br.com.alice.exec.indicator.models.SpecialistResourcePricingListing
import br.com.alice.exec.indicator.models.SpecialistResourcePricingPeriod
import br.com.alice.exec.indicator.models.SpecialistResourceQuantities
import br.com.alice.exec.indicator.models.SpecialistResourceSpecialty
import br.com.alice.exec.indicator.models.SpecialistResourceTuss
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ResourceControllerTest: ControllerTestHelper() {
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val specialistResourceService: SpecialistResourceService = mockk()

    private val controller = ResourceController(
        healthProfessionalService,
        specialistResourceService
    )

    private val staffId = UUID.randomUUID()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staffId,
        tier = SpecialistTier.EXPERT
    )

    private val resource = SpecialistResource(
        id = UUID.randomUUID(),
        primaryTuss = SpecialistResourceTuss("101012", "Teste"),
        secondaryResources = emptyList(),
        code = "ALICE-123",
        description = "Teste de recurso",
        medicalSpecialties = listOf(
            SpecialistResourceSpecialty(
                medicalSpecialtyId = healthProfessional.specialtyId!!,
                description = "Cardiologia",
                pricingStatus = PricingStatus.PRICED,
                recommendationLevel = AppointmentRecommendationLevel.RECOMMENDED
            )
        ),
        serviceType = HealthSpecialistResourceBundleServiceType.EXAM,
        updatedAt = LocalDateTime.now(),
        createdAt = LocalDateTime.now(),
        executionAmount = 1,
        executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
        status = Status.ACTIVE
    )

    private val resourcePricing = SpecialistResourcePricingListing(
        resourceId = resource.id,
        medicalSpecialtyId = healthProfessional.specialtyId!!,
        pricingPeriods = listOf(
            SpecialistResourcePricingPeriod(
                beginAt = LocalDate.now(),
                endAt = null,
                prices = listOf(
                    ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_0, 100.0.toBigDecimal()),
                    ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_1, 100.0.toBigDecimal()),
                    ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_2, 100.0.toBigDecimal()),
                    ResourceBundleSpecialtyPrice(SpecialistTier.EXPERT, TierType.TIER_3, 100.0.toBigDecimal()),
                )
            )
        )
    )


    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
    }

    @AfterTest
    fun afterTest() = clearAllMocks()

    @Test
    fun `#autoComplete should call service`() = runBlocking {
        val expected = listOf(
            ResourceAutocompleteResponse(
                id = resource.id,
                primaryTuss = resource.primaryTuss.code,
                aliceCode = resource.code,
                description = resource.description,
            )
        )

        val expectedFilter = SpecialistResourceFilters(
            query = "test",
            medicalSpecialtyIds = listOf(healthProfessional.specialtyId!!),
            pricingStatus = PricingStatus.PRICED,
            status = Status.ACTIVE,
        )

        coEvery { specialistResourceService.list(expectedFilter, 0..5) } returns PaginatedList(
            items = listOf(resource),
            totalItems = 1,
            range = 0..5
        ).success()


        authenticatedAs(token, staff) {
            get("/v2/health_professionals/$staffId/resources_pricing/autocomplete?filter={'q': 'test'}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { specialistResourceService.list(any(), any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `#getResources should call service`() = runBlocking {
        val quantities = SpecialistResourceQuantities(
            serviceType = mapOf(
                HealthSpecialistResourceBundleServiceType.EXAM to 1,
                HealthSpecialistResourceBundleServiceType.PROCEDURE to 2,
                HealthSpecialistResourceBundleServiceType.CONSULTATION to 3

            ),
            recommendationLevel = mapOf(
                AppointmentRecommendationLevel.NONE to 10,
                AppointmentRecommendationLevel.DEFAULT to 1,
                AppointmentRecommendationLevel.RECOMMENDED to 5,
            ),
            pricingStatus = emptyMap(),
        )

        val expectedFilter = SpecialistResourceFilters(
            query = null,
            medicalSpecialtyIds = listOf(healthProfessional.specialtyId!!),
            pricingStatus = PricingStatus.PRICED,
            status = Status.ACTIVE,
            recommendationLevel = listOf(
                AppointmentRecommendationLevel.RECOMMENDED,
                AppointmentRecommendationLevel.DEFAULT,
            )
        )

        coEvery { specialistResourceService.listQuantities(healthProfessional.specialtyId!!) } returns quantities.success()
        coEvery { specialistResourceService.list(expectedFilter, 0..20) } returns PaginatedList(
            items = listOf(resource),
            totalItems = 1,
            range = 0..20
        ).success()

        coEvery { specialistResourceService.getPricing(listOf(healthProfessional.specialtyId!!), listOf(resource.id)) } returns listOf(resourcePricing).success()

        authenticatedAs(token, staff) {
            get("/v2/health_professionals/$staffId/resources_pricing") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce {
            specialistResourceService.list(any(), any())
            specialistResourceService.listQuantities(any())
            healthProfessionalService.findByStaffId(any(), any())
        }
    }
}
