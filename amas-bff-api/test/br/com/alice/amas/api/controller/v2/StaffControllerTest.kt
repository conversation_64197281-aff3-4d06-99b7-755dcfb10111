package br.com.alice.amas.api.controller.v2

import br.com.alice.amas.api.controller.ControllerTestHelper
import br.com.alice.amas.api.models.FriendlyEnumResponse
import br.com.alice.amas.api.models.v2.HealthProfessionalDetailsResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TierType
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class StaffControllerTest : ControllerTestHelper() {

    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()

    private val controller = StaffController(
        healthProfessionalService,
        providerUnitService
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @AfterTest
    fun afterTest() = clearAllMocks()

    @Test
    fun `#healthProfessionalDetails should return success`() {
        val staffId = RangeUUID.generate()
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = staffId,
            theoristTier = SpecialistTier.ULTRA_EXPERT,
            tier = SpecialistTier.SUPER_EXPERT
        )

        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional

        val expected = HealthProfessionalDetailsResponse(
            id = healthProfessional.staffId.toString(),
            fullName = healthProfessional.name,
            profilePictureUrl = healthProfessional.imageUrl,
            theoreticalTier = FriendlyEnumResponse(
                friendlyName = healthProfessional.theoristTier!!.description,
                value = healthProfessional.theoristTier!!
            ),
            attendanceTiers = FriendlyEnumResponse(
                friendlyName = "Membros tier 0 e 1",
                value = listOf(TierType.TIER_0, TierType.TIER_1)
            ),
            specialtyId = healthProfessional.specialtyId?.toString()
        )

        authenticatedAs(token, staff) {
            get("/staff/$staffId/health_professional") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { healthProfessionalService.findByStaffId(staffId) }

    }

    @Test
    fun `#healthProfessionalDetails should return success without tiers when External paid health professional`() {
        val staffId = RangeUUID.generate()
        val healthProfessional = TestModelFactory.buildHealthProfessional(
            staffId = staffId,
            theoristTier = SpecialistTier.ULTRA_EXPERT,
            tier = SpecialistTier.SUPER_EXPERT,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            role = Role.ANESTHETIST
        )

        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional

        val expected = HealthProfessionalDetailsResponse(
            id = healthProfessional.staffId.toString(),
            fullName = healthProfessional.name,
            profilePictureUrl = healthProfessional.imageUrl,
            theoreticalTier = null,
            attendanceTiers = null,
            specialtyId = healthProfessional.specialtyId.toString()
        )

        authenticatedAs(token, staff) {
            get("/staff/$staffId/health_professional") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { healthProfessionalService.findByStaffId(staffId) }

    }

}
