package br.com.alice.amas.api.controller.v2

import br.com.alice.amas.api.models.Colors
import br.com.alice.amas.api.models.FilterOption
import br.com.alice.amas.api.models.FriendlyEnumResponse
import br.com.alice.amas.api.models.v2.Resource
import br.com.alice.amas.api.models.v2.ResourceAutocompleteResponse
import br.com.alice.amas.api.models.v2.ResourceBundleCode
import br.com.alice.amas.api.models.v2.ResourcePricingResponse
import br.com.alice.amas.api.models.v2.ResourceTierPrice
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Status
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.models.PaginatedList
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import br.com.alice.exec.indicator.models.SpecialistResourcePricingListing
import br.com.alice.exec.indicator.client.specialistResources.SpecialistResourceService
import br.com.alice.exec.indicator.models.SpecialistResourceQuantities
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.map
import io.ktor.http.Parameters
import java.rmi.UnexpectedException
import java.util.UUID

class ResourceController(
   private val healthProfessionalService: HealthProfessionalService,
   private val specialistResourceService: SpecialistResourceService
): Controller() {

    suspend fun autoComplete(staffId: UUID, params: Parameters): Response {
        val query = parseQuery(params)
        val (specialtyId, _) = getMedicalSpecialtyAndTier(staffId)

        return specialistResourceService.list(
            SpecialistResourceFilters(
                query = query,
                medicalSpecialtyIds = listOf(specialtyId),
                pricingStatus = PricingStatus.PRICED,
                status = Status.ACTIVE,
            ),
            range = 0..5,
        )
            .map { it.items }
            .mapEach { resource ->
                ResourceAutocompleteResponse(
                    id = resource.id,
                    primaryTuss = resource.primaryTuss.code,
                    aliceCode = resource.code,
                    description = resource.description,
                )
            }.foldResponse()
    }

    suspend fun getResources(staffId: UUID, params: Parameters): Response {
        val (specialtyId, specialistTier) = getMedicalSpecialtyAndTier(staffId)

        val quantities = specialistResourceService.listQuantities(specialtyId).get()

        val (query, typeFilter) = params.getFilters(quantities)
        return specialistResourceService.list(
            SpecialistResourceFilters(
                query = query,
                medicalSpecialtyIds = listOf(specialtyId),
                pricingStatus = PricingStatus.PRICED,
                serviceType = typeFilter?.toServiceType()?.let { listOf(it) },
                status = Status.ACTIVE,
                recommendationLevel =
                    if (typeFilter == ResourceFilters.SUGGESTED) listOf(AppointmentRecommendationLevel.RECOMMENDED, AppointmentRecommendationLevel.DEFAULT)
                    else null
            ),
            range = 0..20,
        )
            .flatMapPair {
                specialistResourceService.getPricing(
                    medicalSpecialtiesIds = listOf(specialtyId),
                    resourceIds = it.items.map { it.id },
                    onlyCurrent = true,
                )
            }
            .map { it.toResponse(specialistTier) }
            .map {
                ResourcePricingResponse(
                    filters = mapOf(
                        "query" to query.orEmpty(),
                        "type" to (typeFilter?.name).orEmpty(),
                    ),
                    filterOptions = quantities.filterOptions(),
                    results = it,
                )
            }.foldResponse()
    }

    private fun Pair<List<SpecialistResourcePricingListing>, PaginatedList<SpecialistResource>>.toResponse(specialistTier: SpecialistTier) =
        this.second.items.map { resource ->
            Resource(
                primaryTuss = resource.primaryTuss.code,
                bundleCodes = resource.secondaryResources.map {
                    ResourceBundleCode(
                        tussCode = it.code,
                        description = it.description,
                    )
                },
                aliceCode = resource.code,
                description = resource.description,
                type = FriendlyEnumResponse(
                    friendlyName = resource.serviceType.description,
                    value = resource.serviceType,
                    color = when (resource.serviceType) {
                        HealthSpecialistResourceBundleServiceType.CONSULTATION -> Colors.BLUE
                        HealthSpecialistResourceBundleServiceType.EXAM -> Colors.YELLOW
                        HealthSpecialistResourceBundleServiceType.PROCEDURE -> Colors.VIOLET
                        HealthSpecialistResourceBundleServiceType.UNDEFINED -> Colors.GRAY
                    }
                ),
                prices = this.first
                    .find { it.resourceId == resource.id }?.pricingPeriods
                    ?.first()
                    ?.let {
                        it.prices.filter { it.tier == specialistTier }
                    }?.map {
                        ResourceTierPrice(
                            tier = it.productTier.toFriendlyEnumResponse(),
                            price = it.price,)
                    } ?: emptyList()
            )
        }

    private suspend fun getMedicalSpecialtyAndTier(staffId: UUID): Pair<UUID, SpecialistTier> {
        val healthProfessional = healthProfessionalService.findByStaffId(staffId).get()

        val medicalSpecialtyId = healthProfessional.specialtyId?: throw UnexpectedException("Medical specialty not found")
        val specialistTier = healthProfessional.tier ?: throw UnexpectedException("Specialist tier not found")

        return medicalSpecialtyId to specialistTier
    }

    private fun Parameters.getFilters(quantities: SpecialistResourceQuantities): Pair<String?, ResourceFilters?> {
        val query = parseQuery(this)

        val defaultType =
            if (query.isNullOrBlank()) quantities.getDefault()
            else null

        val typeFilter = parseFilter<String>(this, "type")
            ?.let { ResourceFilters.valueOf(it) } ?: defaultType

        return query to typeFilter
    }

    private fun SpecialistResourceQuantities.filterOptions() =
        mapOf(
            "type" to listOf(
                FilterOption(
                    value = ResourceFilters.PROCEDURE.name,
                    name = ResourceFilters.PROCEDURE.description,
                    count = this.serviceType[HealthSpecialistResourceBundleServiceType.PROCEDURE] ?: 0
                ),
                FilterOption(
                    value = ResourceFilters.EXAM.name,
                    name = ResourceFilters.EXAM.description,
                    count = this.serviceType[HealthSpecialistResourceBundleServiceType.EXAM] ?: 0
                ),
                FilterOption(
                    value = ResourceFilters.CONSULTATION.name,
                    name = ResourceFilters.CONSULTATION.description,
                    count = this.serviceType[HealthSpecialistResourceBundleServiceType.CONSULTATION] ?: 0
                ),
                FilterOption(
                    value = ResourceFilters.SUGGESTED.name,
                    name = ResourceFilters.SUGGESTED.description,
                    count = (this.recommendationLevel[AppointmentRecommendationLevel.RECOMMENDED] ?: 0) +
                            (this.recommendationLevel[AppointmentRecommendationLevel.DEFAULT] ?: 0)
                ),
            )
        )

    private fun ResourceFilters.toServiceType(): HealthSpecialistResourceBundleServiceType? {
        return when (this) {
            ResourceFilters.PROCEDURE -> HealthSpecialistResourceBundleServiceType.PROCEDURE
            ResourceFilters.EXAM -> HealthSpecialistResourceBundleServiceType.EXAM
            ResourceFilters.CONSULTATION -> HealthSpecialistResourceBundleServiceType.CONSULTATION
            else -> null
        }
    }

    private fun SpecialistResourceQuantities.getDefault(): ResourceFilters? {
        val suggestedCount =
            (this.recommendationLevel[AppointmentRecommendationLevel.RECOMMENDED] ?: 0) +
                    (this.recommendationLevel[AppointmentRecommendationLevel.DEFAULT] ?: 0)


        return when {
            suggestedCount > 0 -> ResourceFilters.SUGGESTED
            (this.serviceType[HealthSpecialistResourceBundleServiceType.CONSULTATION] ?: 0) > 0 -> ResourceFilters.CONSULTATION
            (this.serviceType[HealthSpecialistResourceBundleServiceType.PROCEDURE] ?: 0) > 0 -> ResourceFilters.PROCEDURE
            (this.serviceType[HealthSpecialistResourceBundleServiceType.EXAM] ?: 0) > 0 -> ResourceFilters.EXAM
            else -> null
        }
    }

    private fun TierType.toFriendlyEnumResponse() =
        FriendlyEnumResponse(
            friendlyName = this.title,
            value = this
        )
}

enum class ResourceFilters(val description: String) {
    PROCEDURE("Procedimentos"),
    EXAM("Exames"),
    CONSULTATION("Consultas"),
    SUGGESTED("Códigos sugeridos")
}

