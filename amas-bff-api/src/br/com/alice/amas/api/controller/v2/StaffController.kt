package br.com.alice.amas.api.controller.v2

import br.com.alice.amas.api.converters.v2.toHealthProfessionalDetailsResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class StaffController(
    private val healthProfessionalService: HealthProfessionalService,
    private val providerUnitService: ProviderUnitService
) : Controller() {

    suspend fun healthProfessionalDetails(staffId: UUID): Response =
        healthProfessionalService.findByStaffId(staffId)
            .map { it.toHealthProfessionalDetailsResponse() }
            .foldResponse()


    suspend fun listHealthProfessional(providerUnitId: UUID): Response {
        val currentUserId = currentUserIdKey() as String?
        val currentStaffId = currentUserId?.toSafeUUID() ?: throw IllegalArgumentException("StaffId not found")

        return providerUnitService.get(providerUnitId)
            .map { it.clinicalStaffIds ?: emptyList() }
            .map { clinicalStaffIds ->
                if (currentStaffId in clinicalStaffIds) listOf(currentStaffId)
                else clinicalStaffIds
            }
            .flatMap { healthProfessionalService.findByStaffIds(it) }
            .mapEach { it.toHealthProfessionalDetailsResponse() }
            .foldResponse()
    }
}
