package br.com.alice.amas.api.models.v2

import br.com.alice.amas.api.models.FilterOption
import br.com.alice.amas.api.models.FriendlyEnumResponse
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.TierType
import java.math.BigDecimal
import java.util.UUID


data class ResourcePricingResponse(
    val filterOptions: Map<String, List<FilterOption>> = emptyMap(),
    val filters: Map<String, Any> = emptyMap(),
    val results: List<Resource>
)

data class Resource(
    val primaryTuss: String,
    val aliceCode: String,
    val description: String,
    val type: FriendlyEnumResponse<HealthSpecialistResourceBundleServiceType>,
    val prices: List<ResourceTierPrice>,
    val bundleCodes: List<ResourceBundleCode>
)

data class ResourceTierPrice(
    val tier: FriendlyEnumResponse<TierType>,
    val price: BigDecimal,
)

data class ResourceBundleCode(
    val tussCode: String,
    val description: String,
)

data class ResourceAutocompleteResponse(
    val id: UUID,
    val primaryTuss: String,
    val aliceCode: String,
    val description: String,
)
