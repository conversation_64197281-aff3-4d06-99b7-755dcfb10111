package br.com.alice.amas.api.models.v2

import br.com.alice.amas.api.models.FriendlyEnumResponse
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.TierType

data class HealthProfessionalDetailsResponse(
    val id: String,
    val fullName: String,
    val profilePictureUrl: String?,
    val theoreticalTier: FriendlyEnumResponse<SpecialistTier>?,
    val attendanceTiers: FriendlyEnumResponse<List<TierType>>?,
    val specialtyId: String? = null,
)
