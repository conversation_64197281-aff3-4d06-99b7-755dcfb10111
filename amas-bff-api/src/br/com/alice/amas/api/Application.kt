package br.com.alice.amas.api

import br.com.alice.amas.api.controller.InvoiceController
import br.com.alice.amas.api.controller.InvoicingController
import br.com.alice.amas.api.controller.NationalReceiptController
import br.com.alice.amas.api.controller.PreviewEarningSummaryController
import br.com.alice.amas.api.controller.TissBatchController
import br.com.alice.amas.api.controller.TissGuiaExpenseController
import br.com.alice.amas.api.controller.TissGuiaProcedureController
import br.com.alice.amas.api.controller.v2.ResourceController
import br.com.alice.amas.api.ioc.ServiceModule
import br.com.alice.amas.api.routes.apiRoutes
import br.com.alice.amas.api.services.UploadPreviewEarningSummaryService
import br.com.alice.amas.api.services.UploadTissBatchService
import br.com.alice.amas.ioc.AmasDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.plugin.RateLimitPlugin
import br.com.alice.common.application.setupBffApi
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.headerOpenTelemetryTraceId
import br.com.alice.common.headerTraceId
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.AMAS_API_ROOT_SERVICE_NAME
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.HttpRedirect
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module
import java.time.Duration

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        AmasDomainClientModule,
        FileVaultClientModule,
        ExecIndicatorDomainClientModule,
        ProviderDomainClientModule,
        ServiceModule,
        KafkaProducerModule,
        PersonDomainClientModule,
        EhrDomainClientModule,
        StaffDomainClientModule,

        module(createdAtStart = true) {
            single { config }
            single {
                DefaultHttpClient({
                    install(HttpRedirect) {
                        checkHttpMethod = false
                    }
                    install(ContentNegotiation) { simpleGson() }
                }, timeoutInMillis = 15_000)
            }

            // Controllers
            single { TissBatchController(get(), get()) }
            single { HealthController(AMAS_API_ROOT_SERVICE_NAME) }
            single { InvoicingController(get(), get(), get()) }
            single { TissGuiaProcedureController(get(), get(), get(), get(), get(), get()) }
            single { TissGuiaExpenseController(get(), get(), get(), get()) }
            single { NationalReceiptController(get(), get(), get()) }
            single { PreviewEarningSummaryController(get(), get()) }
            single { InvoiceController(get(), get()) }
            single { ResourceController(get(), get()) }

            // Services
            single { UploadTissBatchService(get(), get(), get()) }
            single { UploadPreviewEarningSummaryService(get(), get()) }
            single { UploadTissBatchService(get(), get(), get()) }
            single { UploadPreviewEarningSummaryService(get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules
) {
    val maxAgeInDays = 7.toLong()
    setupBffApi(dependencyInjectionModules) {
        install(CORS) {
            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Patch)
            allowMethod(HttpMethod.Delete)
            allowMethod(HttpMethod.Options)
            allowHeader(HttpHeaders.Authorization)
            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.ContentRange)
            allowHeadersPrefixed("X-Datadog-")
            allowHeader("traceparent")
            exposeHeader(HttpHeaders.ContentRange)
            exposeHeader(headerTraceId)
            exposeHeader(headerOpenTelemetryTraceId)
            anyHost()
            maxAgeInSeconds = Duration.ofDays(maxAgeInDays).seconds
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, AMAS_API_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        install(RateLimitPlugin)
    }
}
