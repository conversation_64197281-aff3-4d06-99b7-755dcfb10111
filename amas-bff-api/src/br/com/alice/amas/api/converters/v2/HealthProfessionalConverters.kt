package br.com.alice.amas.api.converters.v2

import br.com.alice.amas.api.models.FriendlyEnumResponse
import br.com.alice.amas.api.models.v2.HealthProfessionalDetailsResponse
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.TierType

private object TierMappings {
    val EXPERT_TIERS = listOf(TierType.TIER_0, TierType.TIER_1, TierType.TIER_2)
    const val EXPERT_TIERS_DESCRIPTION = "Membros tier 0, 1, e 2"

    val SUPER_EXPERT_TIERS = listOf(TierType.TIER_0, TierType.TIER_1)
    const val SUPER_EXPERT_TIERS_DESCRIPTION = "Membros tier 0 e 1"

    val ULTRA_EXPERT_TIERS = listOf(TierType.TIER_0)
    const val ULTRA_EXPERT_TIERS_DESCRIPTION = "Membros tier 0"

    val ALL_TIERS = listOf(TierType.TIER_0, TierType.TIER_1, TierType.TIER_2, TierType.TIER_3, TierType.TIER_4)
    const val ALL_TIERS_DESCRIPTION = "Todos os tiers de membro"
}

fun HealthProfessional.toHealthProfessionalDetailsResponse(): HealthProfessionalDetailsResponse {
    val isExternal = isExternalPaidHealthProfessional()

    return HealthProfessionalDetailsResponse(
        id = this.staffId.toString(),
        fullName = name,
        profilePictureUrl = imageUrl,
        theoreticalTier = if (isExternal) null else theoristTier?.toEnumResponse(),
        attendanceTiers = if (isExternal) null else tier.toAttendanceTiersResponse(),
        specialtyId = this.specialtyId?.toString(),
    )
}

fun SpecialistTier.toEnumResponse() = FriendlyEnumResponse(
    friendlyName = description,
    value = this
)

fun SpecialistTier?.toAttendanceTiersResponse(): FriendlyEnumResponse<List<TierType>> =
    when (this) {
        SpecialistTier.EXPERT -> FriendlyEnumResponse(
            friendlyName = TierMappings.EXPERT_TIERS_DESCRIPTION,
            value = TierMappings.EXPERT_TIERS
        )

        SpecialistTier.SUPER_EXPERT -> FriendlyEnumResponse(
            friendlyName = TierMappings.SUPER_EXPERT_TIERS_DESCRIPTION,
            value = TierMappings.SUPER_EXPERT_TIERS
        )

        SpecialistTier.ULTRA_EXPERT -> FriendlyEnumResponse(
            friendlyName = TierMappings.ULTRA_EXPERT_TIERS_DESCRIPTION,
            value = TierMappings.ULTRA_EXPERT_TIERS
        )

        else -> FriendlyEnumResponse(
            friendlyName = TierMappings.ALL_TIERS_DESCRIPTION,
            value = TierMappings.ALL_TIERS
        )
    }

fun HealthProfessional.isExternalPaidHealthProfessional() = type == StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL
