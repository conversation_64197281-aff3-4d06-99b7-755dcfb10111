package br.com.alice.amas.api.routes

import br.com.alice.amas.api.controller.InvoiceController
import br.com.alice.amas.api.controller.InvoicingController
import br.com.alice.amas.api.controller.NationalReceiptController
import br.com.alice.amas.api.controller.PreviewEarningSummaryController
import br.com.alice.amas.api.controller.TissBatchController
import br.com.alice.amas.api.controller.TissGuiaExpenseController
import br.com.alice.amas.api.controller.TissGuiaProcedureController
import br.com.alice.amas.api.controller.v2.HealthSpecialistResourceBundleController
import br.com.alice.amas.api.controller.v2.InvoiceCritiqueController
import br.com.alice.amas.api.controller.v2.ResourceController
import br.com.alice.amas.api.controller.v2.StaffController
import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.coHandlerLimited
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.patch
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

@Suppress("LongMethod")
fun Routing.apiRoutes() {
    val healthController by inject<HealthController>()
    get("/") { coHandler(healthController::checkHealthWithAuth) }

    authenticate {
        val invoicingController by inject<InvoicingController>()
        val tissBatchController by inject<TissBatchController>()
        val tissGuiaProcedureController by inject<TissGuiaProcedureController>()
        val tissGuiaExpenseController by inject<TissGuiaExpenseController>()
        val nationalReceiptController by inject<NationalReceiptController>()
        val previewEarningSummaryController by inject<PreviewEarningSummaryController>()
        val invoiceController by inject<InvoiceController>()
        val resourceController by inject<ResourceController>()
        val staffController by inject<StaffController>()


        route("/medical-bills") {
            route("/invoice") {

                post("/") {
                    coHandlerLimited(invoicingController::createInvoicingByExecAuthorizerId)
                }

                get("/") {
                    coHandlerLimited(invoicingController::findInvoicing)
                }

                get("/paginated") {
                    coHandlerLimited(invoicingController::findInvoicingPaginated)
                }

                get("/{invoiceId}") {
                    coHandlerLimited("invoiceId", invoicingController::findInvoiceById)
                }

                delete("/{invoiceId}") {
                    asyncLayer {
                        coHandlerLimited("invoiceId", invoicingController::deleteInvoiceById)
                    }
                }

                patch("/{invoiceId}/update-to-receive") {
                    coHandlerLimited("invoiceId", invoicingController::updateInvoiceToReceive)
                }

                patch("/{invoiceId}/update-to-analysis") {
                    coHandlerLimited("invoiceId", invoicingController::updateInvoiceToAnalysis)
                }

                patch("/{invoiceId}/save-analysis") {
                    coHandlerLimited("invoiceId", invoicingController::saveAnalysis)
                }

                patch("/{invoiceId}/save-revision") {
                    coHandlerLimited("invoiceId", invoicingController::saveRevision)
                }

                post("/{invoiceId}/upload-file") {
                    multipartHandler("invoiceId", tissBatchController::uploadTissBatch)
                }

                post("/{invoiceId}/associate-with-nfs") {
                    coHandlerLimited("invoiceId", invoicingController::associateWithNf)
                }

                get("/tiss-batch/{tissBatchId}") {
                    coHandlerLimited("tissBatchId", invoicingController::findInvoiceByTissBatchId)
                }

                post("/tiss-batch/{tissBatchId}/reprocess") {
                    coHandlerLimited("tissBatchId", tissBatchController::reprocessBatchById)
                }

                post("/totvs-approve") {
                    coHandlerLimited(invoicingController::totvsApprove)
                }

                patch("/file/{fileId}") {
                    coHandlerLimited("fileId", invoiceController::updateDraftInvoicesByFileId)
                }

                route("/payment") {
                    get("/{issuerCnpj}") {
                        coHandlerLimited("issuerCnpj", nationalReceiptController::findInvoicePaymentsByIssuerCnpj)
                    }
                    get("/{issuerCnpj}/resync") {
                        coHandlerLimited("issuerCnpj", nationalReceiptController::findInvoicePaymentsByIssuerCnpjResync)
                    }
                    get("/{issuerCnpj}/{invoiceId}") {
                        coHandlerLimited(
                            "issuerCnpj",
                            "invoiceId",
                            nationalReceiptController::findInvoicePaymentsByIssuerCnpjAndInvoiceId
                        )
                    }
                    get("/danfe") {
                        coHandlerLimited(nationalReceiptController::downloadDanfeByAccessKey)
                    }
                }

            }

            route("/batch") {

                get("/invoice/{invoiceId}") {
                    coHandlerLimited("invoiceId", tissBatchController::findTissBatchesByInvoiceId)
                }

                get("/{tissBatchId}") {
                    coHandlerLimited("tissBatchId", tissBatchController::findBatchById)
                }

                patch("/{tissBatchId}/save-analysis") {
                    coHandlerLimited("tissBatchId", tissBatchController::saveAnalysis)
                }

                delete("/{batchId}") {
                    coHandlerLimited("batchId", tissBatchController::removeTissBatch)
                }
            }

            route("/earning-summary") {
                post("/upload") {
                    multipartHandler(previewEarningSummaryController::uploadPreviewEarningSummary)
                }
                get("/") {
                    coHandlerLimited(previewEarningSummaryController::findPreviewEarningSummary)
                }
                delete("/{previewEarningSummaryId}") {
                    coHandlerLimited(
                        "previewEarningSummaryId",
                        previewEarningSummaryController::deletePreviewEarningSummary
                    )
                }
            }

            route("/procedure") {
                post("/") {
                    coHandlerLimited(tissGuiaProcedureController::create)
                }

                patch("/{procedureId}/update-criticize") {
                    coHandlerLimited("procedureId", tissGuiaProcedureController::criticize)
                }

                patch("/{guiaProcedureId}") {
                    coHandlerLimited("guiaProcedureId", tissGuiaProcedureController::update)
                }

                delete("/{guiaProcedureId}") {
                    coHandlerLimited("guiaProcedureId", tissGuiaProcedureController::delete)
                }

            }

            route("/expense") {
                post("/") {
                    coHandlerLimited(tissGuiaExpenseController::create)
                }

                patch("/{expenseId}/update-criticize") {
                    coHandlerLimited("expenseId", tissGuiaExpenseController::criticize)
                }

                patch("/{expenseId}") {
                    coHandlerLimited("expenseId", tissGuiaExpenseController::update)
                }

                delete("/{expenseId}") {
                    coHandlerLimited("expenseId", tissGuiaExpenseController::delete)
                }
            }

            route("/national-receipt") {
                get("/type") {
                    coHandlerLimited(nationalReceiptController::listExpenseTypes)
                }

                post("/") {
                    multipartHandler(nationalReceiptController::upload)
                }

                put("/{nationalReceiptId}") {
                    coHandlerLimited("nationalReceiptId", nationalReceiptController::update)
                }

                delete("/{nationalReceiptId}") {
                    coHandlerLimited("nationalReceiptId", nationalReceiptController::delete)
                }

            }

            route("/adm") {

                route("/invoice") {
                    patch("/{invoiceId}") {
                        coHandlerLimited("invoiceId", invoicingController::updateInvoiceStatus)
                    }
                }

                route("/batch") {
                    patch("/{tissBatchId}") {
                        coHandlerLimited("tissBatchId", tissBatchController::updateBatchStatus)
                    }
                }

            }
        }

        route("/v2") {
            val invoiceController by inject<br.com.alice.amas.api.controller.v2.InvoiceController>()
            val nationalReceiptControllerV2 by inject<br.com.alice.amas.api.controller.v2.NationalReceiptController>()
            val invoiceCritiqueController by inject<InvoiceCritiqueController>()
            val healthSpecialistResourceBundleController by inject<HealthSpecialistResourceBundleController>()

            get("/provider_units/{providerUnitId}/invoices") {
                coHandlerLimited("providerUnitId", invoiceController::getInvoicesByProviderUnit)
            }

            route("/invoices") {
                get("/{invoiceId}") { coHandlerLimited("invoiceId", invoiceController::getInvoice) }
                get("/{invoiceId}/appointment_bills") {
                    coHandlerLimited("invoiceId", invoiceController::getAppointmentBills)
                }
                patch("/{invoiceId}/approve") { coHandlerLimited("invoiceId", invoiceController::approveInvoice) }

                get("/{invoiceId}/other_expenses_bills") {
                    coHandlerLimited("invoiceId", invoiceController::getOtherExpensesBills)
                }

                route("/{invoiceId}/national_receipt") {
                    post("/") { multipartHandler("invoiceId", nationalReceiptControllerV2::upload) }

                    delete("/{nationalReceiptId}") {
                        coHandlerLimited("nationalReceiptId", nationalReceiptController::delete)
                    }

                    get("/") { coHandlerLimited("invoiceId", nationalReceiptControllerV2::listNationalReceipts) }
                }

                get("/{invoiceId}/critiques") {
                    coHandlerLimited("invoiceId", invoiceCritiqueController::listInvoiceCritiques)
                }
                post("/{invoiceId}/critiques") {
                    coHandlerLimited("invoiceId", invoiceCritiqueController::createAdjustmentRequest)
                }
            }

            // Temporary here, should be moved to a backoffice route as soon as ops stops using EITA
            route("/critiques/{critiqueId}") {
                patch("/accept") { coHandlerLimited("critiqueId", invoiceCritiqueController::accept) }
                patch("/reject") { coHandlerLimited("critiqueId", invoiceCritiqueController::reject) }
            }

            get("/enums/critique_reasons") { coHandlerLimited(invoiceCritiqueController::getReasons) }
            get("/health_specialist_resource_bundles") { coHandlerLimited(healthSpecialistResourceBundleController::getHealthSpecialistResourceBundles) }

            get("/provider_units/{providerUnitId}/health_professionals") { coHandlerLimited("providerUnitId", staffController::listHealthProfessional) }
            get("/health_professionals/{staffId}/resources_pricing/autocomplete") { coHandlerLimited("staffId", resourceController::autoComplete) }
            get("/health_professionals/{staffId}/resources_pricing") { coHandlerLimited("staffId", resourceController::getResources) }
        }

        route("/staff") {

            get("/{staffId}/health_professional") {
                coHandlerLimited("staffId", staffController::healthProfessionalDetails)
            }
        }
    }
}
