package br.com.alice.secondary.attention.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType.REFERRAL
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import br.com.alice.product.model.ProductWithBundles
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.secondary.attention.client.SpecialistEligibility
import br.com.alice.secondary.attention.logics.EligibilityLogic
import br.com.alice.secondary.attention.models.EligibilityCheckedExternallyResponse
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import br.com.alice.eita.nullvs.client.NullvsEligibilityService as EitaNullvsEligibilityService
import br.com.alice.eita.nullvs.models.client.NullvsEligibilityCheckResponse as EitaNullvsEligibilityCheckResponse

class EligibilityServiceImplTest {
    private val memberService: MemberService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val producerService: KafkaProducerService = mockk()
    private val productService: ProductService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val specialtyService: MedicalSpecialtyService = mockk()
    private val eitaNullvsEligibilityService: EitaNullvsEligibilityService = mockk()
    private val personService: PersonService = mockk()
    private val cache: GenericCache = mockk()

    private val service = EligibilityServiceImpl(
        memberService,
        producerService,
        productService,
        healthPlanTaskService,
        specialtyService,
        providerUnitService,
        eitaNullvsEligibilityService,
        personService,
        cache
    )

    private val personId = PersonId()
    private val memberMock: Member = mockk()
    private val providerUnit: ProviderUnit = TestModelFactory.buildProviderUnit()
    private val userEmail = "<EMAIL>"
    private val nowDateTime = LocalDateTime.of(2021, 1, 1, 0, 0, 0)
    private val specialist = TestModelFactory.buildHealthProfessional()

    @BeforeTest
    fun setup() {
        every { memberMock.productId } returns mockk()
        coEvery { producerService.produce(any(), any()) } returns mockk()

        mockkStatic(LocalDateTime::class)
        coEvery { LocalDateTime.now() } returns nowDateTime
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Nested
    inner class GetPersonEligibilityOnProviderUnit {

        @Test
        fun `#getPersonEligibilityOnProviderUnit - get values and call core function`() = runBlocking {
            mockkObject(EligibilityLogic)
            val providerId = RangeUUID.generate()
            val providerUnit = providerUnit.copy(providerId = providerId)
            val member = TestModelFactory.buildMember()
            val specialist = TestModelFactory.buildHealthProfessional(specialtyId = RangeUUID.generate())
            val product = TestModelFactory.buildProduct()
            val tiers = SpecialtyTiers(
                tiers = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT),
                specialtyId = specialist.specialtyId!!
            )

            val tierBundle = TestModelFactory.buildProductBundle(
                specialtyTiers = listOf(tiers),
                type = ProductBundleType.SPECIALITY_TIERS
            )
            val providerBundle = TestModelFactory.buildProductBundle(
                providerIds = listOf(providerId),
                type = ProductBundleType.CLINICAL_COMMUNITY
            )
            val productWithBundle = ProductWithBundles(
                product = product,
                bundles = listOf(tierBundle, providerBundle)
            )
            val referral = TestModelFactory.buildHealthPlanTaskReferral()

            coEvery { memberService.findActiveMembership(personId) } returns member.success()
            coEvery { productService.getByIdWithBundles(member.productId) } returns productWithBundle.success()
            coEvery {
                healthPlanTaskService.getAllActiveByPersonAndType(
                    personId,
                    REFERRAL
                )
            } returns listOf(referral).success()
            coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
            every {
                EligibilityLogic.getPersonEligibilityForProviderUnit(
                    personId,
                    providerUnit.id,
                    listOf(providerUnit.id),
                    listOf(referral.specialize()),
                )
            } returns true

            val response = service.getPersonEligibilityOnProviderUnit(
                personId,
                userEmail,
                providerUnit.id
            ).get()

            assertThat(response).isEqualTo(true)

            coVerifyOnce { memberService.findActiveMembership(personId) }
            coVerifyOnce { productService.getByIdWithBundles(any()) }
            coVerifyOnce { healthPlanTaskService.getAllActiveByPersonAndType(any(), any()) }
            coVerifyOnce { EligibilityLogic.getPersonEligibilityForProviderUnit(any(), any(), any(), any()) }
            coVerifyOnce { producerService.produce(any(), personId.toString()) }
        }

        @Test
        fun `#ggetPersonEligibilityOnProviderUnit - get values and call core function when referrals flag is enabled`() =
            runBlocking {
                withFeatureFlag(
                    FeatureNamespace.HEALTH_PLAN,
                    "should_use_new_health_plan_task_deadline_calculation_flow",
                    true
                ) {
                    mockLocalDate { now ->
                        mockkObject(EligibilityLogic)
                        val providerId = RangeUUID.generate()
                        val providerUnit = providerUnit.copy(providerId = providerId)
                        val member = TestModelFactory.buildMember()
                        val specialist =
                            TestModelFactory.buildHealthProfessional(specialtyId = RangeUUID.generate())
                        val product = TestModelFactory.buildProduct()
                        val tiers = SpecialtyTiers(
                            tiers = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT),
                            specialtyId = specialist.specialtyId!!
                        )

                        val tierBundle = TestModelFactory.buildProductBundle(
                            specialtyTiers = listOf(tiers),
                            type = ProductBundleType.SPECIALITY_TIERS
                        )
                        val providerBundle = TestModelFactory.buildProductBundle(
                            providerIds = listOf(providerId),
                            type = ProductBundleType.CLINICAL_COMMUNITY
                        )
                        val productWithBundle = ProductWithBundles(
                            product = product,
                            bundles = listOf(tierBundle, providerBundle)
                        )
                        val referral = TestModelFactory.buildHealthPlanTaskReferral()

                        coEvery { memberService.findActiveMembership(personId) } returns member.success()
                        coEvery { productService.getByIdWithBundles(member.productId) } returns productWithBundle.success()
                        coEvery {
                            healthPlanTaskService.findByPersonAndFilters(
                                personId = personId,
                                filterOptions = HealthPlanTaskFilters(
                                    statuses = listOf(
                                        HealthPlanTaskStatus.ACTIVE,
                                        HealthPlanTaskStatus.DELETED_BY_MEMBER
                                    ),
                                    types = listOf(REFERRAL),
                                    dueDateGreater = now
                                )
                            )
                        } returns listOf(referral).success()
                        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
                        every {
                            EligibilityLogic.getPersonEligibilityForProviderUnit(
                                personId,
                                providerUnit.id,
                                listOf(providerUnit.id),
                                listOf(referral.specialize()),
                            )
                        } returns true

                        val response = service.getPersonEligibilityOnProviderUnit(
                            personId,
                            userEmail,
                            providerUnit.id
                        ).get()

                        assertThat(response).isEqualTo(true)

                        coVerifyOnce { memberService.findActiveMembership(personId) }
                        coVerifyOnce { productService.getByIdWithBundles(any()) }
                        coVerifyOnce { healthPlanTaskService.findByPersonAndFilters(any(), any()) }
                        coVerifyOnce {
                            EligibilityLogic.getPersonEligibilityForProviderUnit(
                                any(),
                                any(),
                                any(),
                                any()
                            )
                        }
                        coVerifyOnce { producerService.produce(any(), personId.toString()) }
                    }
                }

            }

        @Test
        fun `#getPersonEligibilityBySpecialist - use referrals parameter and call core function`() =
            runBlocking {
                mockkObject(EligibilityLogic)
                val providerId = RangeUUID.generate()
                val providerUnit = providerUnit.copy(providerId = providerId)
                val member = TestModelFactory.buildMember()
                val specialist = TestModelFactory.buildHealthProfessional(specialtyId = RangeUUID.generate())
                val product = TestModelFactory.buildProduct()
                val tiers = SpecialtyTiers(
                    tiers = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT),
                    specialtyId = specialist.specialtyId!!
                )

                val tierBundle = TestModelFactory.buildProductBundle(
                    specialtyTiers = listOf(tiers),
                    type = ProductBundleType.SPECIALITY_TIERS
                )
                val providerBundle = TestModelFactory.buildProductBundle(
                    providerIds = listOf(providerId),
                    type = ProductBundleType.CLINICAL_COMMUNITY
                )
                val specialty = TestModelFactory.buildMedicalSpecialty(id = specialist.specialtyId!!)
                val productWithBundle = ProductWithBundles(
                    product = product,
                    bundles = listOf(tierBundle, providerBundle)
                )
                val referral = TestModelFactory.buildHealthPlanTaskReferral().specialize<Referral>()
                val referrals = listOf(referral)

                coEvery { memberService.findActiveMembership(personId) } returns member.success()
                coEvery { productService.getByIdWithBundles(member.productId) } returns productWithBundle.success()
                coEvery { specialtyService.getByIds(listOf(specialist.specialtyId!!)) } returns listOf(specialty).success()
                coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()

                every {
                    EligibilityLogic.getPersonEligibilityForProviderUnit(
                        any(),
                        any(),
                        any(),
                        any(),
                    )
                } returns true

                val response = service.getPersonEligibilityOnProviderUnit(
                    personId,
                    userEmail,
                    providerUnit.id,
                    referrals
                ).get()

                assertThat(response).isEqualTo(true)
                coVerifyOnce {
                    EligibilityLogic.getPersonEligibilityForProviderUnit(
                        personId,
                        providerUnit.id,
                        listOf(providerUnit.id),
                        referrals,
                    )
                }

                coVerifyOnce { memberService.findActiveMembership(personId) }
                coVerifyOnce { productService.getByIdWithBundles(any()) }
                coVerifyNone { healthPlanTaskService.getAllActiveByPersonAndType(any(), any()) }
                coVerifyOnce { producerService.produce(any(), personId.toString()) }
            }
    }

    @Nested
    inner class GetPersonEligibilityBySpecialist {
        @Test
        fun `#getPersonEligibilityBySpecialist - get values and call core function`() = runBlocking {
            mockkObject(EligibilityLogic)
            val providerId = RangeUUID.generate()
            val member = TestModelFactory.buildMember()
            val specialist = specialist.copy(specialtyId = RangeUUID.generate())
            val product = TestModelFactory.buildProduct()
            val tiers = SpecialtyTiers(
                tiers = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT),
                specialtyId = specialist.specialtyId!!
            )

            val tierBundle = TestModelFactory.buildProductBundle(
                specialtyTiers = listOf(tiers),
                type = ProductBundleType.SPECIALITY_TIERS
            )
            val providerBundle = TestModelFactory.buildProductBundle(
                providerIds = listOf(providerId),
                type = ProductBundleType.CLINICAL_COMMUNITY
            )
            val specialty = TestModelFactory.buildMedicalSpecialty(id = specialist.specialtyId!!)
            val productWithBundle = ProductWithBundles(
                product = product,
                bundles = listOf(tierBundle, providerBundle)
            )
            val referral = TestModelFactory.buildHealthPlanTaskReferral()
            val specialistEligibility = SpecialistEligibility(
                id = specialist.id,
                name = specialist.name,
                specialtyName = specialty.name,
                specialtyId = specialty.id,
                tier = specialist.tier,
                isEligible = true,
                isTherapy = false,
                profileImage = null,
                staffId = specialist.staffId,
                subSpecialties = specialist.subSpecialtyIds
            )

            coEvery { memberService.findActiveMembership(personId) } returns member.success()
            coEvery { productService.getByIdWithBundles(member.productId) } returns productWithBundle.success()
            coEvery {
                healthPlanTaskService.getAllActiveByPersonAndType(
                    personId,
                    REFERRAL
                )
            } returns listOf(referral).success()
            coEvery { specialtyService.getByIds(listOf(specialist.specialtyId!!)) } returns listOf(specialty).success()
            coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
            coEvery {
                EligibilityLogic.getPersonEligibilityBySpecialist(
                    personId,
                    specialist,
                    providerUnit.id,
                    emptyList(),
                    listOf(tiers),
                    listOf(referral.specialize()),
                    specialty
                )
            } returns specialistEligibility

            val response = service.getPersonEligibilityBySpecialist(
                personId,
                listOf(specialist),
                userEmail,
                providerUnit.id
            ).get()

            assertThat(response).isEqualTo(listOf(specialistEligibility))

            coVerifyOnce { memberService.findActiveMembership(personId) }
            coVerifyOnce { productService.getByIdWithBundles(any()) }
            coVerifyOnce { healthPlanTaskService.getAllActiveByPersonAndType(any(), any()) }
            coVerifyOnce {
                EligibilityLogic.getPersonEligibilityBySpecialist(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { producerService.produce(any(), personId.toString()) }
        }

        @Test
        fun `#getPersonEligibilityBySpecialist - use referrals parameter and call core function`() =
            runBlocking {
                mockkObject(EligibilityLogic)
                val providerId = RangeUUID.generate()
                val member = TestModelFactory.buildMember()
                val specialist = specialist.copy(specialtyId = RangeUUID.generate())
                val product = TestModelFactory.buildProduct()
                val tiers = SpecialtyTiers(
                    tiers = listOf(SpecialistTier.TALENTED, SpecialistTier.EXPERT),
                    specialtyId = specialist.specialtyId!!
                )

                val tierBundle = TestModelFactory.buildProductBundle(
                    specialtyTiers = listOf(tiers),
                    type = ProductBundleType.SPECIALITY_TIERS
                )
                val providerBundle = TestModelFactory.buildProductBundle(
                    providerIds = listOf(providerId),
                    type = ProductBundleType.CLINICAL_COMMUNITY
                )
                val specialty = TestModelFactory.buildMedicalSpecialty(id = specialist.specialtyId!!)
                val productWithBundle = ProductWithBundles(
                    product = product,
                    bundles = listOf(tierBundle, providerBundle)
                )
                val referral = TestModelFactory.buildHealthPlanTaskReferral().specialize<Referral>()
                val referrals = listOf(referral)
                val specialistEligibility = SpecialistEligibility(
                    id = specialist.id,
                    name = "specialist",
                    specialtyName = "specialty",
                    specialtyId = specialty.id,
                    tier = specialist.tier,
                    isEligible = true,
                    isTherapy = false,
                    profileImage = null,
                    staffId = specialist.staffId
                )

                coEvery { memberService.findActiveMembership(personId) } returns member.success()
                coEvery { productService.getByIdWithBundles(member.productId) } returns productWithBundle.success()
                coEvery { specialtyService.getByIds(listOf(specialist.specialtyId!!)) } returns listOf(specialty).success()
                coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
                coEvery {
                    EligibilityLogic.getPersonEligibilityBySpecialist(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                } returns specialistEligibility

                val response = service.getPersonEligibilityBySpecialist(
                    personId,
                    listOf(specialist),
                    userEmail,
                    providerUnit.id,
                    referrals
                ).get()

                assertThat(response).isEqualTo(listOf(specialistEligibility))
                coVerifyOnce {
                    EligibilityLogic.getPersonEligibilityBySpecialist(
                        personId,
                        specialist,
                        providerUnit.id,
                        emptyList(),
                        listOf(tiers),
                        referrals,
                        specialty
                    )
                }

                coVerifyOnce { memberService.findActiveMembership(personId) }
                coVerifyOnce { productService.getByIdWithBundles(any()) }
                coVerifyNone { healthPlanTaskService.getAllActiveByPersonAndType(any(), any()) }
                coVerifyOnce { producerService.produce(any(), personId.toString()) }
            }
    }

    @Test
    fun `#isSpecialtyEligibility should return if is therapy succesfully`() = runBlocking {
        val medicalSpecialty = TestModelFactory.buildMedicalSpecialty(
            isTherapy = true
        )

        coEvery {
            specialtyService.getById(medicalSpecialty.id)
        } returns medicalSpecialty.success()

        val result = service.isSpecialtyEligibility(
            medicalSpecialty.id
        ).get()

        assertTrue(result)
    }

    @Nested
    inner class CheckPersonEligibilityOnProviderUnitExternally {

        @Test
        fun `#checkEligibilityExternally should return true if person is eligible`() =
            withInvalidCache("checkPersonEligibilityInProviderUnit-60904895068-12345678901234") {
                val personId = PersonId()
                val providerUnitId = RangeUUID.generate()
                val person = TestModelFactory.buildPerson(personId = personId, nationalId = "60904895068")
                val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId, cnpj = "12345678901234")
                val member = TestModelFactory.buildMember(personId = personId)
                val nullvsResponse = EitaNullvsEligibilityCheckResponse(eligible = true, message = null)
                val expectedResult = EligibilityCheckedExternallyResponse(
                    eligible = true,
                    message = null
                )

                coEvery { personService.get(personId) } returns person.success()
                coEvery { providerUnitService.get(providerUnitId) } returns providerUnit.success()
                coEvery {
                    eitaNullvsEligibilityService.checkPersonEligibilityInProviderUnit(
                        person.nationalId,
                        providerUnit.cnpj!!
                    )
                } returns nullvsResponse.success()
                coEvery { memberService.findActiveMembership(personId) } returns member.success()

                val result = service.checkEligibilityExternally(
                    personId,
                    providerUnitId,
                    "email.com"
                ).get()

                assertEquals(expectedResult.eligible, result.eligible)
                assertEquals(expectedResult.message, result.message)

                coVerifyOnce { producerService.produce(any(), personId.toString()) }
            }

        @Test
        fun `#checkEligibilityExternally should return true if there is an error on external check but is not hospital`() =
            withInvalidCache("checkPersonEligibilityInProviderUnit-60904895068-12345678901234") {
                val personId = PersonId()
                val providerUnitId = RangeUUID.generate()
                val person = TestModelFactory.buildPerson(personId = personId, nationalId = "60904895068")
                val providerUnit = TestModelFactory.buildProviderUnit(
                    id = providerUnitId,
                    cnpj = "12345678901234",
                    type = ProviderUnit.Type.LABORATORY
                )
                val member = TestModelFactory.buildMember(personId = personId)
                val expectedResult = EligibilityCheckedExternallyResponse(
                    eligible = true,
                    message = null
                )

                coEvery { personService.get(personId) } returns person.success()
                coEvery { providerUnitService.get(providerUnitId) } returns providerUnit.success()
                coEvery {
                    eitaNullvsEligibilityService.checkPersonEligibilityInProviderUnit(
                        person.nationalId,
                        providerUnit.cnpj!!
                    )
                } returns Exception("").failure()
                coEvery { memberService.findActiveMembership(personId) } returns member.success()

                val result = service.checkEligibilityExternally(
                    personId,
                    providerUnitId,
                    "email.com"
                ).get()

                assertEquals(expectedResult.eligible, result.eligible)
                assertEquals(expectedResult.message, result.message)

                coVerifyOnce { producerService.produce(any(), personId.toString()) }
            }

        @Test
        fun `#checkEligibilityExternally should return false if there is an error on external check and is hospital`() =
            withInvalidCache("checkPersonEligibilityInProviderUnit-60904895068-12345678901234") {
                val personId = PersonId()
                val providerUnitId = RangeUUID.generate()
                val person = TestModelFactory.buildPerson(personId = personId, nationalId = "60904895068")
                val providerUnit = TestModelFactory.buildProviderUnit(
                    id = providerUnitId,
                    cnpj = "12345678901234",
                    type = ProviderUnit.Type.HOSPITAL
                )
                val member = TestModelFactory.buildMember(personId = personId)
                val expectedResult = EligibilityCheckedExternallyResponse(
                    eligible = false,
                    message = "Erro ao verificar elegibilidade, entre em contato com o suporte"
                )

                coEvery { personService.get(personId) } returns person.success()
                coEvery { providerUnitService.get(providerUnitId) } returns providerUnit.success()
                coEvery {
                    eitaNullvsEligibilityService.checkPersonEligibilityInProviderUnit(
                        person.nationalId,
                        providerUnit.cnpj!!
                    )
                } returns Exception("").failure()
                coEvery { memberService.findActiveMembership(personId) } returns member.success()

                val result = service.checkEligibilityExternally(
                    personId,
                    providerUnitId,
                    "email.com"
                ).get()

                assertEquals(expectedResult.eligible, result.eligible)
                assertEquals(expectedResult.message, result.message)

                coVerifyOnce { producerService.produce(any(), personId.toString()) }
            }

        @Test
        fun `#checkPersonEligibilityOnProviderUnitExternally should return false if person is not eligible`() =
            withInvalidCache("checkPersonEligibilityInProviderUnit-60904895068-12345678901234") {
                val personId = PersonId()
                val providerUnitId = RangeUUID.generate()
                val person = TestModelFactory.buildPerson(personId = personId, nationalId = "60904895068")
                val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId, cnpj = "12345678901234")
                val member = TestModelFactory.buildMember(personId = personId)
                val nullvsResponse = EitaNullvsEligibilityCheckResponse(eligible = true, message = "RDA não atendida")
                val expectedResult = EligibilityCheckedExternallyResponse(
                    eligible = true,
                    message = "RDA não atendida"
                )

                coEvery { personService.get(personId) } returns person.success()
                coEvery { providerUnitService.get(providerUnitId) } returns providerUnit.success()
                coEvery {
                    eitaNullvsEligibilityService.checkPersonEligibilityInProviderUnit(
                        person.nationalId,
                        providerUnit.cnpj!!
                    )
                } returns nullvsResponse.success()
                coEvery { memberService.findActiveMembership(personId) } returns member.success()

                val result = service.checkEligibilityExternally(
                    personId,
                    providerUnitId,
                    "email.com"
                ).get()

                assertEquals(expectedResult.eligible, result.eligible)
                assertEquals(expectedResult.message, result.message)

                coVerifyOnce { producerService.produce(any(), personId.toString()) }
            }

        @Test
        fun `#checkEligibilityExternally should return cached response`() =
            runBlocking {
                val personId = PersonId()
                val providerUnitId = RangeUUID.generate()
                val person = TestModelFactory.buildPerson(personId = personId, nationalId = "60904895068")
                val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId, cnpj = "12345678901234")
                val member = TestModelFactory.buildMember(personId = personId)
                val nullvsResponse = NullvsEligibility(eligible = true, message = null)
                val expectedResult = EligibilityCheckedExternallyResponse(
                    eligible = true,
                    message = null
                )

                coEvery {
                    cache.get(
                        "checkPersonEligibilityInProviderUnit-60904895068-12345678901234",
                        NullvsEligibility::class,
                        any(),
                        any(),
                        any()
                    )
                } returns nullvsResponse

                coEvery { personService.get(personId) } returns person.success()
                coEvery { providerUnitService.get(providerUnitId) } returns providerUnit.success()
                coEvery { memberService.findActiveMembership(personId) } returns member.success()

                val result = service.checkEligibilityExternally(
                    personId,
                    providerUnitId,
                    "email.com"
                ).get()

                assertEquals(expectedResult.eligible, result.eligible)
                assertEquals(expectedResult.message, result.message)

                coVerifyOnce { producerService.produce(any(), personId.toString()) }
                coVerifyNone { eitaNullvsEligibilityService.checkPersonEligibilityInProviderUnit(any(), any()) }
            }
    }

    @Test
    fun `checkEligibilityExternally without using cache - on eita nullvs`() = runBlocking {
        val personId = PersonId()
        val providerUnitId = RangeUUID.generate()
        val person = TestModelFactory.buildPerson(personId = personId, nationalId = "60904895068")
        val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId, cnpj = "12345678901234")
        val member = TestModelFactory.buildMember(personId = personId)
        val nullvsResponse = EitaNullvsEligibilityCheckResponse(eligible = true, message = null)
        val expectedResult = EligibilityCheckedExternallyResponse(
            eligible = true,
            message = null
        )

        coEvery { personService.get(personId) } returns person.success()
        coEvery { providerUnitService.get(providerUnitId) } returns providerUnit.success()
        coEvery {
            eitaNullvsEligibilityService.checkPersonEligibilityInProviderUnit(person.nationalId, providerUnit.cnpj!!)
        } returns nullvsResponse.success()
        coEvery { memberService.findActiveMembership(personId) } returns member.success()

        withFeatureFlag(FeatureNamespace.EXEC_INDICATOR, "should_use_eligibility_cache", false) {
            val result = service.checkEligibilityExternally(
                personId,
                providerUnitId,
                "email.com"
            ).get()

            assertEquals(expectedResult.eligible, result.eligible)
            assertEquals(expectedResult.message, result.message)
        }


        coVerifyOnce { producerService.produce(any(), personId.toString()) }
        coVerifyNone { cache.get(any() as String, any(), any(), any(), any()) }
    }

    private fun withInvalidCache(key: String, block: suspend () -> Unit) = runBlocking {
        coEvery { cache.get(key, NullvsEligibility::class, any(), any(), any()) } coAnswers {
            arg<suspend () -> NullvsEligibility>(4).invoke()
        }

        block()
    }

}
