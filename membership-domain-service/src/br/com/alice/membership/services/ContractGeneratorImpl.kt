package br.com.alice.membership.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberContract
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.TermType
import br.com.alice.membership.client.ContractGenerator
import br.com.alice.membership.client.ContractNotReadyException
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.metrics.Metrics
import br.com.alice.membership.model.CreateTermRequest
import br.com.alice.membership.model.events.ContractGeneratedEvent
import br.com.alice.membership.services.contract.ContractTermBuilder
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ContractGeneratorImpl(
    private val personService: PersonService,
    private val contractTermBuilder: ContractTermBuilder,
    private val contractRegistry: ContractRegistry,
    private val onboardingService: OnboardingService,
    private val kafkaProducerService: KafkaProducerService,
) : ContractGenerator {

    override suspend fun generateContract(personId: PersonId): Result<OnboardingContract, Throwable> {
        val onboardingPhase = onboardingService.findByPerson(personId).getOrNullIfNotFound()?.currentPhase

        if (onboardingPhase != OnboardingPhase.CONTRACT) {
            logger.info("Person is not on CONTRACT phase. Ignoring event")
            return ContractNotReadyException().failure()
        }

        return personService.get(personId)
            .flatMap { contractTermBuilder.generateDocument(it) }
            .flatMap { contractRegistry.store(it.person, it.productOrder, it.documentUrl) }
            .then { Metrics.onboardingContractGenerated(it) }
            .then { kafkaProducerService.produce(ContractGeneratedEvent(it)) }
    }

    override suspend fun generateContractB2B(
        member: Member,
        beneficiary: Beneficiary
    ): Result<MemberContract, Throwable> {
        return personService.get(member.personId)
            .map { generateB2BTermsRequest(it, beneficiary) }
            .flatMap { contractRegistry.createMemberContract(member, it) }
    }

    private suspend fun generateB2BTermsRequest(person: Person, beneficiary: Beneficiary): List<CreateTermRequest> =
        coroutineScope {
            val healthDeclarationDocumentTermRequestDeferred = async {
                contractTermBuilder.generateHealthDeclarationDocumentB2B(person, beneficiary).map {
                    CreateTermRequest(it.id, TermType.HEALTH_DECLARATION)
                }.get()
            }
            val gracePeriodDocumentDeferred =
                async { contractTermBuilder.generateGracePeriodDocumentB2B(person, beneficiary) }
            val healthDeclarationDocumentTermRequest = healthDeclarationDocumentTermRequestDeferred.await()
            val gracePeriodDocument = gracePeriodDocumentDeferred.await()
            if (gracePeriodDocument != null) {
                logger.info(
                    "Grace period document generated for person",
                    "beneficiary_id" to beneficiary.id,
                    "gracePeriodType" to beneficiary.gracePeriodType
                )
                
                return@coroutineScope listOf(healthDeclarationDocumentTermRequest) + CreateTermRequest(
                    gracePeriodDocument.id,
                    TermType.GRACE_PERIOD
                )
            }
            return@coroutineScope listOf(healthDeclarationDocumentTermRequest)
        }

}
