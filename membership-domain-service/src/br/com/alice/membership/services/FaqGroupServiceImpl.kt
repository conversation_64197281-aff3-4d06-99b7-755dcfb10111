package br.com.alice.membership.services

import br.com.alice.common.Brand
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.FaqGroup
import br.com.alice.data.layer.models.FaqGroupType
import br.com.alice.data.layer.services.FaqGroupModelDataService
import br.com.alice.membership.client.FaqGroupService
import br.com.alice.membership.converters.toModel
import br.com.alice.membership.converters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class FaqGroupServiceImpl(
    private val faqGroupModelDataService: FaqGroupModelDataService
) : FaqGroupService {

    override suspend fun add(model: FaqGroup) = faqGroupModelDataService.add(model.toModel()).map { it.toTransport() }
    override suspend fun get(id: UUID) = faqGroupModelDataService.get(id).map { it.toTransport() }
    override suspend fun update(model: FaqGroup): Result<FaqGroup, Throwable> =
        faqGroupModelDataService.update(model.toModel()).map { it.toTransport() }

    override suspend fun findByQuery(query: Query): Result<List<FaqGroup>, Throwable> =
        faqGroupModelDataService.findByQuery(query).mapEach { it.toTransport() }

    override suspend fun countByQuery(query: Query): Result<Int, Throwable> =
        faqGroupModelDataService.countByQuery(query)

    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable> =
        faqGroupModelDataService.countGroupedByQuery(query)

    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable> =
        faqGroupModelDataService.existsByQuery(query)

    override suspend fun findActive(brand: Brand): Result<List<FaqGroup>, Throwable> = useReadDatabase {
        faqGroupModelDataService.find { where { this.active.eq(true) and this.brand.eq(brand) } }.mapEach { it.toTransport() }
    }

    override suspend fun findByType(type: FaqGroupType): Result<List<FaqGroup>, Throwable> =
        faqGroupModelDataService.find { where { this.active.eq(true) and this.groupType.eq(type) } }.mapEach { it.toTransport() }
}
