package br.com.alice.membership.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.FaqGroup
import br.com.alice.data.layer.models.FaqGroupType
import br.com.alice.data.layer.services.FaqGroupModelDataService
import br.com.alice.membership.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.Test

class FaqGroupServiceTest {

    val person = TestModelFactory.buildPerson()

    private val faqGroupModelDataService: FaqGroupModelDataService = mockk()
    private val faqGroupService = FaqGroupServiceImpl(faqGroupModelDataService)

    @Test
    fun `#queryBuilder returns QueryBuilder`() = runBlocking<Unit> {
        Assertions.assertThat(FaqGroupServiceImpl(faqGroupModelDataService).queryBuilder()).isInstanceOf(
            QueryBuilder::class.java
        )
    }

    @Test
    fun `#findActive should return expected Faq Groups`() = runBlocking {
        val brand = Brand.ALICE
        val faqGroups = listOf(
            TestModelFactory.buildRandom(FaqGroup::class),
            TestModelFactory.buildRandom(FaqGroup::class)
        )

        coEvery { faqGroupModelDataService.find(queryEq { where { this.active.eq(true) and this.brand.eq(brand) } }) } returns
                listOf(faqGroups[0].toModel(), faqGroups[1].toModel()).success()

        val result = faqGroupService.findActive(brand)
        assertThat(result).isSuccessWithData(faqGroups)
    }

    @Test
    fun `#findByType - should return Faq Group by type`() = runBlocking {
        val faqGroup = TestModelFactory.buildRandom(FaqGroup::class)
        val groupType = FaqGroupType.TIER_1

        coEvery {
            faqGroupModelDataService.find(
                queryEq {
                    where { this.active.eq(true) and this.groupType.eq(groupType) }
                }
            )} returns listOf(faqGroup.toModel()).success()

        val result = faqGroupService.findByType(groupType)
        assertThat(result).isSuccessWithData(listOf(faqGroup))
        coVerifyOnce { faqGroupModelDataService.find(any()) }
    }
}
