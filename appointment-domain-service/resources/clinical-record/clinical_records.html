<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8"/>
    <meta content="text/html; charset=UTF-8" http-equiv="content-type"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="https://ui.alice.com.br/fonts/index.css"/>

    <style type="text/css">
        body {
            font-family: 'Poligon', sans-serif;
            padding: 0;
            margin: 0 auto;
        }
        header, #footer {
            display: block;
            background-color: #F3F2F0;
            padding: 12px 20px;
        }

        .logo, .issue-date {
            display: inline-block;
        }

        .issue-date {
            display: block;
            float: right;
            vertical-align: middle;
        }

        .logo img {
            display: block;
            width: 85px;
        }
        main {
            padding: 24px;
        }

        .clinical-record-header {
            padding-top: 40px;
            padding-bottom: 56px;
        }

        .clinical-record-header h2 {
            font-size: 24px;
            font-weight: 500;
            line-height: 125%;
        }

        .clinical-record-header ul {
            display: block;
            color: #6F6F6F;
            padding: 0;
        }

        .clinical-record-header li {
            display: inline-block;
            padding-right: 16px;
        }

        .clinical-record-cards {
            display: block;
            width: 100%;
            text-align: left;
        }

        .clinical-record-card-item {
            display: inline-block;
            width: 100%;
            max-width: 25%;
            padding: 16px;
            margin-top: 32px;
            margin-right: 8px;
            border: 1px solid #DDDDD5;
            border-radius: 16px;
            text-align: left;
        }

        .clinical-record-card-item h3 {
            color: #6F6F6F;
            font-size: 12px;
            font-weight: 400;
            line-height: 150%;
        }

        .clinical-record-card-item p {
            font-size: 14px;
            line-height: 150%;
        }

        table.clinical-records-table {
            width: 100%;
            -fs-table-paginate: paginate;
            border-spacing: 0;
        }

        table tbody tr td {
            text-align: start;
            padding: 40px 0;
            font-size: 16px;
            font-weight: 400;
            line-height: 150%;
        }

        table tbody tr td span {
            color: #6F6F6F;
        }

        .td-key {
            font-size: 14px;
            color: #6F6F6F;
            vertical-align: top;
            text-align: left;
            padding: 30px 30px 0px 0px;
        }

        .td-content {
            display: block;
            vertical-align: top;
            padding: 15px 0px 0px 0px;
        }

        .divider-row {
            position: relative;
        }

        .divider-row td {
            text-align: center;
        }

        .divider-row span {
            padding: 20px;
        }

        .divider-bar {
            display: inline-block;
            width: 100%;
            height: 0;
            color: #E0E0E0;
            border: 1px solid #E0E0E0;
        }

        #pagecount::after {
            content: counter(pages);
        }

        #pagenumber::after {
            counter-increment: page;
            counter-reset: page 1;

            content: counter(page);
        }

        @page {
            margin: 0.5cm 0.5cm 2cm 0.5cm;

            @bottom-right {
                content: "Página " counter(page) " de " counter(pages);
            }
        }
    </style>
    <title>Registro Clínico</title>
</head>
<body>
    <header>
        <div class="logo">
            <img src="https://s3.us-east-1.amazonaws.com/web.assets.alice.com.br/logo/alice-logo-magenta.png" alt="alice logo" />
        </div>
        <div class="issue-date">
            <p>{{ISSUE_DATE}}</p>
        </div>
    </header>
    <div class="clinical-record-header">
        <h2>{{FULL_NAME}}</h2>
        <ul>
            <li>{{AGE}}</li>
            <li>{{WEIGHT}}</li>
            <li>{{HEIGHT}}</li>
        </ul>
        <div class="clinical-record-cards">
            <div class="clinical-record-card-item">
                <h3>Data da Consulta</h3>
                <p>{{APPOINTMENT_DATE}}</p>
            </div>
            <div class="clinical-record-card-item">
                <h3>Especialidade</h3>
                <p>{{SPECIALTY}}</p>
            </div>
            <div class="clinical-record-card-item">
                <h3>Médico(a) responsável</h3>
                <p>{{RESPONSIBLE_DOCTOR}}</p>
            </div>
        </div>
    </div>
    <table class="clinical-records-table">
        <tbody>
            <tr>
                <td class="td-key">Detalhes do registro</td>
                <td class="td-content">
                    {{APPOINTMENT_DESCRIPTION}}
                </td>
            </tr>

            <tr>
                <td class="td-key">Demandas avaliadas</td>
                <td class="td-content">{{CASE_RECORDS}}</td>
            </tr>

            <tr>
                <td class="td-key">Procedimentos executados</td>
                <td class="td-content">{{PROCEDURES_EXECUTED}}</td>
            </tr>
            {{ANESTHETIST}}
            {{TASK_DIVIDER}}
            {{TASKS}}
            {{EVOLUTION_DIVIDER}}
            {{EVOLUTIONS}}
        </tbody>
    </table>
</body>
</html>
