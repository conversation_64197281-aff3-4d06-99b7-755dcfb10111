package br.com.alice.appointment.logics

import br.com.alice.appointment.model.AnesthetistPdfResponse
import br.com.alice.appointment.model.ClinicalRecordTransport
import br.com.alice.common.Disease
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.clearWhitespaces
import br.com.alice.common.document.pdf.PDFConverter
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.common.readFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthMeasurement
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.EmergencySpecialty
import br.com.alice.data.layer.models.FollowUpInterval
import br.com.alice.data.layer.models.FollowUpIntervalType
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.FrequencyType
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEIGHT
import br.com.alice.data.layer.models.HealthMeasurementInternalType.WEIGHT
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.ReferralSpecialty
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.exec.indicator.models.HealthSpecialistProcedureServiceType
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Ignore
import kotlin.test.Test
import kotlin.test.assertEquals

class RecordTemplateTest {
    private val person = TestModelFactory.buildPerson(
        dateOfBirth = LocalDate.of(1997, 2, 14).atBeginningOfTheDay()
    )
    private val nowMock = LocalDateTime.of(2021, 1, 1, 0, 0)
    private val appointment = TestModelFactory.buildAppointment(
        personId = person.id,
        appointmentDate = nowMock.toLocalDate(),
        description = "O membro não esta bem",
        caseRecordDetails = listOf(
            CaseRecordDetails(
                description = DiseaseDetails(
                    type = Disease.Type.CID_10,
                    value = "A10",
                    description = "CID A10"
                ),
                severity = CaseSeverity.COMPENSATED,
                follow = null,
                observation = "",
            )
        )
    )
    private val frequency = Frequency(
        type = FrequencyType.EVERY,
        unit = PeriodUnit.DAY,
        quantity = 1
    )
    private val tasks = listOf(
        TestModelFactory.buildHealthPlanTaskReferral(
            appointmentId = appointment.id,
            personId = person.id,
            title = "Cardiologia",
            specialty = ReferralSpecialty(name = "Cardiologia"),
            subSpecialty = ReferralSpecialty(name = "Generalista"),
        ),
        TestModelFactory.buildHealthPlanTaskReferral(
            appointmentId = appointment.id,
            personId = person.id,
            title = "Psicologia",
            specialty = ReferralSpecialty(name = "Psicologia"),
            subSpecialty = ReferralSpecialty(name = "Generalista"),
            sessionsQuantity = 10
        ),
        TestModelFactory.buildHealthPlanTaskEmergency(
            appointmentId = appointment.id,
            personId = person.id,
            specialty = EmergencySpecialty(name = "Cardiologia"),
            caseRecordDetails = appointment.caseRecordDetails.orEmpty()
        ),
        TestModelFactory.buildHealthPlanTaskFollowUpRequest(
            appointmentId = appointment.id,
            personId = person.id,
            followUpInterval = FollowUpInterval(
                type = FollowUpIntervalType.DATE_INTERVAL,
                unit = PeriodUnit.DAY,
                quantity = 7
            )
        ),
        TestModelFactory.buildHealthPlanTaskFollowUpRequest(
            appointmentId = appointment.id,
            personId = person.id,
            followUpInterval = FollowUpInterval(
                type = FollowUpIntervalType.AFTER_MEDICAL_DATE_INTERVAL,
                unit = PeriodUnit.DAY,
                quantity = 7
            )
        ),
        TestModelFactory.buildHealthPlanTaskFollowUpRequest(
            appointmentId = appointment.id,
            personId = person.id,
            followUpInterval = FollowUpInterval(
                type = FollowUpIntervalType.AFTER_MEDICAL_TREATMENT,
                unit = null,
                quantity = null
            )
        ),
        TestModelFactory.buildHealthPlanTaskTestRequest(
            personId = person.id,
            appointmentId = appointment.id,
            title = "Exame"
        ),
        TestModelFactory.buildHealthPlanTaskPrescription(personId = person.id, appointmentId = appointment.id),
        TestModelFactory.buildHealthPlanTaskSurgeryPrescription(personId = person.id, appointmentId = appointment.id),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai treinar",
            type = HealthPlanTaskType.PHYSICAL_ACTIVITY
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai comer",
            type = HealthPlanTaskType.EATING
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai dormir",
            type = HealthPlanTaskType.SLEEP
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai sorrir",
            type = HealthPlanTaskType.MOOD
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai fazer outra coisa",
            type = HealthPlanTaskType.OTHERS
        )
    )

    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        name = "Dr. José",
    )
    private val healthProfessionalEvolution = TestModelFactory.buildHealthProfessional(
        name = "Joana"
    )
    private val specialty = TestModelFactory.buildMedicalSpecialty(
        id = healthProfessional.specialtyId!!,
        name = "Cardiologia"
    )

    private val healthSpecialistProcedure = HealthSpecialistProcedure(
        tussCode = "12345678",
        aliceCode = "12345678",
        description = "Consulta Alice",
        serviceType = HealthSpecialistProcedureServiceType.CONSULTATION,
        hasPrice = true,
    )
    private val evolutions = listOf(
        TestModelFactory.buildAppointmentEvolution(
            appointmentId = appointment.id,
            staffId = healthProfessionalEvolution.staffId,
            description = "Evolução",
            createdAt = nowMock
        )
    )

    private val anesthetist = TestModelFactory.buildHealthProfessional(
        name = "Dr. Anestesia",
        council = Council(
            number = "102030",
            state = State.SP,
            type = CouncilType.CRM
        )
    )

    private val heightMeasurement =
        buildHealthMeasurement(value = BigDecimal("1.70"), type = HEIGHT, personId = person.id)
    private val weightMeasurement =
        buildHealthMeasurement(value = BigDecimal("70"), type = WEIGHT, personId = person.id)

    private val transport = ClinicalRecordTransport(
        appointment = appointment,
        person = person,
        tasks = tasks,
        procedures = mapOf(healthSpecialistProcedure.tussCode to healthSpecialistProcedure),
        professionalAndSpecialty = Pair(healthProfessional, specialty),
        evolutions = evolutions.map { it to healthProfessionalEvolution },
        personInfoData = listOf(heightMeasurement, weightMeasurement),
        anesthetist = AnesthetistPdfResponse(
            type = AnesthetistType.INDIVIDUAL,
            healthProfessional = anesthetist,
        ),
    )

    @Test
    fun `applyClinicalRecord should return html`() = mockLocalDateTime(nowMock) {
        mockLocalDate(nowMock.toLocalDate()) {
            val responseString = readFile("testResources/html-with-tasks.html")

            val result = RecordTemplate.applyClinicalRecord(transport)
            assertEquals(result.clearWhitespaces(), responseString.clearWhitespaces())
        }
    }

    @Test
    fun `applyClinicalRecord should return html when anesthetist in house`() = mockLocalDateTime(nowMock) {
        mockLocalDate(nowMock.toLocalDate()) {
            val transport = transport.copy(
                anesthetist = AnesthetistPdfResponse(
                    type = AnesthetistType.IN_HOUSE,
                    healthProfessional = null
                )
            )
            val responseString = readFile("testResources/html-with-tasks.html")
                .replace("<p>Dr.Anestesia</p>", "<p>${AnesthetistType.IN_HOUSE.description}</p>")
                .replace("<span>CRM 102030-SP</span>", "<span></span>")

            val result = RecordTemplate.applyClinicalRecord(transport)
            assertEquals(result.clearWhitespaces(), responseString.clearWhitespaces())
        }
    }

    @Test
    fun `applyClinicalRecord should return html without tasks, evolutions and anesthetist`() =
        mockLocalDateTime(nowMock) {
            mockLocalDate(nowMock.toLocalDate()) {
                val dataWithOutTasksAndEvolution =
                    transport.copy(tasks = emptyList(), evolutions = emptyList(), anesthetist = null)
                val responseString = readFile("testResources/html-without-tasks.html")

                val result = RecordTemplate.applyClinicalRecord(dataWithOutTasksAndEvolution)
                assertEquals(result.clearWhitespaces(), responseString.clearWhitespaces())
            }
        }

    @Test
    fun `applyClinicalRecord should return html without tasks, evolutions and anesthetist not applicable`() =
        mockLocalDateTime(nowMock) {
            mockLocalDate(nowMock.toLocalDate()) {
                val dataWithOutTasksAndEvolution = transport.copy(
                    tasks = emptyList(),
                    evolutions = emptyList(),
                    anesthetist = AnesthetistPdfResponse(
                        type = AnesthetistType.NOT_APPLICABLE,
                        healthProfessional = null
                    )
                )
                val responseString = readFile("testResources/html-without-tasks.html")

                val result = RecordTemplate.applyClinicalRecord(dataWithOutTasksAndEvolution)
                assertEquals(result.clearWhitespaces(), responseString.clearWhitespaces())
            }
        }

    @Test
    @Ignore
    fun `should generated html with tasks`() {
        val result = RecordTemplate.applyClinicalRecord(transport)
        val pdf = PDFConverter.htmlToPDF(result)
        File("appointment-${LocalDateTime.now()}.pdf").writeBytes(pdf)
    }
}
