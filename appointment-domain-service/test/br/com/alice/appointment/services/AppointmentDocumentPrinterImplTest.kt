package br.com.alice.appointment.services

import br.com.alice.appointment.client.AppointmentEvolutionService
import br.com.alice.appointment.client.AppointmentFileTransport
import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.appointment.logics.RecordTemplate
import br.com.alice.appointment.model.AnesthetistPdfResponse
import br.com.alice.appointment.model.ClinicalRecordTransport
import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.clearWhitespaces
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.document.pdf.PDFConverter
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthMeasurement
import br.com.alice.data.layer.models.Anesthetist
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.EmergencySpecialty
import br.com.alice.data.layer.models.FollowUpInterval
import br.com.alice.data.layer.models.FollowUpIntervalType
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.FrequencyType
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEIGHT
import br.com.alice.data.layer.models.HealthMeasurementInternalType.WEIGHT
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.ehr.client.HealthMeasurementService
import br.com.alice.exec.indicator.client.HealthSpecialistProcedureService
import br.com.alice.exec.indicator.client.TussCodeWithAliceCodeRequest
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.exec.indicator.models.HealthSpecialistProcedureServiceType
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class AppointmentDocumentPrinterImplTest {
    private val personService: PersonService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val appointmentProcedureExecutedService: AppointmentProcedureExecutedService = mockk()
    private val healthSpecialistProcedureService: HealthSpecialistProcedureService = mockk()
    private val appointmentEvolutionService: AppointmentEvolutionService = mockk()
    private val healthMeasurementService: HealthMeasurementService = mockk()
    val service = AppointmentDocumentPrinterImpl(
        personService,
        appointmentService,
        healthPlanTaskService,
        healthProfessionalService,
        medicalSpecialtyService,
        appointmentProcedureExecutedService,
        healthSpecialistProcedureService,
        appointmentEvolutionService,
        healthMeasurementService
    )
    private val person = TestModelFactory.buildPerson(
        dateOfBirth = LocalDate.of(1997, 2, 14).atBeginningOfTheDay()
    )
    private val nowMock = LocalDate.of(2021, 1, 1).atBeginningOfTheDay()
    private val dateCustomFormatter = "20210101"
    private val anesthetistHp = TestModelFactory.buildHealthProfessional(
        name = "Dr. Anestesista",
    )
    private val anesthetist = AnesthetistPdfResponse(
        type = AnesthetistType.INDIVIDUAL,
        healthProfessional = anesthetistHp
    )

    private val appointment = TestModelFactory.buildAppointment(
        personId = person.id,
        appointmentDate = nowMock.toLocalDate(),
        description = "O membro não esta bem",
        caseRecordDetails = listOf(
            CaseRecordDetails(
                description = DiseaseDetails(
                    type = Disease.Type.CID_10,
                    value = "A10",
                    description = "CID A10"
                ),
                severity = CaseSeverity.COMPENSATED,
                follow = null,
                observation = "",
            )
        )
    ).copy(
        anesthetist = Anesthetist(
            type = AnesthetistType.INDIVIDUAL,
            staffId = anesthetistHp.staffId
        )
    )

    private val frequency = Frequency(type = FrequencyType.TIMES, unit = PeriodUnit.MONTH, quantity = 1)
    private val tasks = listOf(
        TestModelFactory.buildHealthPlanTask(appointmentId = appointment.id, personId = person.id),
        TestModelFactory.buildHealthPlanTaskEmergency(
            appointmentId = appointment.id,
            personId = person.id,
            specialty = EmergencySpecialty(name = "Cardiologia"),
            caseRecordDetails = appointment.caseRecordDetails.orEmpty()
        ),
        TestModelFactory.buildHealthPlanTaskFollowUpRequest(
            appointmentId = appointment.id,
            personId = person.id,
            followUpInterval = FollowUpInterval(
                type = FollowUpIntervalType.DATE_INTERVAL,
                unit = PeriodUnit.DAY,
                quantity = 7
            )
        ),
        TestModelFactory.buildHealthPlanTaskFollowUpRequest(
            appointmentId = appointment.id,
            personId = person.id,
            followUpInterval = FollowUpInterval(
                type = FollowUpIntervalType.AFTER_MEDICAL_DATE_INTERVAL,
                unit = PeriodUnit.DAY,
                quantity = 7
            )
        ),
        TestModelFactory.buildHealthPlanTaskFollowUpRequest(
            appointmentId = appointment.id,
            personId = person.id,
            followUpInterval = FollowUpInterval(
                type = FollowUpIntervalType.AFTER_MEDICAL_TREATMENT,
                unit = null,
                quantity = null
            )
        ),
        TestModelFactory.buildHealthPlanTaskTestRequest(personId = person.id, appointmentId = appointment.id),
        TestModelFactory.buildHealthPlanTaskPrescription(personId = person.id, appointmentId = appointment.id),
        TestModelFactory.buildHealthPlanTaskSurgeryPrescription(personId = person.id, appointmentId = appointment.id),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai treinar",
            type = HealthPlanTaskType.PHYSICAL_ACTIVITY
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai Comer",
            type = HealthPlanTaskType.EATING
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai Dormir",
            type = HealthPlanTaskType.SLEEP
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai Relaxar",
            type = HealthPlanTaskType.MOOD
        ),
        TestModelFactory.buildHealthPlanTask(
            frequency = frequency,
            appointmentId = appointment.id,
            personId = person.id,
            description = "Vai Fazer Outra Coisa",
            type = HealthPlanTaskType.OTHERS
        )
    )

    private val healthProfessional = TestModelFactory.buildHealthProfessional()
    private val specialty = TestModelFactory.buildMedicalSpecialty(
        id = healthProfessional.specialtyId!!
    )
    private val procedureExecuted = TestModelFactory.buildAppointmentProcedureExecuted(
        appointmentId = appointment.id
    )

    private val healthSpecialistProcedure = HealthSpecialistProcedure(
        tussCode = procedureExecuted.tussCode,
        aliceCode = procedureExecuted.healthSpecialistResourceBundleCode,
        description = "Consulta Alice",
        serviceType = HealthSpecialistProcedureServiceType.CONSULTATION,
        hasPrice = true,
    )
    private val evolutions = listOf(
        TestModelFactory.buildAppointmentEvolution(
            appointmentId = appointment.id,
            staffId = healthProfessional.staffId,
            createdAt = nowMock
        ),
        TestModelFactory.buildAppointmentEvolution(
            appointmentId = appointment.id,
            staffId = healthProfessional.staffId,
            createdAt = nowMock
        ),
    )

    private val heightMeasurement =
        buildHealthMeasurement(value = BigDecimal("1.750"), type = HEIGHT, personId = person.id)
    private val weightMeasurement =
        buildHealthMeasurement(value = BigDecimal("56.645"), type = WEIGHT, personId = person.id)

    private val htmlResult = "html"
    private val fakePdf = "pdf".toByteArray()

    @Test
    fun `printPdf should print`(): Unit = mockLocalDateTime(nowMock) {
        val data = ClinicalRecordTransport(
            appointment = appointment,
            person = person,
            tasks = tasks,
            procedures = mapOf(procedureExecuted.tussCode to healthSpecialistProcedure),
            professionalAndSpecialty = Pair(healthProfessional, specialty),
            evolutions = listOf(
                evolutions.first() to healthProfessional,
                evolutions.last() to healthProfessional
            ),
            personInfoData = listOf(heightMeasurement, weightMeasurement),
            anesthetist = anesthetist
        )
        mockkObject(PDFConverter)
        mockkObject(RecordTemplate)

        coEvery { appointmentService.get(appointment.id) } returns appointment.success()
        coEvery { healthPlanTaskService.getByAppointmentId(appointment.id) } returns tasks.success()
        coEvery { healthProfessionalService.findByStaffId(appointment.staffId) } returns healthProfessional.success()
        coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()
        coEvery { personService.get(appointment.personId) } returns person.success()
        coEvery { appointmentProcedureExecutedService.getByAppointmentId(appointment.id) } returns listOf(
            procedureExecuted
        ).success()
        coEvery {
            healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                listOf(
                    TussCodeWithAliceCodeRequest(
                        tussCode = procedureExecuted.tussCode,
                        aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                    )
                )
            )
        } returns listOf(healthSpecialistProcedure).success()
        coEvery { appointmentEvolutionService.getByAppointment(appointment.id) } returns evolutions.success()
        coEvery {
            healthProfessionalService.getByStaffIds(evolutions.map { it.staffId }.distinct())
        } returns listOf(
            healthProfessional
        ).success()
        coEvery { healthMeasurementService.getActivesByPersonId(person.id) } returns listOf(
            heightMeasurement,
            weightMeasurement
        ).success()

        coEvery {
            healthProfessionalService.findByStaffId(anesthetistHp.staffId)
        } returns anesthetistHp.success()

        every { RecordTemplate.applyClinicalRecord(data) } returns htmlResult
        every { PDFConverter.htmlToPDF(htmlResult) } returns fakePdf


        val result = service.printPdf(appointment.id, appointment.staffId)
        assertThat(result).isSuccessWithData(
            AppointmentFileTransport(
                file = fakePdf,
                name = "${person.fullSocialName.clearWhitespaces()}-${person.nationalId.onlyDigits()}-$dateCustomFormatter.pdf"
            )
        )

        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { healthPlanTaskService.getByAppointmentId(any()) }
        coVerify(exactly = 2) { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { medicalSpecialtyService.getById(any()) }
        coVerifyOnce { personService.get(any()) }
        verifyOnce { RecordTemplate.applyClinicalRecord(any()) }
        verifyOnce { PDFConverter.htmlToPDF(any()) }
    }

    @Test
    fun `now allow print when staff is not owner`() = runBlocking {
        val staffId = RangeUUID.generate()
        val appointment = TestModelFactory.buildAppointment(
            staffId = RangeUUID.generate()
        )
        coEvery { appointmentService.get(appointment.id) } returns appointment.success()

        val result = service.printPdf(appointment.id, staffId)
        assertThat(result).isFailureOfType(AccessForbiddenException::class)
    }

    @Test
    fun `now allow print when status is not allowed`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(
            status = AppointmentStatus.DISCARDED
        )

        coEvery { appointmentService.get(appointment.id) } returns appointment.success()

        val result = service.printPdf(appointment.id, appointment.staffId)
        assertThat(result).isFailureOfType(AccessForbiddenException::class)
    }
}
