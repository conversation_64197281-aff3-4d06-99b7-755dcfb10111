package br.com.alice.appointment.services

import br.com.alice.appointment.event.AppointmentExecutedProcedureGroupEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Anesthetist
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import br.com.alice.data.layer.models.ProcedureExecutedStatus
import br.com.alice.data.layer.services.AppointmentProcedureExecutedDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class AppointmentProcedureExecutedServiceImplTest {
    private val appointmentProcedureExecutedDataService: AppointmentProcedureExecutedDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    val service = AppointmentProcedureExecutedServiceImpl(appointmentProcedureExecutedDataService, kafkaProducerService)

    val appointment = TestModelFactory.buildAppointment(
        anesthetist = Anesthetist(
            type = AnesthetistType.INDIVIDUAL,
            staffId = RangeUUID.generate()
        )
    )

    private val appointmentProcedureExecuted = TestModelFactory.buildAppointmentProcedureExecuted(
        appointmentId = appointment.id,
        tussCode = "123",
        tussProcedureAliceCode = "456",
        healthSpecialistResourceBundleCode = "456",
        status = ProcedureExecutedStatus.DRAFT
    )

    @Test
    fun `add should add procedure`() = runBlocking {
        coEvery { appointmentProcedureExecutedDataService.add(appointmentProcedureExecuted) } returns appointmentProcedureExecuted.success()

        val result = service.add(appointmentProcedureExecuted)
        assertThat(result).isSuccessWithData(appointmentProcedureExecuted)

        coVerifyOnce { appointmentProcedureExecutedDataService.add(appointmentProcedureExecuted) }
    }

    @Test
    fun `addDefaultProcedureByAppointment should add procedure`() = runBlocking {
        coEvery {
            appointmentProcedureExecutedDataService.findOne(queryEq {
                where {
                    this.appointmentId.eq(
                        appointmentProcedureExecuted.appointmentId
                    )
                }
            })
        } returns NotFoundException().failure()

        coEvery { appointmentProcedureExecutedDataService.add(appointmentProcedureExecuted) } returns appointmentProcedureExecuted.success()

        val result = service.addDefaultProcedureByAppointment(appointmentProcedureExecuted)
        assertThat(result).isSuccessWithData(appointmentProcedureExecuted)

        coVerifyOnce { appointmentProcedureExecutedDataService.add(appointmentProcedureExecuted) }
        coVerifyOnce { appointmentProcedureExecutedDataService.findOne(any()) }
    }

    @Test
    fun `addDefaultProcedureByAppointment should return procedure`() = runBlocking {
        coEvery {
            appointmentProcedureExecutedDataService.findOne(queryEq {
                where {
                    this.appointmentId.eq(
                        appointmentProcedureExecuted.appointmentId
                    )
                }
            })
        } returns appointmentProcedureExecuted.success()

        val result = service.addDefaultProcedureByAppointment(appointmentProcedureExecuted)
        assertThat(result).isSuccessWithData(appointmentProcedureExecuted)

        coVerifyOnce { appointmentProcedureExecutedDataService.findOne(any()) }
        coVerifyNone { appointmentProcedureExecutedDataService.add(any()) }
    }

    @Test
    fun `get should return appointment procedure`() = runBlocking {
        coEvery { appointmentProcedureExecutedDataService.get(appointmentProcedureExecuted.id) } returns appointmentProcedureExecuted.success()

        val result = service.get(appointmentProcedureExecuted.id)
        assertThat(result).isSuccessWithData(appointmentProcedureExecuted)

        coVerifyOnce { appointmentProcedureExecutedDataService.get(appointmentProcedureExecuted.id) }
    }

    @Test
    fun `deleteDraftProcedure should delete DRAFT appointment procedure`() = runBlocking {
        coEvery { appointmentProcedureExecutedDataService.get(appointmentProcedureExecuted.id) } returns appointmentProcedureExecuted.success()
        coEvery { appointmentProcedureExecutedDataService.softDelete(appointmentProcedureExecuted) } returns true.success()

        val result = service.deleteDraftProcedure(appointmentProcedureExecuted.id)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentProcedureExecutedDataService.get(appointmentProcedureExecuted.id) }
        coVerifyOnce { appointmentProcedureExecutedDataService.softDelete(appointmentProcedureExecuted) }
    }

    @Test
    fun `deleteDraftProcedure should not delete non-DRAFT appointment procedure`() = runBlocking {
        val appointmentProcedureExecuted = TestModelFactory.buildAppointmentProcedureExecuted(
            appointmentId = RangeUUID.generate(),
            tussCode = "123",
            tussProcedureAliceCode = "456",
            healthSpecialistResourceBundleCode = "456",
            status = ProcedureExecutedStatus.DONE
        )

        coEvery { appointmentProcedureExecutedDataService.get(appointmentProcedureExecuted.id) } returns appointmentProcedureExecuted.success()

        val result = service.deleteDraftProcedure(appointmentProcedureExecuted.id)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { appointmentProcedureExecutedDataService.get(appointmentProcedureExecuted.id) }
        coVerifyNone { appointmentProcedureExecutedDataService.softDelete(appointmentProcedureExecuted) }
    }

    @Test
    fun `getByAppointmentId should return procedures by appointment id`() = runBlocking {
        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns listOf(appointmentProcedureExecuted).success()

        val result = service.getByAppointmentId(appointmentProcedureExecuted.appointmentId)
        assertThat(result).isSuccessWithData(listOf(appointmentProcedureExecuted))

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
    }

    @Test
    fun `changeProceduresToDone should change procedures status to DONE`() = runBlocking {
        val appointmentProcedureExecuted2 = appointmentProcedureExecuted.copy(id = RangeUUID.generate())
        val appointmentProcedureExecuted2Done =
            appointmentProcedureExecuted2.copy(status = ProcedureExecutedStatus.DONE)
        val appointmentProcedureExecutedDone = appointmentProcedureExecuted.copy(status = ProcedureExecutedStatus.DONE)
        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns listOf(appointmentProcedureExecuted, appointmentProcedureExecuted2).success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentExecutedProcedureGroupEvent(
                    listOf(
                        appointmentProcedureExecutedDone, appointmentProcedureExecuted2Done
                    )
                )
            )
        } returns ProducerResult(
            LocalDateTime.now(),
            "1",
            1
        )
        coEvery {
            appointmentProcedureExecutedDataService.update(appointmentProcedureExecutedDone)
        } returns appointmentProcedureExecutedDone.success()
        coEvery {
            appointmentProcedureExecutedDataService.update(appointmentProcedureExecuted2Done)
        } returns appointmentProcedureExecuted2Done.success()

        val result = service.changeProceduresToDone(appointmentProcedureExecuted.appointmentId)
        assertThat(result).isSuccessWithData(
            listOf(
                appointmentProcedureExecutedDone,
                appointmentProcedureExecuted2Done
            )
        )

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerify(exactly = 2) { appointmentProcedureExecutedDataService.update(any()) }
    }

    @Test
    fun `changeProceduresToDone should ignore procedure when status is DONE`() = runBlocking {
        val appointmentProcedureExecuted = appointmentProcedureExecuted.copy(status = ProcedureExecutedStatus.DONE)

        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns listOf(appointmentProcedureExecuted).success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentExecutedProcedureGroupEvent(
                    listOf(
                        appointmentProcedureExecuted
                    )
                )
            )
        } returns ProducerResult(
            LocalDateTime.now(),
            "1",
            1
        )


        val result = service.changeProceduresToDone(appointmentProcedureExecuted.appointmentId)
        assertThat(result).isSuccessWithData(listOf(appointmentProcedureExecuted))

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyNone { appointmentProcedureExecutedDataService.update(any()) }
    }

    @Test
    fun `changeProceduresToDoneAndPublish should change procedures status to DONE`() = runBlocking {
        val appointmentProcedureExecuted2 = appointmentProcedureExecuted.copy(id = RangeUUID.generate())
        val appointmentProcedureExecuted2Done =
            appointmentProcedureExecuted2.copy(status = ProcedureExecutedStatus.DONE)
        val appointmentProcedureExecutedDone = appointmentProcedureExecuted.copy(status = ProcedureExecutedStatus.DONE)
        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns listOf(appointmentProcedureExecuted, appointmentProcedureExecuted2).success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentExecutedProcedureGroupEvent(
                    listOf(
                        appointmentProcedureExecutedDone, appointmentProcedureExecuted2Done
                    ),
                    appointment.anesthetist
                )
            )
        } returns ProducerResult(
            LocalDateTime.now(),
            "1",
            1
        )
        coEvery {
            appointmentProcedureExecutedDataService.update(appointmentProcedureExecutedDone)
        } returns appointmentProcedureExecutedDone.success()
        coEvery {
            appointmentProcedureExecutedDataService.update(appointmentProcedureExecuted2Done)
        } returns appointmentProcedureExecuted2Done.success()

        val result = service.changeProceduresToDoneAndPublish(appointment)
        assertThat(result).isSuccessWithData(
            listOf(
                appointmentProcedureExecutedDone,
                appointmentProcedureExecuted2Done
            )
        )

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerify(exactly = 2) { appointmentProcedureExecutedDataService.update(any()) }
    }

    @Test
    fun `changeProceduresToDoneAndPublish should ignore procedure when status is DONE`() = runBlocking {
        val appointmentProcedureExecuted = appointmentProcedureExecuted.copy(status = ProcedureExecutedStatus.DONE)

        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns listOf(appointmentProcedureExecuted).success()
        coEvery {
            kafkaProducerService.produce(
                AppointmentExecutedProcedureGroupEvent(
                    listOf(
                        appointmentProcedureExecuted,
                    ),
                    appointment.anesthetist
                )
            )
        } returns ProducerResult(
            LocalDateTime.now(),
            "1",
            1
        )


        val result = service.changeProceduresToDoneAndPublish(appointment)
        assertThat(result).isSuccessWithData(listOf(appointmentProcedureExecuted))

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
        coVerifyNone { appointmentProcedureExecutedDataService.update(any()) }
    }

    @Test
    fun `deleteProcedures should delete all procedures`() = runBlocking {
        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns listOf(appointmentProcedureExecuted).success()
        coEvery {
            appointmentProcedureExecutedDataService.softDelete(appointmentProcedureExecuted)
        } returns true.success()

        val result = service.deleteProcedures(appointmentProcedureExecuted.appointmentId)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
        coVerifyOnce { appointmentProcedureExecutedDataService.softDelete(any()) }
    }

    @Test
    fun `deleteProcedures should not delete any procedure`() = runBlocking {
        coEvery {
            appointmentProcedureExecutedDataService.find(queryEq {
                this.where {
                    this.appointmentId.eq(appointmentProcedureExecuted.appointmentId)
                }
            })
        } returns emptyList<AppointmentProcedureExecuted>().success()

        val result = service.deleteProcedures(appointmentProcedureExecuted.appointmentId)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentProcedureExecutedDataService.find(any()) }
        coVerifyNone { appointmentProcedureExecutedDataService.softDelete(any()) }
    }
}
