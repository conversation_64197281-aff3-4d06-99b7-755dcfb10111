package br.com.alice.appointment.consumers

import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.ProcedureExecutedStatus
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test


class AppointmentProcedureExecutedConsumerTest : ConsumerTest() {
    private val appointmentProcedureExecutedService: AppointmentProcedureExecutedService = mockk()

    val consumer = AppointmentProcedureExecutedConsumer(appointmentProcedureExecutedService)

    private val appointmentProcedureExecuted = TestModelFactory.buildAppointmentProcedureExecuted(
        appointmentId = RangeUUID.generate(),
        tussCode = "123",
        tussProcedureAliceCode = "456",
        healthSpecialistResourceBundleCode = "456",
        status = ProcedureExecutedStatus.DRAFT
    )
    private val appointment = TestModelFactory.buildAppointment()

    @Test
    fun `changeProcedureToDone should change procedure to done`() = runBlocking {
        val event = AppointmentCompletedEvent(appointment = appointment)

        coEvery { appointmentProcedureExecutedService.changeProceduresToDoneAndPublish(event.payload.appointment) } returns listOf(
            appointmentProcedureExecuted
        ).success()

        val result = consumer.changeProcedureToDone(event)
        assertThat(result).isSuccessWithData(listOf(appointmentProcedureExecuted))

        coVerifyOnce { appointmentProcedureExecutedService.changeProceduresToDoneAndPublish(any()) }
    }

    @Test
    fun `deleteProcedures should delete procedures`() = runBlocking {
        val event = DraftAppointmentDeletedEvent(
            appointmentId = appointment.id,
            description = appointment.content,
            personId = appointment.personId,
            type = appointment.type,
            staffId = appointment.staffId,
            createdAt = appointment.createdAt,
            discardedType = AppointmentDiscardedType.OTHERS
        )

        coEvery { appointmentProcedureExecutedService.deleteProcedures(event.payload.appointmentId) } returns true.success()

        val result = consumer.deleteProcedures(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { appointmentProcedureExecutedService.deleteProcedures(any()) }
    }
}
