package br.com.alice.appointment.logics

import br.com.alice.appointment.converters.clinical_data.tasks.EatingContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.EmergencyContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.FollowUpRequestContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.ModContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.OthersContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.PhysicalActivityContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.PrescriptionContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.ReferralContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.SleepContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.SurgeryContentConverter
import br.com.alice.appointment.converters.clinical_data.tasks.TestRequestContentConverter
import br.com.alice.appointment.model.AnesthetistPdfResponse
import br.com.alice.appointment.model.ClinicalRecordTransport
import br.com.alice.appointment.storages.ResourceLoader
import br.com.alice.common.Disease
import br.com.alice.common.core.extensions.plusSafe
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.data.layer.models.AnesthetistType
import br.com.alice.data.layer.models.AppointmentEvolution
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.HealthMeasurementInternalType.HEIGHT
import br.com.alice.data.layer.models.HealthMeasurementInternalType.WEIGHT
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.getByType
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import java.time.LocalDateTime

object RecordTemplate {

    private val clinicalRecord = ResourceLoader.load("clinical-record/clinical_records.html")
        .readText()
    private val caseRecord = ResourceLoader.load("clinical-record/case_record.html")
        .readText()

    private val procedureRecord = ResourceLoader.load("clinical-record/procedure_executed_entry.html")
        .readText()

    private val evolutionsHtml = ResourceLoader.load("clinical-record/evolutions.html")
        .readText()

    private val evolutionEntries = ResourceLoader.load("clinical-record/evolutions_entry.html")
        .readText()

    private val divider = ResourceLoader.load("clinical-record/divider.html")
        .readText()

    private val anesthetistHtml = ResourceLoader.load("clinical-record/anesthetist.html")
        .readText()

    fun applyClinicalRecord(source: ClinicalRecordTransport): String {
        val (professional, specialty) = source.professionalAndSpecialty
        val placeHolders: List<Pair<String, String>> = listOf(
            "{{ISSUE_DATE}}" to "Emitido em: ${LocalDateTime.now().toSaoPauloTimeZone().toBrazilianDateTimeFormat()}",
            "{{FULL_NAME}}" to source.person.fullSocialName,
            "{{AGE}}" to source.person.friendlyAge().orValue("-"),
            "{{WEIGHT}}" to source.personInfoData.getByType(WEIGHT)?.normalizeValue()?.let { "${it}kg" }.orEmpty(),
            "{{HEIGHT}}" to source.personInfoData.getByType(HEIGHT)?.normalizeValue()?.let { "${it}m" }.orEmpty(),
            "{{APPOINTMENT_DATE}}" to source.appointment.appointmentDate?.toBrazilianDateFormat().orValue("-"),
            "{{APPOINTMENT_DESCRIPTION}}" to source.appointment.description.orEmpty(),
            "{{CASE_RECORDS}}" to buildCaseRecords(source.appointment.caseRecordDetails),
            "{{RESPONSIBLE_DOCTOR}}" to professional.name,
            "{{SPECIALTY}}" to specialty?.name.orValue("-"),
            "{{PROCEDURES_EXECUTED}}" to buildProcedures(source.procedures),
            "{{ANESTHETIST}}" to buildAnesthetist(source.anesthetist),
        ).plusSafe(appendTasks(source.tasks)).plusSafe(appendEvolutions(source.evolutions))

        return placeHolders.fold(clinicalRecord) { acc, placeHolder ->
            acc.replace(placeHolder.first, placeHolder.second)
        }
    }

    private fun appendTasks(tasks: List<HealthPlanTask>) = listOfNotNull(
        "{{TASK_DIVIDER}}" to if (tasks.isNotEmpty()) divider.replace(
            "{{TITLE}}", "Tarefas para o membro"
        ) else "",
        "{{TASKS}}" to buildTasks(tasks),
    )

    private fun appendEvolutions(evolutions: List<Pair<AppointmentEvolution, HealthProfessional?>>) = listOfNotNull(
        "{{EVOLUTION_DIVIDER}}" to if (evolutions.isNotEmpty()) divider.replace(
            "{{TITLE}}", "Evoluções"
        ) else "",
        "{{EVOLUTIONS}}" to buildEvolutions(evolutions),
    )

    private fun buildCaseRecords(caseRecords: List<CaseRecordDetails>?) = buildString {
        caseRecords?.map { it.description }?.forEach {
            val caseRecord = caseRecord
                .replace("{{CASE_DESCRIPTION}}", it.description.orValue("-"))
                .replace("{{CASE_CID}}", buildDiseaseCode(it))
            append(caseRecord)
        }
    }

    private fun buildProcedures(procedures: Map<String, HealthSpecialistProcedure>) = buildString {
        procedures.forEach {
            val procedure = it.value
            val caseRecord = procedureRecord
                .replace("{{PROCEDURE_NAME}}", procedure.description)
                .replace("{{PROCEDURE_TUSS}}", procedure.tussCode)
            append(caseRecord)
        }
    }

    private fun buildDiseaseCode(it: DiseaseDetails): String = when (it.type) {
        Disease.Type.CID_10 -> "CID ${it.value}"
        Disease.Type.CIAP_2 -> "CIAP ${it.value}"
        else -> "${it.type} ${it.value}"
    }

    private fun buildTasks(tasks: List<HealthPlanTask>) = listOf(
        ReferralContentConverter,
        EmergencyContentConverter,
        FollowUpRequestContentConverter,
        TestRequestContentConverter,
        PrescriptionContentConverter,
        SurgeryContentConverter,
        PhysicalActivityContentConverter,
        EatingContentConverter,
        ModContentConverter,
        SleepContentConverter,
        OthersContentConverter
    ).mapNotNull { it.buildToHtml(tasks) }.joinToString("")

    private fun buildEvolutions(evolutions: List<Pair<AppointmentEvolution, HealthProfessional?>>) =
        if (evolutions.isEmpty()) {
            ""
        } else {
            buildString {
                val evolutionEntries = evolutions.map {
                    val (evolution, hp) = it
                    evolutionEntries
                        .replace(
                            "{{EVOLUTION}}",
                            "Feito por ${hp?.name.orValue("-")} - ${
                                evolution.createdAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat()
                            }<br/>${evolution.description}"
                        )
                }
                append(evolutionsHtml.replace("{{ITEMS}}", evolutionEntries.joinToString("")))
            }
        }

    private fun buildAnesthetist(anesthetist: AnesthetistPdfResponse?): String {
        if (anesthetist == null || anesthetist.type == AnesthetistType.NOT_APPLICABLE) return ""
        return anesthetistHtml
            .replace("{{ANESTHETIST_NAME}}", buildAnesthetistName(anesthetist))
            .replace("{{ANESTHETIST_CRM}}", anesthetist.healthProfessional?.council?.toString().orEmpty())
    }

    private fun buildAnesthetistName(anesthetist: AnesthetistPdfResponse): String =
        anesthetist.healthProfessional?.name ?: anesthetist.type.description

    private fun String?.orValue(value: String): String = this ?: value

}
