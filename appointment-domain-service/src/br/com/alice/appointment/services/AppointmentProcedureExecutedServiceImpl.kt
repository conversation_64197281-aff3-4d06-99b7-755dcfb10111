package br.com.alice.appointment.services

import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.event.AppointmentExecutedProcedureGroupEvent
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import br.com.alice.data.layer.models.ProcedureExecutedStatus
import br.com.alice.data.layer.services.AppointmentProcedureExecutedDataService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class AppointmentProcedureExecutedServiceImpl(
    private val appointmentProcedureExecutedDataService: AppointmentProcedureExecutedDataService,
    private val kafkaProducerService: KafkaProducerService
) : AppointmentProcedureExecutedService {
    override suspend fun add(model: AppointmentProcedureExecuted): Result<AppointmentProcedureExecuted, Throwable> =
        appointmentProcedureExecutedDataService.add(model)

    override suspend fun addDefaultProcedureByAppointment(model: AppointmentProcedureExecuted): Result<AppointmentProcedureExecuted, Throwable> =
        appointmentProcedureExecutedDataService.findOne { where { this.appointmentId.eq(model.appointmentId) } }
            .coFoldNotFound {
                appointmentProcedureExecutedDataService.add(model)
            }

    override suspend fun get(id: UUID): Result<AppointmentProcedureExecuted, Throwable> =
        appointmentProcedureExecutedDataService.get(id)

    override suspend fun deleteDraftProcedure(id: UUID): Result<Boolean, Throwable> =
        appointmentProcedureExecutedDataService.get(id).flatMap {
            if (it.status == ProcedureExecutedStatus.DRAFT) {
                appointmentProcedureExecutedDataService.softDelete(it)
            } else {
                logger.error(
                    "Procedure executed is not in draft status",
                    "procedure_executed_id" to id,
                    "status" to it.status
                )
                false.success()
            }
        }

    override suspend fun getByAppointmentId(id: UUID): Result<List<AppointmentProcedureExecuted>, Throwable> =
        appointmentProcedureExecutedDataService.find { where { this.appointmentId.eq(id) } }

    override suspend fun existByAppointmentId(id: UUID): Result<Boolean, Throwable> =
        appointmentProcedureExecutedDataService.exists { where { this.appointmentId.eq(id) } }


    override suspend fun changeProceduresToDone(appointmentId: UUID): Result<List<AppointmentProcedureExecuted>, Throwable> =
        appointmentProcedureExecutedDataService.find { where { this.appointmentId.eq(appointmentId) } }
            .map { procedures ->
                procedures.pmap { procedure ->
                    if (procedure.status == ProcedureExecutedStatus.DONE) procedure
                    else appointmentProcedureExecutedDataService.update(procedure.copy(status = ProcedureExecutedStatus.DONE))
                        .get()
                }
            }.then {
                if (it.isNotEmpty())
                    kafkaProducerService.produce(AppointmentExecutedProcedureGroupEvent(it))
            }

    override suspend fun changeProceduresToDoneAndPublish(appointment: Appointment): Result<List<AppointmentProcedureExecuted>, Throwable> =
        appointmentProcedureExecutedDataService.find { where { this.appointmentId.eq(appointment.id) } }
            .map { procedures ->
                procedures.pmap { procedure ->
                    if (procedure.status == ProcedureExecutedStatus.DONE) procedure
                    else appointmentProcedureExecutedDataService.update(procedure.copy(status = ProcedureExecutedStatus.DONE))
                        .get()
                }
            }.then {
                if (it.isNotEmpty())
                    kafkaProducerService.produce(AppointmentExecutedProcedureGroupEvent(it, appointment.anesthetist))
            }

    override suspend fun deleteProcedures(appointmentId: UUID): Result<Boolean, Throwable> =
        appointmentProcedureExecutedDataService.find { where { this.appointmentId.eq(appointmentId) } }
            .map { procedures ->
                procedures.pmap { procedure ->
                    appointmentProcedureExecutedDataService.softDelete(procedure).get()
                }
            }.map { true }

}
