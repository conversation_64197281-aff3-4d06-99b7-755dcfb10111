package br.com.alice.appointment.services

import br.com.alice.appointment.client.AppointmentEventService
import br.com.alice.appointment.client.AppointmentFilter
import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.appointment.client.PublishExcuseNotesRequest
import br.com.alice.appointment.models.AppointmentValidationError
import br.com.alice.appointment.models.AppointmentWithStaff
import br.com.alice.appointment.models.AppointmentWithValidation
import br.com.alice.appointment.services.internal.AppointmentNotificationService
import br.com.alice.channel.client.ChannelService
import br.com.alice.common.Disease
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.document.html.removeHtmlAndPreserveNewLines
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel
import br.com.alice.data.layer.models.Appointment.ReferencedLink
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentDiscardedType.NO_SHOW
import br.com.alice.data.layer.models.AppointmentEventDetail
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentFinishType
import br.com.alice.data.layer.models.AppointmentStatus.DISCARDED
import br.com.alice.data.layer.models.AppointmentStatus.DRAFT
import br.com.alice.data.layer.models.AppointmentStatus.FINISHED
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.models.AppointmentType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.AppointmentType.COUNTER_REFERRAL
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.ChannelId
import br.com.alice.data.layer.models.ConditionOptions
import br.com.alice.data.layer.models.ExcuseNote
import br.com.alice.data.layer.models.ExternalFile
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.ProtocolNavigation
import br.com.alice.data.layer.models.ServiceScriptNavigationSource
import br.com.alice.data.layer.models.ServiceScriptNavigationSourceType
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.areFilledAllCaseRecord
import br.com.alice.data.layer.services.AppointmentDataService
import br.com.alice.data.layer.strategies.ConflictResolutionStrategy
import br.com.alice.documentsigner.services.DocumentPrinterService
import br.com.alice.ehr.model.ciapSearchEngine.CiapSearchEngine
import br.com.alice.ehr.model.cidSearchEngine.CidSearchEngine
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.BudService
import br.com.alice.healthlogic.models.ETATriggerEvent
import br.com.alice.healthlogic.models.TriggerActionType
import br.com.alice.healthlogic.models.bud.BudNodeNavigationHistory
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.apache.commons.lang3.RandomStringUtils
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class AppointmentServiceImpl(
    private val appointmentDataService: AppointmentDataService,
    private val staffService: StaffService,
    private val appointmentNotificationService: AppointmentNotificationService,
    private val printerService: DocumentPrinterService,
    private val fileVaultStorage: FileVaultStorage,
    private val appointmentEventService: AppointmentEventService,
    private val channelService: ChannelService,
    private val budService: BudService,
    private val procedureExecutedService: AppointmentProcedureExecutedService,
    private val healthPlanTaskService: HealthPlanTaskService,
) : AppointmentService {

    override suspend fun add(model: Appointment): Result<Appointment, Throwable> = span("add") { span ->
        coroutineScope {
            span.setAttribute("person_id", model.personId)
            span.setAttribute("staff_id", model.staffId)

            appointmentDataService.findOne {
                where {
                    this.personId.eq(model.personId) and
                            this.staffId.eq(model.staffId) and
                            this.status.eq(DRAFT)
                }
            }.recordResult("find_draft_result", span)
                .coFoldNotFound {
                    val protocolHistoryDeferred = async { enrichProtocolNavigation(model) }

                    val appointmentEvent =
                        model.event?.let { event ->
                            appointmentEventService.getByReferenceId(event.referenceModelId)
                                .getOrNullIfNotFound()
                        }

                    appointmentDataService.add(
                        model.copy(
                            name = appointmentEvent?.clinicalRecordName ?: model.name ?: model.type.description,
                            protocolNavigationHistory = protocolHistoryDeferred.await(),
                            event = if (appointmentEvent != null) model.event?.copy(
                                name = appointmentEvent.name
                            ) else null

                        )
                    ).recordResult("add_result", span)
                        .then { appointmentNotificationService.notifyCreation(it) }
                }
        }.recordResult(span)
    }

    override suspend fun update(
        loggedStaffId: UUID,
        appointmentId: UUID,
        request: Appointment
    ): Result<Appointment, Throwable> = coroutineScope {
        val loggedStaffDeferred = async { staffService.get(loggedStaffId).get() }
        val protocolHistoryDeferred = async { enrichProtocolNavigation(request) }

        appointmentDataService.get(appointmentId).flatMap { appointment ->
            checkConflict(request, appointment, loggedStaffId)
            isAuthorized(appointment, loggedStaffId)
            isValid(request, appointment)

            val updatedAppointment = updateFullAppointment(
                appointment,
                request,
                loggedStaffDeferred.await(),
                protocolHistoryDeferred.await()
            )

            appointmentDataService.update(updatedAppointment).map {
                it.copyWithSubjectiveCodesDescriptions()
            }
        }
    }

    override suspend fun get(id: UUID) = appointmentDataService.get(id)
        .map { it.copyWithSubjectiveCodesDescriptions() }

    override suspend fun getAppointmentsByPerson(
        personId: PersonId,
        formatted: Boolean,
        includeInternalTypes: Boolean,
        staffId: UUID?,
    ): Result<List<AppointmentWithStaff>, Throwable> =
        getAppointments(personId, getAppointmentTypes(includeInternalTypes), staffId).flatMapPair { appointments ->
            getAppointmentStaffMap(appointments) { staffIds ->
                staffService.findByList(staffIds).get().associateBy { it.id }
            }
        }.map { (staffMap, appointments) ->
            appointments.map { appointment ->
                AppointmentWithStaff(
                    if (formatted) appointment else copyEscaped(appointment),
                    staffMap.getValue(appointment.staffId)
                )
            }
        }

    override suspend fun publishExcuseNotes(request: PublishExcuseNotesRequest): Result<List<ExcuseNote>, Throwable> =
        generateTaskToken().let { documentToken ->
            get(request.appointmentId).flatMapPair { appointment ->
                printerService.printExcuseNotes(
                    appointment = appointment.copy(excuseNotes = listOf(request.excuseNote)),
                    staffId = request.staffId,
                    token = request.token,
                    documentToken = documentToken
                )
            }.map { (byteArray, appointment) ->
                printerService.saveFile(
                    id = appointment.id,
                    personId = appointment.personId,
                    doc = byteArray,
                    namespace = "signed_excuse_notes",
                    fileName = "Atestado.pdf"
                ) to appointment
            }.map { (attachment, appointment) ->
                appointment.copy(
                    excuseNotes = appointment.excuseNotes.filter { it.id != request.excuseNote.id } +
                            request.excuseNote.copy(attachmentId = attachment.id.toString(), token = documentToken)
                )
            }.flatMap { appointment ->
                appointmentDataService.update(appointment)
            }.map { it.excuseNotes }
        }

    override suspend fun getExcuseNoteById(excuseNotedId: UUID): Result<String, Throwable> = coResultOf {
        fileVaultStorage.getFileById(excuseNotedId.toString())?.url
            ?: throw NotFoundException("excuse_note not found.")
    }

    override suspend fun getAndValidate(id: UUID, staffId: UUID): Result<AppointmentWithValidation, Throwable> =
        span("validate") { span ->
            span.setAttribute("staff_id", staffId)
            span.setAttribute("appointment_id", id)

            appointmentDataService.get(id)
                .flatMap { appointment -> validateAppointment(appointment, staffId) }
                .recordResult(span)
        }

    override suspend fun finishNew(
        id: UUID,
        staffId: UUID,
        finishType: AppointmentFinishType,
        token: String?
    ): Result<Boolean, Throwable> = span("finishNew") { span ->
        appointmentDataService.get(id).flatMap { appointment ->
            finishNew(appointment, staffId, finishType, token)
        }.recordResult(span)
    }

    suspend fun finishNew(
        appointment: Appointment,
        staffId: UUID,
        finishType: AppointmentFinishType,
        token: String? = null
    ): Result<Boolean, Throwable> = span("finishNew_by_model") { span ->
        span.setAttribute("staff_id", staffId)
        span.setAttribute("appointment_id", appointment.id)
        span.setAttribute("has_token", token != null)

        catchResult {
            val appointmentValidation = validateAppointment(appointment, staffId).get()
            if (appointmentValidation.isValid) {
                val appointmentToUpdate = checkIfFinishAppointment(appointment.fillDemands(), finishType).get()
                val appointmentUpdated = appointmentDataService.update(appointmentToUpdate).get()

                appointmentNotificationService.notifyIfNeeded(appointmentUpdated).success()
            } else {
                throw InvalidArgumentException("The appointment is not valid", "invalid_appointment")
            }
        }.map { true }.recordResult(span)
    }

    override suspend fun hasDraft(
        personId: PersonId,
        staffId: UUID,
    ): Result<Boolean, Throwable> =
        coResultOf { getDraft(staffId, personId).getOrNullIfNotFound() != null }

    override suspend fun currentDraft(
        personId: PersonId,
        staffId: UUID,
    ): Result<Appointment, Throwable> =
        getDraft(staffId, personId)

    override suspend fun delete(
        id: UUID,
        discardedType: AppointmentDiscardedType,
        discardedReason: String?,
    ): Result<Appointment, Throwable> =
        appointmentDataService.get(id).flatMap { appointment ->
            delete(appointment, discardedType, discardedReason)
        }

    suspend fun delete(
        appointment: Appointment,
        discardedType: AppointmentDiscardedType,
        discardedReason: String?,
    ): Result<Appointment, Throwable> = span("delete_by_model") { span ->
        span.setAttribute("appointment_id", appointment.id)

        catchResult {
            appointment.checkDraft()

            appointmentDataService.update(
                appointment.copy(
                    status = DISCARDED,
                    discardedType = discardedType,
                    discardedReason = discardedReason
                )
            ).then { deleted ->
                appointmentNotificationService.notifyDeletion(deleted)
            }
        }.recordResult(span)
    }

    override suspend fun existsValidByReferencedLink(
        id: UUID,
        personId: PersonId,
        model: ReferenceLinkModel,
        date: LocalDate
    ): Result<Boolean, Throwable> =
        appointmentDataService.exists {
            where {
                this.personId.eq(personId) and
                        this.status.inList(listOf(DRAFT, FINISHED)) and
                        this.referencedLinks.jsonSearch(
                            ReferencedLink(
                                id,
                                model
                            )
                        ) and
                        this.createdAt.less(date.atEndOfTheDay()) and
                        this.createdAt.greater(date.atStartOfDay())
            }
        }.flatMapError { false.success() }

    override suspend fun buildAutomaticTaskEngineTriggerById(
        id: UUID
    ): Result<ETATriggerEvent, Throwable> =
        get(id).map { appointment ->
            val typeCondition = mapOf(
                ConditionOptions.APPOINTMENT_TYPE.key to appointment.type
            )

            val demands = appointment.caseRecordDetails?.map {
                "${it.description.type.name}_${it.description.value}"
            }

            val conditions = if (!demands.isNullOrEmpty()) {
                typeCondition.plus(ConditionOptions.CID_CIAP.key to demands)
            } else typeCondition

            ETATriggerEvent(
                trigger = TriggerActionType.APPOINTMENT,
                triggerId = appointment.id.toString(),
                conditions = conditions
            )
        }

    override suspend fun personHasAppointments(personId: PersonId): Result<Boolean, Throwable> =
        appointmentDataService.exists { where { this.personId.eq(personId) } }

    override suspend fun getDraftByPersonAndRelatedStaff(
        personId: PersonId,
        staff: UUID
    ): Result<List<Appointment>, Throwable> = appointmentDataService.find {
        where {
            this.personId.eq(personId).and(this.status.eq(DRAFT))
        }
    }.map { appointments ->
        appointments.filter { isStaffDraft(it, staff) }
    }

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun find(filter: AppointmentFilter): Result<List<Appointment>, Throwable> =
        appointmentDataService.find {
            where {
                this.personId.eq(filter.personId)
                    .withFilter(filter.status) { status -> this.status.eq(status) }
                    .withFilter(filter.type) { type -> this.appointmentType.eq(type) }
                    .withFilter(filter.createdAtGreater) { createdAtGreater -> this.createdAt.greater(createdAtGreater) }
                    .withFilter(filter.createdAtLess) { createdAtLess -> this.createdAt.less(createdAtLess) }
                    .withFilter(filter.event) { event ->
                        event.referenceModelId
                            ?.let { this.event.jsonSearch(it, event.referenceModel) }
                            ?: this.event.jsonSearch(event.referenceModel)
                    }
                    .withFilter(filter.staffId) { staffId -> this.staffId.eq(staffId) }!!
            }.let { query ->
                filter.paginate?.let { range ->
                    query.offset { range.first }
                        .limit { range.last }
                } ?: query
            }.let { query ->
                takeIf { filter.orderByCreatedAt }?.let {
                    query.orderBy { createdAt }
                } ?: query
            }.let { query ->
                filter.sortOrder?.let { sortOrder ->
                    query.sortOrder { sortOrder }
                } ?: query
            }
        }

    override suspend fun countByPersonId(personId: PersonId): Result<Int, Throwable> =
        appointmentDataService.count {
            where {
                this.personId.eq(personId)
            }
        }

    override suspend fun getByIds(ids: List<UUID>): Result<List<Appointment>, Throwable> =
        appointmentDataService.find {
            where {
                this.id.inList(ids)
            }
        }

    @OptIn(OrPredicateUsage::class)
    override suspend fun recentByStaff(staffId: UUID, limit: Int): Result<List<Appointment>, Throwable> =
        appointmentDataService.find {
            where {
                this.staffId.eq(staffId).and(scope(status.inList(listOf(DRAFT, FINISHED)) or discardedType.eq(NO_SHOW)))
            }.orderBy { this.createdAt }
                .sortOrder { SortOrder.Descending }
                .limit { limit }
        }

    private fun copyEscaped(appointment: Appointment) =
        appointment.copy(
            description = appointment.content.removeHtmlAndPreserveNewLines(),
            guidance = appointment.guidance.orEmpty().removeHtmlAndPreserveNewLines()
        )

    private fun updateOwnerStaffIds(appointment: Appointment, loggedStaff: Staff) =
        appointment.ownerStaffIds + loggedStaff.id


    private suspend fun getAppointments(
        personId: PersonId,
        types: List<AppointmentType>,
        staffId: UUID?
    ) =
        appointmentDataService.find {
            where {
                this.personId.eq(personId) and
                        this.appointmentType.inList(types)
            }.orderBy { createdAt }.sortOrder { desc }
        }.map { appointments ->
            appointments.filter {
                it.isFinished() || isStaffDraft(it, staffId) || isNoShowDiscarded(it)
            }
        }

    private fun isStaffDraft(appointment: Appointment, staffId: UUID?): Boolean =
        appointment.status == DRAFT && (
                appointment.staffId == staffId ||
                        appointment.draftGroupStaffIds?.any { it == staffId } ?: false
                )

    private fun isNoShowDiscarded(appointment: Appointment): Boolean =
        appointment.status == DISCARDED && appointment.discardedType == NO_SHOW

    private fun generateTaskToken() = RandomStringUtils.randomNumeric(4)

    private fun Appointment.copyWithSubjectiveCodesDescriptions() =
        copy(
            subjectiveCodes = subjectiveCodes.map { buildDisease(it) },
            objectiveCodes = objectiveCodes.map { buildDisease(it) }
        )

    private fun buildDisease(it: Disease) = when (it.type) {
        Disease.Type.CID_10 -> it.copy(description = "${CidSearchEngine.descriptionOfCode(it.value)} - CID ${it.value}")
        Disease.Type.CIAP_2 -> it.copy(
            description = "${CiapSearchEngine.descriptionOfCode(it.value)} - CIAP ${it.value}"
        )

        else -> it.copy(description = it.value)
    }

    private suspend fun getDraft(staffId: UUID, personId: PersonId) =
        appointmentDataService.findOne {
            where {
                this.staffId.eq(staffId) and
                        this.personId.eq(personId) and
                        this.status.eq(DRAFT)
            }
        }

    private fun isAuthorized(appointment: Appointment, loggedStaffId: UUID) {
        if (appointment.staffId != loggedStaffId)
            throw AuthorizationException("It is not allowed to edit an another user appointment")
    }

    private suspend fun checkConflict(
        request: Appointment,
        appointment: Appointment,
        loggedStaffId: UUID
    ) {
        if (ConflictResolutionStrategy.hasConflict(request, appointment) { loggedStaffId })
            throw ConflictException("Conflict on update of appointmentId=${appointment.id}")
    }

    private fun isValid(request: Appointment, appointment: Appointment) {
        if (
            appointment.type == AppointmentType.ANNOTATION_HEALTH_COMMUNITY &&
            request.completed &&
            request.specialist == null
        ) throw InvalidArgumentException(
            code = "invalid_annotation_health_community",
            message = "It is not allowed to finish an annotation without specialist"
        )
    }

    private fun updateFullAppointment(
        appointment: Appointment,
        request: Appointment,
        loggedStaff: Staff,
        protocolNavigationHistory: List<ProtocolNavigation>
    ): Appointment =
        appointment.copy(
            personId = request.personId,
            description = request.description,
            guidance = request.guidance,
            excuseNotes = request.excuseNotes,
            completedAt = request.completedAt ?: appointment.completedAt,
            type = request.type,
            serviceScriptId = request.serviceScriptId,
            ownerStaffIds = updateOwnerStaffIds(appointment, loggedStaff),
            subjectiveCodes = request.subjectiveCodes,
            objectiveCodes = request.objectiveCodes,
            objective = request.objective,
            plan = request.plan,
            startedAt = request.startedAt,
            endedAt = request.endedAt,
            attachments = request.attachments.ifEmpty { appointment.attachments },
            specialty = request.specialty,
            specialist = request.specialist,
            status = request.status,
            referencedLinks = request.referencedLinks,
            finishType = request.finishType,
            clinicalEvaluation = request.clinicalEvaluation,
            treatedBy = request.treatedBy,
            outcome = request.outcome,
            channelId = request.channelId,
            caseRecordDetails = request.caseRecordDetails,
            draftGroupStaffIds = request.draftGroupStaffIds,
            event = request.event,
            name = request.name,
            components = request.components,
            subjective = request.subjective,
            templateType = request.templateType,
            staffRole = loggedStaff.role,
            emptyEventReason = request.emptyEventReason,
            protocolNavigationHistory = protocolNavigationHistory,
            externalFiles = request.externalFiles,
            contractualRisks = request.contractualRisks,
            discardedType = request.discardedType,
            medicalDischarge = request.medicalDischarge,
            primaryAttentionRequest = request.primaryAttentionRequest,
            appointmentDate = request.appointmentDate,
            anesthetist = request.anesthetist
        )

    private fun getAppointmentTypes(includeInternalTypes: Boolean) =
        if (includeInternalTypes) {
            AppointmentType.values().toList()
        } else {
            AppointmentType.values().filterNot { it.isInternal }
        }

    private suspend fun <T> getAppointmentStaffMap(
        appointments: List<Appointment>,
        function: suspend (List<UUID>) -> Map<UUID, T>
    ): Result<Map<UUID, T>, Throwable> = coResultOf {
        appointments
            .map(Appointment::staffId)
            .distinct()
            .takeIf { it.isNotEmpty() }
            ?.let { staffIds ->
                function(staffIds)
            } ?: emptyMap()
    }

    private fun Appointment.eventIsValid(staff: Staff) =
        (this.templateType != null &&
                this.event == null &&
                !canFinishWithoutEvent(staff) &&
                this.emptyEventReason.isNullOrEmpty()).not()

    private fun Appointment.freeTextCounterReferralIsValid() =
        (this.type == COUNTER_REFERRAL && this.description?.trim().isNullOrEmpty()).not()

    private fun Appointment.demandIsValid(staff: Staff) =
        (this.caseRecordDetails.isNullOrEmpty() && !canFinishWithoutDemand(staff)).not()

    private fun Appointment.excuseNotesIsValid() =
        (this.caseRecordDetails.isNullOrEmpty() && this.excuseNotes.isNotEmpty()).not()

    private fun canFinishWithoutDemand(staff: Staff) =
        getRolesToFinishAppointmentWithoutDemand().contains(staff.role)

    private fun canFinishWithoutEvent(staff: Staff) =
        getRolesToFinishAppointmentWithoutEvent().contains(staff.role)

    private fun getRolesToFinishAppointmentWithoutEvent() =
        FeatureService.getList(
            namespace = FeatureNamespace.APPOINTMENT,
            key = "roles_to_finish_appointment_without_event",
            defaultValue = listOf(
                Role.ON_SITE_NURSE.name,
                Role.ON_SITE_PHYSICIAN.name,
                Role.CARE_COORD_NURSE.name,
                Role.TECHNIQUE_NURSE.name,
                Role.HEALTH_COMMUNITY.name,
            )
        ).map { Role.valueOf(it) }

    private fun getRolesToFinishAppointmentWithoutDemand() =
        FeatureService.getList(
            namespace = FeatureNamespace.APPOINTMENT,
            key = "roles_to_finish_appointment_without_demand",
            defaultValue = listOf(
                Role.CHIEF_NAVIGATOR.name,
                Role.CHIEF_NAVIGATOR_OPS.name,
                Role.NAVIGATOR.name,
                Role.NAVIGATOR_OPS.name,
                Role.TECHNIQUE_NURSE.name
            )
        ).map { Role.valueOf(it) }

    private suspend fun enrichProtocolNavigation(appointment: Appointment): List<ProtocolNavigation> =
        if (appointment.event?.referenceModel == AppointmentEventReferenceModel.CHANNEL
            && appointment.protocolNavigationHistory.isNullOrEmpty()
        ) {
            channelService.get(appointment.event!!.referenceModelId).flatMap { channel ->
                channel.screeningNavigation
                    ?.takeIf { it.hasProtocol }
                    ?.let { screening ->
                        budService.getNavigationHistory(
                            ServiceScriptNavigationSource(
                                type = ServiceScriptNavigationSourceType.SCREENING_NAVIGATION,
                                id = screening.id.toString()
                            )
                        ).mapEach { protocolNavigationFrom(it) }
                    } ?: emptyList<ProtocolNavigation>().success()
            }.getOrElse { emptyList() }
        } else appointment.protocolNavigationHistory ?: emptyList()

    private fun protocolNavigationFrom(navigation: BudNodeNavigationHistory) =
        ProtocolNavigation(
            rootNodeId = navigation.rootNodeId,
            protocolName = navigation.protocolName,
            nodeId = navigation.nodeId,
            question = navigation.question,
            answer = navigation.answer,
            date = navigation.date,
            type = navigation.type?.name,
            index = navigation.index
        )

    private suspend fun validateAppointment(
        appointment: Appointment,
        staffId: UUID
    ): Result<AppointmentWithValidation, Throwable> {
        appointment.checkStatus()

        val errors = buildList {
            if (appointment.freeTextCounterReferralIsValid().not())
                add("invalid_counter_referral_free_text" to "The free text is required")
            if (appointment.excuseNotesIsValid().not())
                add("excuse_notes_without_appointment_case_record_details" to "The demand is required when generate excuse notes")

            staffService.get(staffId).map { staff ->
                if (appointment.procedureExecutedIsValid().not())
                    add("invalid_procedure_executed" to "It is not allowed to finish a counter referral without procedures executed")
                if (appointment.followUpWithMedicalDischargeIsValid().not())
                    add("invalid_follow_up_with_medical_discharge" to "It is not allowed to create a follow-up return with medical discharge")
                if (appointment.eventIsValid(staff).not())
                    add("invalid_appointment_event" to "The event is required")
                if (appointment.demandIsValid(staff).not())
                    add("invalid_appointment_case_record_details" to "The demand is required")
            }
        }

        return AppointmentWithValidation(
            isValid = errors.isEmpty(),
            appointment = appointment,
            errors = errors.map { (code, message) -> AppointmentValidationError(code, message) }
        ).success()
    }

    private fun Appointment.checkStatus() {
        if (this.status != DRAFT) {
            throw IllegalArgumentException("Cannot finish appointment on ${this.status} status")
        }
    }

    private suspend fun checkIfFinishAppointment(
        appointment: Appointment,
        finishType: AppointmentFinishType
    ) = coResultOf<Appointment, Throwable> {
        if (appointment.areFilledAllCaseRecord() || appointment.caseRecordDetails.isNullOrEmpty()) {
            appointment.fillFinishedAppointment(finishType)
        } else {
            appointment.copy(
                completedAt = LocalDateTime.now(),
                externalFiles = appointment.externalFiles?.getOnlyStored()
            )
        }
    }

    private fun Appointment.fillFinishedAppointment(
        finishType: AppointmentFinishType
    ) = this.copy(
        completedAt = LocalDateTime.now(),
        status = FINISHED,
        finishType = finishType,
        // temp until migrate for new UX
        name = this.name ?: this.type.description,
        subjective = this.subjective
            ?: takeIf { this.type == ASSISTANCE_CARE }?.let { this.description },
        plan = this.plan ?: this.guidance,
        externalFiles = this.externalFiles?.getOnlyStored()
    ).fillDemands()

    private fun List<ExternalFile>.getOnlyStored() = this.filter { it.store }

    private fun Appointment.fillDemands() =
        copy(caseRecordDetails = this.caseRecordDetails?.fillChannel(this.event))

    private suspend fun Appointment.procedureExecutedIsValid() =
        if (type == COUNTER_REFERRAL)
            procedureExecutedService.existByAppointmentId(id).get()
        else true

    private suspend fun Appointment.followUpWithMedicalDischargeIsValid() =
        if (this.medicalDischarge == true)
            healthPlanTaskService.existsByFilters(
                HealthPlanTaskFilters(
                    appointmentIds = listOf(this.id),
                    types = listOf(HealthPlanTaskType.FOLLOW_UP_REQUEST),
                    statuses = listOf(HealthPlanTaskStatus.DRAFT)
                )
            ).get().not()
        else true

    private fun List<CaseRecordDetails>.fillChannel(event: AppointmentEventDetail?) =
        event?.takeIf { it.referenceModel == AppointmentEventReferenceModel.CHANNEL }
            ?.let { eventDetail ->
                map { case ->
                    if (case.channel == null) case.copy(
                        channel = ChannelId(
                            id = eventDetail.referenceModelId,
                            name = eventDetail.name
                        )
                    ) else case
                }
            } ?: this

    private fun Appointment.checkDraft() {
        if (this.status != DRAFT) {
            throw InvalidArgumentException(
                code = "invalid_appointment_status",
                message = "cannot delete appointment on ${this.status} status"
            )
        }
    }
}
