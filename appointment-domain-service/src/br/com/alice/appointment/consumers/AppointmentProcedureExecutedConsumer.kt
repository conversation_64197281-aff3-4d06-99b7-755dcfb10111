package br.com.alice.appointment.consumers

import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent

class AppointmentProcedureExecutedConsumer(
    private val appointmentProcedureExecutedService: AppointmentProcedureExecutedService
) : Consumer() {

    suspend fun changeProcedureToDone(event: AppointmentCompletedEvent) = withSubscribersEnvironment {
        appointmentProcedureExecutedService.changeProceduresToDoneAndPublish(event.payload.appointment)
    }

    suspend fun deleteProcedures(event: DraftAppointmentDeletedEvent) = withSubscribersEnvironment {
        appointmentProcedureExecutedService.deleteProcedures(event.payload.appointmentId)
    }

}
