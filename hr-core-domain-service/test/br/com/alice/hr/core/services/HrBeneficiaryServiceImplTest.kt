package br.com.alice.hr.core.services

import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.clients.interfaces.AmazonS3Client
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.DependentTransport
import br.com.alice.hr.core.services.useCases.AddressValidateBeneficiariesUseCase
import br.com.alice.hr.core.services.useCases.CreateBeneficiaryDependentUseCase
import br.com.alice.hr.core.services.useCases.DynamicValidateBeneficiariesUseCase
import br.com.alice.hr.core.services.useCases.GenerateTemplateSheetsUseCase
import br.com.alice.hr.core.services.useCases.GetBeneficiaryRelationTypeOptionsUseCase
import br.com.alice.hr.core.services.useCases.ProcessBeneficiaryBatchUseCase
import br.com.alice.hr.core.services.useCases.StaticValidateBeneficiariesUseCase
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals

class HrBeneficiaryServiceImplTest {
    private val companyService: CompanyService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()
    private val productService: ProductService = mockk()
    private val getBeneficiaryRelationTypeOptionsUseCase: GetBeneficiaryRelationTypeOptionsUseCase = mockk()
    private val createBeneficiaryDependentUseCase: CreateBeneficiaryDependentUseCase = mockk()
    private val generateTemplateSheetsUseCase: GenerateTemplateSheetsUseCase = mockk()
    private val staticValidateBeneficiariesUseCase: StaticValidateBeneficiariesUseCase = mockk()
    private val processBeneficiaryBatchUseCase: ProcessBeneficiaryBatchUseCase = mockk()
    private val amazonS3Client: AmazonS3Client = mockk()
    private val dynamicValidateBeneficiariesUseCase: DynamicValidateBeneficiariesUseCase = mockk()
    private val addressValidateBeneficiariesUseCase: AddressValidateBeneficiariesUseCase = mockk()

    private val hrBeneficiaryService = HrBeneficiaryServiceImpl(
        companyService,
        companySubContractService,
        companyProductPriceListingService,
        productService,
        getBeneficiaryRelationTypeOptionsUseCase,
        createBeneficiaryDependentUseCase,
        staticValidateBeneficiariesUseCase,
        processBeneficiaryBatchUseCase,
        generateTemplateSheetsUseCase,
        amazonS3Client,
        dynamicValidateBeneficiariesUseCase,
        addressValidateBeneficiariesUseCase,
    )

    private val company = TestModelFactory.buildCompany()
    private val subContract = TestModelFactory.buildCompanySubContract(companyId = company.id)
    private val relationTypes = listOf(ParentBeneficiaryRelationType.CHILD)
    private val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing(companyId = company.id)
    private val product = TestModelFactory.buildProduct(id = companyProductPriceListing.productId)

    @BeforeEach
    fun setup() {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns relationTypes.success()
    }

    @Test
    fun`#getRelationTypes should return relation types`() = runBlocking {
        val result = hrBeneficiaryService.getRelationTypes(company.id, subContract.id).get()

        assertEquals(relationTypes, result)

        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
    }

    private val address = TestModelFactory.buildAddress()
    private val beneficiary = TestModelFactory.buildBeneficiary()

    private val dependent = DependentTransport(
        firstName = "John",
        lastName = "Doe",
        mothersName = "Jane Doe",
        nationalId = "1234567890",
        email = "<EMAIL>",
        sex = Sex.MALE,
        birthDate = LocalDateTime.now(),
        phoneNumber = "1234567890",
        activatedAt = LocalDateTime.now(),
        address = address,
        productId = UUID.randomUUID(),
        parentBeneficiary = UUID.randomUUID(),
        parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        parentBeneficiaryRelatedAt = LocalDateTime.now(),
        cnpj = "1234567890",
        subcontractId = subContract.id
    )

    @Test
    fun `#createDependent should create dependent beneficiary`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns relationTypes.success()
        coEvery { createBeneficiaryDependentUseCase.run(dependent, company, relationTypes) } returns beneficiary.success()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
        coVerifyOnce { createBeneficiaryDependentUseCase.run(any(), any(), any()) }
    }

    @Test
    fun `#createDependent should return exeception when companyService returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
    }

    @Test
    fun `#createDependent should return exception when getBeneficiaryRelationTypeOptionsUseCase returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
    }

    @Test
    fun `#createDependent should return exception when createBeneficiaryDependentUseCase returns failure`() = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.get(subContract.id) } returns subContract.success()
        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract) } returns relationTypes.success()
        coEvery { createBeneficiaryDependentUseCase.run(dependent, company, relationTypes) } returns Exception().failure()

        val result = hrBeneficiaryService.createDependent(dependent, company.id)

        assertThat(result).isFailure()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
        coVerifyOnce { createBeneficiaryDependentUseCase.run(any(), any(), any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return template sheet result`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { amazonS3Client.getS3ObjectContent(any()) } returns byteArrayOf(1, 2, 3).inputStream().success()
        coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(subContract).success()
        coEvery { companyProductPriceListingService.findCurrentByCompanyIds(listOf(company.id)) } returns listOf(companyProductPriceListing).success()
        coEvery { productService.findByIds(listOf(companyProductPriceListing.productId)) } returns listOf(product).success()
        coEvery { generateTemplateSheetsUseCase.run(any(), any(), any(), any()) } returns byteArrayOf(1, 2, 3).success()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isSuccess()

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { amazonS3Client.getS3ObjectContent(any()) }
        coVerifyOnce { companySubContractService.findByCompanyId(any()) }
        coVerifyOnce { companyProductPriceListingService.findCurrentByCompanyIds(any()) }
        coVerifyOnce { productService.findByIds(any()) }
        coVerifyOnce { generateTemplateSheetsUseCase.run(any(), any(), any(), any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return internal service error exception when amazonS3Client returns failure`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(subContract).success()
        coEvery { companyProductPriceListingService.findCurrentByCompanyIds(listOf(company.id)) } returns listOf(companyProductPriceListing).success()
        coEvery { productService.findByIds(listOf(companyProductPriceListing.productId)) } returns listOf(product).success()
        coEvery { amazonS3Client.getS3ObjectContent(any()) } returns Exception().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.findByCompanyId(any()) }
        coVerifyOnce { companyProductPriceListingService.findCurrentByCompanyIds(any()) }
        coVerifyOnce { productService.findByIds(any()) }
        coVerifyOnce { amazonS3Client.getS3ObjectContent(any()) }
    }

    @Test
    fun `#addBeneficiariesBatch should returns successfully`(): Unit = runBlocking {
        val uploadId = UUID.randomUUID()
        val companyStaffId = UUID.randomUUID()

        val transport = BeneficiaryBatchTransport(
            uploadId = uploadId,
            items = listOf(
                BeneficiaryBatchItemTransport(
                    index = 1,
                    nationalId = "12345678901",
                    fullName = "John Doe",
                    cnpj = "12345678000195",
                    subContractTitle = "Matriz",
                    sex = "M",
                    dateOfBirth = "1990-01-01",
                    mothersName = "Jane Doe",
                    email = "<EMAIL>",
                    phoneNumber = "1234567890",
                )
            )
        )
        val expected = BeneficiaryBatchValidation(
            success = listOf(1, 2),
            errors = emptyList(),
        )

        coEvery {
            staticValidateBeneficiariesUseCase.run(transport)
        } returns expected.success()
        coEvery {
            processBeneficiaryBatchUseCase.run(
                uploadId = uploadId,
                companyId = company.id,
                companyStaffId = companyStaffId,
                batchTransport = transport,
                validationResult = expected
            )
        } returns true.success()
        coEvery {
            dynamicValidateBeneficiariesUseCase.run(company,transport)
        } returns expected.copy(success = listOf(1, 2)).success()
        coEvery {
            addressValidateBeneficiariesUseCase.run(transport)
        } returns (expected to transport.items).success()

        val result = hrBeneficiaryService.addBeneficiariesBatch(
            uploadId = uploadId,
            companyId = company.id,
            companyStaffId = companyStaffId,
            transport,
        )

        assertThat(result).isSuccess()
        assertEquals(expected, result.get())

        coVerifyOnce { staticValidateBeneficiariesUseCase.run(any()) }
        coVerifyOnce { dynamicValidateBeneficiariesUseCase.run(any(), any()) }
    }

    @Test
    fun `#addBeneficiariesBatch should return internal service error exception when usecase returns failure`(): Unit = runBlocking {
        val transport = BeneficiaryBatchTransport(
            uploadId = RangeUUID.generate(),
            items = listOf(
                BeneficiaryBatchItemTransport(
                    index = 1,
                    nationalId = "12345678901",
                    fullName = "John Doe",
                    cnpj = "12345678000195",
                    subContractTitle = "Matriz",
                    sex = "M",
                    dateOfBirth = "1990-01-01",
                    mothersName = "Jane Doe",
                    email = "<EMAIL>",
                    phoneNumber = "1234567890",
                )
            )
        )

        coEvery { staticValidateBeneficiariesUseCase.run(transport) } returns InternalServiceErrorException().failure()

        val result = hrBeneficiaryService.addBeneficiariesBatch(
            uploadId = UUID.randomUUID(),
            companyId = company.id,
            companyStaffId = UUID.randomUUID(),
            transport,
        )

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { staticValidateBeneficiariesUseCase.run(any()) }
    }

    @Test
    fun `#addBeneficiariesBatch should return invalid argument exception when processBeneficiaryBatchUseCase returns failure`(): Unit = runBlocking {
        val uploadId = UUID.randomUUID()
        val companyStaffId = UUID.randomUUID()

        val transport = BeneficiaryBatchTransport(
            uploadId = uploadId,
            items = listOf(
                BeneficiaryBatchItemTransport(
                    index = 1,
                    nationalId = "12345678901",
                    fullName = "John Doe",
                    cnpj = "12345678000195",
                    subContractTitle = "Matriz",
                    sex = "M",
                    dateOfBirth = "1990-01-01",
                    mothersName = "Jane Doe",
                    email = "<EMAIL>",
                    phoneNumber = "1234567890",
                )
            )
        )
        val expected = BeneficiaryBatchValidation(
            success = listOf(1),
            errors = emptyList(),
        )
        val addressUseCaseExpected = (expected to transport.items)

        coEvery { staticValidateBeneficiariesUseCase.run(transport) } returns expected.success()
        coEvery {
            dynamicValidateBeneficiariesUseCase.run(company, transport)
        } returns expected.success()
        coEvery { processBeneficiaryBatchUseCase.run(
            uploadId = uploadId,
            companyId = company.id,
            companyStaffId = companyStaffId,
            batchTransport = transport,
            validationResult = expected
        ) } returns InvalidArgumentException().failure()
        coEvery {
            addressValidateBeneficiariesUseCase.run(transport)
        } returns addressUseCaseExpected.success()

        val result = hrBeneficiaryService.addBeneficiariesBatch(
            uploadId = uploadId,
            companyId = company.id,
            companyStaffId = companyStaffId,
            transport,
        )

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { staticValidateBeneficiariesUseCase.run(any()) }
        coVerifyOnce { processBeneficiaryBatchUseCase.run(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return not found exception when companyService returns failure`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns NotFoundException().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { companyService.get(any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return error exception when companySubContractService returns failure`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.findByCompanyId(company.id) } returns Exception().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.findByCompanyId(any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return error exception when companyProductPriceListingService returns failure`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(subContract).success()
        coEvery { companyProductPriceListingService.findCurrentByCompanyIds(listOf(company.id)) } returns Exception().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.findByCompanyId(any()) }
        coVerifyOnce { companyProductPriceListingService.findCurrentByCompanyIds(any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return error exception when productService returns failure`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(subContract).success()
        coEvery { companyProductPriceListingService.findCurrentByCompanyIds(listOf(company.id)) } returns listOf(companyProductPriceListing).success()
        coEvery { productService.findByIds(listOf(companyProductPriceListing.productId)) } returns Exception().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.findByCompanyId(any()) }
        coVerifyOnce { companyProductPriceListingService.findCurrentByCompanyIds(any()) }
        coVerifyOnce { productService.findByIds(any()) }
    }

    @Test
    fun `#getBeneficiarySheetTemplate should return error exception when generateTemplateSheetsUseCase returns failure`(): Unit = runBlocking {
        coEvery { companyService.get(company.id) } returns company.success()
        coEvery { companySubContractService.findByCompanyId(company.id) } returns listOf(subContract).success()
        coEvery { companyProductPriceListingService.findCurrentByCompanyIds(listOf(company.id)) } returns listOf(companyProductPriceListing).success()
        coEvery { productService.findByIds(listOf(companyProductPriceListing.productId)) } returns listOf(product).success()
        coEvery { amazonS3Client.getS3ObjectContent(any()) } returns byteArrayOf(1, 2, 3).inputStream().success()
        coEvery { generateTemplateSheetsUseCase.run(any(), any(), any(), any()) } returns Exception().failure()

        val result = hrBeneficiaryService.getBeneficiarySheetTemplate(company.id)

        assertThat(result).isFailureOfType(InternalServiceErrorException::class)

        coVerifyOnce { companyService.get(any()) }
        coVerifyOnce { companySubContractService.findByCompanyId(any()) }
        coVerifyOnce { companyProductPriceListingService.findCurrentByCompanyIds(any()) }
        coVerifyOnce { productService.findByIds(any()) }
        coVerifyOnce { generateTemplateSheetsUseCase.run(any(), any(), any(), any()) }
    }
}
