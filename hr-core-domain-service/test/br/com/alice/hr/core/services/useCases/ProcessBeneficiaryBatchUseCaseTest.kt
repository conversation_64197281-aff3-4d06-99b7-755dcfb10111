package br.com.alice.hr.core.services.useCases

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.services.HrMemberUploadTrackingDataService
import br.com.alice.data.layer.models.HrMemberUploadTracking
import br.com.alice.hr.core.events.HrCreateBeneficiaryBatchEvent
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.util.UUID

class ProcessBeneficiaryBatchUseCaseTest {
    private val hrMemberUploadTrackingDataService = mockk<HrMemberUploadTrackingDataService>()
    private val kafkaProducerService = mockk<KafkaProducerService>()
    private val processBeneficiaryBatchUseCase = ProcessBeneficiaryBatchUseCase(hrMemberUploadTrackingDataService, kafkaProducerService)

    private val uploadId = UUID.randomUUID()
    private val companyId = UUID.randomUUID()
    private val companyStaffId = UUID.randomUUID()

    private val beneficiaryItem1 = BeneficiaryBatchItemTransport(
        index = 1,
        nationalId = "12345678901",
        fullName = "João Silva",
        email = "<EMAIL>"
    )

    private val beneficiaryItem2 = BeneficiaryBatchItemTransport(
        index = 2,
        nationalId = "98765432109",
        fullName = "Maria Santos",
        email = "<EMAIL>"
    )

    private val originalBatch = BeneficiaryBatchTransport(
        items = listOf(beneficiaryItem1, beneficiaryItem2),
        uploadId = RangeUUID.generate(),
    )

    private val validationResult = BeneficiaryBatchValidation(
        success = listOf(1, 2),
        errors = emptyList()
    )

    @Test
    fun `#run should process beneficiary batch successfully`() = runBlocking {
        coEvery { kafkaProducerService.produce(match { it is HrCreateBeneficiaryBatchEvent }) } returns mockk()
        coEvery { hrMemberUploadTrackingDataService.addList(any()) } returns emptyList<HrMemberUploadTracking>().success()

        val result = processBeneficiaryBatchUseCase.run(
            uploadId = uploadId,
            companyId = companyId,
            companyStaffId = companyStaffId,
            batchTransport = originalBatch,
            validationResult = validationResult
        )

        assertThat(result).isSuccess()

        coVerify (exactly = 2) { kafkaProducerService.produce(any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.addList(any()) }
    }

    @Test
    fun `#run should return Exception when at least one nationalId is null`() = runBlocking {
        val originalBatch = originalBatch.copy(items = listOf(beneficiaryItem1.copy(nationalId = null)))

        val validationResult = validationResult.copy(success = listOf(1))
    
        val result = processBeneficiaryBatchUseCase.run(
            uploadId = uploadId,
            companyId = companyId,
            companyStaffId = companyStaffId,
            batchTransport = originalBatch,
            validationResult = validationResult
        )

        assertThat(result).isFailureOfType(InvalidArgumentException::class)
    }

    @Test
    fun `#run should return Exception when kafka producer fails`() = runBlocking {
        coEvery { kafkaProducerService.produce(match { it is HrCreateBeneficiaryBatchEvent }) } throws Exception("Kafka producer failed")

        val result = processBeneficiaryBatchUseCase.run(
            uploadId = uploadId,
            companyId = companyId,
            companyStaffId = companyStaffId,
            batchTransport = originalBatch,
            validationResult = validationResult
        )

        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#run should return Exception when hrMemberUploadTrackingDataService fails`() = runBlocking {
        coEvery { kafkaProducerService.produce(match { it is HrCreateBeneficiaryBatchEvent }) } returns mockk()
        coEvery { hrMemberUploadTrackingDataService.addList(any()) } throws Exception("HrMemberUploadTrackingDataService failed")

        val result = processBeneficiaryBatchUseCase.run(
            uploadId = uploadId,
            companyId = companyId,
            companyStaffId = companyStaffId,
            batchTransport = originalBatch,
            validationResult = validationResult
        )

        assertThat(result).isFailureOfType(Exception::class)

        coVerify (exactly = 2) { kafkaProducerService.produce(any()) }
        coVerifyOnce { hrMemberUploadTrackingDataService.addList(any()) }
    }
}
