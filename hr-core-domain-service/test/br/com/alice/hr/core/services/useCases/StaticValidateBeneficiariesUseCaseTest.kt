package br.com.alice.hr.core.services.useCases

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate

class StaticValidateBeneficiariesUseCaseTest {
    private val staticValidateBeneficiariesUseCase = StaticValidateBeneficiariesUseCase()

    private val today = LocalDate.now().toBrazilianDateFormat()
    private val beneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
        index = 1,
        nationalId = "438.595.570-09",
        dateOfBirth = "01/01/1990",
        fullName = "Calango Zokas",
        sex = "M",
        cnpj = "75.006.845/0001-93",
        subContractTitle = "Matriz",
        phoneNumber = "1191234-5678",
        mothersName = "Jane Doe",
        email = "<EMAIL>",
        addressNumber = "222",
        addressPostalCode = "12345678",
        addressComplement = null,
        productTitle = "Plano de Saúde Bonzao",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        hiredAt = "01/02/2023",
        parentNationalId = null,
        parentBeneficiaryRelationType = null,
        relationExceeds30Days = "nao",
        ownership = "Titular",
    )
    private val beneficiaryBatchTransport = BeneficiaryBatchTransport(
        items = emptyList(),
        uploadId = RangeUUID.generate(),
    )

    @Test
    fun `#run should return an error for duplicated beneficiaries`() = runBlocking {
        val duplicated = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
                beneficiaryBatchItemTransportValid.copy(index = 2),
            )
        )
        val expectedErrors = listOf(
            BeneficiaryBatchValidationError(
                index = 1,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "nationalId",
                        message = "CPF duplicado na planilha."
                    )
                )
            ),
            BeneficiaryBatchValidationError(
                index = 2,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "nationalId",
                        message = "CPF duplicado na planilha."
                    )
                )
            )
        )

        val result = staticValidateBeneficiariesUseCase
            .run(duplicated)
            .get()

        assert(result.success.isEmpty())
        assert(result.errors.size == expectedErrors.size)
        assert(result.errors == expectedErrors)
    }

    @Nested
    inner class ValidateBasicInfoTests {
        @Nested
        inner class ValidateOwnershipTests {
            @Test
            fun `#run should return an error when ownership is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(ownership = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "ownership",
                                message = "Titulariedade obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when ownership is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(ownership = "depedente"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "ownership",
                                message = "Titulariedade inválida, escreva somente Titular ou Dependente."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateFullNameTests {
            @Test
            fun `#run should return an error when name is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Nome completo obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when name has less than 5 letters`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = "Zok"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Nome completo deve ter ao menos 5 caracteres."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when name has no last name`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = "Calango"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Insira o nome e sobrenome do beneficiário."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateNationalIdTests {
            @Test
            fun `#run should return an error when national id is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(nationalId = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "CPF obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when national id is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(nationalId = "12345678901"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "CPF inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateSexTests {
            @Test
            fun `#run should return an error when sex is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(sex = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "sex",
                                message = "Sexo biológico obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when sex is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(sex = "homem"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "sex",
                                message = "Sexo biológico inválido, selecione uma das opções da lista."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateMothersNameTests {
            @Test
            fun `#run should return an error when mothers name is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(mothersName = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "mothersName",
                                message = "Nome completo da mãe obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when mothers name has less than 5 letters`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(mothersName = "Mae"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "mothersName",
                                message = "Insira o nome e sobrenome da mãe."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when mothers name has no last name`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(mothersName = "Maria"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "mothersName",
                                message = "Insira o nome e sobrenome da mãe."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidatePhoneNumberTests {
            @Test
            fun `#run should return an error when phone number is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "phoneNumber",
                                message = "Telefone obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when phone number is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = "1232322hj22"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "phoneNumber",
                                message = "Telefone inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateAddressPostalCodeTests {
            @Test
            fun `#run should return an error when address postal code is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressPostalCode = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressPostalCode",
                                message = "CEP obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when address postal code is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressPostalCode = "1232322hj22"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressPostalCode",
                                message = "CEP inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateAddressNumberTests {
            @Test
            fun `#run should return an error when address number is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressNumber = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressNumber",
                                message = "Número do logradouro inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when address number is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressNumber = "sdadsa"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressNumber",
                                message = "Número do logradouro inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateProductTitleTests {
            @Test
            fun `#run should return an error when product title is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(productTitle = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "productTitle",
                                message = "Produto obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateBirthDateTests {
            @Test
            fun `#run should return an error when birth date is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when birth date is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = "13/13/1212"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when birth date is after today`() = runBlocking {
                val today = LocalDate.now()

                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = today.plusMonths(3).toString()),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento inválida."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when birth date is before 1905`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = LocalDate.of(1904, 1, 1).toString()),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento inválida."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateEmailTests {
            @Test
            fun `#run should return an error when email is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "E-mail obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when email has typo`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = "<EMAIL>"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "E-mail inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when email has invalid domain`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = "asasas@<EMAIL>"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "E-mail inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateActivatedAtTests {
            @Test
            fun `#run should return an error when activated at is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(activatedAt = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "activatedAt",
                                message = "Data de ativação obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when activated at is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(activatedAt = "13/13/1212"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "activatedAt",
                                message = "Data de ativação obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when activated at is before today`() = runBlocking {
                val today = LocalDate.now()

                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(activatedAt = today.minusDays(3).toString()),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "activatedAt",
                                message = "Data de ativação deve ser maior que a data atual."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }
    }

    @Nested
    inner class ValidateHolderFieldsTests {
        private val holderBeneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
            index = 1,
            nationalId = "438.595.570-09",
            dateOfBirth = "01/01/1990",
            fullName = "Calanga Zokas",
            sex = "F",
            cnpj = "75.006.845/0001-93",
            subContractTitle = "Matriz",
            phoneNumber = "1191234-5678",
            mothersName = "Jane Doe",
            email = "<EMAIL>",
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            productTitle = "Plano de Saúde Bonzao",
            activatedAt = today,
            beneficiaryContractType = "CLT",
            hiredAt = "01/02/2023",
            parentNationalId = null,
            parentBeneficiaryRelationType = null,
            relationExceeds30Days = "nao",
            ownership = "Titular",
        )

        @Nested
        inner class ValidateSubContractTitleTests {
            @Test
            fun `#run should return an error when subContractTitle is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(subContractTitle = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "subContractTitle",
                                message = "Contrato para faturamento obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when subContractTitle is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(subContractTitle = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "subContractTitle",
                                message = "Contrato para faturamento obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateBeneficiaryContractTypeTests {
            @Test
            fun `#run should return an error when beneficiaryContractType is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(beneficiaryContractType = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "beneficiaryContractType",
                                message = "Regime de contratação obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when beneficiaryContractType is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(beneficiaryContractType = "inválido"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "beneficiaryContractType",
                                message = "Regime de contratação inválido, escolha CLT ou PJ."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateCnpjTests {
            @Test
            fun `#run should return an error when cnpj is missing when beneficiaryContractType is PJ`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(
                            cnpj = null,
                            beneficiaryContractType = "PJ",
                        ),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "cnpj",
                                message = "CNPJ do titular obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when cnpj is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(
                            cnpj = "12345678901234",
                            beneficiaryContractType = "PJ"
                        ),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "cnpj",
                                message = "CNPJ do titular inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateHiredAtTests {
            @Test
            fun `#run should return an error when hired at is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(hiredAt = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "hiredAt",
                                message = "Data de admissão do titular obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when hired at is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(hiredAt = "13/13/1212"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "hiredAt",
                                message = "Data de admissão do titular com formato inválido, use DD/MM/AAAA."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }
    }

    @Nested
    inner class ValidateDependentFieldsTests {
        private val dependentBeneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
            index = 3,
            nationalId = "358.213.480-64",
            dateOfBirth = "01/01/2010",
            fullName = "Calanguinho Zokas",
            sex = "M",
            cnpj = null,
            subContractTitle = "Matriz",
            phoneNumber = "1191234-5678",
            mothersName = "Calanga Zokas",
            email = "<EMAIL>",
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            productTitle = "Plano de Saúde Bonzao",
            activatedAt = today,
            beneficiaryContractType = "CLT",
            hiredAt = null,
            parentNationalId = "438.595.570-09",
            parentBeneficiaryRelationType = "Filha ou filho",
            relationExceeds30Days = "sim",
            ownership = "Dependente",
        )

        @Nested
        inner class ValidateRelationExceeds30DaysTests {
            @Test
            fun `#run should return an error when relationExceeds30Days is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(relationExceeds30Days = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "relationExceeds30Days",
                                message = "Vínculo com titular a mais de 30 dias obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when relationExceeds30Days is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(relationExceeds30Days = "dsffsdf"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "relationExceeds30Days",
                                message = "Vínculo com titular a mais de 30 dias inválido, selecione uma das opções da lista."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateParentBeneficiaryRelationTypeTests {
            @Test
            fun `#run should return an error when parentBeneficiaryRelationType is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentBeneficiaryRelationType = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentBeneficiaryRelationType",
                                message = "Relação com titular obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when parentBeneficiaryRelationType is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentBeneficiaryRelationType = "Filho"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentBeneficiaryRelationType",
                                message = "Relação com titular inválida, selecione uma das opções da lista."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateParentNationalIdTests {
            @Test
            fun `#run should return an error when parentNationalId is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentNationalId = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentNationalId",
                                message = "CPF do titular obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when parentNationalId is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentNationalId = "123.456.789-00"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = 3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentNationalId",
                                message = "CPF do titular inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }
    }
}
