package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.Product
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime

class DynamicValidateBeneficiariesUseCaseTest {
    private val productService: ProductService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val personService: PersonService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val companyProductPriceListingService: CompanyProductPriceListingService = mockk()

    private val dynamicValidateBeneficiariesUseCase = DynamicValidateBeneficiariesUseCase(
        productService,
        beneficiaryService,
        personService,
        companySubContractService,
        companyProductPriceListingService,
    )

    private val today = LocalDate.now().minusDays(5).toBrazilianDateFormat()
    private val companyId = RangeUUID.generate()
    private val localDateTime = LocalDateTime.now()
    private val beneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
        index = 1,
        nationalId = "438.595.570-09",
        dateOfBirth = "01/01/1990",
        fullName = "Calango Zokas",
        sex = "M",
        cnpj = "75.006.845/0001-93",
        subContractTitle = "Matriz",
        phoneNumber = "1191234-5678",
        mothersName = "Jane Doe",
        email = "<EMAIL>",
        addressNumber = "222",
        addressPostalCode = "12345678",
        addressComplement = null,
        productTitle = "Plano de Saúde Bonzao",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        hiredAt = "01/02/2023",
        parentNationalId = null,
        parentBeneficiaryRelationType = null,
        relationExceeds30Days = "nao",
        ownership = "Titular",
    )
    private val dependentBeneficiaryBatchItemTransportValid = beneficiaryBatchItemTransportValid.copy(
        index = 2,
        ownership = "Dependente",
        parentNationalId = "438.595.570-09",
        parentBeneficiaryRelationType = "Cônjuge",
        relationExceeds30Days = "sim",
    )
    private val beneficiaryBatchTransport = BeneficiaryBatchTransport(
        items = emptyList(),
        uploadId = RangeUUID.generate(),
    )
    private val company = TestModelFactory.buildCompany(
        id = companyId,
        contractStartedAt = localDateTime.minusDays(30),
    )
    private val product = TestModelFactory.buildProduct(
        title = "Plano de Saúde Bonzao",
    )
    private val person = TestModelFactory.buildPerson()
    private val billingAccountableParty = TestModelFactory.buildBillingAccountableParty(
        nationalId = "75.006.845/0001-93"
    )
    private val companySubContract = TestModelFactory.buildCompanySubContract(
        title = "Matriz",
        companyId = companyId,
        billingAccountablePartyId = billingAccountableParty.id,
        availableProducts = listOf(product.id),
    )
    private val companyProductPriceListing = TestModelFactory.buildCompanyProductPriceListing(
        productId = product.id,
        companySubContractId = companySubContract.id
    ).copy(isBlockedForSale = false)

    @Test
    fun `run should return empty validation when no items`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = emptyList()
        )

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(BeneficiaryBatchValidation(), result)
    }

    @Test
    fun `run should validate product exists`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery {
            productService.findActiveByTitles(any())
        } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado.",
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate return error when holder does not exist`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns NotFoundException().failure()
        
        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        
        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )
        
        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should return success when all validations pass`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should validate dependent activation date is after holder hired date`() = runBlocking {
        val holderHiredDate = localDateTime.plusDays(5)
        val dependentActivationDate = localDateTime.toLocalDate().minusDays(10).toBrazilianDateFormat()

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = holderHiredDate,
            activatedAt = localDateTime.minusDays(10),
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = dependentActivationDate,
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem),
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentItem.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação deve ser igual ou posterior à contratação do titular."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate dependent activation date is after holder activation date`() = runBlocking {
        val holderActivationDate = localDateTime.plusDays(5)
        val dependentActivationDate = localDateTime.toLocalDate().minusDays(2).toBrazilianDateFormat()

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            parentBeneficiary = null,
            parentBeneficiaryRelationType = null,
            hiredAt = LocalDateTime.of(2023, 1, 1, 0, 0),
            activatedAt = holderActivationDate
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = dependentActivationDate
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação do dependente deve ser igual ou posterior à data de ativação do titular."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate dependent activation date is after holder activation date from holder in batch`() = runBlocking {
        val holderActivationDate = localDateTime.plusDays(5)
        val dependentActivationDate = localDateTime.toLocalDate().minusDays(2).toBrazilianDateFormat()
        val holderInBatch = beneficiaryBatchItemTransportValid.copy(
            index = 1,
            activatedAt = holderActivationDate.toBrazilianDateFormat(),
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            activatedAt = dependentActivationDate
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem, holderInBatch)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação do dependente deve ser igual ou posterior à data de ativação do titular."
                )
            )
        )

        assertEquals(listOf(1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle multiple beneficiaries with mixed validation results`() = runBlocking {
        val validBeneficiary = beneficiaryBatchItemTransportValid.copy(index = 1)
        val validDependent = dependentBeneficiaryBatchItemTransportValid.copy(index = 3)

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validBeneficiary, validDependent)
        )

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(validBeneficiary.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1, 3), result.success)
        assertEquals(0, result.errors.size)
    }

    @Test
    fun `run should handle beneficiary with both product and holder validation errors`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.plusDays(5),
            activatedAt = localDateTime.minusDays(10)
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid.copy(
            productTitle = "Produto Inexistente",
            activatedAt = LocalDate.now().minusDays(2).toBrazilianDateFormat()
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentItem.productTitle!!)) } returns emptyList<Product>().success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedProductError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        val expectedHolderError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação deve ser igual ou posterior à contratação do titular."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(2, result.errors.size)
        assertEquals(expectedProductError, result.errors[0])
        assertEquals(expectedHolderError, result.errors[1])
    }

    @Test
    fun `run should skip holder validation when parentNationalId is null`() = runBlocking {
        val holderItem = beneficiaryBatchItemTransportValid.copy(
            parentNationalId = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(holderItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle successful dependent validation with valid holder`() = runBlocking {
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val dependentItem = dependentBeneficiaryBatchItemTransportValid
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentItem)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle product service returning null`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns NotFoundException().failure()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle multiple products with some found and some not found`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = 1,
            productTitle = "Plano de Saúde Bonzao"
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = 2,
            productTitle = "Produto Inexistente"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )
        val titles = transport.items.map { it.subContractTitle!! }.distinct()

        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!, beneficiary2.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(titles) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        assertEquals(listOf(1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle large batch with multiple validation scenarios`() = runBlocking {
        val validHolder = beneficiaryBatchItemTransportValid.copy(index = 1)
        val invalidProduct = beneficiaryBatchItemTransportValid.copy(
            index = 2,
            productTitle = "Produto Inexistente"
        )
        val validDependent = dependentBeneficiaryBatchItemTransportValid.copy(index = 3)
        val invalidDateDependent = dependentBeneficiaryBatchItemTransportValid.copy(
            index = 4,
            activatedAt = localDateTime.minusDays(2).toBrazilianDateFormat()
        )
        val anotherValidHolder = beneficiaryBatchItemTransportValid.copy(
            index = 5,
            nationalId = "123.456.789-00"
        )

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validHolder, invalidProduct, validDependent, invalidDateDependent, anotherValidHolder)
        )

        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val invalidDateHolderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.plusDays(5),
            activatedAt = localDateTime.minusDays(10)
        )

        coEvery { productService.findActiveByTitles(listOf(validHolder.productTitle!!, invalidProduct.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(validHolder.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returnsMany listOf(
            holderBeneficiary.success(),
            invalidDateHolderBeneficiary.success()
        )

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1, 3, 5), result.success)
        assertEquals(2, result.errors.size)

        val productError = result.errors.find { it.index == 2 }
        assertEquals("productTitle", productError?.error?.first()?.field)
        assertEquals("Produto não encontrado.", productError?.error?.first()?.message)

        val dateError = result.errors.find { it.index == 4 }
        assertEquals("activatedAt", dateError?.error?.first()?.field)
        assertEquals("Data de ativação deve ser igual ou posterior à contratação do titular.", dateError?.error?.first()?.message)
    }

    @Test
    fun `run should handle duplicate product titles in batch`() = runBlocking {
        val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
            index = 1,
            productTitle = "Plano de Saúde Bonzao"
        )
        val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
            index = 2,
            productTitle = "Plano de Saúde Bonzao"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiary1, beneficiary2)
        )

        coEvery { productService.findActiveByTitles(listOf(beneficiary1.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiary1.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1, 2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle mixed null and valid product titles`() = runBlocking {
        val beneficiaryWithValidProduct = beneficiaryBatchItemTransportValid.copy(
            index = 1,
            productTitle = "Plano de Saúde Bonzao"
        )
        val beneficiaryWithNullProduct = beneficiaryBatchItemTransportValid.copy(
            index = 2,
            productTitle = null
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryWithValidProduct, beneficiaryWithNullProduct)
        )

        coEvery { productService.findActiveByTitles(listOf(beneficiaryWithValidProduct.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto não encontrado."
                )
            )
        )

        assertEquals(listOf(1), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate product is not blocked for sale`() = runBlocking {
        val blockedPriceListing = companyProductPriceListing.copy(isBlockedForSale = true)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns blockedPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate product is available in subcontract`() = runBlocking {
        val subContractWithoutProduct = companySubContract.copy(availableProducts = emptyList())
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(subContractWithoutProduct).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder has required cnpj information`() = runBlocking {
        val holderWithoutCnpj = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = null,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithoutCnpj.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder has required contract type information`() = runBlocking {
        val holderWithoutContractType = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = null,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithoutContractType.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder has required hired date information`() = runBlocking {
        val holderWithoutHiredAt = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = null,
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithoutHiredAt.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate holder belongs to same company`() = runBlocking {
        val differentCompanyId = RangeUUID.generate()
        val holderFromDifferentCompany = TestModelFactory.buildBeneficiary(
            companyId = differentCompanyId,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderFromDifferentCompany.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should validate activation date is after company contract start`() = runBlocking {
        val companyWithLateContract = company.copy(
            contractStartedAt = localDateTime.plusDays(10)
        )
        val holderBeneficiary = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderBeneficiary.success()

        val result = dynamicValidateBeneficiariesUseCase.run(companyWithLateContract, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "activatedAt",
                    message = "Data de ativação deve ser após o início do contrato com a Alice."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle missing subcontract for beneficiary`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns emptyList<CompanySubContract>().success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "subContractTitle",
                    message = "Contrato para faturamento inválido, selecione uma das opções da lista.",
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(expectedError, result.errors.first())
    }

    @Test
    fun `run should handle missing billing accountable party`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(beneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle empty subcontract title list`() = runBlocking {
        val beneficiaryWithoutSubcontractTitle = beneficiaryBatchItemTransportValid.copy(
            subContractTitle = null,
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryWithoutSubcontractTitle)
        )

        coEvery { productService.findActiveByTitles(listOf(beneficiaryWithoutSubcontractTitle.productTitle!!)) } returns listOf(product).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "subContractTitle",
                    message = "Contrato para faturamento inválido, selecione uma das opções da lista."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(expectedError, result.errors.first())
    }

    @Test
    fun `run should handle dependent with holder in same batch`() = runBlocking {
        val holderInBatch = beneficiaryBatchItemTransportValid.copy(
            index = 1,
            nationalId = "438.595.570-09",
            parentNationalId = null
        )
        val dependentInBatch = dependentBeneficiaryBatchItemTransportValid.copy(
            index = 2,
            parentNationalId = "438.595.570-09"
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(holderInBatch, dependentInBatch)
        )

        coEvery { productService.findActiveByTitles(listOf(holderInBatch.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1, 2), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle beneficiary with parent beneficiary relation type missing`() = runBlocking {
        val holderWithMissingRelationType = TestModelFactory.buildBeneficiary(
            companyId = companyId,
            cnpj = "75.006.845/0001-93",
            contractType = BeneficiaryContractType.CLT,
            parentBeneficiary = RangeUUID.generate(),
            parentBeneficiaryRelationType = null,
            hiredAt = localDateTime.minusDays(30),
            activatedAt = localDateTime.minusDays(20)
        )
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(dependentBeneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns companyProductPriceListing.success()
        coEvery { personService.findByNationalId("43859557009") } returns person.success()
        coEvery { beneficiaryService.findByPersonId(person.id) } returns holderWithMissingRelationType.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 2,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle combination of product blocked and product not available errors`() = runBlocking {
        val blockedPriceListing = companyProductPriceListing.copy(isBlockedForSale = true)
        val subContractWithoutProduct = companySubContract.copy(availableProducts = emptyList())
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(any(), any())
        } returns blockedPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )

        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }

    @Test
    fun `run should handle price listing service returning null`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns NotFoundException().failure()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle price listing with isBlockedForSale null`() = runBlocking {
        val priceListingWithNullBlocked = companyProductPriceListing.copy(isBlockedForSale = null)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(companySubContract).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(companySubContract.id, product.id)
        } returns priceListingWithNullBlocked.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()

        assertEquals(listOf(1), result.success)
        assertEquals(emptyList<BeneficiaryBatchValidationError>(), result.errors)
    }

    @Test
    fun `run should handle subcontract with null available products`() = runBlocking {
        val subContractWithNullProducts = companySubContract.copy(availableProducts = null)
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        coEvery { productService.findActiveByTitles(listOf(dependentBeneficiaryBatchItemTransportValid.productTitle!!)) } returns listOf(product).success()
        coEvery { companySubContractService.findByTitles(listOf(beneficiaryBatchItemTransportValid.subContractTitle!!)) } returns listOf(subContractWithNullProducts).success()
        coEvery {
            companyProductPriceListingService.findCurrentBySubContractIdAndProductId(subContractWithNullProducts.id, product.id)
        } returns companyProductPriceListing.success()

        val result = dynamicValidateBeneficiariesUseCase.run(company, transport).get()
        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "productTitle",
                    message = "Produto inválido, use a lista da planilha modelo."
                )
            )
        )
        
        assertEquals(emptyList<Int>(), result.success)
        assertEquals(1, result.errors.size)
        assertEquals(expectedError, result.errors[0])
    }
}
