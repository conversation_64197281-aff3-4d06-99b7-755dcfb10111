package br.com.alice.hr.core.services.useCases

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.googlemaps.services.Address
import br.com.alice.common.googlemaps.services.GoogleMapsService
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertFailsWith

class AddressValidateBeneficiariesUseCaseTest {
    private val googleMapsService: GoogleMapsService = mockk()

    private val addressValidateBeneficiariesUseCase = AddressValidateBeneficiariesUseCase(
        googleMapsService,
    )

    private val today = LocalDate.now().toBrazilianDateFormat()
    private val beneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
        index = 1,
        nationalId = "438.595.570-09",
        dateOfBirth = "01/01/1990",
        fullName = "Calango Zokas",
        sex = "M",
        cnpj = "75.006.845/0001-93",
        subContractTitle = "Matriz",
        phoneNumber = "1191234-5678",
        mothersName = "Jane Doe",
        email = "<EMAIL>",
        addressNumber = "222",
        addressPostalCode = "12345678",
        addressComplement = null,
        productTitle = "Plano de Saúde Bonzao",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        hiredAt = "01/02/2023",
        parentNationalId = null,
        parentBeneficiaryRelationType = null,
        relationExceeds30Days = "nao",
        ownership = "Titular",
    )
    private val beneficiaryBatchTransport = BeneficiaryBatchTransport(
        items = emptyList(),
        uploadId = RangeUUID.generate(),
    )
    private val address = Address(
        street = "Avenida Rebouças",
        number = "222",
        complement = null,
        neighbourhood = "Pinheiros",
        city = "São Paulo",
        state = "SP",
        postalCode = "12345678",
        lat = 0.0,
        lng = 0.0,
        id = "place_id",
        country = "Brasil",
    )
    private val emptyAddress = Address(
        street = "",
        number = "",
        complement = "",
        neighbourhood = "",
        city = "",
        state = "",
        postalCode = "",
        lat = 0.0,
        lng = 0.0,
        id = "",
        country = "",
    )

    @Test
    fun `#run should return the beneficiaries with complete address from google service`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
            )
        )
        val beneficiaryWithAddress = beneficiaryBatchItemTransportValid.copy(
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            addressNeighborhood = "Pinheiros",
            addressCity = "São Paulo",
            addressState = "SP",
            addressStreet = "Avenida Rebouças",
        )

        coEvery {
            googleMapsService.getAddressByQuery(beneficiaryBatchItemTransportValid.addressPostalCode!!)
        } returns address.success()

        val (validation, beneficiaries) = addressValidateBeneficiariesUseCase
            .run(transport)
            .get()

        assert(validation.success.size == 1)
        assert(beneficiaries.size == 1)
        assert(beneficiaries[0] == beneficiaryWithAddress)
    }

    @Test
    fun `#run should return an error when address is not found`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
            )
        )

        coEvery {
            googleMapsService.getAddressByQuery(beneficiaryBatchItemTransportValid.addressPostalCode!!)
        } returns emptyAddress.success()

        val (validation, beneficiaries) = addressValidateBeneficiariesUseCase
            .run(transport)
            .get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "addressPostalCode",
                    message = "CEP inválido.",
                )
            )
        )

        assert(validation.success.isEmpty())
        assert(validation.errors.size == 1)
        assert(validation.errors[0] == expectedError)
        assert(beneficiaries.isEmpty())
    }

    @Test
    fun `#run should return an error when postal code is not the same as the one in the request`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
            )
        )
        val addressWrongPostalCode = address.copy(postalCode = "12345679")

        coEvery {
            googleMapsService.getAddressByQuery(beneficiaryBatchItemTransportValid.addressPostalCode!!)
        } returns addressWrongPostalCode.success()

        val (validation, beneficiaries) = addressValidateBeneficiariesUseCase
            .run(transport)
            .get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "addressPostalCode",
                    message = "CEP inválido.",
                )
            )
        )

        assert(validation.success.isEmpty())
        assert(validation.errors.size == 1)
        assert(validation.errors[0] == expectedError)
        assert(beneficiaries.isEmpty())
    }

    @Test
    fun `#run should return an error google maps service throws an exception`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
            )
        )

        coEvery {
            googleMapsService.getAddressByQuery(beneficiaryBatchItemTransportValid.addressPostalCode!!)
        } returns InternalServiceErrorException("Error").failure()

        assertFailsWith <InternalServiceErrorException> {
            addressValidateBeneficiariesUseCase
                .run(transport)
                .get()
        }
    }

    @Test
    fun `#run should return the validate error when no address is found`(): Unit = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
            )
        )

        coEvery {
            googleMapsService.getAddressByQuery(beneficiaryBatchItemTransportValid.addressPostalCode!!)
        } returns NotFoundException().failure()

        val (validation, beneficiaries) = addressValidateBeneficiariesUseCase
            .run(transport)
            .get()

        val expectedError = BeneficiaryBatchValidationError(
            index = 1,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "addressPostalCode",
                    message = "CEP inválido.",
                )
            )
        )

        assert(validation.success.isEmpty())
        assert(validation.errors.size == 1)
        assert(validation.errors[0] == expectedError)
        assert(beneficiaries.isEmpty())
    }
}
