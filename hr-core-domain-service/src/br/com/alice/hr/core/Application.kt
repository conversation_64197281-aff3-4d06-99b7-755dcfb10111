package br.com.alice.hr.core


import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.data.layer.HR_CORE_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.hr.core.ioc.ClientsModule
import br.com.alice.hr.core.ioc.ControllersModule
import br.com.alice.hr.core.ioc.DataLayerServiceModule
import br.com.alice.hr.core.ioc.ServiceModule
import br.com.alice.hr.core.routes.backfillRoutes
import br.com.alice.hr.core.routes.kafkaRoutes
import br.com.alice.hr.core.routes.recurrentRoutes
import br.com.alice.moneyin.ioc.MoneyInClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import io.ktor.server.application.Application
import io.ktor.server.routing.routing
import org.koin.core.module.Module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {
    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        DataLayerServiceModule,
        BusinessDomainClientModule,
        HealthLogicDomainClientModule,
        KafkaProducerModule,
        ClientsModule,
        ServiceModule,
        ControllersModule,
        PersonDomainClientModule,
        MoneyInClientModule,
        ProductDomainClientModule,
        GoogleMapsModule,
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
    startRoutesSync: Boolean = true
) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()
        featureConfigBootstrap(
            FeatureNamespace.BUSINESS,
            FeatureNamespace.HR_CORE
        )

        routing {
            application.attributes.put(PolicyRootServiceKey, HR_CORE_DOMAIN_ROOT_SERVICE_NAME)
            recurrentRoutes()
            backfillRoutes()
        }

        <EMAIL>(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

    }
}

