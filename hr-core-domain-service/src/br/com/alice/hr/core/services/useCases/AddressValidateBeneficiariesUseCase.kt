package br.com.alice.hr.core.services.useCases

import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.googlemaps.services.Address
import br.com.alice.common.googlemaps.services.GoogleMapsService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class AddressValidateBeneficiariesUseCase(
    private val googleMapsService: GoogleMapsService,
): Spannable {
    suspend fun run(beneficiaryBatch: BeneficiaryBatchTransport): Result<Pair<BeneficiaryBatchValidation, List<BeneficiaryBatchItemTransport>>, Throwable> = span("AddressValidateBeneficiaries") {
        if (beneficiaryBatch.items.isEmpty()) {
            return@span (BeneficiaryBatchValidation() to emptyList<BeneficiaryBatchItemTransport>()).success()
        }

        val errors = mutableListOf<BeneficiaryBatchValidationError>()
        val success = mutableListOf<Int>()
        val beneficiaries = mutableListOf<BeneficiaryBatchItemTransport>()

        beneficiaryBatch.items.forEach { beneficiary ->
            googleMapsService.getAddressByQuery(beneficiary.addressPostalCode!!)
                .getOrNullIfNotFound()
                .let { address ->
                    if (address == null) {
                        errors.add(
                            BeneficiaryBatchValidationError(
                                index = beneficiary.index,
                                error = listOf(
                                    BeneficiaryBatchValidationErrorItem(
                                        field = "addressPostalCode",
                                        message = "CEP inválido."
                                    )
                                )
                            )
                        )
                        return@let
                    }

                    logger.info(
                        "AddressValidateBeneficiariesUseCase::run",
                        "uploadId" to beneficiaryBatch.uploadId,
                        "beneficiaryIndex" to beneficiary.index,
                        "beneficiaryPostalCode" to beneficiary.addressPostalCode,
                        "beneficiaryName" to beneficiary.fullName,
                        "addressFromGoogleStreet" to address.street,
                        "addressFromGoogleCity" to address.city,
                        "addressFromGoogleState" to address.state,
                        "addressFromGoogleNeighborhood" to address.neighbourhood,
                        "addressFromGoogleNumber" to address.number,
                        "addressFromGooglePostalCode" to address.postalCode,
                    )

                    if (validateAddressExist(address, beneficiary)) {
                        success.add(beneficiary.index)
                        beneficiaries.add(
                            beneficiary.copy(
                                addressStreet = address.street,
                                addressCity = address.city,
                                addressState = address.state,
                                addressNeighborhood = address.neighbourhood,
                                addressNumber = address.number,
                            )
                        )
                    } else {
                        errors.add(
                            BeneficiaryBatchValidationError(
                                index = beneficiary.index,
                                error = listOf(
                                    BeneficiaryBatchValidationErrorItem(
                                        field = "addressPostalCode",
                                        message = "CEP inválido."
                                    )
                                )
                            )
                        )
                    }
                }
        }

        return@span (
            BeneficiaryBatchValidation(
                success = success,
                errors = errors,
            ) to beneficiaries
        ).success()
    }

    private fun validateAddressExist(
        address: Address,
        beneficiary: BeneficiaryBatchItemTransport,
    ) = address.postalCode.onlyNumbers() == beneficiary.addressPostalCode && (
            address.street.isNotNullOrBlank() ||
            address.city.isNotNullOrBlank() ||
            address.state.isNotNullOrBlank() ||
            address.neighbourhood.isNotNullOrBlank()
        )
}
