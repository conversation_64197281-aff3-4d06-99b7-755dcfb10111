package br.com.alice.hr.core.services.useCases

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.logging.logger
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.HrMemberUploadTracking
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus
import br.com.alice.data.layer.services.HrMemberUploadTrackingDataService
import br.com.alice.hr.core.events.HrCreateBeneficiaryBatchEvent
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.success
import java.util.UUID

class ProcessBeneficiaryBatchUseCase(
    private val hrMemberUploadTrackingDataService: HrMemberUploadTrackingDataService,
    private val kafkaProducerService: KafkaProducerService
) : Spannable {

    suspend fun run(
        uploadId: UUID,
        companyId: UUID,
        companyStaffId: UUID,
        batchTransport: BeneficiaryBatchTransport,
        validationResult: BeneficiaryBatchValidation
    ): Result<Boolean, Throwable> = span("ProcessBeneficiaryBatch") {
        logger.info("Processing beneficiary batch with ${validationResult.success.size} successful items",
            "uploadId" to uploadId,
            "companyId" to companyId,
            "companyStaffId" to companyStaffId,
            "successCount" to validationResult.success.size,
        )

        mapBeneficiariesByIndex(uploadId, companyId, batchTransport).flatMap { beneficiariesByIndex ->
            processSuccessfulBeneficiaries(
                uploadId = uploadId,
                companyId = companyId,
                companyStaffId = companyStaffId,
                beneficiariesByIndex = beneficiariesByIndex,
                validationResult = validationResult
            ).flatMap {
                logger.info("Successfully processed ${validationResult.success.size} beneficiaries for tracking",
                    "uploadId" to uploadId,
                    "companyId" to companyId,
                    "companyStaffId" to companyStaffId,
                    "successCount" to validationResult.success.size
                )

                true.success()
            }
        }
    }

    private suspend fun mapBeneficiariesByIndex(uploadId: UUID, companyId: UUID, originalBatch: BeneficiaryBatchTransport) =
        span("MapBeneficiariesByIndex") {
            originalBatch.items.associateBy {
                if (it.nationalId == null) {
                    return@span InvalidArgumentException(
                        message = "National ID cannot be null for beneficiary at index ${it.index}, uploadId $uploadId, companyId $companyId"
                    ).failure()
                }
                it.index
            }.success()
        }

    private suspend fun processSuccessfulBeneficiaries(
        uploadId: UUID,
        companyId: UUID,
        companyStaffId: UUID,
        beneficiariesByIndex: Map<Int, BeneficiaryBatchItemTransport>,
        validationResult: BeneficiaryBatchValidation
    ): Result<Boolean, Throwable> = span("processSuccessfulBeneficiaries") {
        try {
            validationResult.success.chunked(50).forEach { beneficiariesSuccessIndexes ->
                produceBeneficiaryBatchEvents(uploadId, companyId, beneficiariesSuccessIndexes, beneficiariesByIndex) 

                insertChunkedBeneficiariesTracking(
                    uploadId = uploadId,
                    companyId = companyId,
                    companyStaffId = companyStaffId,
                    beneficiariesByIndex = beneficiariesByIndex,
                    beneficiariesSuccessIndexes = beneficiariesSuccessIndexes
                )
            }

            true.success()
        } catch (e: Exception) {
            logger.error("Error processing successful beneficiaries",
                "uploadId" to uploadId,
                "companyId" to companyId,
                "error" to e.message
            )

            e.failure()
        }
    }

    private suspend fun insertChunkedBeneficiariesTracking(
        uploadId: UUID,
        companyId: UUID,
        companyStaffId: UUID,
        beneficiariesByIndex: Map<Int, BeneficiaryBatchItemTransport>,
        beneficiariesSuccessIndexes: List<Int>
    ): Result<Boolean, Throwable> = span("InsertChunkedBeneficiariesTracking") {
        val trackingModels = buildTrackingModels(
            uploadId = uploadId,
            companyId = companyId,
            companyStaffId = companyStaffId,
            beneficiariesByIndex = beneficiariesByIndex,
            beneficiariesSuccessIndexes = beneficiariesSuccessIndexes
        )

        hrMemberUploadTrackingDataService.addList(trackingModels)
        .flatMap {
            true.success()
        }
        .flatMapError {
            logger.error("Error inserting chunked tracking for beneficiaries",
                "size" to trackingModels.size,
                "companyId" to companyId,
                "companyStaffId" to companyStaffId,
                "uploadId" to uploadId
            )

            throw it
        }
    }

    private fun buildTrackingModels(
        uploadId: UUID,
        companyId: UUID,
        companyStaffId: UUID,
        beneficiariesByIndex: Map<Int, BeneficiaryBatchItemTransport>,
        beneficiariesSuccessIndexes: List<Int>
    ): List<HrMemberUploadTracking> =
        beneficiariesSuccessIndexes.map { beneficiaryIndex ->
            val beneficiary = beneficiariesByIndex[beneficiaryIndex]

            HrMemberUploadTracking(
                uploadId = uploadId,
                companyId = companyId,
                companyStaffId = companyStaffId,
                status = HrMemberUploadTrackingStatus.QUEUED,
                memberNationalId = beneficiary!!.nationalId!!,
                errors = null,
                reportNotifiedAt = null
            ).sanitize()
        }

    private suspend fun produceBeneficiaryBatchEvents(uploadId: UUID, companyId: UUID, beneficiariesSuccessIndexes: List<Int>, beneficiariesByIndex: Map<Int, BeneficiaryBatchItemTransport>) = span("ProduceBeneficiaryBatchEvents") {
        beneficiariesSuccessIndexes.forEach { beneficiaryIndex ->
            val beneficiary = beneficiariesByIndex[beneficiaryIndex]

            kafkaProducerService.produce(
                HrCreateBeneficiaryBatchEvent(
                    uploadId = uploadId,
                    companyId = companyId,
                    beneficiaryItem = beneficiary!!
                )
            )
        }
    }
}
