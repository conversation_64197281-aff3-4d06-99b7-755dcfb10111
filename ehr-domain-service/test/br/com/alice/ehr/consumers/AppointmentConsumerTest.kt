package br.com.alice.ehr.consumers

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.AppointmentCreatedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildCounterReferral
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentEventDetail
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentType.ANNOTATION
import br.com.alice.data.layer.models.AppointmentType.ANNOTATION_HEALTH_COMMUNITY
import br.com.alice.data.layer.models.AppointmentType.COUNTER_REFERRAL
import br.com.alice.data.layer.models.AppointmentType.STATEMENT_OF_HEALTH
import br.com.alice.data.layer.models.AttendanceType
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.CaseSeverity
import br.com.alice.data.layer.models.CounterReferralGenericTask
import br.com.alice.data.layer.models.CounterReferralTypeOfService
import br.com.alice.common.Disease
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.data.layer.models.AppointmentType.FREE_TEXT
import br.com.alice.data.layer.models.ConsolidatedRewardsClinicalRecords
import br.com.alice.data.layer.models.ConsolidatedRewardsType
import br.com.alice.data.layer.models.DiseaseDetails
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.NotOccurredReason
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.ehr.converters.toModel
import br.com.alice.ehr.services.internal.CounterReferralInternalService
import br.com.alice.ehr.services.internal.consolidated_rewards.ConsolidatedRewardsService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import java.time.LocalDate

class AppointmentConsumerTest : ConsumerTest() {

    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val staffService: StaffService = mockk()

    private val counterReferralService: CounterReferralService = mockk()
    private val appointmentService: AppointmentService = mockk()
    private val counterReferralInternalService: CounterReferralInternalService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val consolidatedRewardsService: ConsolidatedRewardsService = mockk()

    private val consumer = AppointmentConsumer(
        personClinicalAccountService,
        staffService,
        counterReferralService,
        counterReferralInternalService,
        appointmentService,
        healthProfessionalService,
        consolidatedRewardsService
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        personClinicalAccountService,
        staffService
    )

    private val staff = TestModelFactory.buildStaff(role = Role.PHYSICAL_EDUCATOR)
    private val appointment = TestModelFactory.buildAppointment(
        staffId = staff.id,
        type = COUNTER_REFERRAL
    )
    private val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount()
    private val event = AppointmentCreatedEvent(appointment)

    @Test
    fun `#addStaffOnPersonClinicalAccount add Staff on multi team`() = runBlocking {

        coEvery {
            personClinicalAccountService.addStaffOnMultiTeam(any(), any())
        } returns personClinicalAccount.success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        val result = consumer.addStaffOnPersonClinicalAccount(event)
        assertThat(result).isSuccessWithData(personClinicalAccount)

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { personClinicalAccountService.addStaffOnMultiTeam(any(), any()) }
    }

    @Test
    fun `#addStaffOnPersonClinicalAccount returns true and not call pca when Staff not is multi`() = runBlocking {
        coEvery { staffService.get(staff.id) } returns staff.copy(role = Role.ON_SITE_PHYSICIAN).success()

        val result = consumer.addStaffOnPersonClinicalAccount(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffService.get(any()) }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#addStaffOnPersonClinicalAccount discard event if appointment type is ANNOTATION`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(type = ANNOTATION)
        val event = AppointmentCreatedEvent(appointment)

        val result = consumer.addStaffOnPersonClinicalAccount(event)
        assertThat(result).isSuccessWithData(true)

        coVerify { staffService wasNot called }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#addStaffOnPersonClinicalAccount discard event if appointment type is ANNOTATION_HEALTH_COMMUNITY`() =
        runBlocking {
            val appointment = TestModelFactory.buildAppointment(type = ANNOTATION_HEALTH_COMMUNITY)
            val event = AppointmentCreatedEvent(appointment)

            val result = consumer.addStaffOnPersonClinicalAccount(event)
            assertThat(result).isSuccessWithData(true)

            coVerify { staffService wasNot called }
            coVerify { personClinicalAccountService wasNot called }
        }

    @Test
    fun `#addStaffOnPersonClinicalAccount discard event if appointment type is STATEMENT_OF_HEALTH`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(type = STATEMENT_OF_HEALTH)
        val event = AppointmentCreatedEvent(appointment)

        val result = consumer.addStaffOnPersonClinicalAccount(event)
        assertThat(result).isSuccessWithData(true)

        coVerify { staffService wasNot called }
        coVerify { personClinicalAccountService wasNot called }
    }

    @Test
    fun `#addStaffOnPersonClinicalAccount returns true when not fount PersonClinicalAccount`() = runBlocking {
        coEvery { staffService.get(staff.id) } returns staff.success()
        coEvery { personClinicalAccountService.addStaffOnMultiTeam(any(), any()) } returns NotFoundException().failure()

        val result = consumer.addStaffOnPersonClinicalAccount(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { personClinicalAccountService.addStaffOnMultiTeam(any(), any()) }
    }

    @Test
    fun `#addCounterReferralByAppointment should add CounterReferral`() = runBlocking {
        val referralId = RangeUUID.generate()
        val appointment = TestModelFactory.buildAppointment(
            type = COUNTER_REFERRAL,
            description = "test CR",
            startedAt = appointment.createdAt,
            event = AppointmentEventDetail(
                referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                referenceModelId = referralId.toString(),
                name = "Consulta com fulano"
            ),
            caseRecordDetails = listOf(
                CaseRecordDetails(
                    caseId = null,
                    description = DiseaseDetails(
                        id = RangeUUID.generate().toString(),
                        type = Disease.Type.CID_10,
                        value = "A10",
                        description = null
                    ),
                    severity = CaseSeverity.DECOMPENSATED,
                )
            )
        )

        val counterReferral = buildCounterReferral(
            personId = appointment.personId,
            referralId = referralId,
            appointmentDate = appointment.startedAt?.toLocalDate()!!,
            typeOfService = CounterReferralTypeOfService.OUTPATIENT_PROCEDURE,
            attendanceType = AttendanceType.CLINIC,
            diagnosticHypothesis = "",
            clinicalEvaluation = appointment.description!!,
            healthcareTeamOrientation = "",
            testRequests = listOf(
                CounterReferralGenericTask(
                    code = "40101010",
                    caseRecordValues = emptyList()
                )
            ),
            referrals = listOf(
                CounterReferralGenericTask(
                    description = "Cardiologia",
                    caseRecordValues = emptyList()
                )
            ),
            procedures = listOf(
                CounterReferralGenericTask(
                    code = "40101010",
                    description = "Exame Ecg",
                    caseRecordValues = listOf("A10")
                )
            ),
            medicines = listOf(
                CounterReferralGenericTask(
                    description = "Dipirona",
                    caseRecordValues = emptyList()
                )
            ),
            appointmentOccurred = true,
            isEligible = true,
            followUpDate = appointment.startedAt!!.plusDays(31).toLocalDate(),
            followUpDays = 31,
            appointmentId = appointment.id,
        )

        val event = AppointmentCompletedEvent(appointment)

        coEvery { counterReferralService.getByAppointmentId(appointment.id) } returns NotFoundException().failure()
        coEvery { counterReferralInternalService.buildCounterReferral(appointment) } returns counterReferral.success()

        coEvery {
            counterReferralService.create(counterReferral)
        } returns counterReferral.success()

        val result = consumer.addCounterReferralByAppointment(event)
        assertThat(result).isSuccessWithData(counterReferral)

        coVerifyOnce { counterReferralService.create(any()) }
        coVerifyOnce { counterReferralService.getByAppointmentId(any()) }
    }

    @Test
    fun `#addCounterReferralByAppointmentDeleted should add counter referral when appointment no show`() = runBlocking {
        val referralId = UUID.randomUUID()
        val appointment = TestModelFactory.buildAppointment(
            type = COUNTER_REFERRAL,
            description = "test CR",
            startedAt = appointment.createdAt,
            event = AppointmentEventDetail(
                referenceModel = AppointmentEventReferenceModel.HEALTH_PLAN_TASK,
                referenceModelId = referralId.toString(),
                name = "Consulta com fulano"
            ),
            discardedType = AppointmentDiscardedType.NO_SHOW,
        )

        val counterReferral = buildCounterReferral(
            personId = appointment.personId,
            appointmentId = appointment.id,
            appointmentDate = appointment.startedAt?.toLocalDate()!!,
            appointmentOccurred = false,
            isEligible = true,
            referralId = referralId,
            notOccurredReason = NotOccurredReason.NO_SHOW,
            healthcareTeamOrientation = ""
        )

        val event = DraftAppointmentDeletedEvent(
            appointmentId = appointment.id,
            discardedType = appointment.discardedType!!,
            description = appointment.content,
            personId = appointment.personId,
            type = appointment.type,
            staffId = appointment.staffId,
            createdAt = appointment.createdAt,
            appointmentEvent = appointment.event
        )

        coEvery { counterReferralService.getByAppointmentId(appointment.id) } returns NotFoundException().failure()
        coEvery { appointmentService.get(appointment.id) } returns appointment.success()
        coEvery { counterReferralInternalService.buildCounterReferralNoShow(appointment) } returns counterReferral.success()

        coEvery {
            counterReferralService.create(counterReferral)
        } returns counterReferral.success()

        val result = consumer.addCounterReferralByAppointmentDeleted(event)
        assertThat(result).isSuccessWithData(counterReferral)

        coVerifyOnce { counterReferralService.getByAppointmentId(any()) }
        coVerifyOnce { appointmentService.get(any()) }
        coVerifyOnce { counterReferralService.create(any()) }
    }

    @Test
    fun `processAppointment should return success`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            key = "send_process_appointment",
            value = true
        ) {
            val appointment = appointment.copy(appointmentDate = LocalDate.of(2000, 1, 1))
            val healthProfessional = TestModelFactory.buildHealthProfessional()
            val consolidatedRewards = TestModelFactory.buildConsolidatedRewards()
            val createdAtZone = appointment.completedAt!!.toSaoPauloTimeZone().toLocalDate()
            val event = AppointmentCompletedEvent(appointment)

            coEvery {
                healthProfessionalService.findByStaffId(appointment.staffId)
            } returns healthProfessional.success()

            coEvery {
                consolidatedRewardsService.getOrCreate(
                    healthProfessional.id,
                    createdAtZone
                )
            } returns consolidatedRewards.toModel().success()
            val toUpdate = consolidatedRewards.copy(
                clinicalRecords = consolidatedRewards.clinicalRecords + ConsolidatedRewardsClinicalRecords(
                    id = appointment.id,
                    type = ConsolidatedRewardsType.APPOINTMENT,
                    createdAt = createdAtZone,
                    appointmentDate = appointment.appointmentDate!!
                )
            ).toModel()
            coEvery {
                consolidatedRewardsService.update(toUpdate)
            } returns toUpdate.success()

            val result = consumer.processAppointment(event)
            assertThat(result).isSuccessWithData(toUpdate)

            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { consolidatedRewardsService.getOrCreate(any(), any()) }
            coVerifyOnce { consolidatedRewardsService.update(any()) }
        }
    }

    @Test
    fun `processAppointment should return if appointment type is not COUNTER_REFERRAL`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            key = "send_process_appointment",
            value = true
        ) {
            val appointment = appointment.copy(
                appointmentDate = LocalDate.of(2000, 1, 1),
                type = FREE_TEXT
            )
            val event = AppointmentCompletedEvent(appointment)

            val result = consumer.processAppointment(event)
            assertThat(result).isSuccessWithData(false)

            coVerify { healthProfessionalService wasNot called }
            coVerify { consolidatedRewardsService wasNot called }
        }
    }

    @Test
    fun `processAppointment should return if appointment discardedType is not null`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            key = "send_process_appointment",
            value = true
        ) {
            val appointment = appointment.copy(
                appointmentDate = LocalDate.of(2000, 1, 1),
                discardedType = AppointmentDiscardedType.NO_SHOW
            )
            val event = AppointmentCompletedEvent(appointment)

            val result = consumer.processAppointment(event)
            assertThat(result).isSuccessWithData(false)

            coVerify { healthProfessionalService wasNot called }
            coVerify { consolidatedRewardsService wasNot called }
        }
    }
}
