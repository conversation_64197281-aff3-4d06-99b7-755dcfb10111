package br.com.alice.ehr.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TertiaryIntentionCoordinated
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationResponsible
import br.com.alice.data.layer.models.TertiaryIntentionIntensiveCare
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.data.layer.models.TertiaryIntentionType.TIT_EMERGENCY
import br.com.alice.data.layer.services.TertiaryIntentionTouchPointDataService
import br.com.alice.ehr.event.TertiaryIntentionTouchPointCreatedEvent
import br.com.alice.ehr.event.TertiaryIntentionTouchPointUpdatedEvent
import br.com.alice.ehr.model.TertiaryIntentionTouchPointFilter
import br.com.alice.ehr.services.TertiaryIntentionTouchPointServiceImpl.Companion.CURRENT_ATTENDANCES_STATUS
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class TertiaryIntentionTouchPointServiceImplTest {
    private val dataService: TertiaryIntentionTouchPointDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = TertiaryIntentionTouchPointServiceImpl(dataService, kafkaProducerService)

    private val tertiaryIntention = TertiaryIntentionTouchPoint(
        personId = PersonId(),
        staffId = RangeUUID.generate(),
        providerUnitId = RangeUUID.generate(),
        type = TIT_EMERGENCY,
        coordinated = TertiaryIntentionCoordinated.TIR_EXPERIENCE,
    )

    @Test
    fun `#get should get TertiaryIntention by id`() = runBlocking {
        coEvery { dataService.get(tertiaryIntention.id) } returns tertiaryIntention.success()
        val queryResult = service.get(tertiaryIntention.id)

        assertThat(queryResult).isSuccessWithData(tertiaryIntention)
        coVerify(exactly = 1) { dataService.get(tertiaryIntention.id) }
    }

    @Test
    fun `#getByIdAndPersonId should get TertiaryIntention by id and personId`() = runBlocking {
        coEvery {
            dataService.findOne(
                queryEq {
                    where {
                        this.id.eq(tertiaryIntention.id) and this.personId.eq(tertiaryIntention.personId)
                    }
                }
            )
        } returns tertiaryIntention.success()

        val queryResult = service.getByIdAndPersonId(tertiaryIntention.id, tertiaryIntention.personId)

        assertThat(queryResult).isSuccessWithData(tertiaryIntention)
        coVerifyOnce { dataService.findOne(any()) }
    }

    @Test
    fun `#findByTotvsGuia should get TertiaryIntention by totvs guia id`() = runBlocking {
        val tertiaryIntention = tertiaryIntention.copy(totvsGuiaId = RangeUUID.generate())
        coEvery { dataService.findOne(queryEq { where { this.totvsGuiaId.eq(tertiaryIntention.totvsGuiaId!!) } }) } returns tertiaryIntention.success()

        val queryResult = service.findByTotvsGuia(tertiaryIntention.totvsGuiaId!!)

        assertThat(queryResult).isSuccessWithData(tertiaryIntention)
        coVerifyOnce { dataService.findOne(any()) }
    }

    @Test
    fun `#findByPersonId should get TertiaryIntention by personId`() = runBlocking {
        coEvery {
            dataService.find(
                queryEq {
                    where { this.personId.eq(tertiaryIntention.personId).and(this.type.eq(TIT_EMERGENCY)) }
                }
            )
        } returns listOf(tertiaryIntention).success()

        val event = TertiaryIntentionTouchPointCreatedEvent(tertiaryIntention)
        coEvery { kafkaProducerService.produce(event) } returns mockk()

        val queryResult = service.findByPersonIdAndType(tertiaryIntention.personId, TIT_EMERGENCY)

        assertThat(queryResult).isSuccessWithData(listOf(tertiaryIntention))
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `#create should create TertiaryIntention entity`() = runBlocking {
        coEvery { dataService.add(tertiaryIntention) } returns tertiaryIntention.success()
        coEvery { kafkaProducerService.produce(any()) } returns mockk()
        val queryResult = service.create(tertiaryIntention)

        assertThat(queryResult).isSuccessWithData(tertiaryIntention)
        coVerify(exactly = 1) { dataService.add(tertiaryIntention) }
    }

    @Test
    fun `#create should return validation error`() = runBlocking {
        val invalidTertiary = tertiaryIntention.copy(
            startedAt = LocalDateTime.of(2024, 10, 10, 12, 0),
            intensiveCare = listOf(
                TertiaryIntentionIntensiveCare(
                    date = LocalDate.of(2024, 9, 10),
                    requiresMechanicalVentilation = false
                )
            )
        )

        val result = service.create(invalidTertiary)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)
    }

    @Test
    fun `#update should update TertiaryIntention entity`() = runBlocking {
        val event = TertiaryIntentionTouchPointUpdatedEvent(tertiaryIntention)
        coEvery { dataService.update(tertiaryIntention) } returns tertiaryIntention.success()
        coEvery { kafkaProducerService.produce(event) } returns mockk()
        val queryResult = service.update(tertiaryIntention)

        assertThat(queryResult).isSuccessWithData(tertiaryIntention)
        coVerifyOnce { dataService.update(tertiaryIntention) }
        coVerifyOnce { kafkaProducerService.produce(event) }
    }

    @Test
    fun `#update should return validation error`() {
        val invalidTertiary = tertiaryIntention.copy(
            startedAt = LocalDateTime.of(2024, 10, 10, 12, 0),
            intensiveCare = listOf(
                TertiaryIntentionIntensiveCare(
                    date = LocalDate.of(2024, 9, 10),
                    requiresMechanicalVentilation = false
                )
            )
        )

        assertThrows<InvalidArgumentException> {
            runBlocking { service.update(invalidTertiary) }
        }
    }

    @Test
    fun `#findTertiaryIntentionByPeriod should return TertiaryIntention`() = runBlocking {
        val dataMock = LocalDateTime.now()
        val providerUnit = RangeUUID.generate()
        coEvery {
            dataService.findOne(queryEq {
                where {
                    this.personId.eq(tertiaryIntention.personId) and
                            this.providerUnitId.eq(providerUnit) and
                            this.type.eq(TIT_EMERGENCY) and
                            this.startedAt.greater(dataMock) and
                            this.startedAt.less(dataMock)
                }
            })
        } returns tertiaryIntention.success()

        val queryResult = service.findTertiaryIntentionByPeriod(
            tertiaryIntention.personId,
            providerUnit,
            TIT_EMERGENCY,
            dataMock,
            dataMock
        )

        assertThat(queryResult).isSuccessWithData(tertiaryIntention)
        coVerifyOnce { dataService.findOne(any()) }
    }

    @Test
    fun `#findTertiaryIntentionByPeriodAndTypes should return TertiaryIntention`() = runBlocking {
        val dataMock = LocalDateTime.now()
        val types = listOf(TIT_EMERGENCY, TertiaryIntentionType.TIT_SURGERY)
        coEvery {
            dataService.find(queryEq {
                where {
                    this.personId.eq(tertiaryIntention.personId) and
                            this.type.inList(types) and
                            this.startedAt.greater(dataMock) and
                            this.startedAt.less(dataMock)
                }
            })
        } returns listOf(tertiaryIntention).success()

        val queryResult = service.findTertiaryIntentionByPeriodAndTypes(
            tertiaryIntention.personId,
            types = types,
            dataMock,
            dataMock
        )

        assertThat(queryResult).isSuccessWithData(listOf(tertiaryIntention))
        coVerifyOnce { dataService.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findAllInCurrentAttendance should find all attendances in progress`() = runBlocking {
        val filters = TertiaryIntentionTouchPointFilter()
        val range = IntRange(0, 14)
        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.startedAt.isNotNull() and
                                this.endedAt.isNull() and
                                scope(
                                    this.type.eq(TertiaryIntentionType.TIT_HOSPITALIZATION) or
                                            scope(
                                                this.type.eq(TertiaryIntentionType.TIT_SURGERY) and
                                                        this.surgeryStatus.inList(CURRENT_ATTENDANCES_STATUS)
                                            )
                                )
                    }.orderBy { startedAt }
                        .sortOrder { desc }
                        .offset { range.first }
                        .limit { range.last }
                }
            )
        } returns listOf(tertiaryIntention).success()

        val result = service.findAllInCurrentAttendance(filters, range)

        assertThat(result).isSuccessWithData(listOf(tertiaryIntention))
        coVerifyOnce { dataService.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findAllInCurrentAttendance should find all attendances in progress with all filters`() = runBlocking {
        mockLocalDateTime {
            val person = TestModelFactory.buildPerson()
            val providerUnit = TestModelFactory.buildProviderUnit()
            val filters = TertiaryIntentionTouchPointFilter(
                personId = person.id,
                medicalSpecialtyName = "Cardiologia",
                type = TertiaryIntentionType.TIT_HOSPITALIZATION,
                providerUnits = listOf(providerUnit.id),
                sort = TertiaryIntentionTouchPointFilter.SortingOperation(
                    field = TertiaryIntentionTouchPointFilter.SortField.HAS_REAR,
                    order = TertiaryIntentionTouchPointFilter.SortDirection.DESC
                )
            )
            val range = IntRange(0, 14)

            coEvery {
                dataService.find(
                    queryEq {
                        where {
                            this.startedAt.isNotNull() and
                                    this.endedAt.isNull() and
                                    scope(
                                        this.type.eq(TertiaryIntentionType.TIT_HOSPITALIZATION) or
                                                scope(
                                                    this.type.eq(TertiaryIntentionType.TIT_SURGERY) and
                                                            this.surgeryStatus.inList(CURRENT_ATTENDANCES_STATUS)
                                                )
                                    ) and
                                    ((((this.type.eq(filters.type!!)) and
                                            this.personId.eq(filters.personId!!)) and
                                            this.hospitalizationSpecialty.eq(filters.medicalSpecialtyName!!)) and
                                            this.providerUnitId.inList(filters.providerUnits!!))
                        }.orderBy { hospitalizationResponsible }
                            .sortOrder { desc }
                            .offset { range.first }
                            .limit { range.last }
                    }
                )
            } returns listOf(tertiaryIntention).success()

            val result = service.findAllInCurrentAttendance(filters, range)

            assertThat(result).isSuccessWithData(listOf(tertiaryIntention))
            coVerifyOnce { dataService.find(any()) }
        }
    }

    @Test
    fun `#findByPersonIdAndBetweenDates should find all attendances by person between start and end time`() =
        runBlocking {
            val dataMock = LocalDateTime.now()
            coEvery {
                dataService.find(queryEq {
                    where {
                        this.personId.eq(tertiaryIntention.personId) and
                                this.startedAt.greaterEq(dataMock) and
                                this.startedAt.lessEq(dataMock)
                    }.orderBy { this.startedAt }.sortOrder { desc }
                })
            } returns listOf(tertiaryIntention).success()

            val result = service.findByPersonIdAndBetweenDates(
                tertiaryIntention.personId,
                dataMock,
                dataMock
            )

            assertThat(result).isSuccessWithData(listOf(tertiaryIntention))
            coVerifyOnce { dataService.find(any()) }
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#countAllInCurrent should count all attendances in progress with all filters`() = runBlocking {
        mockLocalDateTime {
            val person = TestModelFactory.buildPerson()
            val filters = TertiaryIntentionTouchPointFilter(
                personId = person.id,
                medicalSpecialtyName = "Cardiologia",
                type = TertiaryIntentionType.TIT_HOSPITALIZATION,
                stayInDays = 3,
                hasRear = true
            )

            coEvery {
                dataService.count(
                    queryEq {
                        where {
                            this.startedAt.isNotNull() and
                                    this.startedAt.lessEq(LocalDateTime.now().minusDays(3)) and
                                    this.endedAt.isNull() and
                                    scope(
                                        this.type.eq(TertiaryIntentionType.TIT_HOSPITALIZATION) or
                                                scope(
                                                    this.type.eq(TertiaryIntentionType.TIT_SURGERY) and
                                                            this.surgeryStatus.inList(CURRENT_ATTENDANCES_STATUS)
                                                )
                                    ) and
                                    (((this.type.eq(filters.type!!) and
                                            this.personId.eq(filters.personId!!)) and
                                            this.hospitalizationSpecialty.eq(filters.medicalSpecialtyName!!)) and
                                            this.hospitalizationResponsible.eq(
                                                TertiaryIntentionHospitalizationResponsible.ALICE_REAR
                                            ))
                        }
                    }
                )
            } returns 5.success()

            val result = service.countAllInCurrent(filters)

            assertThat(result).isSuccessWithData(5)
            coVerifyOnce { dataService.count(any()) }
        }
    }
}
