package br.com.alice.staff.event

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.Staff
import br.com.alice.staff.SERVICE_NAME

/**
 * Event triggered when a new anesthetist is created through the automation flow.
 * This event carries the anesthetist's staff information and notifies other systems
 * about the creation of a new anesthetist in the platform.
 *
 * @property staff The staff information of the created anesthetist
 */
data class AnesthetistCreatedEvent(
    private val staff: Staff,
) : NotificationEvent<AnesthetistCreatedEventPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = AnesthetistCreatedEventPayload(staff = staff)
) {
    companion object {
        const val name = "ehr-auto-anesthetist-created"
    }
}

/**
 * Payload containing the staff information for the AnesthetistCreatedEvent.
 *
 * @property staff The staff information of the created anesthetist
 */
data class AnesthetistCreatedEventPayload(val staff: Staff)
