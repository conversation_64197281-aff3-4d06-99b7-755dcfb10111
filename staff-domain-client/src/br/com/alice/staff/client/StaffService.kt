package br.com.alice.staff.client

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Staff
import br.com.alice.staff.models.StaffHpKey
import br.com.alice.staff.models.StaffValidation
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface StaffService : Service {
    override val namespace get() = "staff"
    override val serviceName get() = "staff"

    suspend fun findWithAnyRole(
        roles: List<Role>,
    ): Result<List<Staff>, Throwable>

    suspend fun findActivesWithAnyRole(
        roles: List<Role>,
    ): Result<List<Staff>, Throwable>

    suspend fun findByEmail(email: String): Result<Staff, Throwable>

    suspend fun findActiveByEmail(email: String): Result<Staff, Throwable>

    suspend fun findByEmailList(emails: List<String>): Result<List<Staff>, Throwable>

    suspend fun findByRange(range: IntRange): Result<List<Staff>, Throwable>

    suspend fun get(id: UUID): Result<Staff, Throwable>

    suspend fun getActive(id: UUID): Result<Staff, Throwable>

    suspend fun saveProfileImageUrl(
        id: UUID, imageUrl: String
    ): Result<Staff, Throwable>

    suspend fun update(
        model: Staff,
        healthProfessional: HealthProfessional? = null,
        contacts: List<Contact>? = null
    ): Result<Staff, Throwable>

    suspend fun add(
        model: Staff,
        healthProfessional: HealthProfessional? = null,
        contacts: List<Contact>? = null
    ): Result<Staff, Throwable>

    suspend fun addAndPublishAnesthetist(
        model: Staff,
        healthProfessional: HealthProfessional,
        contacts: List<Contact>? = null
    ): Result<Staff, Throwable>

    suspend fun count(): Result<Int, Throwable>

    suspend fun findByList(
        staffIds: List<UUID>,
    ): Result<List<Staff>, Throwable>

    suspend fun searchActiveStaff(
        namePrefix: String,
    ): Result<List<Staff>, Throwable>

    suspend fun findByNameWithRoleAndRange(
        name: String,
        roles: List<Role>,
        range: IntRange,
    ): Result<List<Staff>, Throwable>

    suspend fun findByRoleAndRange(
        role: Role,
        range: IntRange
    ): Result<List<Staff>, Throwable>

    suspend fun searchActivesByIdsNameAndRole(
        ids: List<UUID>? = null,
        name: String? = null,
        role: Role
    ): Result<List<Staff>, Throwable>

    suspend fun searchByNameAndRoleWithRange(
        ids: List<UUID>? = null,
        range: IntRange,
        roles: List<Role>? = null,
        namePrefix: String? = null,
        active: Boolean? = null,
        types: List<StaffType>? = null,
    ): Result<List<Staff>, Throwable>

    suspend fun countByNameAndRoleWithRange(
        ids: List<UUID>? = null,
        roles: List<Role>? = null,
        namePrefix: String? = null,
        active: Boolean? = null,
        types: List<StaffType>? = null,
    ): Result<Int, Throwable>

    suspend fun countByRole(
        roles: List<Role>
    ): Result<Int, Throwable>

    suspend fun getActiveWithRole(
        id: UUID,
        roles: List<Role>,
    ): Result<Staff, Throwable>

    suspend fun findActivesById(
        ids: List<UUID>,
    ): Result<List<Staff>, Throwable>

    suspend fun findBy(filters: StaffFilters): Result<List<Staff>, Throwable>

    suspend fun countBy(filters: StaffFilters): Result<Int, Throwable>

    suspend fun countActiveByToken(term: String, active: Boolean = true): Result<Int, Throwable>

    suspend fun setOnCall(staffId: UUID, onCall: Boolean): Result<Staff, Throwable>

    suspend fun findByTokenAndRange(term: String, range: IntRange): Result<List<Staff>, Throwable>

    suspend fun validHealthProfessional(key: StaffHpKey): Result<StaffValidation, Throwable>

}

data class StaffFilters(
    val roles: List<Role>? = null,
    val emails: List<String>? = null,
    val searchTerm: String? = null,
    val ids: List<UUID>? = null,
    val namePrefix: String? = null,
    val active: Boolean? = null,
    val types: List<StaffType>? = null,
    val range: IntRange? = null
) {
    fun isValidate(): Boolean =
        roles.isNotNullOrEmpty() ||
                emails.isNotNullOrEmpty() ||
                searchTerm != null ||
                ids.isNotNullOrEmpty() ||
                namePrefix != null ||
                active != null ||
                types.isNotNullOrEmpty() ||
                range != null

}
