package br.com.alice.api.backoffice.controllers.execIndicator

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.mappers.execIndicator.ResourceSpecialistPricingOutputMapper.toPaginatedResponse
import br.com.alice.api.backoffice.transfers.execIndicator.DownloadCSVRequest
import br.com.alice.api.backoffice.transfers.execIndicator.EffectiveDatesResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CSVPricingUpdateError
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.exec.indicator.client.CSVGenerationResponse
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.client.ResourceSpecialtyPricingUpdateService
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.exec.indicator.models.ProcessingResourceBundleSpecialtyPricingUpdateResponse
import br.com.alice.filevault.models.FileType
import io.ktor.http.HttpMethod
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import io.ktor.http.Parameters
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.io.File
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test


class ResourceBundleSpecialistPricingControllerTest : ControllerTestHelper() {
    private val resourceSpecialistCSVService: ResourceSpecialtyPricingUpdateService = mockk()

    private val controller = ResourceBundleSpecialistPricingController(
        resourceSpecialistCSVService,
    )

    private val csvFile = File(javaClass.classLoader.getResource("execIndicator/test.csv")!!.path)

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { controller }
    }

    @Test
    fun `effectiveDates should return current and next month dates less than 5 work days`() = runBlocking {
        val localDate = LocalDate.of(2023, 10, 8)

        val expected = EffectiveDatesResponse(
            dates = listOf("01/09/2023", "01/10/2023")
        )

        mockLocalDate(localDate) {
            authenticatedAs(idToken, staff) {
                get("/resourceBundleSpecialtyPricing/effectiveDates") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `effectiveDates should return current and next month dates less than 5 work days `() = runBlocking {
        val localDate = LocalDate.of(2025, 5, 8)

        val expected = EffectiveDatesResponse(
            dates = listOf("01/05/2025", "01/06/2025")
        )

        mockLocalDate(localDate) {
            authenticatedAs(idToken, staff) {
                get("/resourceBundleSpecialtyPricing/effectiveDates") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expected)
                }
            }
        }
    }

    @Test
    fun `downloadCSV should return csv file`() = runBlocking {
        val resourceBundleSpecialtyIds = listOf(RangeUUID.generate())

        val csvResponse = CSVGenerationResponse(
            fileName = "arquivo-teste",
            bytes = csvFile.readBytes()
        )

        val requestBody = DownloadCSVRequest(
            resourceBundleSpecialtyIds = resourceBundleSpecialtyIds
        )

        coEvery {
            resourceSpecialistCSVService.generate(resourceBundleSpecialtyIds)
        } returns csvResponse

        authenticatedAs(idToken, staff) {
            post("/resourceBundleSpecialtyPricing/csv", body = requestBody) { response ->
                ResponseAssert.assertThat(response).isOK()
                ResponseAssert.assertThat(response).containsHeaderWithValue(
                    expectedHeader = "Content-Disposition",
                    expectedHeaderValue = "attachment; filename=${csvResponse.fileName}.csv"
                )
            }
        }
    }

    @Test
    fun `getProcessingResourceBundleSpecialtyPricingUpdate should return processing update`() = runBlocking {
        val expectedResponse = ProcessingResourceBundleSpecialtyPricingUpdateResponse(
            isProcessing = true,
            resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()
        )

        coEvery { resourceSpecialistCSVService.getProcessingResourceBundleSpecialtyPricingUpdate() } returns expectedResponse

        authenticatedAs(idToken, staff) {
            get("/resourceBundleSpecialtyPricing/processing") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `downloadFailedLinesFile should return csv file`() = runBlocking {
        val resourceBundleSpecialtyPricingUpdateId = RangeUUID.generate()

        val csvResponse = CSVGenerationResponse(
            fileName = "arquivo-teste",
            bytes = csvFile.readBytes()
        )

        coEvery {
            resourceSpecialistCSVService.generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId)
        } returns csvResponse

        authenticatedAs(idToken, staff) {
            get("/resourceBundleSpecialtyPricingUpdate/$resourceBundleSpecialtyPricingUpdateId/failedLinesFile") { response ->
                ResponseAssert.assertThat(response).isOK()
                ResponseAssert.assertThat(response).containsHeaderWithValue(
                    expectedHeader = "Content-Disposition",
                    expectedHeaderValue = "attachment; filename=${csvResponse.fileName}.csv"
                )
            }
        }
    }

    @Test
    fun `getPricingUpdateHistory should return pricing update history`() = runBlocking {
        val startDate = LocalDate.of(2023, 10, 8)
        val endDate = LocalDate.of(2023, 10, 15)
        val status = ResourceBundleSpecialtyPricingUpdateStatus.PROCESSED
        val page = 1
        val pageSize = 10
        val range = IntRange(0, 9)

        val staff = TestModelFactory.buildStaff()

        val historyItem = ResourceBundleSpecialtyPricingUpdateHistoryItem(
            id = RangeUUID.generate(),
            fileName = "preços_2024-12-01",
            fileUrl = "https://example.com/file.csv",
            createdByStaff = staff,
            processingAt = LocalDateTime.now().minusMinutes(10),
            completedAt = LocalDateTime.now(),
            rowsCount = 100,
            failedRowsCount = 10,
            failedRowsErrors = listOf(
                CSVPricingUpdateError(
                    row = 1,
                    error = "Invalid price format"
                )
            ),
            parsingError = null,
            pricesBeginAt = LocalDate.of(2024, 12, 1),
            createdAt = LocalDateTime.now().minusMinutes(15)
        )

        val historyWithCount = ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
            count = 1,
            items = listOf(historyItem)
        )

        coEvery {
            resourceSpecialistCSVService.getPricingUpdateHistory(
                PricingUpdateHistoryFilters(
                    startDate = startDate,
                    endDate = endDate,
                    status = listOf(status)
                ),
                range
            )
        } returns historyWithCount

        val queryParams = Parameters.build {
            append("page", page.toString())
            append("pageSize", pageSize.toString())
            append("startDate", startDate.toString())
            append("endDate", endDate.toString())
            append("status", status.name)
        }

        val expected = historyWithCount.toPaginatedResponse(queryParams)

        authenticatedAs(idToken, staff) {
            get("/resourceBundleSpecialtyPricingUpdate?filter={startDate:\"$startDate\",endDate:\"$endDate\",status:[\"$status\"]}&page=$page&pageSize=$pageSize") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }
    }

    @Test
    fun `upload should upload file at file vault and save its reference on database`() = runBlocking {
        val fileName = "csv-example-correct.csv"
        val effectiveDate = controller.getEffectiveDaysCheckingWorkDays().first()
        val file = csvFile.readBytes()
        val requestBody = UploadPriceChangesRequest(
            fileName = csvFile.name,
            fileType = FileType.fromExtension(csvFile.extension)!!,
            content = file,
            pricesBeginAt = effectiveDate,
            staffId = staff.id
        )

        val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        coEvery {
            resourceSpecialistCSVService.uploadPriceChanges(match {
                it.pricesBeginAt == requestBody.pricesBeginAt &&
                        it.staffId == requestBody.staffId
            })
        } returns resourceBundleSpecialtyPricingUpdate

        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/resourceBundleSpecialtyPricing/upload",
                fileName = fileName,
                parameters = mapOf(
                    "effectiveDate" to effectiveDate.toString(),
                )
            ) { response ->
                ResponseAssert.assertThat(response).isOK()

                coVerifyOnce { resourceSpecialistCSVService.uploadPriceChanges(any()) }
            }
        }
    }

    @Test
    fun `upload should return bad request if there is a process already running`() = runBlocking {
        val fileName = "csv-example-correct.csv"
        val effectiveDate = controller.getEffectiveDaysCheckingWorkDays().first()

        val requestBody = UploadPriceChangesRequest(
            fileName = csvFile.name,
            fileType = FileType.fromExtension(csvFile.extension)!!,
            content = csvFile.readBytes(),
            pricesBeginAt = effectiveDate,
            staffId = staff.id
        )

        coEvery {
            resourceSpecialistCSVService.uploadPriceChanges(match {
                it.pricesBeginAt == requestBody.pricesBeginAt &&
                        it.staffId == requestBody.staffId
            })
        } returns InvalidArgumentException("")

        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/resourceBundleSpecialtyPricing/upload",
                fileName = fileName,
                parameters = mapOf(
                    "effectiveDate" to effectiveDate.toString(),
                )
            ) { response ->
                ResponseAssert.assertThat(response).isBadRequest()

                coVerifyOnce { resourceSpecialistCSVService.uploadPriceChanges(any()) }
            }
        }
    }

    @Test
    fun `upload should return bad request if effective date is invalid`() = runBlocking {
        val fileName = "csv-example-correct.csv"
        val effectiveDate = LocalDate.of(2023, 10, 15)

        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/resourceBundleSpecialtyPricing/upload",
                fileName = fileName,
                parameters = mapOf(
                    "effectiveDate" to effectiveDate.toString(),
                )
            ) { response ->
                ResponseAssert.assertThat(response).isBadRequest()

                coVerifyNone { resourceSpecialistCSVService.uploadPriceChanges(any()) }
            }
        }
    }
}
