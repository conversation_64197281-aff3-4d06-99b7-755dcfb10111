package br.com.alice.schedule.services

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.helpers.TestModelFactory.buildStaffSchedule
import br.com.alice.data.layer.models.AgeRatingType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeMemberRisk
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleOptionModel
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.CalendarProviderUnit
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.PrimaryAttentionType
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.services.AppointmentScheduleOptionModelDataService
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionFilters
import br.com.alice.schedule.converters.AppointmentScheduleOptionCreator
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.model.AppointmentScheduleEventTypeLocationAvailability
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import br.com.alice.schedule.model.EventTypeProviderUnits
import br.com.alice.schedule.model.events.AppointmentScheduleOptionCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleOptionUpdatedEvent
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Ignore
import kotlin.test.Test

class AppointmentScheduleOptionServiceImplTest {

    private val appointmentScheduleOptionModelDataService: AppointmentScheduleOptionModelDataService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val riskService: RiskService = mockk()
    private val eventTypeProviderUnitService: EventTypeProviderUnitServiceImpl = mockk()
    private val staffService: StaffService = mockk()
    private val personService: PersonService = mockk()
    private val staffScheduleService: StaffScheduleServiceImpl = mockk()
    private val cache: GenericCache = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val appointmentScheduleOptionService = AppointmentScheduleOptionServiceImpl(
        appointmentScheduleOptionModelDataService,
        healthcareTeamService,
        appointmentScheduleEventTypeService,
        riskService,
        eventTypeProviderUnitService,
        staffService,
        staffScheduleService,
        personService,
        cache,
        kafkaProducerService,
        providerUnitService
    )

    private val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.of(1990, 12, 12, 0, 0))
    private val specialist = TestModelFactory.buildHealthProfessional()
    private val specialty = TestModelFactory.buildMedicalSpecialty()
    private val staff = TestModelFactory.buildStaff(profileImageUrl = null)
    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
        isMultiProfessionalReferral = true,
        groupByType = AppointmentScheduleType.OTHER
    )
    private val providerUnits = listOf(
        TestModelFactory.buildProviderUnit()
    )
    private val providerUnitIds = providerUnits.map { it.id }
    private val appointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(
        specialistId = specialist.id,
        calendarUrl = "calendarUrl",
        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
        staffId = staff.id,
        calendarProviderUnits = providerUnits.map {
            CalendarProviderUnit(
                name = it.name,
                providerUnitId = it.id
            )
        }
    )
    private val appointmentScheduleEventTypes = listOf(
        appointmentScheduleEventType,
        TestModelFactory.buildAppointmentScheduleEventType(
            isMultiProfessionalReferral = true,
            status = Status.INACTIVE,
        ),
        TestModelFactory.buildAppointmentScheduleEventType(
            isMultiProfessionalReferral = true,
            showOnApp = false,
        ),
    )
    private val physicianStaffId = RangeUUID.generate()
    private val nurseStaffId = RangeUUID.generate()
    private val otherStaff = RangeUUID.generate()
    private val appointmentScheduleEventTypeLowRisk = TestModelFactory.buildAppointmentScheduleEventType(
        membersRisk = listOf(AppointmentScheduleEventTypeMemberRisk.LOW_RISK)
    )
    private val appointmentScheduleEventTypeHighRisk = TestModelFactory.buildAppointmentScheduleEventType(
        membersRisk = listOf(AppointmentScheduleEventTypeMemberRisk.HIGH_RISK)
    )
    private val appointmentScheduleEventTypeNoRisk = TestModelFactory.buildAppointmentScheduleEventType(
        membersRisk = listOf(AppointmentScheduleEventTypeMemberRisk.NO_RISK)
    )
    private val appointmentScheduleEventTypeAnyRisk = TestModelFactory.buildAppointmentScheduleEventType(
        membersRisk = emptyList()
    )
    private val appointmentScheduleEventTypeForChildren = appointmentScheduleEventTypeAnyRisk.copy(forChildren = true)
    private val eventTypesForChildren = listOf(appointmentScheduleEventTypeForChildren)
    private val appointmentScheduleOptionHighRisk = TestModelFactory.buildAppointmentScheduleOption(
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeHighRisk.id,
        staffId = physicianStaffId,
    )
    private val appointmentScheduleOptionLowRisk = TestModelFactory.buildAppointmentScheduleOption(
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeLowRisk.id,
        staffId = nurseStaffId,
    )
    private val appointmentScheduleOptionAnyRisk = TestModelFactory.buildAppointmentScheduleOption(
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeAnyRisk.id,
        staffId = nurseStaffId,
    )
    private val appointmentScheduleOptionNoRisk = TestModelFactory.buildAppointmentScheduleOption(
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeNoRisk.id,
        staffId = nurseStaffId,
    )
    private val risk = TestModelFactory.buildRisk(personId = person.id)
    private val lowRisk = TestModelFactory.buildRisk(personId = person.id, riskDescription = RiskDescription.LOW_RISK)
    private val highRisk = TestModelFactory.buildRisk(personId = person.id, riskDescription = RiskDescription.HIGH_RISK)
    private val healthCareTeam = HealthcareTeam(
        physicianStaffId = physicianStaffId,
        nurseStaffId = nurseStaffId
    )
    private val healthcareModelType = HealthcareModelType.V3
    private val typesOfOptionsThatAreVisibleToLeagueMembers = listOf(
        AppointmentScheduleType.TEST,
        AppointmentScheduleType.HEALTHCARE_TEAM
    )
    private val staffSchedules = listOf(
        buildStaffSchedule(staffId = physicianStaffId),
        buildStaffSchedule(staffId = nurseStaffId),
        buildStaffSchedule(staffId = otherStaff),
    )
    private val staffSchedulesOnSite = listOf(
        buildStaffSchedule(staffId = staff.id).copy(
            providerUnitId = providerUnits[0].id
        )
    )

    private val cacheKey = "findScheduleOptionsActiveWithTypes-TEST-PROC_ONSITE-PROC_NURSE"

    @BeforeTest
    fun setup() {
        clearAllMocks()

        coEvery {
            appointmentScheduleOptionModelDataService.findOne {
                where {
                    this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id) and
                            this.staffId.eq(staff.id) and
                            this.active.eq(true)
                }
            }
        }

        coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
        coEvery { riskService.getByPerson(person.id) } returns risk.success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { staffScheduleService.getActiveByStaffs(any()) } returns staffSchedules.success()
    }

    @Test
    fun `#listByPerson should return expected options when person has healthcare team`() =
        runBlocking {
            val expectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.PHYSICAL_EDUCATOR),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = physicianStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = nurseStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.COMMUNITY,
                    staffId = null
                )
            )
            val unexpectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.IMMERSION, staffId = otherStaff),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.HEALTHCARE_TEAM),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = otherStaff,
                    active = false
                )
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where { active.eq(true) and showOnApp.eq(true) and modelType.eq(healthcareModelType) }
                })
            } returns (expectedAppointmentScheduleOptions + unexpectedAppointmentScheduleOptions).map { it.toModel() }.success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false)
            assertThat(result).isSuccessWithData(expectedAppointmentScheduleOptions)
        }

    @Test
    fun `#listByPerson should return expected options when person has healthcare lean`() =
        runBlocking {
            val healthcareModelType = HealthcareModelType.V3
            val productInfo = ProductInfo(
                brand = Brand.ALICE,
                primaryAttention = PrimaryAttentionType.ALICE,
                tier = TierType.TIER_1,
                coPayment = CoPaymentType.FULL,
                healthcareModelType = HealthcareModelType.V3,
                refund = RefundType.FULL
            )
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = physicianStaffId,
                type = HealthcareTeam.Type.LEAN
            )
            val person = person.copy(productInfo = productInfo)
            val casaAliceAppointment = TestModelFactory.buildAppointmentScheduleOption(
                type = AppointmentScheduleType.TEST,
                staffId = nurseStaffId
            )
            val expectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = physicianStaffId
                )
            )
            val unexpectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.IMMERSION),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.HEALTHCARE_TEAM),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    active = false
                ), TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = nurseStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.COMMUNITY,
                    staffId = null
                )
            )
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns risk.success()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where { active.eq(true) and showOnApp.eq(true) and type.inList(typesOfOptionsThatAreVisibleToLeagueMembers) and modelType.eq(healthcareModelType) }
                })
            } returns (expectedAppointmentScheduleOptions + unexpectedAppointmentScheduleOptions).map { it.toModel() }.success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns listOf(casaAliceAppointment)

            val result = appointmentScheduleOptionService.listByPerson(person.id, false)

            assertThat(result).isSuccessWithData(expectedAppointmentScheduleOptions + listOf(casaAliceAppointment))
        }


    @Test
    fun `#listByPerson should return options of healthcare team other than the member if it is from referral`() =
        runBlocking {
            val expectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.PHYSICAL_EDUCATOR),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = physicianStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = nurseStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.COMMUNITY,
                    staffId = null
                ),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.IMMERSION),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.HEALTHCARE_TEAM),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    active = false
                )
            )

            coEvery {
                cache.getList(cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()

            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and
                                this.subSpecialtyIds.containsAny(listOf(specialty.id)) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns expectedAppointmentScheduleOptions.map { it.toModel() }.success()

            val result = appointmentScheduleOptionService.listByPerson(
                personId = person.id,
                fromReferral = true,
                subSpecialtyIds = listOf(specialty.id)
            )
            assertThat(result).isSuccessWithData(expectedAppointmentScheduleOptions)
        }

    @Test
    fun `#listByPersonAndType should return expected options when person has healthcare team, filtered by type`() =
        runBlocking {
            val nutritionistId = RangeUUID.generate()
            val physicalEducatorId = RangeUUID.generate()
            val physiotherapistId = RangeUUID.generate()
            val type = AppointmentScheduleType.NUTRITIONIST
            val expectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.NUTRITIONIST,
                    staffId = nutritionistId
                )
            )
            val unexpectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.IMMERSION),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.HEALTHCARE_TEAM),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    active = false
                ),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.PHYSICAL_EDUCATOR),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = physicianStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = nurseStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.COMMUNITY,
                    staffId = null
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.PHYSICAL_EDUCATOR,
                    staffId = physicalEducatorId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.PHYSIOTHERAPIST,
                    staffId = physiotherapistId
                )
            )

            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()

            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and
                                this.subSpecialtyIds.containsAny(listOf(specialty.id)) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns (expectedAppointmentScheduleOptions + unexpectedAppointmentScheduleOptions).map { it.toModel() }.success()

            val result = appointmentScheduleOptionService.listByPersonAndType(person.id, type, true, listOf(specialty.id))
            assertThat(result).isSuccessWithData(expectedAppointmentScheduleOptions)
        }

    @Test
    fun `#listByPerson should return expected options when person has no healthcare team`() =
        runBlocking {
            val expectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.PHYSICAL_EDUCATOR),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.COMMUNITY,
                    staffId = null
                )
            )
            val unexpectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.IMMERSION),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.HEALTHCARE_TEAM),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = physicianStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    staffId = nurseStaffId
                ),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    active = false
                )
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq { where { active.eq(true) and showOnApp.eq(true) and modelType.eq(healthcareModelType) } })
            } returns (expectedAppointmentScheduleOptions + unexpectedAppointmentScheduleOptions).map { it.toModel() }.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns NotFoundException("Not found: personClinicalAccount not found for personId=${person.id}").failure()

            coEvery {
                cache.getList(cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false)
            assertThat(result).isSuccessWithData(expectedAppointmentScheduleOptions)
        }

    @Test
    fun `#searchScheduleOption should return filtered options`() = runBlocking {
        val appointmentScheduleOptions = listOf(
            TestModelFactory.buildAppointmentScheduleOption(
                title = "prefix",
                type = AppointmentScheduleType.PHYSICAL_EDUCATOR
            ),
            TestModelFactory.buildAppointmentScheduleOption(
                title = "filtro prefixo",
                type = AppointmentScheduleType.COMMUNITY,
                staffId = null
            )
        )
        val expectedAppointmentScheduleOption = appointmentScheduleOptions.minByOrNull { it.title }
        val expectedAppointmentScheduleOptions = listOfNotNull(expectedAppointmentScheduleOption)
        coEvery {
            appointmentScheduleOptionModelDataService.find(queryEq {
                where {
                    this.title.like("prefix") and
                            this.active.eq(true)
                }
                    .orderBy { title }
                    .sortOrder { asc }
                    .offset { 0 }
                    .limit { 1 }
            })
        } returns expectedAppointmentScheduleOptions.map { it.toModel() }.success()
        val result = appointmentScheduleOptionService.searchScheduleOption(
            titlePrefix = "prefix",
            range = IntRange(0, 0)
        )
        assertThat(result).isSuccessWithData(expectedAppointmentScheduleOptions)
    }

    @Test
    fun `#getForSpecialist should return appointment schedule option of the specialist`() = runBlocking {
        coEvery {
            appointmentScheduleOptionModelDataService.findOne(queryEq {
                where {
                    this.healthCommunitySpecialistId.eq(
                        specialist.id
                    )
                }
            })
        } returns appointmentScheduleOption.toModel().success()
        val result = appointmentScheduleOptionService.getForSpecialist(specialist.id)
        assertThat(result).isSuccessWithData(appointmentScheduleOption)
    }

    @Test
    fun `#getForSpecialist should throw exception when there is no appointment schedule option of the specialist`() =
        runBlocking {
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.healthCommunitySpecialistId.eq(
                            specialist.id
                        )
                    }
                })
            } returns NotFoundException("not found").failure()
            val result = appointmentScheduleOptionService.getForSpecialist(specialist.id)
            assertThat(result).isFailureOfType(NotFoundException::class)
        }

    @Test
    fun `#getForSpecialists should return appointment schedule option of the specialists`() = runBlocking {
        coEvery {
            appointmentScheduleOptionModelDataService.find(queryEq {
                where {
                    this.healthCommunitySpecialistId.inList(
                        listOf(specialist.id)
                    ).and(this.active.eq(true))
                }
            })
        } returns listOf(appointmentScheduleOption.toModel()).success()
        val result = appointmentScheduleOptionService.getForSpecialists(listOf(specialist.id))
        assertThat(result).isSuccessWithData(listOf(appointmentScheduleOption))
    }

    @Test
    fun `#getByAppointmentScheduleEventTypeAndStaff should return appointment schedule option for appointment schedule event type and staff`() =
        runBlocking {
            val appointmentScheduleEventTypeId = RangeUUID.generate()
            val staffId = RangeUUID.generate()
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId)
                            .and(this.staffId.eq(staffId))
                            .and(this.active.eq(true))
                    }
                })
            } returns appointmentScheduleOption.toModel().success()
            val result = appointmentScheduleOptionService.getByAppointmentScheduleEventTypeAndStaff(
                appointmentScheduleEventTypeId,
                staffId
            )
            assertThat(result).isSuccessWithData(appointmentScheduleOption)
        }

    @Test
    fun `#getByAppointmentScheduleEventType should return appointment schedule option for appointment schedule event type`() =
        runBlocking {
            val appointmentScheduleEventTypeId = RangeUUID.generate()
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(
                            appointmentScheduleEventTypeId
                        ).and(this.active.eq(true))
                    }
                })
            } returns listOf(appointmentScheduleOption.toModel()).success()
            val result = appointmentScheduleOptionService.getByAppointmentScheduleEventType(appointmentScheduleEventTypeId, null)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleOption))
        }

    @Test
    fun `#getForStaff should return appointment schedule option for staff`() = runBlocking {
        coEvery {
            appointmentScheduleOptionModelDataService.find(queryEq {
                where {
                    this.staffId.eq(staff.id).and(this.active.eq(true))
                }
            })
        } returns listOf(appointmentScheduleOption.toModel()).success()
        val result = appointmentScheduleOptionService.getForStaff(staff.id)
        assertThat(result).isSuccessWithData(listOf(appointmentScheduleOption))
    }

    @Test
    fun `#getEventsWithProviderUnits should return event types for given staff`() = runBlocking {
        coEvery {
            appointmentScheduleOptionModelDataService.find(queryEq {
                where {
                    this.staffId.eq(staff.id).and(this.active.eq(true))
                }
            })
        } returns listOf(appointmentScheduleOption.toModel()).success()

        val eventTypeProviderUnit = TestModelFactory.buildEventTypeProviderUnit(
            providerUnitId = RangeUUID.generate(),
            appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
            availabilityStartTime = null,
            availabilityEndTime = null
        )
        val eventTypeProviderUnits = listOf(eventTypeProviderUnit)
        val providerUnit = TestModelFactory.buildProviderUnit(id = eventTypeProviderUnit.providerUnitId!!)
        val appointmentScheduleEventTypeWithProviderUnits = AppointmentScheduleEventTypeWithProviderUnits(
            id = appointmentScheduleEventType.id,
            title = appointmentScheduleEventType.title,
            specialtyId = appointmentScheduleEventType.specialtyId,
            subSpecialtyIds = appointmentScheduleEventType.subSpecialtyIds,
            showOnApp = appointmentScheduleEventType.showOnApp,
            category = appointmentScheduleEventType.category,
            duration = appointmentScheduleEventType.duration,
            locationType = appointmentScheduleEventType.locationType,
            description = appointmentScheduleEventType.description,
            userType = appointmentScheduleEventType.userType,
            status = appointmentScheduleEventType.status,
            searchTokens = appointmentScheduleEventType.searchTokens,
            healthcareModelType = appointmentScheduleEventType.healthcareModelType,
            minimumTimeToScheduleBeforeAppointmentTime = appointmentScheduleEventType
                .minimumTimeToScheduleBeforeAppointmentTime,
            isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral,
            numberOfDaysFromNowToAllowScheduling = appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling,
            internalObservation = appointmentScheduleEventType.internalObservation,
            membersRisk = appointmentScheduleEventType.membersRisk,
            providerUnitIds = eventTypeProviderUnits.mapNotNull { it.providerUnitId },
            groupByType = appointmentScheduleEventType.groupByType,
            locations = listOf(
                AppointmentScheduleEventTypeLocationAvailability(
                    providerUnitId = eventTypeProviderUnit.providerUnitId,
                    type = AppointmentScheduleEventTypeLocation.ON_SITE,
                    duration = eventTypeProviderUnit.duration,
                    minimumTimeToScheduleBeforeAppointmentTime = eventTypeProviderUnit.minimumTimeToScheduleBeforeAppointmentTime,
                    numberOfDaysFromNowToAllowScheduling = eventTypeProviderUnit.numberOfDaysFromNowToAllowScheduling,
                    availableWeekDays = eventTypeProviderUnit.availableWeekDays,
                    providerUnitName = providerUnit.name,
                )
            ),
            updatedAt = appointmentScheduleEventType.updatedAt,
        )

        coEvery {
            appointmentScheduleEventTypeService.getActiveByIds(listOf(appointmentScheduleEventType.id))
        } returns listOf(appointmentScheduleEventType).success()

        coEvery {
            eventTypeProviderUnitService.getForEventTypes(listOf(appointmentScheduleEventType.id))
        } returns eventTypeProviderUnits.success()

        coEvery {
            appointmentScheduleEventTypeService.getProviderUnitsName(eventTypeProviderUnits)
        } returns mapOf(providerUnit.id to providerUnit).success()

        val result = appointmentScheduleOptionService.getEventsWithProviderUnits(staff.id)

        assertThat(result).isSuccessWithData(listOf(appointmentScheduleEventTypeWithProviderUnits))
    }

    @Test
    fun `#getStaffsAssociatedTo should return staffs associated to event type`() = runBlocking {
        val appointmentScheduleEventType = appointmentScheduleEventTypes.first()
        val staffs =  listOf(staff, staff.copy(active = false))

        coEvery {
            appointmentScheduleOptionModelDataService.find(queryEq {
                where {
                    this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id).and(this.active.eq(true))
                }
            })
        } returns listOf(appointmentScheduleOption.toModel()).success()

        coEvery {
            staffService.findByList(listOf(staff.id))
        } returns staffs.success()

        val result = appointmentScheduleOptionService.getStaffsAssociatedToEvents(appointmentScheduleEventType.id, null)
        assertThat(result).isSuccessWithData(listOf(staff))
    }

    @Test
    fun `#getActiveForSpecialtyAndSubSpecialty should return appointment schedule option for specialty and sub specialty`() =
        runBlocking {
            val specialtyId = RangeUUID.generate()
            val subSpecialtyId = RangeUUID.generate()
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        this.specialtyId.eq(specialtyId)
                            .and(this.subSpecialtyIds.contains(subSpecialtyId))
                            .and(this.active.eq(true))
                    }
                })
            } returns listOf(appointmentScheduleOption.toModel()).success()
            val result = appointmentScheduleOptionService.getActiveForSpecialtyAndSubSpecialty(specialtyId, subSpecialtyId)
            assertThat(result).isSuccessWithData(listOf(appointmentScheduleOption))
        }

    @Test
    fun `#listByPerson should enrich appointment schedule option with event type information`(): Unit =
        runBlocking {
            val appointmentScheduleEventTypesModified = appointmentScheduleEventTypes.map {
                it.copy(title = "Chuva forte")
            }
            val firstPoolAppointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(
                type = AppointmentScheduleType.PHYSICAL_EDUCATOR,
                staffId = physicianStaffId,
                appointmentScheduleEventTypeId = appointmentScheduleEventTypesModified.first().id,
            )
            val firstNotPoolAppointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(
                type = AppointmentScheduleType.HEALTHCARE_TEAM,
                staffId = nurseStaffId
            )
            val secondNotPoolAppointmentScheduleOption = TestModelFactory.buildAppointmentScheduleOption(
                type = AppointmentScheduleType.COMMUNITY,
                staffId = null
            )
            val expectedAppointmentScheduleOptions = listOf(
                firstPoolAppointmentScheduleOption,
                firstNotPoolAppointmentScheduleOption,
                secondNotPoolAppointmentScheduleOption,
            )
            val unexpectedAppointmentScheduleOptions = listOf(
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.IMMERSION),
                TestModelFactory.buildAppointmentScheduleOption(type = AppointmentScheduleType.HEALTHCARE_TEAM),
                TestModelFactory.buildAppointmentScheduleOption(
                    type = AppointmentScheduleType.HEALTHCARE_TEAM,
                    active = false
                )
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where { active.eq(true) and showOnApp.eq(true) and modelType.eq(healthcareModelType) }
                })
            } returns (expectedAppointmentScheduleOptions + unexpectedAppointmentScheduleOptions).map { it.toModel() }.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(appointmentScheduleEventTypes.map { it.id }.subList(0, 1))
            } returns appointmentScheduleEventTypesModified.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    appointmentScheduleEventTypesModified.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false).get()
            assertThat(result).containsExactlyInAnyOrder(
                firstPoolAppointmentScheduleOption.copy(staffId = null, imageUrl = ""),
                firstNotPoolAppointmentScheduleOption,
                secondNotPoolAppointmentScheduleOption
            )
        }

    @Test
    fun `#listByPerson should return high risk appointment schedule options for high risk members on league for non referral request`(): Unit =
        runBlocking {
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = physicianStaffId,
                nurseStaffId = nurseStaffId,
                type = HealthcareTeam.Type.LEAGUE,
            )
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and
                                showOnApp.eq(true) and
                                type.inList(typesOfOptionsThatAreVisibleToLeagueMembers) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns highRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionHighRisk,
            )
        }

    @Test
    fun `#listByPerson should return only high risk appointment schedule options for high risk members on league that are related to person team for non referral request`(): Unit =
        runBlocking {
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = RangeUUID.generate(),
                nurseStaffId = RangeUUID.generate(),
                type = HealthcareTeam.Type.LEAGUE,
            )
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and showOnApp.eq(true) and
                                type.inList(typesOfOptionsThatAreVisibleToLeagueMembers) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns highRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false).get()
            assertThat(result).isEmpty()
        }

    @Test
    fun `#listByPerson should return all appointment schedule options for high risk members on league for referral request`(): Unit =
        runBlocking {
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = RangeUUID.generate(),
                nurseStaffId = RangeUUID.generate(),
                type = HealthcareTeam.Type.LEAGUE,
            )
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns highRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where { active.eq(true) and this.subSpecialtyIds.containsAny(listOf(specialty.id)) and
                            modelType.eq(healthcareModelType) }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()

            val result = appointmentScheduleOptionService.listByPerson(person.id, true, listOf(specialty.id)).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionLowRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionAnyRisk,
            )
        }

    @Test
    fun `#listByPerson should return low risk appointment schedule options for low risk members on league for non referral request`(): Unit =
        runBlocking {
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = physicianStaffId,
                nurseStaffId = nurseStaffId,
                type = HealthcareTeam.Type.LEAGUE,
            )
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and showOnApp.eq(true) and
                                type.inList(typesOfOptionsThatAreVisibleToLeagueMembers) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns lowRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionLowRisk,
            )
        }

    @Test
    fun `#listByPerson should return all appointment schedule options for low risk members on league for referral request`(): Unit =
        runBlocking {
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = physicianStaffId,
                nurseStaffId = nurseStaffId,
                type = HealthcareTeam.Type.LEAGUE,
            )
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and showOnApp.eq(true) and
                                type.inList(typesOfOptionsThatAreVisibleToLeagueMembers)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns lowRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and this.subSpecialtyIds.containsAny(listOf(specialty.id)) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()

            val result = appointmentScheduleOptionService.listByPerson(person.id, true, listOf(specialty.id)).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
        }

    @Test
    fun `#listByPerson should return no risk appointment schedule options for members without risk data that are on league for non referral request`(): Unit =
        runBlocking {
            val healthCareTeam = HealthcareTeam(
                physicianStaffId = physicianStaffId,
                nurseStaffId = nurseStaffId,
                type = HealthcareTeam.Type.LEAGUE,
            )
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk,
                appointmentScheduleEventTypeNoRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
                appointmentScheduleOptionNoRisk
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and showOnApp.eq(true) and
                                type.inList(typesOfOptionsThatAreVisibleToLeagueMembers) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { healthcareTeamService.getHealthcareTeamByPerson(person.id) } returns healthCareTeam.success()
            coEvery { riskService.getByPerson(person.id) } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionNoRisk,
            )
        }

    @Test
    fun `#listByPerson should return any appointment schedule options for low risk members not on league for non referral request`(): Unit =
        runBlocking {
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where { active.eq(true) and showOnApp.eq(true) and modelType.eq(healthcareModelType) }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { riskService.getByPerson(person.id) } returns lowRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            val result = appointmentScheduleOptionService.listByPerson(person.id, false).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionAnyRisk,
            )
        }

    @Test
    fun `#listByPerson should return all appointment schedule options for low risk members not on league for referral request`(): Unit =
        runBlocking {
            val eventTypes = listOf(
                appointmentScheduleEventTypeAnyRisk,
                appointmentScheduleEventTypeHighRisk,
                appointmentScheduleEventTypeLowRisk
            )
            val appointmentScheduleOptionsForRisk = listOf(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
            coEvery {
                cache.getList(
                    cacheKey,
                    AppointmentScheduleOption::class,
                    callbackWithResult = any(),
                    putFunction = any(),
                )
            } returns emptyList()
            coEvery {
                appointmentScheduleOptionModelDataService.find(queryEq {
                    where {
                        active.eq(true) and
                                this.subSpecialtyIds.containsAny(listOf(specialty.id)) and
                                modelType.eq(healthcareModelType)
                    }
                })
            } returns appointmentScheduleOptionsForRisk.map { it.toModel() }.success()
            coEvery { riskService.getByPerson(person.id) } returns lowRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypes.map { it.id })
            } returns eventTypes.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypes.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            val result = appointmentScheduleOptionService.listByPerson(
                person.id,
                true,
                listOf(specialty.id)
            ).get()
            assertThat(result).containsExactlyInAnyOrder(
                appointmentScheduleOptionAnyRisk,
                appointmentScheduleOptionHighRisk,
                appointmentScheduleOptionLowRisk,
            )
        }

    @Ignore
    @Test
    fun `#listByPerson should return all appointment schedule coordinated (from referrals) options for children`(): Unit =
        runBlocking {
            val childrenAppointmentScheduleOption =
                appointmentScheduleOptionAnyRisk.copy(ageRating = AgeRatingType.CHILDREN)
            val child = person.copy(dateOfBirth = LocalDateTime.of(LocalDateTime.now().year, 1, 1, 0, 0))
            val eventTypeIds = listOfNotNull(childrenAppointmentScheduleOption.appointmentScheduleEventTypeId)
            coEvery { personService.get(person.id) } returns child.success()
            coEvery { riskService.getByPerson(person.id) } returns lowRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypeIds)
            } returns eventTypesForChildren.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypesForChildren.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            val result = appointmentScheduleOptionService.listByPerson(
                personId = person.id,
                fromReferral = true
            ).get()
            assertThat(result).containsOnly(childrenAppointmentScheduleOption)
        }

    @Ignore
    @Test
    fun `#listByPerson should return all appointment schedule uncoordinated options for children`(): Unit =
        runBlocking {
            val childrenAppointmentScheduleOption = appointmentScheduleOptionAnyRisk.copy(ageRating = AgeRatingType.CHILDREN)
            val child = person.copy(dateOfBirth = LocalDateTime.of(LocalDateTime.now().year, 1, 1, 0, 0))
            val eventTypeIds = listOfNotNull(childrenAppointmentScheduleOption.appointmentScheduleEventTypeId)
            coEvery { personService.get(person.id) } returns child.success()
            coEvery { riskService.getByPerson(person.id) } returns lowRisk.success()
            coEvery {
                appointmentScheduleEventTypeService.getByIds(eventTypeIds)
            } returns eventTypesForChildren.success()
            coEvery {
                eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(
                    eventTypesForChildren.map { it.id }
                )
            } returns emptyList<EventTypeProviderUnits>().success()
            val result = appointmentScheduleOptionService.listByPerson(
                personId = person.id,
                fromReferral = false
            ).get()
            assertThat(result).containsOnly(childrenAppointmentScheduleOption)
        }

    @Test
    fun `#associateStaff should create appointment schedule option to hold the association of staff with event type without staff profile image if staff don't have one`(): Unit =
        runBlocking {
            val notMultiProfessionalEventType = appointmentScheduleEventType.copy(isMultiProfessionalReferral = false)
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(notMultiProfessionalEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleEventTypeService.get(notMultiProfessionalEventType.id)
            } returns notMultiProfessionalEventType.success()
            coEvery {
                appointmentScheduleOptionModelDataService.add(match {
                    it.appointmentScheduleEventTypeId == notMultiProfessionalEventType.id &&
                            it.staffId == staff.id &&
                            it.type == notMultiProfessionalEventType.category &&
                            it.title == notMultiProfessionalEventType.title + " - " + staff.fullName &&
                            it.ageRating == notMultiProfessionalEventType.ageRating &&
                            it.imageUrl == ""
                })
            } returns appointmentScheduleOption.copy(
                title = appointmentScheduleEventType.title + " - " + staff.fullName
            ).toModel().success()
            coEvery {
                staffService.get(staff.id)
            } returns staff.success()
            coEvery {
                kafkaProducerService.produce(match { it is AppointmentScheduleOptionCreatedEvent })
            } returns mockk()

            val result = appointmentScheduleOptionService.associateStaff(staff.id, appointmentScheduleEventType.id)
            assertThat(result).isSuccessWithData(
                appointmentScheduleOption.copy(title = appointmentScheduleEventType.title + " - " + staff.fullName)
            )

            coVerifyOnce { kafkaProducerService.produce(match { it is AppointmentScheduleOptionCreatedEvent }) }
        }

    @Test
    fun `#associateStaff should create appointment schedule option to hold the association of staff with event type with staff profile image when they have one`(): Unit =
        runBlocking {
            val staff = TestModelFactory.buildStaff(profileImageUrl = "foto")
            val notMultiProfessionalEventType = appointmentScheduleEventType.copy(isMultiProfessionalReferral = false)
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(notMultiProfessionalEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns NotFoundException().failure()
            coEvery {
                appointmentScheduleEventTypeService.get(notMultiProfessionalEventType.id)
            } returns notMultiProfessionalEventType.success()
            coEvery {
                appointmentScheduleOptionModelDataService.add(match {
                    it.appointmentScheduleEventTypeId == notMultiProfessionalEventType.id &&
                            it.staffId == staff.id &&
                            it.type == notMultiProfessionalEventType.category &&
                            it.title == notMultiProfessionalEventType.title + " - " + staff.fullName &&
                            it.ageRating == notMultiProfessionalEventType.ageRating &&
                            it.imageUrl == staff.profileImageUrl
                })
            } returns appointmentScheduleOption.copy(
                title = appointmentScheduleEventType.title + " - " + staff.fullName
            ).toModel().success()
            coEvery {
                staffService.get(staff.id)
            } returns staff.success()
            coEvery {
                kafkaProducerService.produce(match { it is AppointmentScheduleOptionCreatedEvent })
            } returns mockk()

            val result = appointmentScheduleOptionService.associateStaff(staff.id, appointmentScheduleEventType.id)
            assertThat(result).isSuccessWithData(
                appointmentScheduleOption.copy(title = appointmentScheduleEventType.title + " - " + staff.fullName)
            )

            coVerifyOnce { kafkaProducerService.produce(match { it is AppointmentScheduleOptionCreatedEvent }) }
        }

    @Ignore //FIXME
    @Test
    fun `#associateStaff should activate and return existing appointment schedule option if there is already association`() =
        runBlocking {
            val appointmentScheduleOption = appointmentScheduleOption
            val appointmentScheduleOptionToUpdate = AppointmentScheduleOptionCreator.convert(
                appointmentScheduleEventType, staff
            ).copy(id = appointmentScheduleOption.id, version = appointmentScheduleOption.version)
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns appointmentScheduleOption.toModel().success()
            coEvery { appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id) } returns appointmentScheduleEventType.success()
            coEvery { staffService.get(staff.id) } returns staff.success()
            coEvery {
                appointmentScheduleOptionService.update(match {
                    it == appointmentScheduleOptionToUpdate.copy(
                        createdAt = it.createdAt,
                        updatedAt = it.updatedAt,
                    )
                })
            } returns appointmentScheduleOptionToUpdate.success()
            coEvery {
                kafkaProducerService.produce(match { it is AppointmentScheduleOptionUpdatedEvent })
            } returns mockk()

            val result = appointmentScheduleOptionService.associateStaff(staff.id, appointmentScheduleEventType.id)
            assertThat(result).isSuccessWithData(appointmentScheduleOptionToUpdate)

            coVerifyOnce { kafkaProducerService.produce(match { it is AppointmentScheduleOptionUpdatedEvent }) }
        }

    @Test
    fun `#disassociateStaff inactivate appointment schedule option`() = runBlocking {
        coEvery {
            appointmentScheduleOptionModelDataService.findOne(queryEq {
                where {
                    this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                        .and(this.staffId.eq(staff.id))
                        .and(this.active.eq(true))
                }
            })
        } returns appointmentScheduleOption.toModel().success()
        coEvery { appointmentScheduleOptionModelDataService.update(
            appointmentScheduleOption.copy(active = false).toModel()
        ) } returns appointmentScheduleOption.toModel().success()
        coEvery {
            kafkaProducerService.produce(match { it is AppointmentScheduleOptionUpdatedEvent })
        } returns mockk()

        val result = appointmentScheduleOptionService.disassociateStaff(staff.id, appointmentScheduleEventType.id)
        assertThat(result).isSuccessWithData(appointmentScheduleOption)

        coVerifyOnce { kafkaProducerService.produce(match { it is AppointmentScheduleOptionUpdatedEvent }) }
    }

    @Test
    fun `#disassociateStaff should return exception when it does not exist`(): Unit = runBlocking {
        coEvery {
            appointmentScheduleOptionModelDataService.findOne(queryEq {
                where {
                    this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                        .and(this.staffId.eq(staff.id))
                        .and(this.active.eq(true))
                }
            })
        } returns NotFoundException().failure()

        val result = appointmentScheduleOptionService.disassociateStaff(staff.id, appointmentScheduleEventType.id)
        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#findOnSiteActiveByStaffIdAndType should on site appointment schedule options`(): Unit = runBlocking {
        val expected = 1

        coEvery {
            appointmentScheduleOptionModelDataService.count(
                queryEq {
                    where {
                        this.staffId.eq(staff.id) and
                            this.type.eq(AppointmentScheduleType.HEALTHCARE_TEAM) and
                            this.active.eq(true) and
                            this.calendarProviderUnits.isNotEmpty()
                    }
                }
            )
        } returns expected.success()

        val result = appointmentScheduleOptionService.countOnSiteActiveByStaffIdAndType(staff.id, AppointmentScheduleType.HEALTHCARE_TEAM)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { appointmentScheduleOptionModelDataService.count(any()) }
    }

    @Test
    fun `findBy returns options find by all filters`() = runBlocking {
        val filters = AppointmentScheduleOptionFilters(
            titlePrefix = "my title",
            specialistIds = listOf(RangeUUID.generate()),
            staffIds = listOf(RangeUUID.generate()),
            appointmentScheduleEventTypeIds = listOf(RangeUUID.generate()),
            specialtyId = RangeUUID.generate(),
            subSpecialtyIds = listOf(RangeUUID.generate()),
            appointmentScheduleEventTypeId = RangeUUID.generate(),
            types = listOf(AppointmentScheduleType.TEST),
            ageRating = listOf(AgeRatingType.BOTH),
            active = true,
            range = IntRange(0, 1),
            sortOrder = SortOrder.Ascending
        )

        coEvery {
            appointmentScheduleOptionModelDataService.find(
                queryEq {
                    where {
                        this.title.like(filters.titlePrefix!!) and
                                this.healthCommunitySpecialistId.inList(filters.specialistIds!!) and
                                this.staffId.inList(filters.staffIds!!) and
                                this.appointmentScheduleEventTypeId.inList(filters.appointmentScheduleEventTypeIds!!) and
                                this.specialtyId.eq(filters.specialtyId!!) and
                                this.subSpecialtyIds.containsAny(filters.subSpecialtyIds!!) and
                                this.appointmentScheduleEventTypeId.eq(filters.appointmentScheduleEventTypeId!!) and
                                this.ageRating.inList(filters.ageRating!!) and
                                this.type.inList(filters.types!!) and
                                this.active.eq(filters.active!!)
                    }.offset { 0 }
                        .limit { 2 }
                        .orderBy { createdAt }
                        .sortOrder { asc }
                }
            )
        } returns emptyList<AppointmentScheduleOptionModel>().success()

        val result = appointmentScheduleOptionService.findBy(filters)
        assertThat(result).isSuccessWithData(emptyList())

        coVerifyOnce { appointmentScheduleOptionModelDataService.find(any()) }
    }

    @Test
    fun `findBy returns error when filters is empty`() = runBlocking {
        val filters = AppointmentScheduleOptionFilters()

        val result = appointmentScheduleOptionService.findBy(filters)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify { appointmentScheduleOptionModelDataService wasNot called }
    }

    @Test
    fun `#countBy returns count by all filters`() = runBlocking {
        val filters = AppointmentScheduleOptionFilters(
            titlePrefix = "my title",
            specialistIds = listOf(RangeUUID.generate()),
            staffIds = listOf(RangeUUID.generate()),
            appointmentScheduleEventTypeIds = listOf(RangeUUID.generate()),
            specialtyId = RangeUUID.generate(),
            subSpecialtyIds = listOf(RangeUUID.generate()),
            appointmentScheduleEventTypeId = RangeUUID.generate(),
            types = listOf(AppointmentScheduleType.TEST),
            ageRating = listOf(AgeRatingType.BOTH),
            active = true,
            range = IntRange(0, 1),
            sortOrder = SortOrder.Ascending
        )

        coEvery {
            appointmentScheduleOptionModelDataService.count(
                queryEq {
                    where {
                        this.title.like(filters.titlePrefix!!) and
                                this.healthCommunitySpecialistId.inList(filters.specialistIds!!) and
                                this.staffId.inList(filters.staffIds!!) and
                                this.appointmentScheduleEventTypeId.inList(filters.appointmentScheduleEventTypeIds!!) and
                                this.specialtyId.eq(filters.specialtyId!!) and
                                this.subSpecialtyIds.containsAny(filters.subSpecialtyIds!!) and
                                this.appointmentScheduleEventTypeId.eq(filters.appointmentScheduleEventTypeId!!) and
                                this.ageRating.inList(filters.ageRating!!) and
                                this.type.inList(filters.types!!) and
                                this.active.eq(filters.active!!)
                    }.offset { 0 }
                        .limit { 2 }
                        .orderBy { createdAt }
                        .sortOrder { asc }
                }
            )
        } returns 1.success()

        val result = appointmentScheduleOptionService.countBy(filters)
        assertThat(result).isSuccessWithData(1)

        coVerifyOnce { appointmentScheduleOptionModelDataService.count(any()) }
    }

    @Test
    fun `#countBy returns error when filters is empty`() = runBlocking {
        val filters = AppointmentScheduleOptionFilters()

        val result = appointmentScheduleOptionService.countBy(filters)
        assertThat(result).isFailureOfType(IllegalArgumentException::class)

        coVerify { appointmentScheduleOptionModelDataService wasNot called }
    }

    @Test
    fun `#getProviderUnitDetailsByStaffSchedule returns provider unit details`() =
        runBlocking {
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns appointmentScheduleOption.toModel().success()
            coEvery { staffScheduleService.getActiveOnSiteByStaff(staffId = staff.id) } returns staffSchedulesOnSite.success()
            coEvery { providerUnitService.getByIds(providerUnitIds, true) } returns providerUnits.success()

            val expected = providerUnits

            val result = appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
                person.id,
                appointmentScheduleEventType.id,
                staff.id
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                staffScheduleService.getActiveOnSiteByStaff(any())
                providerUnitService.getByIds(any(), any())
            }
        }

    @Test
    fun `#getProviderUnitDetailsByStaffSchedule returns error when get error to find staff schedule`() =
        runBlocking {
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns appointmentScheduleOption.toModel().success()
            coEvery { staffScheduleService.getActiveOnSiteByStaff(staffId = staff.id) } returns Exception("ex").failure()

            val result = appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
                person.id,
                appointmentScheduleEventType.id,
                staff.id
            )

            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce {
                staffScheduleService.getActiveOnSiteByStaff(any())
            }
            coVerifyNone {
                providerUnitService.getByIds(any(), any())
            }
        }

    @Test
    fun `#getProviderUnitDetailsByStaffSchedule returns error when get error to find appointment schedule option`() =
        runBlocking {
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns Exception("ex").failure()
            val result = appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
                person.id,
                appointmentScheduleEventType.id,
                staff.id
            )

            assertThat(result).isFailureOfType(Exception::class)

            coVerifyNone {
                staffScheduleService.getActiveOnSiteByStaff(any())
                providerUnitService.getByIds(any(), any())
            }
        }

    @Test
    fun `#getProviderUnitDetailsByStaffSchedule returns empty list when calendar provider unit is empty`() =
        runBlocking {
            coEvery {
                appointmentScheduleOptionModelDataService.findOne(queryEq {
                    where {
                        this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventType.id)
                            .and(this.staffId.eq(staff.id))
                            .and(this.active.eq(true))
                    }
                })
            } returns appointmentScheduleOption.toModel().success()
            coEvery { staffScheduleService.getActiveOnSiteByStaff(staffId = staff.id) } returns staffSchedules.success()
            coEvery { providerUnitService.getByIds(emptyList(), true) } returns emptyList<ProviderUnit>().success()

            val expected = emptyList<ProviderUnit>()

            val result = appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
                person.id,
                appointmentScheduleEventType.id,
                staff.id
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce {
                staffScheduleService.getActiveOnSiteByStaff(any())
            }
            coVerifyNone {
                providerUnitService.getByIds(any(), any())
            }
        }
}
