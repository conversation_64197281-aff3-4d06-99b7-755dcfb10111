package br.com.alice.channel.assistance.services.internal

import br.com.alice.channel.assistance.services.internal.firestore.VirtualClinicQueueFirestoreService
import br.com.alice.channel.client.VideoCallService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.extensions.getStaffRoles
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.services.internal.firestore.StaffFirestoreService
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelSegment
import br.com.alice.channel.models.DynamicContent
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.ModalInfo
import br.com.alice.channel.models.QuestionContent
import br.com.alice.channel.models.StaffDocument
import br.com.alice.channel.models.StaffDocumentStatus
import br.com.alice.channel.models.VirtualClinicAutoServicePromptOptions
import br.com.alice.channel.models.budTriageTag
import br.com.alice.channel.models.inProgressTag
import br.com.alice.channel.models.modalContentImagePrefix
import br.com.alice.channel.notifier.MemberDropVirtualClinicEvent
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistoryExtraInfo
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ExtraInfoType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.VirtualClinicStatusControl
import br.com.alice.data.layer.services.NaiveTextualDeIdentificationService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.models.HealthFormQuestionOptionResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.google.cloud.Timestamp
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import kotlin.math.max

class VirtualClinicMemberExperienceService(
    private val virtualClinicQueueFirestoreService: VirtualClinicQueueFirestoreService,
    private val staffFirestoreService: StaffFirestoreService,
    private val messageFirestoreService: MessageFirestoreService,
    private val channelService: ChannelService,
    private val channelNotificationService: ChannelNotificationService,
    private val videoCallService: VideoCallService,
    private val personService: PersonService
) : Spannable {

    @FirestoreContextUsage
    suspend fun fillMemberQueueModalInformation(
        channelId: String,
        channelSegment: ChannelSegment,
        appVersion: String
    ) = span("fillMemberQueueModalInformation") { span ->
        catchResult {
            span.setAttribute("channel_id", channelId)

            val (estimatedAttendanceTime, queueSize, staffsAvailable) = getAttendenceEstimateInfo(channelSegment)
            val positionInQueue = max(queueSize, 1)

            span.setAttribute("estimated_medical_care_at", estimatedAttendanceTime.toString())
            span.setAttribute("queue_size", queueSize.toString())
            span.setAttribute("staffsAvailable", staffsAvailable.toString())

            channelService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::digitalCareQueueModalInfo to ModalInfo(
                        content = parseModalContent(appVersion),
                        medicalCareAt = estimatedAttendanceTime,
                        positionInQueue = positionInQueue
                    ),
                    ChannelDocument::videoCall to null
                )
            )
                .then { notify(channelId, estimatedAttendanceTime, positionInQueue) }
                .recordResult(span)
        }
    }

    @FirestoreContextUsage
    suspend fun dropMemberFromQueue(channelId: String) = span("dropMemberFromQueue") { span ->
        span.setAttribute("channel_id", channelId)

        channelService.getChannel(channelId).flatMap { channelDocument ->
            virtualClinicQueueFirestoreService.remove(channelId).flatMap {
                if (channelDocument.isDropBeforeVideoCall()) {
                    setChannelAsInactiveAndSendReturnOptionsMessage(channelDocument, this::dropBeforeVideoCallMessage)
                } else {
                    coResultOf { channelDocument.id() }
                }
            }
        }
            .recordResult(span)
    }

    @FirestoreContextUsage
    suspend fun onMemberRejectVideoCall(channelId: String) = span("onMemberRejectVideoCall") { span ->
        span.setAttribute("channel_id", channelId)

        channelService.getChannel(channelId)
            .flatMap { channel ->
                setChannelAsInactive(channel)
                    .flatMap { videoCallService.endMeetingByChannel(it) }
                    .then {
                        sendMessageWithReturnOptions(
                            channel = channel,
                            messageFunc = this::videoCallRejectedMessage
                        )
                    }
            }
            .map { channelId }
    }

    suspend fun getEstimatedTime(
        channelSegment: ChannelSegment
    ) = span("getEstimatedTime") {
        val (staffsAvailable, queueSize) = getStaffAndQueueInfo(channelSegment)
        getAttendenceTimeInMinutes(staffsAvailable, queueSize)
    }

    private suspend fun notify(channelId: String, estimatedAttendanceTime: Timestamp, positionInQueue: Int) =
        channelNotificationService.notify(
            channelId = channelId,
            action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUE_MODAL_INFO_UPDATED,
            extraInfo = listOf(
                ChannelHistoryExtraInfo(
                    key = ExtraInfoType.ESTIMATED_MEDICAL_CARE_AT,
                    value = estimatedAttendanceTime
                ),
                ChannelHistoryExtraInfo(
                    key = ExtraInfoType.POSITION_IN_VIRTUAL_CLINIC_QUEUE,
                    value = positionInQueue
                )
            )
        )

    @FirestoreContextUsage
    suspend fun setChannelAsInactiveAndSendReturnOptionsMessage(
        channel: ChannelDocument,
        messageFunc: suspend (person: Person) -> String
    ) = span("setChannelAsInactiveAndSendReturnOptionsMessage") { span ->
        span.setAttribute("channel_id", channel.id())

        setChannelAsInactive(channel)
            .then {
                sendMessageWithReturnOptions(
                    channel = channel,
                    messageFunc = messageFunc
                )
            }.recordResult(span)
    }

    private suspend fun getAttendenceEstimateInfo(
        channelSegment: ChannelSegment
    ) = getStaffAndQueueInfo(channelSegment).let { (staffsAvailable, queueSize) ->
        getAttendenceTimeInMinutes(staffsAvailable, queueSize).let { minutes ->
            val estimatedAttendanceTime = LocalDateTime.now().plusMinutes(minutes).toTimestamp()
            Triple(estimatedAttendanceTime, queueSize, staffsAvailable)
        }
    }

    private fun getAttendenceTimeInMinutes(
        staffsAvailable: Int,
        queueSize: Int
    ) = getMinimalAttendenceTimeInMinutes().toLong().let { minimalAttendenceInMinutes ->
        val estimatedTimeInMinutes = (queueSize * 10L) / staffsAvailable

        max(estimatedTimeInMinutes, minimalAttendenceInMinutes)
    }

    private suspend fun getStaffAndQueueInfo(
        channelSegment: ChannelSegment
    ) = coroutineScope {
        val staffOnlineDeferred = async { getStaffOnline(channelSegment.getStaffRoles()) }
        val queueSize = async { getQueueSize(channelSegment) }

        staffOnlineDeferred.await() to queueSize.await()
    }

    private suspend fun getStaffOnline(roles: List<Role>) =
        staffFirestoreService.find { collectionReference ->
            collectionReference
                .whereEqualTo(StaffDocument::status.name, StaffDocumentStatus.AVAILABLE.name)
                .whereIn(StaffDocument::role.name, roles.map { it.name })
        }
            .get()
            .ifEmpty { listOf(1) }
            .size

    private suspend fun getQueueSize(
        channelSegment: ChannelSegment
    ) = virtualClinicQueueFirestoreService
        .getQueue(channelSegment)
        .get()
        .ifEmpty { emptyList() }
        .size

    @FirestoreContextUsage
    private suspend fun setChannelAsInactive(channel: ChannelDocument) =
        channelService.updateFields(
            channel.id(),
            mapOf(
                ChannelDocument::subCategory to ChannelSubCategory.SCREENING,
                ChannelDocument::subCategoryClassifier to null,
                ChannelDocument::routingTo to emptyList<String>(),
                ChannelDocument::hideMemberInput to true,
                ChannelDocument::canBeArchived to true,
                ChannelDocument::digitalCareQueueModalInfo to null,
                ChannelDocument::tags to listOf(inProgressTag, budTriageTag),
                ChannelDocument::inactiveAt to Timestamp.now(),
                ChannelDocument::isWaiting to false,
            )
        ).then {
            removeStaffs(channel)

            channelNotificationService.produceGenericEvent(
                MemberDropVirtualClinicEvent(channel)
            )
        }

    @FirestoreContextUsage
    private suspend fun removeStaffs(channel: ChannelDocument) =
        channel.staff.keys.forEach { staffId ->
            channelService.removeStaff(
                channel = channel,
                staffId = staffId,
                requesterStaffId = staffId,
                notify = false,
                checkOwner = false,
                checkSize = false
            ).get()
        }

    @FirestoreContextUsage
    private suspend fun sendMessageWithReturnOptions(
        channel: ChannelDocument,
        messageFunc: suspend (person: Person) -> String
    ) =
        span("sendMessageWithReturnOptions") { span ->
            span.setAttribute("channel_id", channel.id())

            personService.get(channel.personId.toPersonId())
                .flatMap { person ->
                    messageFirestoreService.add(
                        channel.id(),
                        MessageDocument(
                            userId = "",
                            content = messageFunc(person),
                            type = MessageType.QUESTION,
                            question = QuestionContent(
                                question = "Escolha uma opção:",
                                input = QuestionnaireQuestionInputResponse(
                                    action = "",
                                    displayAttributes = null,
                                    options = listOf(
                                        VirtualClinicAutoServicePromptOptions.BACK_VIRTUAL_CLINIC,
                                        VirtualClinicAutoServicePromptOptions.ARCHIVE,
                                    ).map {
                                        HealthFormQuestionOptionResponse(
                                            label = it.description,
                                            value = it.name
                                        )
                                    },
                                    type = HealthFormQuestionType.OPTION_BUTTONS
                                ),
                                required = true
                            )
                        )
                    )
                }
        }

    private fun getMinimalAttendenceTimeInMinutes() =
        FeatureService.get(
            FeatureNamespace.CHANNELS,
            "minimal_attendence_time_in_minutes",
            10
        )

    private fun ChannelDocument.isDropBeforeVideoCall() =
        this.virtualClinicStatusControl != null && this.virtualClinicStatusControl == VirtualClinicStatusControl.DROP_BEFORE_VIDEO_CALL

    private fun parseModalContent(appVersion: String) = getModelContentFromFlag(appVersion).let { jsonFlag ->
        gson.fromJson<List<DynamicContent>>(jsonFlag)
            .map { dynamicContent -> dynamicContent.addImageUrlPrefix() }
    }

    private fun DynamicContent.addImageUrlPrefix() = this.copy(
        imageUrl = "${modalContentImagePrefix}${this.imageUrl}"
    )

    private fun dropBeforeVideoCallMessage(person: Person) =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "virtual_clinic_drop_before_call_return_message",
            defaultValue = ""
        ).ifEmpty {
            throw InvalidArgumentException(
                code = "invalid_drop_before_video_call_return_message",
                message = "Invalid virtual_clinic_drop_before_call_return_message"
            )
        }.let { NaiveTextualDeIdentificationService.identify(it, person.toPersonPII()) }

    private fun videoCallRejectedMessage(person: Person) =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "virtual_clinic_video_call_rejected_return_message",
            defaultValue = ""
        ).ifEmpty {
            throw InvalidArgumentException(
                code = "invalid_video_call_rejected_return_message",
                message = "Invalid virtual_clinic_video_call_rejected_return_message"
            )
        }.let { NaiveTextualDeIdentificationService.identify(it, person.toPersonPII()) }

    private fun getModelContentFromFlag(appVersion: String) =
        if (shouldShowV2ModalContent(appVersion))  newModalDynamic() else oldModalDynamic()

    private fun oldModalDynamic(): String = FeatureService.get(
        namespace = FeatureNamespace.CHANNELS,
        key = "pa_queue_dynamic_content",
        defaultValue = ""
    )

    private fun newModalDynamic(): String = FeatureService.get(
        namespace = FeatureNamespace.CHANNELS,
        key = "pa_queue_dynamic_content_v2",
        defaultValue = ""
    )


    private fun shouldShowV2ModalContent(appVersion: String): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.CHANNELS,
            key = "version_min_pa_queue_dynamic_content_v2",
            defaultValue = "99.99.99"
        ).let {
            SemanticVersion(it) <= SemanticVersion(appVersion)
        }
}
