package br.com.alice.channel.assistance.consumers

import br.com.alice.channel.assistance.services.VideoCallServiceImpl
import br.com.alice.channel.assistance.services.internal.VirtualClinicMemberExperienceService
import br.com.alice.channel.core.consumers.Consumer
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldError
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.serialization.gson
import br.com.alice.communication.apns.ApnsService
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.Device
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.VideoCall
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class VirtualClinicMemberQueueExperienceConsumer(
    private val virtualClinicMemberExperienceService: VirtualClinicMemberExperienceService,
    private val pushService: PushService,
    private val deviceService: DeviceService,
    private val videCallService: VideoCallServiceImpl,
    private val apnsService: ApnsService
): Consumer() {

    companion object {
        const val PUSH_TITLE_QUEUED = "Menos de @estimated_time minutos"
        const val PUSH_BODY_QUEUED = "Procure por um lugar reservado e silencioso."
        const val PUSH_TITLE_VIDEO_CALL_STARTED = "O médico está te esperando"
        const val PUSH_BODY_VIDEO_CALL_STARTED = "Está na hora da sua consulta"
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun handleVirtualClinicMemberQueueExperience(event: ChannelUpsertedEvent) =
        span("handleVirtualClinicMemberQueueExperience") { span ->
            withSubscribersEnvironment {
                event.payload.let { payload ->
                    span.setChannel(event)

                    if (isNotVirtualClinic(payload)) return@withSubscribersEnvironment false.success()

                    when (payload.action) {
                        ChannelChangeAction.VIRTUAL_CLINIC_QUEUED -> fillChannelDocumentWithModalInformation(payload)
                        ChannelChangeAction.MEMBER_QUEUE_EXPERIENCE_DROP_BEFORE_VIDEO_CALL ->
                            virtualClinicMemberExperienceService.dropMemberFromQueue(payload.channelId)
                        ChannelChangeAction.VIRTUAL_CLINIC_MEMBER_DECLINE_VIDEO_CALL ->
                            virtualClinicMemberExperienceService.onMemberRejectVideoCall(payload.channelId)
                        else -> false.success()
                    }
                        .recordResult(span)
                }
            }
        }

    suspend fun sendPushForQueueSpecificStatus(event: ChannelUpsertedEvent) =
        span("sendPushForQueueSpecificStatus") { span ->
            withSubscribersEnvironment {
                event.payload.let { payload ->
                    span.setChannel(event)

                    if (isNotVirtualClinic(payload)) return@withSubscribersEnvironment false.success()

                    when (payload.action) {
                        ChannelChangeAction.VIRTUAL_CLINIC_QUEUED -> {
                            val estimatedTimeInMinutes = virtualClinicMemberExperienceService.getEstimatedTime(payload.segment!!)
                            sendPush(
                                payload,
                                PUSH_TITLE_QUEUED.replace("@estimated_time", estimatedTimeInMinutes.toString()),
                                PUSH_BODY_QUEUED
                            )
                        }

                        ChannelChangeAction.VIRTUAL_CLINIC_VIDEO_CALL_STARTED -> sendPush(
                            payload, PUSH_TITLE_VIDEO_CALL_STARTED, PUSH_BODY_VIDEO_CALL_STARTED
                        )
                        else -> false.success()
                    }
                        .recordResult(span)
                }
            }
        }

    private fun isNotVirtualClinic(payload: ChannelUpsertedPayload) =
        payload.subCategory != ChannelSubCategory.VIRTUAL_CLINIC


    @FirestoreContextUsage
    private suspend fun fillChannelDocumentWithModalInformation(payload: ChannelUpsertedPayload) =
        virtualClinicMemberExperienceService.fillMemberQueueModalInformation(payload.channelId, payload.segment!!, payload.appVersion)

    private suspend fun sendPush(
        payload: ChannelUpsertedPayload,
        pushTitle: String,
        pushMessage: String
    ) = span("sendPushQueued") { span ->
        deviceService.getDeviceByPerson(payload.personId.toString())
            .flatMapPair { videCallService.findActiveByChannelId(payload.channelId) }
            .flatMap { (videoCall, device) ->
                span.setAttribute("app_version", device.appVersion ?: "")
                span.setAttribute("device_id", device.id)

                device.takeIf { it.voipToken != null && it.enableSendPushVoip() }?.let {
                    span.setAttribute("voip_token", device.voipToken!!)
                    sendFromApns(videoCall, payload, device, pushTitle, pushMessage)
                } ?: pushService.send(buildFirebaseVoipPush(device, pushTitle, pushMessage, payload.channelId, videoCall.id.toString()))
            }.coFoldException(NotFoundException::class) { false.success() }
    }

    private suspend fun sendFromApns(
        videoCall: VideoCall,
        payload: ChannelUpsertedPayload,
        device: Device,
        pushTitle: String,
        pushMessage: String
    ) = apnsService.sendPushVoip(videoCall.id.toString(), payload.channelId, device.voipToken!!).foldError(
        BadRequestException::class to {
            pushService.send(
                buildFirebaseDefaultPush(
                    device,
                    pushTitle,
                    pushMessage,
                    payload.channelId
                )
            ).map { true }
        }
    )

    private fun Device.enableSendPushVoip() = enableVoipPush() && this.appVersion != null

    private fun buildFirebaseVoipPush(
        device: Device,
        pushTitle: String,
        pushMessage: String,
        channelId: String,
        chimeId: String
    ): FirebasePush = FirebasePush(
        deviceToken = device.deviceId,
        title = pushTitle,
        body = pushMessage,
        data = buildDataFirebasePush(channelId, chimeId, device.enableSendPushVoip())
    )

    private fun buildDataFirebasePush(
        channelId: String,
        videoCallId: String,
        enableVoipPush: Boolean
    ) = if (enableVoipPush && enableFirebaseVoipPush())
        mapOf(
            "path_to_navigate" to "channel",
            "parameter" to channelId,
            "extra" to gson.toJson(mapOf(
                "voip" to "true",
                "video_call_id" to videoCallId,
                "name_caller" to "Médico(a) Alice",
                "avatar" to "https://alice-member-app-assets.s3.amazonaws.com/channels/alice_logo.png",
                "text_accept" to "Aceitar",
                "text_decline" to "Recusar",
                "callback_text" to "Ir para o chat",
                "channel_id" to channelId
            ))
        )
    else mapOf("path_to_navigate" to "channel", "parameter" to channelId)

    private fun buildFirebaseDefaultPush(
        device: Device,
        pushTitle: String,
        pushMessage: String,
        channelId: String,
    ): FirebasePush = FirebasePush(
        deviceToken = device.deviceId,
        title = pushTitle,
        body = pushMessage,
        data = mapOf("path_to_navigate" to "channel", "parameter" to channelId)
    )


    private fun enableVoipPush() = FeatureService.get(
        namespace = FeatureNamespace.CHANNELS,
        key = "enable_voip_push",
        defaultValue = false
    )

    private fun enableFirebaseVoipPush() = FeatureService.get(
        namespace = FeatureNamespace.CHANNELS,
        key = "enable-firebase-voip-push",
        defaultValue = false
    )
}
