package br.com.alice.channel.assistance.consumers

import br.com.alice.channel.assistance.services.VideoCallServiceImpl
import br.com.alice.channel.assistance.services.internal.VirtualClinicMemberExperienceService
import br.com.alice.channel.core.consumers.ConsumerTest
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.models.ChannelSegment
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.serialization.gson
import br.com.alice.communication.apns.ApnsService
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.Device
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.AfterTest
import kotlin.test.Test

class VirtualClinicMemberQueueExperienceConsumerTest : ConsumerTest() {

    private val virtualClinicMemberExperienceService: VirtualClinicMemberExperienceService = mockk()
    private val pushService: PushService = mockk()
    private val deviceService: DeviceService = mockk()
    private val videCallService: VideoCallServiceImpl = mockk()
    private val apnsService: ApnsService = mockk()

    private val consumer = VirtualClinicMemberQueueExperienceConsumer(
        virtualClinicMemberExperienceService,
        pushService,
        deviceService,
        videCallService,
        apnsService
    )

    private val channelId = "channelId"
    private val personId = PersonId()
    private val staffId = RangeUUID.generate()
    private val segment = ChannelSegment.ADULT
    private val payload = ChannelUpsertedPayload(
        channelId = channelId,
        personId = personId,
        staffIds = listOf(staffId),
        subCategory = ChannelSubCategory.VIRTUAL_CLINIC,
        action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUED,
        type = ChannelType.ASSISTANCE_CARE,
        status = ChannelStatus.ACTIVE,
        segment = segment,
        appVersion = "3.71.0"
    )

    private val device = Device(personId = personId, deviceId = "device_id")
    private val sentMessageId = "sent_message_id"
    private val videoCall = TestModelFactory.buildVideoCall(channelId = channelId, personId = personId)

    @AfterTest
    fun confirmMocks() {
        confirmVerified(
            virtualClinicMemberExperienceService,
            pushService,
            deviceService
        )
        clearAllMocks()
    }

    @Test
    fun `#handleVirtualClinicMemberQueueExperience returns false when event sub category is not a virtual clinic`() = runBlocking {
        val event = ChannelUpsertedEvent(payload.copy(subCategory = ChannelSubCategory.ACUTE))

        val result = consumer.handleVirtualClinicMemberQueueExperience(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { virtualClinicMemberExperienceService wasNot called }
    }

    @Test
    fun `#handleVirtualClinicMemberQueueExperience returns false when event action is not a virtual clinic queued`() = runBlocking {
        val event = ChannelUpsertedEvent(
            payload.copy(
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC,
                action = ChannelChangeAction.VIRTUAL_CLINIC_CAUGHT
            )
        )

        val result = consumer.handleVirtualClinicMemberQueueExperience(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { virtualClinicMemberExperienceService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#handleVirtualClinicMemberQueueExperience returns channelId after process properly with valid app version`() = runBlocking {
        val event = ChannelUpsertedEvent(
            payload.copy(
                appVersion = "3.75.0"
            )
        )

        coEvery {
            virtualClinicMemberExperienceService.fillMemberQueueModalInformation(channelId, segment, event.payload.appVersion)
        } returns channelId.success()

        val result = consumer.handleVirtualClinicMemberQueueExperience(event)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { virtualClinicMemberExperienceService.fillMemberQueueModalInformation(any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#handleVirtualClinicMemberQueueExperience throws error after process properly with valid app version`() = runBlocking {
        val event = ChannelUpsertedEvent(
            payload.copy(
                appVersion = "3.75.0"
            )
        )

        coEvery {
            virtualClinicMemberExperienceService.fillMemberQueueModalInformation(channelId, segment, event.payload.appVersion)
        } returns Exception("ex").failure()

        val result = consumer.handleVirtualClinicMemberQueueExperience(event)
        assertThat(result).isFailureOfType(Exception::class)

        coVerifyOnce { virtualClinicMemberExperienceService.fillMemberQueueModalInformation(any(), any(), any()) }
    }

    @Test
    fun `#sendPushForQueueSpecificStatus returns false when event sub category is not a virtual clinic`() = runBlocking {
        val event = ChannelUpsertedEvent(payload.copy(subCategory = ChannelSubCategory.ACUTE))

        val result = consumer.sendPushForQueueSpecificStatus(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { pushService wasNot called }
        coVerify { deviceService wasNot called }
    }

    @Test
    fun `#sendPushForQueueSpecificStatus returns false when event action is not a virtual clinic queued`() = runBlocking {
        val event = ChannelUpsertedEvent(
            payload.copy(
                subCategory = ChannelSubCategory.VIRTUAL_CLINIC,
                action = ChannelChangeAction.VIRTUAL_CLINIC_CAUGHT
            )
        )

        val result = consumer.sendPushForQueueSpecificStatus(event)
        assertThat(result).isSuccessWithData(false)

        coVerify { pushService wasNot called }
        coVerify { deviceService wasNot called }
    }

    @Test
    fun `#sendPushForQueueSpecificStatus returns false if does not have device id`() = runBlocking {
        val event = ChannelUpsertedEvent(
            payload.copy(
                appVersion = "3.75.0"
            )
        )

        coEvery {
            virtualClinicMemberExperienceService.getEstimatedTime(event.payload.segment!!)
        } returns 10L

        coEvery {
            deviceService.getDeviceByPerson(event.payload.personId.toString())
        } returns NotFoundException("not_found").failure()

        val result = consumer.sendPushForQueueSpecificStatus(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { virtualClinicMemberExperienceService.getEstimatedTime(any()) }
        coVerify { pushService wasNot called }
        coVerifyOnce { deviceService.getDeviceByPerson(any()) }
    }

    @Test
    fun `#sendPushForQueueSpecificStatus returns string after send push properly for QUEUED status`() = runBlocking {
        val mapFlags = mapOf(
            "enable_voip_push" to false,
            "enable-firebase-voip-push" to false,
        )
        withFeatureFlags(FeatureNamespace.CHANNELS, mapFlags) {
            val event = ChannelUpsertedEvent(
                payload.copy(
                    appVersion = "3.75.0"
                )
            )

            coEvery {
                virtualClinicMemberExperienceService.getEstimatedTime(event.payload.segment!!)
            } returns 10L

            coEvery {
                videCallService.findActiveByChannelId(event.payload.channelId)
            } returns videoCall.success()

            coEvery {
                deviceService.getDeviceByPerson(event.payload.personId.toString())
            } returns device.copy(appVersion = "3.60.0").success()

            coEvery {
                pushService.send(
                    FirebasePush(
                        deviceToken = device.deviceId,
                        title = "Menos de 10 minutos",
                        body = "Procure por um lugar reservado e silencioso.",
                        data = mapOf(
                            "path_to_navigate" to "channel",
                            "parameter" to payload.channelId
                        )
                    )
                )
            } returns sentMessageId.success()

            val result = consumer.sendPushForQueueSpecificStatus(event)
            assertThat(result).isSuccessWithData(sentMessageId)

            coVerifyOnce { virtualClinicMemberExperienceService.getEstimatedTime(any()) }
            coVerifyOnce { videCallService.findActiveByChannelId(any()) }
            coVerifyOnce { pushService.send(any()) }
            coVerifyOnce { deviceService.getDeviceByPerson(any()) }
        }
    }

    @Test
    fun `#sendPushForQueueSpecificStatus returns string after send push properly for VIRTUAL_CLINIC_VIDEO_CALL_STARTED status`() =
        runBlocking {
            val mapFlags = mapOf(
                "enable_voip_push" to false,
                "enable-firebase-voip-push" to false,
            )
            withFeatureFlags(FeatureNamespace.CHANNELS, mapFlags) {
                val event = ChannelUpsertedEvent(
                    payload.copy(
                        appVersion = "3.75.0",
                        action = ChannelChangeAction.VIRTUAL_CLINIC_VIDEO_CALL_STARTED
                    )
                )

                coEvery {
                    deviceService.getDeviceByPerson(event.payload.personId.toString())
                } returns device.copy(appVersion = "3.60.0").success()

                coEvery {
                    videCallService.findActiveByChannelId(event.payload.channelId)
                } returns videoCall.success()

                coEvery {
                    pushService.send(
                        FirebasePush(
                            deviceToken = device.deviceId,
                            title = "O médico está te esperando",
                            body = "Está na hora da sua consulta",
                            data = mapOf(
                                "path_to_navigate" to "channel",
                                "parameter" to payload.channelId
                            )
                        )
                    )
                } returns sentMessageId.success()

                val result = consumer.sendPushForQueueSpecificStatus(event)
                assertThat(result).isSuccessWithData(sentMessageId)

                coVerifyOnce { pushService.send(any()) }
                coVerifyOnce { videCallService.findActiveByChannelId(any()) }
                coVerifyOnce { deviceService.getDeviceByPerson(any()) }
            }
        }

    @Test
    fun `#sendPushForQueueSpecificStatus should add extra data in FirebasePush when both voip flags is on`() =
        runBlocking {
            val mapFlags = mapOf(
                "enable_voip_push" to true,
                "enable-firebase-voip-push" to true,
            )
            withFeatureFlags(FeatureNamespace.CHANNELS, mapFlags) {
                val event = ChannelUpsertedEvent(
                    payload.copy(
                        appVersion = "3.75.0",
                        action = ChannelChangeAction.VIRTUAL_CLINIC_VIDEO_CALL_STARTED
                    )
                )

                coEvery {
                    deviceService.getDeviceByPerson(event.payload.personId.toString())
                } returns device.copy(appVersion = "3.60.0").success()

                coEvery {
                    videCallService.findActiveByChannelId(event.payload.channelId)
                } returns videoCall.success()

                coEvery {
                    pushService.send(
                        FirebasePush(
                            deviceToken = device.deviceId,
                            title = "O médico está te esperando",
                            body = "Está na hora da sua consulta",
                            data = mapOf(
                                "path_to_navigate" to "channel",
                                "parameter" to payload.channelId,
                                "extra" to gson.toJson(mapOf(
                                    "voip" to "true",
                                    "video_call_id" to videoCall.id.toString(),
                                    "name_caller" to "Médico(a) Alice",
                                    "avatar" to "https://alice-member-app-assets.s3.amazonaws.com/channels/alice_logo.png",
                                    "text_accept" to "Aceitar",
                                    "text_decline" to "Recusar",
                                    "callback_text" to "Ir para o chat",
                                    "channel_id" to payload.channelId
                                ))
                            )
                        )
                    )
                } returns sentMessageId.success()

                val result = consumer.sendPushForQueueSpecificStatus(event)
                assertThat(result).isSuccessWithData(sentMessageId)

                coVerifyOnce { pushService.send(any()) }
                coVerifyOnce { videCallService.findActiveByChannelId(any()) }
                coVerifyOnce { deviceService.getDeviceByPerson(any()) }
            }
        }

    @Test
    fun `#sendPushForQueueSpecificStatus should send voip push from APNS when FF enable_voip_push is on`() =
        runBlocking {
            val mapFlags = mapOf(
                "enable_voip_push" to true,
                "enable-firebase-voip-push" to true,
            )
            withFeatureFlags(FeatureNamespace.CHANNELS, mapFlags) {
                val event = ChannelUpsertedEvent(
                    payload.copy(
                        appVersion = "3.75.0",
                        action = ChannelChangeAction.VIRTUAL_CLINIC_VIDEO_CALL_STARTED
                    )
                )
                val voipToken = "token"

                coEvery {
                    deviceService.getDeviceByPerson(event.payload.personId.toString())
                } returns device.copy(voipToken = voipToken, appVersion = "3.60.0").success()

                coEvery {
                    videCallService.findActiveByChannelId(event.payload.channelId)
                } returns videoCall.success()

                coEvery {
                    apnsService.sendPushVoip(
                        videoCall.id.toString(),
                        event.payload.channelId,
                        voipToken
                    )
                } returns true.success()

                val result = consumer.sendPushForQueueSpecificStatus(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { videCallService.findActiveByChannelId(any()) }
                coVerifyOnce { deviceService.getDeviceByPerson(any()) }
                coVerifyOnce { apnsService.sendPushVoip(any(), any(), any()) }
                coVerifyNone { pushService.send(any()) }
            }
        }

    @Test
    fun `#sendPushForQueueSpecificStatus should send voip Default Push Firebase if Apns return BadRequest`() =
        runBlocking {
            val mapFlags = mapOf(
                "enable_voip_push" to true,
                "enable-firebase-voip-push" to true,
            )
            withFeatureFlags(FeatureNamespace.CHANNELS, mapFlags) {
                val event = ChannelUpsertedEvent(
                    payload.copy(
                        appVersion = "3.75.0",
                        action = ChannelChangeAction.VIRTUAL_CLINIC_VIDEO_CALL_STARTED
                    )
                )
                val voipToken = "token"

                coEvery {
                    deviceService.getDeviceByPerson(event.payload.personId.toString())
                } returns device.copy(voipToken = voipToken, appVersion = "3.60.0").success()

                coEvery {
                    videCallService.findActiveByChannelId(event.payload.channelId)
                } returns videoCall.success()

                coEvery {
                    apnsService.sendPushVoip(
                        videoCall.id.toString(),
                        event.payload.channelId,
                        voipToken
                    )
                } returns BadRequestException("error").failure()

                coEvery {
                    pushService.send(
                        FirebasePush(
                            deviceToken = device.deviceId,
                            title = "O médico está te esperando",
                            body = "Está na hora da sua consulta",
                            data = mapOf(
                                "path_to_navigate" to "channel",
                                "parameter" to payload.channelId
                            )
                        )
                    )
                } returns sentMessageId.success()

                val result = consumer.sendPushForQueueSpecificStatus(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { videCallService.findActiveByChannelId(any()) }
                coVerifyOnce { deviceService.getDeviceByPerson(any()) }
                coVerifyOnce { apnsService.sendPushVoip(any(), any(), any()) }
                coVerifyOnce { pushService.send(any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#handleVirtualClinicMemberQueueExperience returns channelId after process properly drop process with valid app version`() = runBlocking {
        val event = ChannelUpsertedEvent(
            payload.copy(
                appVersion = "3.75.0",
                action = ChannelChangeAction.MEMBER_QUEUE_EXPERIENCE_DROP_BEFORE_VIDEO_CALL
            )
        )

        coEvery {
            virtualClinicMemberExperienceService.dropMemberFromQueue(channelId)
        } returns channelId.success()

        val result = consumer.handleVirtualClinicMemberQueueExperience(event)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce { virtualClinicMemberExperienceService.dropMemberFromQueue(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#handleVirtualClinicMemberQueueExperience returns channelId after process properly decline process with valid app version`() = runBlocking {
            val event = ChannelUpsertedEvent(
                payload.copy(
                    appVersion = "3.75.0",
                    action = ChannelChangeAction.VIRTUAL_CLINIC_MEMBER_DECLINE_VIDEO_CALL
                )
            )

            coEvery {
                virtualClinicMemberExperienceService.onMemberRejectVideoCall(channelId)
            } returns channelId.success()

            val result = consumer.handleVirtualClinicMemberQueueExperience(event)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { virtualClinicMemberExperienceService.onMemberRejectVideoCall(any()) }
    }

    @Nested
    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    inner class SendPushForQueueSpecificStatusTests {

        private val mapFlags = mutableMapOf<String, Any>()
        private val deviceAppVersion = "3.60.5"

        private fun withoutExtraDataTestParameters() = listOf(
            arrayOf(
                "when both enable voip flags are off",
                mapFlags.plus(
                    mapOf(
                        "enable_voip_push" to false,
                        "enable-firebase-voip-push" to false
                    )
                ),
                deviceAppVersion
            ),
            arrayOf(
                "when enable_voip_push is on and enable-firebase-voip-push is off",
                mapFlags.plus(
                    mapOf(
                        "enable_voip_push" to true,
                        "enable-firebase-voip-push" to false
                    )
                ),
                deviceAppVersion
            ),
            arrayOf(
                "when enable_voip_push is off and enable-firebase-voip-push is on",
                mapFlags.plus(
                    mapOf(
                        "enable_voip_push" to false,
                        "enable-firebase-voip-push" to true
                    )
                ),
                deviceAppVersion
            )
        )

        @ParameterizedTest(name = "{0}")
        @MethodSource("withoutExtraDataTestParameters")
        fun `#should send firebase push without data extra`(
            testName: String,
            flags: Map<String, Any>,
            deviceAppVersion: String
        ) = runBlocking {
            withFeatureFlags(FeatureNamespace.CHANNELS, flags) {
                val event = ChannelUpsertedEvent(
                    payload.copy(
                        appVersion = deviceAppVersion,
                        action = ChannelChangeAction.VIRTUAL_CLINIC_VIDEO_CALL_STARTED
                    )
                )

                coEvery {
                    deviceService.getDeviceByPerson(event.payload.personId.toString())
                } returns device.copy(appVersion = deviceAppVersion).success()

                coEvery {
                    videCallService.findActiveByChannelId(event.payload.channelId)
                } returns videoCall.success()

                coEvery {
                    pushService.send(
                        FirebasePush(
                            deviceToken = device.deviceId,
                            title = "O médico está te esperando",
                            body = "Está na hora da sua consulta",
                            data = mapOf(
                                "path_to_navigate" to "channel",
                                "parameter" to payload.channelId
                            )
                        )
                    )
                } returns sentMessageId.success()

                val result = consumer.sendPushForQueueSpecificStatus(event)
                assertThat(result).isSuccessWithData(sentMessageId)

                coVerifyOnce { pushService.send(any()) }
                coVerifyOnce { videCallService.findActiveByChannelId(any()) }
                coVerifyOnce { deviceService.getDeviceByPerson(any()) }
            }
        }
    }
}
