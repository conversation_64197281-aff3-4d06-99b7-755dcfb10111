package br.com.alice.channel.assistance.services.internal

import br.com.alice.channel.assistance.services.internal.firestore.VirtualClinicQueueFirestoreService
import br.com.alice.channel.client.VideoCallService
import br.com.alice.channel.core.FirestoreTransactionalTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.mockTimestamp
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.services.internal.firestore.StaffFirestoreService
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelSegment
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.ModalInfo
import br.com.alice.channel.models.QuestionContent
import br.com.alice.channel.models.StaffDocument
import br.com.alice.channel.models.VideoCallMeeting
import br.com.alice.channel.models.VirtualClinicAutoServicePromptOptions
import br.com.alice.channel.models.VirtualClinicQueueDocument
import br.com.alice.channel.models.budTriageTag
import br.com.alice.channel.models.inProgressTag
import br.com.alice.channel.models.modalContent2
import br.com.alice.channel.notifier.MemberDropVirtualClinicEvent
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistoryExtraInfo
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ExtraInfoType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.VirtualClinicStatusControl
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.models.HealthFormQuestionOptionResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.Query
import io.mockk.Runs
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals

class VirtualClinicMemberExperienceServiceTest : FirestoreTransactionalTestHelper() {

    private val virtualClinicQueueFirestoreService: VirtualClinicQueueFirestoreService = mockk()
    private val staffFirestoreService: StaffFirestoreService = mockk()
    private val channelService: ChannelService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val channelNotificationService: ChannelNotificationService = mockk()
    private val videoCallService: VideoCallService = mockk()
    private val personService: PersonService = mockk()

    private val service = VirtualClinicMemberExperienceService(
        virtualClinicQueueFirestoreService,
        staffFirestoreService,
        messageFirestoreService,
        channelService,
        channelNotificationService,
        videoCallService,
        personService
    )

    private val staffDocument = StaffDocument(role = Role.VIRTUAL_CLINIC_PHYSICIAN)
    private val channelSegment = ChannelSegment.ADULT
    private val person = TestModelFactory.buildPerson()
    private val appVersion = "4.39.0"

    private val channelDocument = ChannelDocument(
        virtualClinicStatusControl = VirtualClinicStatusControl.QUEUED,
        channelPersonId = person.id.toString(),
        personId = person.id.toString(),
        id = channelId
    )

    private val virtualClinicQueueDocument =
        VirtualClinicQueueDocument(
            id = channelId,
            createdAt = Timestamp.now(),
            queuedAt = Timestamp.now()
        )

    private val modalContentJsonFlag = """
        [
            {"image_url":"queue_experience_selfie.svg","description":"Dê preferência a um ambiente silencioso"},
            {"image_url":"queue_experience_alice_agora.svg","description":"O profissional está conferindo suas informações"},
            {"image_url":"queue_experience_plan.svg","description":"Estamos preparando o seu atendimento"}
        ]
        """

    private val modalContentJsonFlagV2 = """
        [
            {"image_url": "queue_experience_0.svg", "description": "Dê preferência a um ambiente reservado e silencioso" },
            {"image_url": "queue_experience_1.svg", "description": "O tempo de espera é estimado e já já sua consulta vai começar" },
            {"image_url": "queue_experience_2.svg", "description": "Evite entrar na consulta sem camisa ou com trajes íntimos" },
            {"image_url": "queue_experience_3.svg", "description": "Estamos preparando o seu atendimento" }
        ]
        """

    private val flags = mapOf(
        "minimal_attendence_time_in_minutes" to 10,
        "pa_queue_dynamic_content" to modalContentJsonFlag,
        "version_min_pa_queue_dynamic_content_v2" to "5.39.0"
    )

    private val flagsWithModalV2 = mapOf(
        "minimal_attendence_time_in_minutes" to 10,
        "pa_queue_dynamic_content_v2" to modalContentJsonFlagV2,
        "version_min_pa_queue_dynamic_content_v2" to "4.39.0"
    )

    private val staff = ChannelStaffInfo.from(TestModelFactory.buildStaff(id = staffId.toUUID()))

    private val timestampNow = Timestamp.now()
    private val autoServicePromptMessageDocument = MessageDocument(
        userId = "",
        type = MessageType.QUESTION,
        question = QuestionContent(
            question = "Escolha uma opção:",
            input = QuestionnaireQuestionInputResponse(
                action = "",
                displayAttributes = null,
                options = listOf(
                    VirtualClinicAutoServicePromptOptions.BACK_VIRTUAL_CLINIC,
                    VirtualClinicAutoServicePromptOptions.ARCHIVE,
                ).map {
                    HealthFormQuestionOptionResponse(
                        label = it.description,
                        value = it.name
                    )
                },
                type = HealthFormQuestionType.OPTION_BUTTONS
            ),
            required = true
        )
    )
    private val adultVirtualClinicStaffRoles = listOf("VIRTUAL_CLINIC_PHYSICIAN", "MANAGER_PHYSICIAN")

    @AfterTest
    fun confirmMocks() = confirmVerified(
        virtualClinicQueueFirestoreService,
        staffFirestoreService,
        channelService,
        messageFirestoreService,
        channelNotificationService,
        videoCallService,
        personService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation returns document id and fill modal information on channel with configured minimal time`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(FeatureNamespace.CHANNELS, flags) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns listOf(virtualClinicQueueDocument).success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                    staffDocument,
                    staffDocument
                ).success()

                val expectedMedicalCareAt = localDateTime.plusMinutes(10L).toTimestamp()
                val positionInQueue = 1
                val expectedModalInfo = ModalInfo(
                    medicalCareAt = expectedMedicalCareAt,
                    positionInQueue = positionInQueue
                )

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::digitalCareQueueModalInfo to expectedModalInfo,
                            ChannelDocument::videoCall to null
                        )
                    )
                } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        channelId = channelId,
                        action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUE_MODAL_INFO_UPDATED,
                        extraInfo = listOf(
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.ESTIMATED_MEDICAL_CARE_AT,
                                value = expectedMedicalCareAt
                            ),
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.POSITION_IN_VIRTUAL_CLINIC_QUEUE,
                                value = positionInQueue
                            )
                        )
                    )
                } just Runs

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isSuccessWithData(channelId)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<String>(), any(), any(), any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation returns document id and fill modal information V2 on channel with configured minimal time`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(FeatureNamespace.CHANNELS, flagsWithModalV2) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns listOf(virtualClinicQueueDocument).success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                    staffDocument,
                    staffDocument
                ).success()

                val expectedMedicalCareAt = localDateTime.plusMinutes(10L).toTimestamp()
                val positionInQueue = 1
                val expectedModalInfo = ModalInfo(
                    content = modalContent2,
                    medicalCareAt = expectedMedicalCareAt,
                    positionInQueue = positionInQueue
                )

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::digitalCareQueueModalInfo to expectedModalInfo,
                            ChannelDocument::videoCall to null
                        )
                    )
                } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        channelId = channelId,
                        action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUE_MODAL_INFO_UPDATED,
                        extraInfo = listOf(
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.ESTIMATED_MEDICAL_CARE_AT,
                                value = expectedMedicalCareAt
                            ),
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.POSITION_IN_VIRTUAL_CLINIC_QUEUE,
                                value = positionInQueue
                            )
                        )
                    )
                } just Runs

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isSuccessWithData(channelId)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<String>(), any(), any(), any()) }
            }
        }


    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation returns document id and fill modal information on channel with calculated time`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(FeatureNamespace.CHANNELS, flags) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns listOf(
                    virtualClinicQueueDocument,
                    virtualClinicQueueDocument,
                    virtualClinicQueueDocument,
                    virtualClinicQueueDocument,
                    virtualClinicQueueDocument,
                    virtualClinicQueueDocument,
                ).success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                    staffDocument,
                    staffDocument
                ).success()

                val expectedMedicalCareAt = localDateTime.plusMinutes(30L).toTimestamp()
                val positionInQueue = 6
                val expectedModalInfo = ModalInfo(
                    medicalCareAt = expectedMedicalCareAt,
                    positionInQueue = positionInQueue
                )

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::digitalCareQueueModalInfo to expectedModalInfo,
                            ChannelDocument::videoCall to null
                        )
                    )
                } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        channelId = channelId,
                        action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUE_MODAL_INFO_UPDATED,
                        extraInfo = listOf(
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.ESTIMATED_MEDICAL_CARE_AT,
                                value = expectedMedicalCareAt
                            ),
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.POSITION_IN_VIRTUAL_CLINIC_QUEUE,
                                value = positionInQueue
                            )
                        )
                    )
                } just Runs

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isSuccessWithData(channelId)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<String>(), any(), any(), any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation returns document id and fill modal information on channel when staff is empty`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(FeatureNamespace.CHANNELS, flags) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns listOf(virtualClinicQueueDocument).success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns emptyList<StaffDocument>().success()

                val expectedMedicalCareAt = localDateTime.plusMinutes(10L).toTimestamp()
                val positionInQueue = 1

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::digitalCareQueueModalInfo to ModalInfo(
                                medicalCareAt = expectedMedicalCareAt,
                                positionInQueue = positionInQueue
                            ),
                            ChannelDocument::videoCall to null
                        )
                    )
                } returns channelId.success()
                coEvery {
                    channelNotificationService.notify(
                        channelId = channelId,
                        action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUE_MODAL_INFO_UPDATED,
                        extraInfo = listOf(
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.ESTIMATED_MEDICAL_CARE_AT,
                                value = expectedMedicalCareAt
                            ),
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.POSITION_IN_VIRTUAL_CLINIC_QUEUE,
                                value = positionInQueue
                            )
                        )
                    )
                } just Runs

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isSuccessWithData(channelId)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<String>(), any(), any(), any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation returns document id and fill modal information on channel when queue is empty`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(FeatureNamespace.CHANNELS, flags) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns emptyList<VirtualClinicQueueDocument>().success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                    staffDocument,
                    staffDocument
                ).success()

                val expectedMedicalCareAt = localDateTime.plusMinutes(10L).toTimestamp()
                val positionInQueue = 1

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::digitalCareQueueModalInfo to ModalInfo(
                                medicalCareAt = expectedMedicalCareAt,
                                positionInQueue = positionInQueue
                            ),
                            ChannelDocument::videoCall to null
                        )
                    )
                } returns channelId.success()

                coEvery {
                    channelNotificationService.notify(
                        channelId = channelId,
                        action = ChannelChangeAction.VIRTUAL_CLINIC_QUEUE_MODAL_INFO_UPDATED,
                        extraInfo = listOf(
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.ESTIMATED_MEDICAL_CARE_AT,
                                value = expectedMedicalCareAt
                            ),
                            ChannelHistoryExtraInfo(
                                key = ExtraInfoType.POSITION_IN_VIRTUAL_CLINIC_QUEUE,
                                value = positionInQueue
                            )
                        )
                    )
                } just Runs

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isSuccessWithData(channelId)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
                coVerifyOnce { channelNotificationService.notify(any<String>(), any(), any(), any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation throws error when get staffs available throws error`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
        every {
            collectionReference.whereIn(
                "role",
                adultVirtualClinicStaffRoles
            )
        } returns collectionReference

        coEvery { staffFirestoreService.find(capture(functionSlot)) } returns Exception("ex").failure()

        val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
        assertThat(result).isFailureOfType(Exception::class)

        functionSlot.captured.invoke(collectionReference)

        coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
        coVerifyOnce { staffFirestoreService.find(any()) }
        coVerify { virtualClinicQueueFirestoreService wasNot called }
        coVerify { channelService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation throws error when get queue throws error`() = runBlocking {
        coEvery {
            virtualClinicQueueFirestoreService.getQueue(channelSegment)
        } returns Exception("ex").failure()

        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
        every {
            collectionReference.whereIn(
                "role",
                adultVirtualClinicStaffRoles
            )
        } returns collectionReference

        coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
            staffDocument,
            staffDocument
        ).success()

        val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
        assertThat(result).isFailureOfType(Exception::class)

        functionSlot.captured.invoke(collectionReference)

        coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
        coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
        coVerifyOnce { staffFirestoreService.find(any()) }
        coVerify { channelService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation throws error when update fields throws error`() =
        mockLocalDateTime { localDateTime ->
            withFeatureFlags(FeatureNamespace.CHANNELS, flags) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns emptyList<VirtualClinicQueueDocument>().success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                    staffDocument,
                    staffDocument
                ).success()

                val expectedMedicalCareAt = localDateTime.plusMinutes(10L).toTimestamp()

                coEvery {
                    channelService.updateFields(
                        channelId,
                        mapOf(
                            ChannelDocument::digitalCareQueueModalInfo to ModalInfo(
                                medicalCareAt = expectedMedicalCareAt,
                                positionInQueue = 1
                            ),
                            ChannelDocument::videoCall to null
                        )
                    )
                } returns Exception("ex").failure()

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isFailureOfType(Exception::class)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyOnce { channelService.updateFields(any(), any()) }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dropMemberFromQueue returns document id after set channel as inactive`() = mockFeatureFlagsAndTimestamp(
        mapOf(
            "virtual_clinic_drop_before_call_return_message" to "@firstname, envie uma mensagem no chat para continuar"
        )
    ) { now ->
        val channel = channelDocument.copy(
            virtualClinicStatusControl = VirtualClinicStatusControl.DROP_BEFORE_VIDEO_CALL,
            staff = mutableMapOf(staffId to staff)
        )

        coEvery {
            channelService.getChannel(channelId)
        } returns channel.success()

        coEvery {
            virtualClinicQueueFirestoreService.remove(channelId)
        } returns channelId.success()

        coEvery {
            personService.get(person.id)
        } returns person.success()

        coEvery {
            val message = autoServicePromptMessageDocument.copy(
                createdAt = now,
                content = "${person.firstName}, envie uma mensagem no chat para continuar"
            )
            messageFirestoreService.add(channelId, message)
        } returns "message_id".success()

        coEvery {
            channelService.updateFields(
                channelId,
                mapOf(
                    ChannelDocument::subCategory to ChannelSubCategory.SCREENING,
                    ChannelDocument::subCategoryClassifier to null,
                    ChannelDocument::routingTo to emptyList<String>(),
                    ChannelDocument::hideMemberInput to true,
                    ChannelDocument::canBeArchived to true,
                    ChannelDocument::digitalCareQueueModalInfo to null,
                    ChannelDocument::tags to listOf(inProgressTag, budTriageTag),
                    ChannelDocument::inactiveAt to now,
                    ChannelDocument::isWaiting to false,
                )
            )
        } returns channelId.success()

        coEvery {
            channelService.removeStaff(
                channel = channel,
                staffId = staffId,
                requesterStaffId = staffId,
                notify = false,
                checkOwner = false,
                checkSize = false
            )
        } returns channelId.success()

        coEvery {
            channelNotificationService.produceGenericEvent(
                MemberDropVirtualClinicEvent(channel)
            )
        } returns mockk()

        val result = service.dropMemberFromQueue(channelId)
        assertThat(result).isSuccessWithData(channelId)

        coVerifyOnce {
            channelService.getChannel(any())
            virtualClinicQueueFirestoreService.remove(any())
            channelService.updateFields(any(), any())
            messageFirestoreService.add(any(), any())
            channelService.removeStaff(any(), any(), any(), any(), any(), any())
            channelNotificationService.produceGenericEvent(any())
            personService.get(any())
        }
        coVerifyNone { channelService.archiveChannel(any<String>(), any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dropMemberFromQueue returns document id after drop from queue and do not archive channel`() =
        runBlocking {
            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.copy(
                virtualClinicStatusControl = VirtualClinicStatusControl.MEMBER_QUEUE_EXPERIENCE_READY_TO_START_VIDEO_CALL
            ).success()

            coEvery {
                virtualClinicQueueFirestoreService.remove(channelId)
            } returns channelId.success()

            val result = service.dropMemberFromQueue(channelId)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce { channelService.getChannel(any()) }
            coVerifyOnce { virtualClinicQueueFirestoreService.remove(any()) }
            coVerifyNone { channelService.archiveChannel(any<String>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#dropMemberFromQueue throws error when get error removing from queue`() =
        runBlocking {
            coEvery {
                channelService.getChannel(channelId)
            } returns channelDocument.success()

            coEvery {
                virtualClinicQueueFirestoreService.remove(channelId)
            } returns Exception("error").failure()

            val result = service.dropMemberFromQueue(channelId)
            assertThat(result).isFailureOfType(Exception::class)

            coVerifyOnce { channelService.getChannel(any()) }
            coVerifyOnce { virtualClinicQueueFirestoreService.remove(any()) }
            coVerifyNone { channelService.archiveChannel(any<String>(), any(), any(), any()) }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#onMemberRejectVideoCall should inactivate channel and finish video call when member rejects`() =
        mockFeatureFlagsAndTimestamp(
            mapOf(
                "virtual_clinic_video_call_rejected_return_message" to "@firstname, envie uma mensagem no chat para continuar"
            )
        ) { now ->
            val channel = channelDocument.copy(
                virtualClinicStatusControl = VirtualClinicStatusControl.VIDEO_CALL_REJECTED,
                staff = mutableMapOf(staffId to staff)
            )
            val videoCall = VideoCallMeeting(videoCall = TestModelFactory.buildVideoCall())

            coEvery {
                channelService.getChannel(channelId)
            } returns channel.success()

            coEvery {
                channelService.updateFields(
                    channelId,
                    mapOf(
                        ChannelDocument::subCategory to ChannelSubCategory.SCREENING,
                        ChannelDocument::subCategoryClassifier to null,
                        ChannelDocument::routingTo to emptyList<String>(),
                        ChannelDocument::hideMemberInput to true,
                        ChannelDocument::canBeArchived to true,
                        ChannelDocument::digitalCareQueueModalInfo to null,
                        ChannelDocument::tags to listOf(inProgressTag, budTriageTag),
                        ChannelDocument::inactiveAt to now,
                        ChannelDocument::isWaiting to false,
                    )
                )
            } returns channelId.success()

            coEvery {
                personService.get(person.id)
            } returns person.success()

            coEvery {
                val message = autoServicePromptMessageDocument.copy(
                    createdAt = now,
                    content = "${person.firstName}, envie uma mensagem no chat para continuar"
                )
                messageFirestoreService.add(channelId, message)
            } returns "message_id".success()

            coEvery {
                channelService.removeStaff(
                    channel = channel,
                    staffId = staffId,
                    requesterStaffId = staffId,
                    notify = false,
                    checkOwner = false,
                    checkSize = false
                )
            } returns channelId.success()

            coEvery {
                channelNotificationService.produceGenericEvent(
                    MemberDropVirtualClinicEvent(channel)
                )
            } returns mockk()

            coEvery {
                videoCallService.endMeetingByChannel(channelId)
            } returns videoCall.success()

            val result = service.onMemberRejectVideoCall(channelId)
            assertThat(result).isSuccessWithData(channelId)

            coVerifyOnce {
                channelService.getChannel(any())
                channelService.updateFields(any(), any())
                messageFirestoreService.add(any(), any())
                channelService.removeStaff(any(), any(), any(), any(), any(), any())
                channelNotificationService.produceGenericEvent(any())
                videoCallService.endMeetingByChannel(any())
                personService.get(any())
            }
        }

    @Test
    fun `#getEstimatedTime returns estimated time`() = runBlocking {
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        coEvery {
            virtualClinicQueueFirestoreService.getQueue(channelSegment)
        } returns listOf(
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument
        ).success()

        every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
        every {
            collectionReference.whereIn(
                "role",
                adultVirtualClinicStaffRoles
            )
        } returns collectionReference

        coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
            staffDocument,
            staffDocument
        ).success()

        val result = service.getEstimatedTime(channelSegment)
        assertEquals(result, 30L)

        functionSlot.captured.invoke(collectionReference)

        coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
        coVerifyOnce { staffFirestoreService.find(any()) }
        coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
    }

    @Test
    fun `#getEstimatedTime returns estimated time for pediatric queue`() = runBlocking {
        val channelSegment = ChannelSegment.PEDIATRIC
        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        coEvery {
            virtualClinicQueueFirestoreService.getQueue(channelSegment)
        } returns listOf(
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument,
            virtualClinicQueueDocument
        ).success()

        every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
        every {
            collectionReference.whereIn(
                "role",
                listOf("VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN")
            )
        } returns collectionReference

        coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
            staffDocument,
            staffDocument
        ).success()

        val result = service.getEstimatedTime(channelSegment)
        assertEquals(result, 30L)

        functionSlot.captured.invoke(collectionReference)

        coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
        coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
        coVerifyOnce { staffFirestoreService.find(any()) }
        coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#fillMemberQueueModalInformation returns nullpointer when feature flag is empty`() =
        runBlocking {
            withFeatureFlag(FeatureNamespace.CHANNELS, "minimal_attendence_time_in_minutes", 10) {
                coEvery {
                    virtualClinicQueueFirestoreService.getQueue(channelSegment)
                } returns listOf(virtualClinicQueueDocument).success()

                val functionSlot = slot<suspend (CollectionReference) -> Query>()

                every { collectionReference.whereEqualTo("status", "AVAILABLE") } returns collectionReference
                every {
                    collectionReference.whereIn(
                        "role",
                        adultVirtualClinicStaffRoles
                    )
                } returns collectionReference

                coEvery { staffFirestoreService.find(capture(functionSlot)) } returns listOf(
                    staffDocument,
                    staffDocument
                ).success()

                val result = service.fillMemberQueueModalInformation(channelId, channelSegment, appVersion)
                assertThat(result).isFailureOfType(NullPointerException::class)

                functionSlot.captured.invoke(collectionReference)

                coVerifyOnce { collectionReference.whereEqualTo(any<String>(), any()) }
                coVerifyOnce { collectionReference.whereIn(any<String>(), any()) }
                coVerifyOnce { virtualClinicQueueFirestoreService.getQueue(any()) }
                coVerifyOnce { staffFirestoreService.find(any()) }
                coVerifyNone { channelService.updateFields(any(), any()) }
            }
        }

    private fun mockFeatureFlagsAndTimestamp(flags: Map<String, Any>, block: suspend (v: Timestamp) -> Unit) =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.CHANNELS to flags
            ) {
                mockTimestamp(timestampNow) {
                    block.invoke(timestampNow)
                }
            }
        }
}
