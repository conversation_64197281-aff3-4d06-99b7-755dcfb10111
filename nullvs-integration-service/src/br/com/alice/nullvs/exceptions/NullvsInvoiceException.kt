package br.com.alice.nullvs.exceptions

import br.com.alice.common.core.exceptions.BadRequestException
import java.util.UUID

class InvalidNullvsInvoiceRequestForPaymentUpdate(
    message: String = "NullvsInvoiceRequest has one or more required fields nullable",
    code: String = "invalid_nullvs_invoice_request_for_payment_update",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class InvalidNullvsInvoiceRequestForCreateInvoice(
    message: String = "NullvsInvoiceRequest has one or more required fields nullable",
    code: String = "invalid_nullvs_invoice_request_for_create_invoice",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class SubcontractIsNotActivatedYet(
    message: String,
    code: String = "subcontract_is_not_activated_yet",
    cause: Throwable? = null
) :
    BadRequestException(message, code, cause) {
    constructor(companyId: UUID, subcontractId: UUID) : this(
        message = "The subcontract is not activated yet. companyId: $companyId, subcontractId: $subcontractId",
    )
}

class InvoiceItemAlreadyIntegrated(
    message: String,
    code: String = "invoice_item_already_integrated",
    cause: Throwable? = null
) :
    BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(message = "This invoice item already integrated: $id")
}

class FirstPaymentInvoiceItemException(
    message: String,
    code: String = "first_payment_invoice_item",
    cause: Throwable? = null
) :
    BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(message = "This invoice item is for the first payment. Id: $id")
}

class InvoiceItemWasNotIntegrated(
    message: String,
    code: String = "invoice_item_was_not_integrated",
    cause: Throwable? = null
) :
    BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(
        message = "This invoice item was not integrated: $id",
    )
}

class InvalidInvoiceItemIntegration(
    message: String,
    code: String = "invalid_invoice_item_integration",
    cause: Throwable? = null
) :
    BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(
        message = "This invoice item can't integrated once it does not belong to any subcontract. id: $id",
    )
}

class PreActivationPaymentExternalIdNullException(
    message: String,
    code: String = "pre_activation_payment_external_id_null",
    cause: Throwable? = null
) :
    BadRequestException(message, code, cause) {
    constructor(id: UUID) : this(
        message = "The pre activation payment external id is null. id: $id",
    )
}
