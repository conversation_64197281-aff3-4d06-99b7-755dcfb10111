package br.com.alice.nullvs.consumers.invoice

import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.nullvs.clients.TotvsInvoiceClient
import br.com.alice.nullvs.consumers.AutoRetryableConsumer
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.events.NullvsScheduleFirstPaymentResponseEvent
import br.com.alice.nullvs.exceptions.TotvsInvoiceClientPostException
import com.github.kittinunf.result.map

class FirstPaymentScheduleConsumer(
    private val totvsInvoiceClient: TotvsInvoiceClient,
    private val kafkaProducerService: KafkaProducerService,
) :
    AutoRetryableConsumer(TotvsInvoiceClientPostException::class) {

    suspend fun processFirstPaymentScheduled(event: FirstPaymentScheduleCreatedEvent) = withSubscribersEnvironment {
        val request = event.toTotvsScheduleFirstPaymentRequest()

        totvsInvoiceClient.scheduleFirstPayment(request)
            .map {
                kafkaProducerService.produce(
                    NullvsScheduleFirstPaymentResponseEvent(
                        firstPaymentScheduleId = it.aliceId.trim().toUUID(),
                        externalId = it.invoiceScheduleId,
                        error = it.message,
                    )
                )
            }
    }
}
