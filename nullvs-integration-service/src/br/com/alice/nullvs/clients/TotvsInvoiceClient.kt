package br.com.alice.nullvs.clients

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.common.encodeBase64
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequestResponse
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsInvoiceRequest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsReceivableIntegrationRequest
import br.com.alice.nullvs.exceptions.TotvsInvoiceClientPostException
import br.com.alice.nullvs.exceptions.TotvsPostBatchDuplicationException
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.models.TotvsIntegrationBatchResponse
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.HttpClient
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.request.headers
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.contentType


class TotvsInvoiceClient(
    private val baseUrl: String,
    private val secretKey: String,
    private val client: HttpClient,
) {

    suspend fun postInvoice(request: NullvsInvoiceBatchRequest): Result<NullvsInvoiceBatchResponse, Throwable> {
        logger.info("TotvsInvoiceClient::postInvoice - starting post invoice request to totvs")

        return try {
            val convertToTotvsInvoiceBatchRequest = request.toTotvsInvoiceRequest().also {
                logger.info(
                    "Request to postInvoice in TOTVS",
                    "url" to baseUrl,
                    "body" to encodeBase64(gson.toJson(it)),
                    "request" to encodeBase64(it.toString()),
                    "id_soc" to it.idSoc
                )
            }

            val responseString = client.post(baseUrl) {
                headers {
                    append("Authorization", "Basic $secretKey")
                    append("tenantID", "01,01")
                }
                contentType(ContentType.Application.Json)
                setBody(convertToTotvsInvoiceBatchRequest)
            }.bodyAsText()

            logger.info("totvs response", "response" to encodeBase64(responseString))

            val response: TotvsIntegrationBatchResponse = gson.fromJson(responseString)

            TotvsIntegrationMetric.countTotvsIntegration(Method.INVOICE, Status.SUCCESS)
            response.toNullvsInvoiceRequestResponse(request.meta).success()

        } catch (e: ClientRequestException) {
            logger.error("TotvsInvoiceClient::postInvoice - post invoice request to totvs failed", e)

            TotvsIntegrationMetric.countTotvsIntegration(Method.INVOICE, Status.FAILURE)

            if (e.message.lowercase().contains("o lote de id lote") && e.message.lowercase()
                    .contains("já existe no protheus a partir do dia")
            ) {
                TotvsPostBatchDuplicationException(request.action.toString(), "invoice").failure()
            } else {
                TotvsInvoiceClientPostException(request.action, e).failure()
            }
        } catch (e: Exception) {
            logger.error("TotvsInvoiceClient::postInvoice - post invoice request to totvs failed", e)

            TotvsIntegrationMetric.countTotvsIntegration(Method.INVOICE, Status.FAILURE)
            TotvsInvoiceClientPostException(request.action, e).failure()
        }
    }

    suspend fun postInvoicePayment(request: NullvsInvoiceBatchRequest): Result<NullvsInvoiceBatchResponse, Throwable> {
        logger.info("TotvsInvoiceClient::postInvoicePayment - starting post invoice payment request to totvs")

        return try {
            val requestBody = request.toTotvsReceivableIntegrationRequest().also {
                val numbersOfTitles = it.payload.joinToString(separator = ",") { payload -> payload.numberOfTitle }
                logger.info(
                    "Request to postInvoicePayment in TOTVS",
                    "url" to baseUrl,
                    "body" to encodeBase64(gson.toJson(it)),
                    "request" to encodeBase64(it.toString()),
                    "batch_id" to it.batchId,
                    "numbers_of_titles" to numbersOfTitles
                )
            }

            val responseString = client.post(baseUrl) {
                headers {
                    append("Authorization", "Basic $secretKey")
                    append("tenantID", "01,01")
                }
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }.bodyAsText()

            logger.info("totvs response", "response" to encodeBase64(responseString))

            val response: TotvsIntegrationBatchResponse = gson.fromJson(responseString)

            TotvsIntegrationMetric.countTotvsIntegration(Method.PAYMENT, Status.SUCCESS)
            response.toNullvsInvoiceRequestResponse(request.meta).success()

        } catch (e: ClientRequestException) {
            logger.error("TotvsInvoiceClient::postInvoicePayment - post invoice payment request to totvs failed", e)

            TotvsIntegrationMetric.countTotvsIntegration(Method.PAYMENT, Status.FAILURE)

            if (e.message.lowercase().contains("o lote de id lote") and e.message.lowercase()
                    .contains("já existe no protheus a partir do dia")
            ) {
                TotvsPostBatchDuplicationException(request.action.toString(), "invoice").failure()
            } else {
                TotvsInvoiceClientPostException(request.action, e).failure()
            }
        } catch (e: Exception) {
            logger.error("TotvsInvoiceClient::reportInvoicePayment - post invoice payment request to totvs failed", e)

            TotvsIntegrationMetric.countTotvsIntegration(Method.PAYMENT, Status.FAILURE)
            TotvsInvoiceClientPostException(request.action, e).failure()
        }
    }

    suspend fun scheduleFirstPayment(request: TotvsScheduleFirstPaymentRequest): Result<TotvsScheduleFirstPaymentResponse, Throwable> {
        logger.info("TotvsInvoiceClient::scheduleFirstPayment - starting schedule the first payment request to totvs")

        return try {
            val requestBody = request.also {
                logger.info(
                    "Request to schedule the first payment creation in TOTVS",
                    "url" to baseUrl,
                    "body" to encodeBase64(gson.toJson(it)),
                    "request" to encodeBase64(it.toString()),
                    "batch_id" to it.batchId,
                )
            }

            val responseString = client.post(baseUrl) {
                headers {
                    append("Authorization", "Basic $secretKey")
                    append("tenantID", "01,01")
                }
                contentType(ContentType.Application.Json)
                setBody(requestBody)
            }.bodyAsText()

            logger.info("totvs response", "response" to encodeBase64(responseString))

            val response: TotvsScheduleFirstPaymentResponse = gson.fromJson(responseString)

            response.success()

        } catch (e: ClientRequestException) {
            val response: TotvsScheduleFirstPaymentResponse = gson.fromJson(e.response.bodyAsText())
            logger.error("TotvsInvoiceClient::scheduleFirstPayment - schedule first payment request to totvs failed", e)

            if (response.invoiceScheduleId != null) {
                TotvsPostBatchDuplicationException("The schedule already exists").failure()
            } else if (e.message.lowercase().contains("o lote de id lote") and e.message.lowercase()
                    .contains("já existe no protheus a partir do dia")
            ) {
                TotvsPostBatchDuplicationException(request.action, "invoice").failure()
            } else {
                response.success()
            }

        } catch (e: Exception) {
            logger.error("TotvsInvoiceClient::scheduleFirstPayment - schedule first payment request to totvs failed", e)

            TotvsInvoiceClientPostException(NullvsActionType.CREATE, e).failure()
        }
    }
}
