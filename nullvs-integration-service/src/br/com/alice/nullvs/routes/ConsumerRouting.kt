package br.com.alice.nullvs.routes

import br.com.alice.business.events.BeneficiaryCreatedForActiveMemberEvent
import br.com.alice.business.events.BeneficiaryUpdatedEvent
import br.com.alice.business.events.CompanyContractCreatedEvent
import br.com.alice.business.events.CompanyContractUpdatedEvent
import br.com.alice.business.events.CompanySubContractCreatedEvent
import br.com.alice.business.events.CompanySubContractUpdatedEvent
import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.business.events.NewBeneficiaryRelationshipAssignedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.interfaces.AdditionalProperties
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.moneyin.event.BillingAccountablePartyAssignedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyCreatedEvent
import br.com.alice.moneyin.event.BillingAccountablePartyUpdatedEvent
import br.com.alice.moneyin.event.InvoiceItemCanceledEvent
import br.com.alice.moneyin.event.InvoiceItemCreatedEvent
import br.com.alice.moneyin.event.InvoiceLiquidationPaidEvent
import br.com.alice.moneyin.event.MemberInvoiceGroupCanceledEvent
import br.com.alice.moneyin.event.MemberInvoiceGroupCreatedEvent
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import br.com.alice.moneyin.event.PreActivationPaymentPaidEvent
import br.com.alice.nullvs.consumers.NullvsCreateIntegrationRecordConsumer
import br.com.alice.nullvs.consumers.ProductConsumer
import br.com.alice.nullvs.consumers.client.BillingAccountablePartyConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientBatchRequestConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientBatchResponseConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientNotFoundConsumer
import br.com.alice.nullvs.consumers.client.NullvsClientWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.client.NullvsSyncClientRequestConsumer
import br.com.alice.nullvs.consumers.company.CompanyContractConsumer
import br.com.alice.nullvs.consumers.company.CompanySubContractConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanyContractBatchRequestConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanyContractBatchResponseConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanyContractWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanySubContractBatchResponseConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanySubcontractBatchRequestConsumer
import br.com.alice.nullvs.consumers.company.NullvsCompanySubcontractWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.company.NullvsPriceListingWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.invoice.FirstPaymentScheduleConsumer
import br.com.alice.nullvs.consumers.invoice.InvoiceConsumer
import br.com.alice.nullvs.consumers.invoice.InvoiceItemConsumer
import br.com.alice.nullvs.consumers.invoice.MemberInvoiceGroupCanceledConsumer
import br.com.alice.nullvs.consumers.invoice.MemberInvoiceGroupCreatedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceBatchRequestConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCanceledConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCanceledWebhookConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCreatedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceItemCreatedWebhookConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceLiquidationPaidConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceResponseConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsInvoiceWebhookConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsMemberInvoiceBatchConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentGroupCanceledConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentGroupGeneratedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentGroupUpdatedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsPaymentWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.invoice.NullvsSyncInvoiceRequestConsumer
import br.com.alice.nullvs.consumers.member.BeneficiaryConsumer
import br.com.alice.nullvs.consumers.member.BeneficiaryForActiveMemberConsumer
import br.com.alice.nullvs.consumers.member.MemberConsumer
import br.com.alice.nullvs.consumers.member.NullvsMemberBatchConsumer
import br.com.alice.nullvs.consumers.member.NullvsMemberWebhookReceivedConsumer
import br.com.alice.nullvs.consumers.member.PersonConsumer
import br.com.alice.nullvs.consumers.member.ProductChangedConsumer
import br.com.alice.nullvs.events.*
import br.com.alice.person.br.com.alice.person.events.PersonGracePeriodUpdatedForMemberActivatedEvent
import br.com.alice.person.br.com.alice.person.model.events.MemberReactivatedEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.person.model.events.ProductChangedEvent
import br.com.alice.product.model.events.ProductUpdatedEvent
import java.time.Duration

fun ConsumerJob.Configuration.kafkaRoutes() {
    val nullvsMemberBatchConsumer by inject<NullvsMemberBatchConsumer>()
    consume(
        "nullvs-member-batch-nullvs-integration-log",
        NullvsMemberBatchResponseEvent.name,
        nullvsMemberBatchConsumer::createNullvsIntegrationLog,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1))
    )
    consume(
        "nullvs-member-batch-request",
        NullvsMemberBatchRequestEvent.name,
        nullvsMemberBatchConsumer::callTotvsRequest
    )
    consume(
        "nullvs-member-reactivation-batch-request",
        NullvsMemberReactivationBatchRequestEvent.name,
        nullvsMemberBatchConsumer::callTotvsReactivationRequest
    )
    consume(
        "nullvs-sync-member-request",
        NullvsSyncMemberRequestEvent.name,
        nullvsMemberBatchConsumer::syncMember
    )

    val nullvsMemberWebhookReceivedConsumer by inject<NullvsMemberWebhookReceivedConsumer>()
    consume(
        "nullvs-member-webhook-received",
        NullvsMemberWebhookEvent.name,
        nullvsMemberWebhookReceivedConsumer::processIntegratedMemberAtTotvs
    )

    val memberConsumer by inject<MemberConsumer>()
    consume("member-activated", MemberActivatedEvent.name, memberConsumer::memberActivated)
    consume("member-reactivated", MemberReactivatedEvent.name, memberConsumer::memberReactivated)
    consume("member-cancelled", MemberCancelledEvent.name, memberConsumer::memberCancelled)
    consume("nullvs-member-activated", NullvsMemberActivatedEvent.name, memberConsumer::syncDependentsOfActivatedMember)

    val memberInvoiceGroupCreatedConsumer by inject<MemberInvoiceGroupCreatedConsumer>()
    consume(
        "member-invoice-group-created",
        MemberInvoiceGroupCreatedEvent.name,
        memberInvoiceGroupCreatedConsumer::processMemberInvoiceGroup
    )

    val memberInvoiceGroupCanceledConsumer by inject<MemberInvoiceGroupCanceledConsumer>()
    consume(
        "member-invoice-group-canceled",
        MemberInvoiceGroupCanceledEvent.name,
        memberInvoiceGroupCanceledConsumer::cancelMemberInvoiceGroup
    )

    val nullvsClientBatchResponseConsumer by inject<NullvsClientBatchResponseConsumer>()
    consume(
        "nullvs-client-batch-response",
        NullvsClientBatchResponseEvent.name,
        nullvsClientBatchResponseConsumer::saveSyncedClientToNullvsLog
    )

    val nullvsClientNotFoundConsumer by inject<NullvsClientNotFoundConsumer>()
    consume(
        "nullvs-client-not-found",
        NullvsClientNotFoundEvent.name,
        nullvsClientNotFoundConsumer::createBillingAccountablePartyFromTotvs
    )

    val nullvsInvoiceBatchResponseConsumer by inject<NullvsInvoiceResponseConsumer>()
    consume(
        "nullvs-invoice-batch-response",
        NullvsInvoiceBatchResponseEvent.name,
        nullvsInvoiceBatchResponseConsumer::saveSyncedInvoiceToNullvsLog,
    )

    val nullvsClientWebhookReceivedConsumer by inject<NullvsClientWebhookReceivedConsumer>()
    consume(
        "nullvs-client-webhook-received",
        NullvsClientWebhookReceivedEvent.name,
        nullvsClientWebhookReceivedConsumer::processIntegratedClientAtTotvs
    )

    val billingAccountablePartyConsumer by inject<BillingAccountablePartyConsumer>()
    consume(
        "billing-accountable-party-created",
        BillingAccountablePartyCreatedEvent.name,
        billingAccountablePartyConsumer::syncBillingAccountablePartyFromCreate
    )
    consume(
        "billing-accountable-party-updated",
        BillingAccountablePartyUpdatedEvent.name,
        billingAccountablePartyConsumer::syncBillingAccountablePartyFromUpdate
    )

    consume(
        "billing-accountable-party-assigned-update-beneficiary-totvs",
        BillingAccountablePartyAssignedEvent.name,
        billingAccountablePartyConsumer::updateBeneficiaryTotvs,
    )

    consume(
        "reprocess-client-dependencies",
        NullvsClientActivatedEvent.name,
        billingAccountablePartyConsumer::reprocessClientDependencies,
    )

    val nullvsPaymentGroupGeneratedConsumer by inject<NullvsPaymentGroupGeneratedConsumer>()
    consume(
        "nullvs-payment-group-generated",
        NullvsPaymentGroupGeneratedEvent.name,
        nullvsPaymentGroupGeneratedConsumer::processPaymentGroupGenerated
    )

    val nullvsPaymentGroupUpdatedConsumer by inject<NullvsPaymentGroupUpdatedConsumer>()
    consume(
        "nullvs-payment-group-updated",
        NullvsPaymentGroupUpdatedEvent.name,
        nullvsPaymentGroupUpdatedConsumer::processPaymentGroupUpdated
    )

    val nullvsPriceListingWebhookReceivedConsumer by inject<NullvsPriceListingWebhookReceivedConsumer>()
    consume(
        "nullvs-price-listing-webhook-received",
        NullvsPriceListingWebhookReceivedEvent.name,
        nullvsPriceListingWebhookReceivedConsumer::processedAtTotvs
    )

    val nullvsPaymentGroupCanceledConsumer by inject<NullvsPaymentGroupCanceledConsumer>()
    consume(
        "nullvs-payment-group-canceled",
        NullvsPaymentGroupCanceledEvent.name,
        nullvsPaymentGroupCanceledConsumer::processPaymentGroupCanceled
    )


    val nullvsSyncClientRequestConsumer by inject<NullvsSyncClientRequestConsumer>()
    consume(
        "nullvs-sync-client-request",
        NullvsSyncClientRequestEvent.name,
        nullvsSyncClientRequestConsumer::syncClientTowardsTotvs
    )

    val nullvsSyncInvoiceRequestConsumer by inject<NullvsSyncInvoiceRequestConsumer>()
    consume(
        "nullvs-sync-invoice-request",
        NullvsSyncInvoiceRequestEvent.name,
        nullvsSyncInvoiceRequestConsumer::syncInvoiceTowardsTotvs,
    )

    val nullvsClientBatchRequestConsumer by inject<NullvsClientBatchRequestConsumer>()
    consume(
        "nullvs-client-batch-request",
        NullvsClientBatchRequestEvent.name,
        nullvsClientBatchRequestConsumer::createNullvsClient
    )

    val nullvsCreateIntegrationRecordConsumer by inject<NullvsCreateIntegrationRecordConsumer>()
    consume(
        "nullvs-create-integration-record",
        NullvsCreateIntegrationRecordEvent.name,
        nullvsCreateIntegrationRecordConsumer::createNullvsIntegrationRecord
    )

    val nullvsInvoiceWebhookConsumer by inject<NullvsInvoiceWebhookConsumer>()
    consume(
        "nullvs-invoice-webhook",
        NullvsInvoiceWebhookEvent.name,
        nullvsInvoiceWebhookConsumer::associateInvoicePaymentsToMemberInvoice
    )

    val nullvsCompanyContractBatchResponseConsumer by inject<NullvsCompanyContractBatchResponseConsumer>()
    consume(
        "nullvs-company-contract-batch-request",
        NullvsCompanyContractBatchResponseEvent.name,
        nullvsCompanyContractBatchResponseConsumer::createNullvsIntegrationLog
    )

    val nullvsInvoiceBatchRequestConsumer by inject<NullvsInvoiceBatchRequestConsumer>()
    consume(
        "nullvs-invoice-batch-request-report-invoice-payment",
        NullvsInvoiceRequestEvent.name,
        nullvsInvoiceBatchRequestConsumer::reportInvoicePayment
    )

    val invoiceConsumer by inject<InvoiceConsumer>()
    consume(
        "invoice-group-paid-process-first-payment",
        MemberInvoiceGroupPaidEvent.name,
        invoiceConsumer::processFirstPaymentFromGroup
    )
    consume(
        "invoice-group-paid-process-recurrent-payment",
        MemberInvoiceGroupPaidEvent.name,
        invoiceConsumer::processRecurrentPaymentFromGroup
    )

    consume(
        "pre-activation-payment-paid-totvs-requester",
        PreActivationPaymentPaidEvent.name,
        invoiceConsumer::processPreActivationPayment,
    )

    val productChangedConsumer by inject<ProductChangedConsumer>()
    consume("product-changed", ProductChangedEvent.name, productChangedConsumer::changeProductAtTotvs)

    val beneficiaryConsumer by inject<BeneficiaryConsumer>()
    consume(
        "new-relationship-assigned",
        NewBeneficiaryRelationshipAssignedEvent.name,
        beneficiaryConsumer::newRelationshipAssigned,
    )

    consume(
        "update-beneficiary-on-totvs-after-save",
        BeneficiaryUpdatedEvent.name,
        beneficiaryConsumer::updateBeneficiaryTotvs,
    )


    val beneficiaryForActiveMemberConsumer by inject<BeneficiaryForActiveMemberConsumer>()
    consume(
        "beneficiary-created-for-active-member",
        BeneficiaryCreatedForActiveMemberEvent.name,
        beneficiaryForActiveMemberConsumer::beneficiaryCreatedForActiveMember,
    )

    val nullvsPaymentWebhookReceivedConsumer by inject<NullvsPaymentWebhookReceivedConsumer>()
    consume(
        "nullvs-payment-webhook",
        NullvsPaymentWebhookReceivedEvent.name,
        nullvsPaymentWebhookReceivedConsumer::processPaymentReceivedFromTotvs
    )

    val personConsumer by inject<PersonConsumer>()
    consume("person-update", PersonUpdatedEvent.name, personConsumer::updateBeneficiaryTotvs)
    consume(
        "person-grace-period-updated-for-member",
        PersonGracePeriodUpdatedForMemberActivatedEvent.name,
        personConsumer::updateBeneficiaryAfterPersonGracePeriodUpdates
    )

    val nullvsCompanySubContractBatchResponseConsumer by inject<NullvsCompanySubContractBatchResponseConsumer>()
    consume(
        "nullvs-company-sub-contract-batch-request",
        NullvsCompanySubContractBatchResponseEvent.name,
        nullvsCompanySubContractBatchResponseConsumer::createNullvsIntegrationLog
    )

    val companyContractConsumer by inject<CompanyContractConsumer>()
    consume(
        "company-contract-created",
        CompanyContractCreatedEvent.name,
        companyContractConsumer::syncContractCreated
    )

    consume(
        "company-contract-updated",
        CompanyContractUpdatedEvent.name,
        companyContractConsumer::syncContractUpdated
    )

    consume(
        "company-contract-sync",
        NullvsSyncCompanyContractRequestEvent.name,
        companyContractConsumer::syncContract,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1)),
    )

    consume(
        "reprocess-contract-dependencies",
        NullvsCompanyContractActivatedEvent.name,
        companyContractConsumer::reprocessContractDependencies
    )

    val companySubContractConsumer by inject<CompanySubContractConsumer>()
    consume(
        "company-subcontract-created",
        CompanySubContractCreatedEvent.name,
        companySubContractConsumer::syncSubcontractCreated
    )

    consume(
        "company-subcontract-updated",
        CompanySubContractUpdatedEvent.name,
        companySubContractConsumer::syncSubcontractUpdated
    )

    consume(
        "sync-invoice-item-from-subcontract",
        CompanySubContractUpdatedEvent.name,
        companySubContractConsumer::syncInvoiceItemFromSubcontract
    )

    consume(
        "company-subcontract-sync",
        NullvsSyncCompanySubcontractRequestEvent.name,
        companySubContractConsumer::syncSubcontract,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1)),
    )

    consume(
        "reprocess-subcontract-dependencies",
        NullvsCompanySubContractActivatedEvent.name,
        companySubContractConsumer::reprocessSubcontractDependencies,
    )

    val nullvsCompanyContractWebhookReceivedConsumer by inject<NullvsCompanyContractWebhookReceivedConsumer>()
    consume(
        "nullvs-company-contract-webhook-received",
        NullvsCompanyContractWebhookReceivedEvent.name,
        nullvsCompanyContractWebhookReceivedConsumer::processedAtTotvs
    )

    val nullvsCompanyContractBatchRequestConsumer by inject<NullvsCompanyContractBatchRequestConsumer>()
    consume(
        "nullvs-company-contract-batch-request",
        NullvsCompanyContractBatchRequestEvent.name,
        nullvsCompanyContractBatchRequestConsumer::createNullvsContract
    )

    val nullvsCompanySubcontractWebhookReceivedConsumer by inject<NullvsCompanySubcontractWebhookReceivedConsumer>()
    consume(
        "nullvs-company-subcontract-webhook-received",
        NullvsCompanySubcontractWebhookReceivedEvent.name,
        nullvsCompanySubcontractWebhookReceivedConsumer::processedAtTotvs
    )

    val nullvsCompanySubcontractBatchRequestConsumer by inject<NullvsCompanySubcontractBatchRequestConsumer>()
    consume(
        "nullvs-company-contract-batch-request",
        NullvsCompanySubContractBatchRequestEvent.name,
        nullvsCompanySubcontractBatchRequestConsumer::createNullvsSubcontract
    )

    val nullvsInvoiceItemCreatedWebhookConsumer by inject<NullvsInvoiceItemCreatedWebhookConsumer>()
    consume(
        "nullvs-invoice-item-created-webhook-received",
        NullvsInvoiceItemCreatedWebhookReceivedEvent.name,
        nullvsInvoiceItemCreatedWebhookConsumer::processNullvsInvoiceItemCreatedWebhook
    )

    val nullvsInvoiceLiquidationPaidConsumer by inject<NullvsInvoiceLiquidationPaidConsumer>()
    consume(
        "invoice-liquidation-process-payment",
        InvoiceLiquidationPaidEvent.name,
        nullvsInvoiceLiquidationPaidConsumer::invoiceLiquidationPaidToTotvs
    )

    val nullvsInvoiceItemCanceledWebhookConsumer by inject<NullvsInvoiceItemCanceledWebhookConsumer>()
    consume(
        "nullvs-invoice-item-canceled-webhook-received",
        NullvsInvoiceItemCanceledWebhookReceivedEvent.name,
        nullvsInvoiceItemCanceledWebhookConsumer::processInvoiceItemCanceledWebhook
    )

    val invoiceItemConsumer by inject<InvoiceItemConsumer>()
    consume(
        "invoice-item-created",
        InvoiceItemCreatedEvent.name,
        invoiceItemConsumer::processInvoiceItemCreated
    )
    consume(
        "invoice-item-canceled",
        InvoiceItemCanceledEvent.name,
        invoiceItemConsumer::processInvoiceItemCanceled
    )

    val nullvsInvoiceItemCreatedConsumer by inject<NullvsInvoiceItemCreatedConsumer>()
    consume(
        "nullvs-invoice-item-created",
        NullvsInvoiceItemCreatedEvent.name,
        nullvsInvoiceItemCreatedConsumer::processInvoiceItemCreated,
    )

    val nullvsInvoiceItemCanceledConsumer by inject<NullvsInvoiceItemCanceledConsumer>()
    consume(
        "nullvs-invoice-item-canceled",
        NullvsInvoiceItemCanceledEvent.name,
        nullvsInvoiceItemCanceledConsumer::processInvoiceItemCanceled,
    )

    val nullvsMemberInvoiceBatchConsumer by inject<NullvsMemberInvoiceBatchConsumer>()

    consume(
        "nullvs-member-invoice-batch",
        NullvsMemberInvoiceBatchEvent.name,
        nullvsMemberInvoiceBatchConsumer::createMemberInvoices,
    )

    val productConsumer by inject<ProductConsumer>()

    consume("cache-ans-number", ProductUpdatedEvent.name, productConsumer::cacheAnsNumber)

    val scheduleFirstPaymentConsumer by inject<FirstPaymentScheduleConsumer>()

    consume(
        "schedule-first-payment-at-totvs",
        FirstPaymentScheduleCreatedEvent.name,
        scheduleFirstPaymentConsumer::processFirstPaymentScheduled,
    )
}
