package br.com.alice.nullvs.routes

import br.com.alice.common.coHandler
import br.com.alice.nullvs.controllers.FirstPaymentController
import br.com.alice.nullvs.controllers.InvoiceController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.entryRoutes() {

    authenticate {
        route("/entry") {

            val invoiceController by inject<InvoiceController>()
            post("/payment/batch") {
                coHandler(invoiceController::processInvoiceBatch)
            }

            val firstPaymentController by inject<FirstPaymentController>()
            post("/first-payment") {
                coHandler(firstPaymentController::processFirstPaymentSuccessful)
            }
        }
    }
}
