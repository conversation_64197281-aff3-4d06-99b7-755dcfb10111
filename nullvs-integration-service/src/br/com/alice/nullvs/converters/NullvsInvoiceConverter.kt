package br.com.alice.nullvs.converters

import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.money
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.data.layer.models.*
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.event.InvoicePaidEvent
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.common.NullvsActionType.Companion.toTotvs
import br.com.alice.nullvs.common.fromTotvsFormatDateDMY
import br.com.alice.nullvs.common.totvsFormatDateDMY
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsPaymentGroupCanceledEvent
import br.com.alice.nullvs.events.NullvsPaymentGroupGeneratedEvent
import br.com.alice.nullvs.events.NullvsPaymentGroupUpdatedEvent
import br.com.alice.nullvs.exceptions.PreActivationPaymentExternalIdNullException
import br.com.alice.nullvs.exceptions.TotvsActionNotSupportedException
import br.com.alice.nullvs.logics.InvoiceBatchLogic.toTotvsInvoiceIndex
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsDefaultUsusario
import br.com.alice.nullvs.models.TotvsIntegrationBatchResponse
import br.com.alice.nullvs.models.TotvsInvoiceIndex
import br.com.alice.nullvs.models.TotvsReceivableIntegrationRequest
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.models.company.CompanyContractInfo
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceData
import br.com.alice.nullvs.models.payment.NullvsMemberInvoiceDetailTransport
import br.com.alice.nullvs.models.payment.TotvsInvoiceResponse
import br.com.alice.nullvs.models.payment_in.TotvsFirstPaymentSuccessRequest
import br.com.alice.nullvs.models.payment_in.TotvsInvoiceCancellationBatch
import br.com.alice.nullvs.models.payment_in.TotvsInvoiceCreationBatch
import br.com.alice.nullvs.models.payment_in.TotvsInvoiceRequest
import br.com.alice.nullvs.models.payment_out.defaultTotvsInvoiceIntegrationRequestEntity
import br.com.alice.nullvs.models.payment_out.defaultTotvsInvoiceIntegrationRequestType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

object NullvsInvoiceConverter {
    fun MemberInvoice.toNullvsInvoiceRequest(
        meta: Meta,
        actionType: NullvsActionType,
        productType: ProductType,
        paymentType: PaymentMethod,
        clientCode: String,
        titleCode: String?
    ): NullvsInvoiceBatchRequest {
        val titleIndexFields = titleCode?.toTotvsInvoiceIndex()
        return NullvsInvoiceBatchRequest(
            meta = meta,
            action = actionType,
            invoiceCreationData = NullvsInvoiceData(
                titlePrefix = titleIndexFields?.prefix ?: "PPG",
                titleNumber = titleIndexFields?.number,
                titleInstallment = titleIndexFields?.installment,
                type = titleIndexFields?.type
                    ?: paymentType.getType(),
                dueDate = this.dueDate.toSaoPauloTimeZone().toLocalDate(),
                amount = this.totalAmount.toDouble(),
                financialHistory = productType.getFinancialHistory(),
                discount = this.invoiceBreakdown?.discount?.toDouble() ?: 0.0,
                status = this.status.toNullvsInvoiceDataStatus(),
                clientCode = clientCode,
                emittedDate = this.referenceDate,
                fine = 0.0,
                interest = 0.0,
                natureCode = productType.getNatureCode(),
                monetaryCorrection = 0.0,
                paidAt = this.paidAt?.toSaoPauloTimeZone()?.toLocalDate(),
                memberId = this.memberId,
                memberInvoiceGroupId = this.id
            )
        )
    }

    fun InvoicePayment.extractAmountPaidFineInterest(): Triple<BigDecimal, BigDecimal, BigDecimal> {
        val amountPaid = amountPaid ?: amount
        val zero = 0.money
        val totalAmount = amount.plus(fine ?: zero).plus(interest ?: zero)
        val offset = if (totalAmount == amountPaid) fine else amountPaid - amount
        val fine = fine?.let { if (amountPaid != totalAmount) offset else it } ?: zero
        val interest = interest?.let { if (amountPaid != totalAmount) zero else it } ?: zero
        val totalPaid = amountPaid.let {
            val total = it - fine - interest

            if (total > amount) {
                amount
            } else {
                total
            }
        }

        return Triple(totalPaid, fine, interest)
    }

    fun toNullvsInvoiceRequest(
        id: UUID,
        dueDate: LocalDate,
        status: NullvsInvoiceData.Status,
        referenceDate: LocalDate,
        meta: Meta,
        actionType: NullvsActionType,
        productType: ProductType,
        clientCode: String,
        invoicePayment: InvoicePayment,
        companyContractInfo: CompanyContractInfo? = null,
        titlePrefix: String? = null,
        titleNumber: String? = null,
        titleInstallment: String? = null,
        titleType: String? = null,
    ): NullvsInvoiceBatchRequest {
        val (totalPaid, fine, interest) = invoicePayment.extractAmountPaidFineInterest()

        return NullvsInvoiceBatchRequest(
            meta = meta,
            action = actionType,
            invoiceCreationData = NullvsInvoiceData(
                titlePrefix = titlePrefix ?: "PPG",
                titleNumber = titleNumber,
                titleInstallment = titleInstallment,
                type = titleType ?: "DP",
                dueDate = dueDate,
                amount = totalPaid.toDouble(),
                financialHistory = productType.getFinancialHistory(),
                discount = 0.0,
                status = status,
                clientCode = clientCode,
                emittedDate = referenceDate,
                fine = fine.toDouble(),
                interest = interest.toDouble(),
                natureCode = productType.getNatureCode(),
                monetaryCorrection = 0.0,
                paidAt = invoicePayment.approvedAt?.toSaoPauloTimeZone()?.toLocalDate(),
                memberInvoiceGroupId = id,
                groupCompany = companyContractInfo?.groupCompany,
                contract = companyContractInfo?.contractNumber,
                subcontract = companyContractInfo?.subcontractNumber,
            )
        )
    }

    fun MemberInvoiceGroup.toNullvsInvoiceRequest(
        meta: Meta,
        actionType: NullvsActionType,
        productType: ProductType,
        invoicePayment: InvoicePayment,
        clientCode: String,
        companyContractInfo: CompanyContractInfo? = null,
    ): NullvsInvoiceBatchRequest {
        val titleIndexFields =
            externalId?.toTotvsInvoiceIndex() ?: throw IllegalArgumentException("externalId cannot be null")

        val amount = invoicePayment.amount
        val amountPaid = invoicePayment.amountPaid ?: invoicePayment.amount
        val zero = 0.money
        val fine = invoicePayment.fine?.let { if (amountPaid <= amount) zero else it } ?: zero
        val interest = invoicePayment.interest?.let { if (amountPaid <= amount) zero else it } ?: zero
        val totalPaid = invoicePayment.amountPaid?.let {
            val total = it - fine - interest

            if (total > amount) {
                amount
            } else {
                total
            }
        } ?: invoicePayment.amount

        return NullvsInvoiceBatchRequest(
            meta = meta,
            action = actionType,
            invoiceCreationData = NullvsInvoiceData(
                titlePrefix = titleIndexFields.prefix,
                titleNumber = titleIndexFields.number,
                titleInstallment = titleIndexFields.installment,
                type = titleIndexFields.type,
                dueDate = this.dueDate,
                amount = totalPaid.toDouble(),
                financialHistory = productType.getFinancialHistory(),
                discount = 0.0,
                status = this.status.toNullvsInvoiceDataStatus(),
                clientCode = clientCode,
                emittedDate = this.referenceDate,
                fine = fine.toDouble(),
                interest = interest.toDouble(),
                natureCode = productType.getNatureCode(),
                monetaryCorrection = 0.0,
                paidAt = invoicePayment.approvedAt?.toSaoPauloTimeZone()?.toLocalDate(),
                externalPaymentId = invoicePayment.id.toString(),
                groupCompany = companyContractInfo?.groupCompany,
                contract = companyContractInfo?.contractNumber,
                subcontract = companyContractInfo?.subcontractNumber,
            )
        )
    }

    private fun InvoiceStatus.toNullvsInvoiceDataStatus() = when (this) {
        InvoiceStatus.OPEN -> NullvsInvoiceData.Status.OPEN
        InvoiceStatus.PAID -> NullvsInvoiceData.Status.PAID
        InvoiceStatus.CANCELED -> NullvsInvoiceData.Status.CANCELED
        InvoiceStatus.FAILED -> NullvsInvoiceData.Status.FAILED
        InvoiceStatus.CANCELED_BY_LIQUIDATION -> NullvsInvoiceData.Status.CANCELED_BY_LIQUIDATION
    }

    private fun MemberInvoiceGroupStatus.toNullvsInvoiceDataStatus() = when (this) {
        MemberInvoiceGroupStatus.PROCESSING, MemberInvoiceGroupStatus.WAITING_PAYMENT, MemberInvoiceGroupStatus.PROCESSED -> NullvsInvoiceData.Status.OPEN
        MemberInvoiceGroupStatus.PAID, MemberInvoiceGroupStatus.PARTIALLY_PAID -> NullvsInvoiceData.Status.PAID
        MemberInvoiceGroupStatus.CANCELED -> NullvsInvoiceData.Status.CANCELED
        MemberInvoiceGroupStatus.CANCELED_BY_LIQUIDATION -> NullvsInvoiceData.Status.CANCELED_BY_LIQUIDATION
    }

    private fun PreActivationPaymentStatus.toNullvsInvoiceDataStatus() = when (this) {
        PreActivationPaymentStatus.PROCESSED, PreActivationPaymentStatus.PROCESSING -> NullvsInvoiceData.Status.OPEN
        PreActivationPaymentStatus.PAID -> NullvsInvoiceData.Status.PAID
        PreActivationPaymentStatus.CANCELED -> NullvsInvoiceData.Status.CANCELED
    }

    fun NullvsInvoiceBatchResponse.toNullvsIntegrationLog(): NullvsIntegrationLog {
        logger.info(
            "Convert NullvsInvoiceBatchResponseEvent to NullvsIntegrationLog",
            "meta" to metadata,
            "totvs_invoice_response" to totvsInvoiceResponse,
        )

        val batchType = when (totvsInvoiceResponse.action) {
            "I" -> BatchType.CREATE
            "U" -> BatchType.UPDATE
            "D" -> BatchType.CANCEL
            else -> throw TotvsActionNotSupportedException(totvsInvoiceResponse.action)
        }

        return NullvsIntegrationLog(
            eventId = metadata.eventId,
            eventName = metadata.eventName,
            integrationEventName = metadata.integrationEventName,
            internalId = metadata.internalId,
            internalModelName = metadata.internalModelName,
            externalModelName = metadata.externalModelName,
            batchId = totvsInvoiceResponse.batch,
            idSoc = totvsInvoiceResponse.idSoc,
            batchType = batchType,
            payloadSequenceId = 1,
            description = null,
            status = LogStatus.PENDING,
        )
    }

    private fun ProductType.getFinancialHistory() =
        if (this == ProductType.B2B) "RECEITAS B2B - ALICE" else "RECEITAS B2C - ALICE"

    private fun PaymentMethod.getType() =
        if (this == PaymentMethod.SIMPLE_CREDIT_CARD) "NCC"
        else "RA"

    private fun ProductType.getNatureCode() = if (this == ProductType.B2B) "20009" else "20010"

    fun NotificationEvent<*>.toMeta(
        id: UUID,
        internalModelName: InternalModelType,
        externalModelName: ExternalModelType = ExternalModelType.INVOICE,
        integrationEvent: String = "nullvs-invoice-request-event"
    ) = Meta(
        eventId = this.messageId,
        eventName = this.name,
        internalId = id,
        internalModelName = internalModelName,
        externalModelName = externalModelName,
        integratedAt = this.eventDate,
        originalTopic = InvoicePaidEvent.name,
        integrationEventName = integrationEvent,
    )

    fun MemberInvoice.toNullvsInvoiceRequestEvent(
        invoicePaidEvent: NotificationEvent<*>,
        invoicePayment: InvoicePayment,
        clientCode: String,
        actionType: NullvsActionType,
        titleCode: String? = null,
    ) = this.toNullvsInvoiceRequest(
        invoicePaidEvent.toMeta(this.id, InternalModelType.MEMBER_INVOICE),
        actionType,
        ProductType.B2C,
        invoicePayment.method,
        clientCode,
        titleCode,
    )

    fun PreActivationPayment.toNullvsInvoiceRequest(
        event: NotificationEvent<*>,
        invoicePayment: InvoicePayment,
        clientCode: String,
        actionType: NullvsActionType,
        companyContractInfo: CompanyContractInfo? = null,
    ) = toNullvsInvoiceRequest(
        id = id,
        dueDate = dueDate,
        status = status.toNullvsInvoiceDataStatus(),
        referenceDate = referenceDate,
        meta = event.toMeta(id, InternalModelType.PRE_ACTIVATION_PAYMENT),
        actionType = actionType,
        productType = type.let { if (it.isB2B()) ProductType.B2B else ProductType.B2C },
        clientCode = clientCode,
        invoicePayment = invoicePayment,
        companyContractInfo = companyContractInfo,
        titleType = "RA"
    )

    fun MemberInvoiceGroup.toNullvsInvoiceRequest(
        event: NotificationEvent<*>,
        invoicePayment: InvoicePayment,
        clientCode: String,
        actionType: NullvsActionType,
        titleCode: String? = null,
        companyContractInfo: CompanyContractInfo? = null,
    ): NullvsInvoiceBatchRequest {
        val titleIndexFields = titleCode?.toTotvsInvoiceIndex()
        val titlePrefix = titleIndexFields?.prefix ?: "PPG"
        val titleNumber = titleIndexFields?.number
        val titleInstallment = titleIndexFields?.installment
        val titleType = titleIndexFields?.type ?: "DP"

        return toNullvsInvoiceRequest(
            id = id,
            dueDate = dueDate,
            status = status.toNullvsInvoiceDataStatus(),
            referenceDate = referenceDate,
            meta = event.toMeta(id, InternalModelType.MEMBER_INVOICE_GROUP),
            actionType = actionType,
            productType = type?.let { if (it.isB2B()) ProductType.B2B else ProductType.B2C } ?: ProductType.B2B,
            clientCode = clientCode,
            invoicePayment = invoicePayment,
            titlePrefix = titlePrefix,
            titleNumber = titleNumber,
            titleInstallment = titleInstallment,
            titleType = titleType,
            companyContractInfo = companyContractInfo,
        )
    }

    fun MemberInvoiceGroup.toNullvsInvoiceRequestEvent(
        event: NotificationEvent<*>,
        invoicePayment: InvoicePayment,
        clientCode: String,
        actionType: NullvsActionType,
        companyContractInfo: CompanyContractInfo? = null,
    ) = this.toNullvsInvoiceRequest(
        event.toMeta(this.id, InternalModelType.MEMBER_INVOICE_GROUP),
        actionType,
        this.type?.let { if (it == MemberInvoiceType.B2B_REGULAR_PAYMENT) ProductType.B2B else ProductType.B2C }
            ?: ProductType.B2C,
        invoicePayment,
        clientCode,
        companyContractInfo = companyContractInfo,
    )

    fun TotvsInvoiceCancellationBatch.toNullvsInvoiceGroupCanceledEvents(records: Map<String, MemberInvoiceGroup>): List<NullvsPaymentGroupCanceledEvent> {
        logger.info(
            "Convert TotvsInvoiceCancellationBatch to toNullvsInvoiceGroupCanceledEvents",
            "totvs_invoice_webhook" to this,
        )

        return this.payload.map {
            val externalId =
                TotvsInvoiceIndex(it.prefix, it.numberOfTitle, it.installment, it.type).toString()
            val memberInvoiceGroup = records.getValue(externalId)
            NullvsPaymentGroupCanceledEvent(
                externalId,
                memberInvoiceGroup.billingAccountablePartyId,
                memberInvoiceGroup.id
            )
        }
    }

    fun TotvsIntegrationBatchResponse.toNullvsInvoiceRequestResponse(meta: Meta): NullvsInvoiceBatchResponse {
        logger.info(
            "Convert TotvsIntegrationBatchResponse to NullvsInvoiceBatchResponse",
            "meta" to meta,
            "totvs_integration_batch_response" to this,
        )

        return NullvsInvoiceBatchResponse(
            meta,
            TotvsInvoiceResponse(this.httpStatus, this.batch, this.idSoc, this.action),
        )
    }

    fun NullvsInvoiceBatchRequest.toTotvsInvoiceRequest(): TotvsInvoiceRequest {
        logger.info(
            "Convert NullvsInvoiceBatchRequest to TotvsInvoiceRequest",
            "nullvs_member_batch_request" to this,
            "type" to defaultTotvsInvoiceIntegrationRequestType,
            "entity" to defaultTotvsInvoiceIntegrationRequestEntity,
            "action" to this.action.toTotvs(),
        )

        return this.invoiceCreationData.let {
            val payload = this.generateInvoiceRequestPayload()

            TotvsInvoiceRequest(
                user = TotvsDefaultUsusario,
                action = this.action.toTotvs(),
                date = this.meta.integratedAt.totvsFormatDateDMY(),
                idSoc = this.meta.eventId.toString(),
                payload = listOf(payload),
            )
        }
    }

    fun NullvsInvoiceBatchRequest.toTotvsReceivableIntegrationRequest(): TotvsReceivableIntegrationRequest {
        logger.info(
            "Convert NullvsInvoiceBatchRequest to TotvsReceivableIntegrationRequest",
            "nullvs_member_batch_request" to this,
        )
        return TotvsReceivableIntegrationRequest(
            batchId = this.meta.eventId.toString(),
            date = this.meta.integratedAt.totvsFormatDateDMY(),
            action = this.action.toTotvs(),
            payload = listOf(this.invoiceCreationData.toTotvsReceivableIntegrationRequestPayload()),
        )
    }

    private fun NullvsInvoiceData.toTotvsReceivableIntegrationRequestPayload(): TotvsReceivableIntegrationRequest.Payload =
        TotvsReceivableIntegrationRequest.Payload(
            prefix = this.titlePrefix,
            numberOfTitle = this.titleNumber!!,
            installment = this.titleInstallment!!,
            type = this.type,
            client = this.clientCode,
            paidAt = this.paidAt!!.totvsFormatDateDMY(),
            value = this.amount,
            description = "",
            interest = this.interest,
            fine = this.fine,
            discount = this.discount,
            externalPaymentId = this.externalPaymentId
                ?: throw IllegalArgumentException("ExternalPaymentId cannot be null")
        )

    private fun NullvsInvoiceBatchRequest.generateInvoiceRequestPayload() = this.invoiceCreationData.run {
        TotvsInvoiceRequest.Payload(
            prefix = this.titlePrefix,
            type = this.type,
            client = this.clientCode,
            issuedDate = this.emittedDate.totvsFormatDateDMY(),
            value = this.amount,
            dueDate = this.dueDate.totvsFormatDateDMY(),
            financialHistory = this.financialHistory,
            discount = this.discount,
            fine = this.fine,
            situation = "0",
            interest = this.interest,
            monetaryCorrection = this.monetaryCorrection,
            origin = this.natureCode,
            paidAt = this.paidAt!!.totvsFormatDateDMY(),
            memberId = this.memberId,
            memberInvoiceGroupId = this.memberInvoiceGroupId
                ?: throw IllegalArgumentException("memberInvoiceGroupId cannot be null")
        )
    }

    private fun convertToInvoiceItemType(release: String) = when (release) {
        "919" -> InvoiceItemType.SALES
        "933", "934", "116", "185" -> InvoiceItemType.COPAY
        "924", "925" -> InvoiceItemType.PRODUCT_CHANGE
        "101" -> InvoiceItemType.PRODUCT_PRICE
        "920" -> InvoiceItemType.PROMO_CODE
        "128", "190" -> InvoiceItemType.PLAN_READJUSTMENT
        "922", "929", "930" -> InvoiceItemType.OPERATIONAL_ADJUSTMENT
        "197", "905", "906" -> InvoiceItemType.PRORATION
        "118" -> InvoiceItemType.RETROACTIVE_MONTHLY_FEE_BILLING
        else -> InvoiceItemType.OTHERS
    }

    private fun TotvsInvoiceCreationBatch.Payload.Composition.toInvoiceItem(personId: PersonId? = null) = InvoiceItem(
        personId = personId,
        referenceDate = "01${month}${year}".fromTotvsFormatDateDMY(),
        operation = if (type == "1") InvoiceItemOperation.CHARGE else InvoiceItemOperation.DISCOUNT,
        type = convertToInvoiceItemType(release),
        notes = releaseDescription,
        status = InvoiceItemStatus.ACTIVE,
        absoluteValue = value.toBigDecimal(),
    )

    private fun List<TotvsInvoiceCreationBatch.Payload.Composition>.toMemberInvoiceB2B(
        member: Member,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paidAt: LocalDateTime?,
    ): MemberInvoice {
        val totalAmount = sumOf { if (it.type == "1") it.value else -it.value }.toBigDecimal()

        val invoiceItems = map { it.toInvoiceItem(member.personId) }

        val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().getOrNullIfNotFound()

        return MemberInvoice(
            memberId = member.id,
            personId = member.personId,
            totalAmount = totalAmount,
            status = InvoiceStatus.OPEN,
            referenceDate = referenceDate,
            dueDate = dueDate,
            invoiceItems = invoiceBreakdown?.invoiceItems,
            invoiceBreakdown = invoiceBreakdown,
            paidAt = paidAt
        )
    }

    fun TotvsInvoiceCreationBatch.Payload.toNullvsInvoiceGroupGeneratedEvent(
        members: Map<UUID, Member>,
        billingAccountablePartyIds: Map<String, UUID>,
    ): NullvsPaymentGroupGeneratedEvent {
        logger.info(
            "Convert TotvsInvoiceCreationBatch to NullvsPaymentGroupGeneratedEvent",
            "totvs_invoice_creation_batch" to this,
        )
        val invoice = this

        val referenceDate = invoice.referenceDate.fromTotvsFormatDateDMY()
        val dueDate = invoice.realDueDate.fromTotvsFormatDateDMY().atEndOfTheDay()

        val memberInvoices = invoice.toMemberInvoices(members, referenceDate, dueDate)
        val globalItems = compositionsToGlobalItems(invoice)
        val paymentMethod = invoice.companyContract?.let { PaymentMethod.BOLEPIX } ?: PaymentMethod.BOLETO

        return NullvsPaymentGroupGeneratedEvent(
            memberInvoices = memberInvoices,
            referenceDate = referenceDate,
            dueDate = dueDate.toLocalDate(),
            paymentMethod = paymentMethod,
            externalId = TotvsInvoiceIndex(
                invoice.prefix,
                invoice.numberOfTitle,
                invoice.installment,
                invoice.type
            ).toString(),
            billingAccountablePartyId = billingAccountablePartyIds[invoice.client]!!,
            globalItems = globalItems,
            groupCompany = invoice.groupCompany,
            companyContractExternalId = invoice.companyContract,
            companySubContractExternalId = invoice.companySubContract,
        )
    }

    fun TotvsInvoiceCreationBatch.Payload.toMemberInvoiceGroupB2B(
        subContract: CompanySubContract? = null,
        billingAccountablePartyIds: Map<String, UUID>,
        memberInvoiceCount: Int,
    ): MemberInvoiceGroup {
        val referenceDate = this.referenceDate.fromTotvsFormatDateDMY()
        val dueDate = this.realDueDate.fromTotvsFormatDateDMY()
        val globalItems = compositionsToGlobalItems(this)

        return MemberInvoiceGroup(
            billingAccountablePartyId = billingAccountablePartyIds[this.client]!!,
            referenceDate = referenceDate,
            dueDate = dueDate,
            status = MemberInvoiceGroupStatus.PROCESSING,
            externalId = TotvsInvoiceIndex(
                this.prefix,
                this.numberOfTitle,
                this.installment,
                this.type
            ).toString(),
            type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
            totalAmount = this.balance.money,
            globalItems = globalItems,
            companyId = subContract?.companyId,
            companySubcontractId = subContract?.id,
            quantityMemberInvoices = memberInvoiceCount,
        )
    }

    fun TotvsInvoiceCreationBatch.Payload.toNullvsInvoiceGroupUpdatedEvent(
        members: Map<UUID, Member>,
        billingAccountablePartyIds: Map<String, UUID>,
    ): NullvsPaymentGroupUpdatedEvent {
        logger.info(
            "Convert TotvsInvoiceCreationBatch to NullvsInvoiceGroupUpdatedEvent",
            "totvs_invoice_creation_batch" to this,
        )
        val invoice = this

        val referenceDate = invoice.referenceDate.fromTotvsFormatDateDMY()
        val dueDate = invoice.realDueDate.fromTotvsFormatDateDMY().atEndOfTheDay()

        val memberInvoices = invoice.toMemberInvoices(members, referenceDate, dueDate)
        val globalItems = compositionsToGlobalItems(invoice)

        return NullvsPaymentGroupUpdatedEvent(
            memberInvoices = memberInvoices,
            referenceDate = referenceDate,
            dueDate = dueDate.toLocalDate(),
            paymentMethod = PaymentMethod.BOLETO,
            externalId = TotvsInvoiceIndex(
                invoice.prefix,
                invoice.numberOfTitle,
                invoice.installment,
                invoice.type
            ).toString(),
            billingAccountablePartyId = billingAccountablePartyIds[invoice.client]!!,
            globalItems = globalItems,
            groupCompany = invoice.groupCompany,
            companyContractExternalId = invoice.companyContract,
            companySubContractExternalId = invoice.companySubContract,
        )
    }

    private fun compositionsToGlobalItems(invoice: TotvsInvoiceCreationBatch.Payload) =
        invoice.composition.filter { it.totvsId.isBlank() && it.aliceId.isBlank() }
            .map { it.toInvoiceItem() }

    fun TotvsInvoiceCreationBatch.Payload.toMemberInvoices(
        members: Map<UUID, Member>,
        referenceDate: LocalDate,
        dueDate: LocalDateTime
    ) = this.composition.filter { it.totvsId.isNotBlank() && it.aliceId.isNotBlank() }
        .groupBy { it.aliceId }.values.map { compositions ->
            val compositionBase = compositions.first()
            val member = members[compositionBase.aliceId.toUUID()]!!

            compositions.toMemberInvoiceB2B(
                member, referenceDate, dueDate,
                this.paidAt?.fromTotvsFormatDateDMY()?.atBeginningOfTheDay()
            )
        }

    fun TotvsInvoiceCreationBatch.Payload.toInvoiceDetailList(
        members: Map<UUID, Member>,
        referenceDate: LocalDate,
        dueDate: LocalDateTime
    ) = this.composition.filter { it.totvsId.isNotBlank() && it.aliceId.isNotBlank() }
        .groupBy { it.aliceId }.values.map { compositions ->
            val compositionBase = compositions.first()
            val member = members[compositionBase.aliceId.toUUID()]!!

            compositions.toInvoiceDetail(
                member, referenceDate, dueDate, this.paidAt?.fromTotvsFormatDateDMY()?.atBeginningOfTheDay()
            )
        }

    private fun List<TotvsInvoiceCreationBatch.Payload.Composition>.toInvoiceDetail(
        member: Member,
        referenceDate: LocalDate,
        dueDate: LocalDateTime,
        paidAt: LocalDateTime?,
    ): NullvsMemberInvoiceDetailTransport {
        val totalAmount = sumOf { if (it.type == "1") it.value else -it.value }.toBigDecimal()

        val items = map {
            NullvsMemberInvoiceDetailTransport.InvoiceDetailItem(
                referenceDate = "01${it.month}${it.year}".fromTotvsFormatDateDMY(),
                operation = if (it.type == "1") InvoiceItemOperation.CHARGE else InvoiceItemOperation.DISCOUNT,
                type = convertToInvoiceItemType(it.release),
                notes = it.releaseDescription,
                value = it.value.toBigDecimal(),
            )
        }

        return NullvsMemberInvoiceDetailTransport(
            memberId = member.id,
            personId = member.personId,
            totalAmount = totalAmount,
            referenceDate = referenceDate,
            items = items,
            dueDate = dueDate.toLocalDate(),
            paidAt = paidAt,
        )
    }

    fun NullvsMemberInvoiceDetailTransport.toMemberInvoiceB2B(memberInvoiceGroupId: UUID): MemberInvoice {
        val invoiceItems = items.map {
            InvoiceItem(
                personId = personId,
                referenceDate = it.referenceDate,
                operation = it.operation,
                type = it.type,
                notes = it.notes,
                status = InvoiceItemStatus.ACTIVE,
                absoluteValue = it.value,
            )
        }

        val invoiceBreakdown = invoiceItems.toInvoiceBreakdown().getOrNullIfNotFound()

        return MemberInvoice(
            memberId = memberId,
            personId = personId,
            totalAmount = totalAmount,
            status = InvoiceStatus.OPEN,
            referenceDate = referenceDate,
            dueDate = dueDate.atEndOfTheDay(),
            invoiceItems = invoiceBreakdown?.invoiceItems,
            invoiceBreakdown = invoiceBreakdown,
            paidAt = paidAt,
            memberInvoiceGroupId = memberInvoiceGroupId,
            type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
        )
    }


    fun InvoiceItemType.toTotvs(operation: InvoiceItemOperation): String = when (operation) {
        InvoiceItemOperation.CHARGE -> when (this) {
            InvoiceItemType.COPAY -> "130"
            InvoiceItemType.PRODUCT_CHANGE -> "122"
            InvoiceItemType.PRODUCT_PRICE,
            InvoiceItemType.MONTHLY_ORGANIC_INCLUSION -> "134"

            InvoiceItemType.PLAN_READJUSTMENT,
            InvoiceItemType.OPERATIONAL_ADJUSTMENT -> "126"

            InvoiceItemType.FEES_FINES,
            InvoiceItemType.INTEREST_OR_FINE -> "120"

            else -> throw IllegalArgumentException("This invoice type does not have a correspondent TOTVS code for the charge operation")
        }

        InvoiceItemOperation.DISCOUNT ->
            when (this) {
                InvoiceItemType.SALES, InvoiceItemType.SALES_RESULT,
                InvoiceItemType.PROMO_CODE, InvoiceItemType.PROMO_CODE_RESULT -> "116"

                InvoiceItemType.COPAY -> "131"
                InvoiceItemType.PRODUCT_CHANGE -> "121"
                InvoiceItemType.PRODUCT_PRICE,
                InvoiceItemType.MONTHLY_ORGANIC_INCLUSION -> "133"

                InvoiceItemType.PLAN_READJUSTMENT,
                InvoiceItemType.OPERATIONAL_ADJUSTMENT -> "127"

                InvoiceItemType.FEES_FINES,
                InvoiceItemType.INTEREST_OR_FINE -> "119"

                else -> throw IllegalArgumentException("This invoice type does not have a correspondent TOTVS code for the discount operation")
            }
    }

    fun fromTOTVSToItems(composition: List<TotvsFirstPaymentSuccessRequest.Composition>) =
            composition.map {
                NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem(
                    referenceDate = "01${it.month}${it.year}".fromTotvsFormatDateDMY(),
                    operation = if (it.type == "1") InvoiceItemOperation.CHARGE else InvoiceItemOperation.DISCOUNT,
                    type = convertToInvoiceItemType(it.release),
                    notes = it.releaseDescription!!,
                    value = it.value.money,
                )
            }

    fun fromTotvsToMemberInvoiceItems(
        referenceDate: LocalDate,
        dueDate: LocalDate,
        composition: List<TotvsFirstPaymentSuccessRequest.Composition>
    ) =
        composition.filter { it.totvsId.isNotNullOrBlank() && it.aliceId.isNotNullOrBlank() }
            .groupBy {
                it.aliceId!!.toUUID()
            }.map { (memberId, items) ->
                val invoiceItems = fromTOTVSToItems(items)
                val totalAmount = invoiceItems.sumOf { it.value }

                NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail(
                    totalAmount = totalAmount,
                    memberId = memberId,
                    referenceDate = referenceDate,
                    dueDate = dueDate,
                    items = invoiceItems,
                )
            }

    fun FirstPaymentScheduleCreatedEvent.toTotvsScheduleFirstPaymentRequest(): TotvsScheduleFirstPaymentRequest {
        val preActivationPayment = this.payload.firstPaymentScheduleCreated.preActivationPayment
        val firstPaymentSchedule = this.payload.firstPaymentScheduleCreated.firstPaymentSchedule
        val company = this.payload.firstPaymentScheduleCreated.company
        val contract = this.payload.firstPaymentScheduleCreated.companyContract
        val subContract = this.payload.firstPaymentScheduleCreated.companySubContract
        val invoiceItemsWithPeople = this.payload.firstPaymentScheduleCreated.invoiceItemsWithPeople


        val index =
            preActivationPayment.externalId?.toTotvsInvoiceIndex() ?: throw PreActivationPaymentExternalIdNullException(
                preActivationPayment.id
            )

        val additionalBySubContract = preActivationPayment.globalItems.map { invoiceItem ->
            TotvsScheduleFirstPaymentRequest.Payload.Additional(
                level = TotvsScheduleFirstPaymentRequest.Payload.Additional.Level.SUBCONTRACT,
                cpfCnpj = company.cnpj,
                code = invoiceItem.type.toTotvs(invoiceItem.operation),
                valueType = invoiceItem.percentageValue?.let { TotvsScheduleFirstPaymentRequest.Payload.Additional.ValueType.PERCENTAGE }
                    ?: TotvsScheduleFirstPaymentRequest.Payload.Additional.ValueType.ABSOLUTE,
                value = invoiceItem.value.toDouble(),
                observation = invoiceItem.notes ?: "",
                idAddBilling = invoiceItem.id,
            )
        }

        val additionalByPeople = invoiceItemsWithPeople.map { (invoiceItem, person) ->
            TotvsScheduleFirstPaymentRequest.Payload.Additional(
                level = TotvsScheduleFirstPaymentRequest.Payload.Additional.Level.MEMBER,
                cpfCnpj = person.nationalId,
                code = invoiceItem.type.toTotvs(invoiceItem.operation),
                valueType = invoiceItem.percentageValue?.let { TotvsScheduleFirstPaymentRequest.Payload.Additional.ValueType.PERCENTAGE }
                    ?: TotvsScheduleFirstPaymentRequest.Payload.Additional.ValueType.ABSOLUTE,
                value = invoiceItem.value.toDouble(),
                observation = invoiceItem.notes ?: "",
                idAddBilling = invoiceItem.id,
            )
        }

        return TotvsScheduleFirstPaymentRequest(
            batchId = this.messageId.toString(),
            date = this.eventDate.totvsFormatDateDMY(),
            payload = TotvsScheduleFirstPaymentRequest.Payload(
                aliceId = firstPaymentSchedule.id.toString(),
                scheduleDay = firstPaymentSchedule.scheduledDate.dayOfMonth.toString(),
                prePayment = TotvsScheduleFirstPaymentRequest.Payload.PrePayment(
                    prefix = index.prefix,
                    number = index.number,
                    installment = index.installment,
                    type = index.type,
                ),
                additional = additionalBySubContract + additionalByPeople,
                groupCompany = contract.groupCompany!!,
                contractNumber = contract.externalId!!,
                subContractNumber = subContract.externalId!!,
            ),
        )
    }
}
