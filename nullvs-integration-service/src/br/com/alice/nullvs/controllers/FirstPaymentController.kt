package br.com.alice.nullvs.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.money
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.NULLVS_INTEGRATION_ENVIRONMENT_ENTRY
import br.com.alice.nullvs.common.fromTotvsFormatDateDMY
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.fromTOTVSToItems
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.fromTotvsToMemberInvoiceItems
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentFailedEvent
import br.com.alice.nullvs.models.TotvsInvoiceIndex
import br.com.alice.nullvs.models.payment_in.TotvsFirstPaymentFailureRequest
import br.com.alice.nullvs.models.payment_in.TotvsFirstPaymentSuccessRequest

class FirstPaymentController(
    private val kafkaProducerService: KafkaProducerService,
) {

    private suspend fun withEntryEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(NULLVS_INTEGRATION_ENVIRONMENT_ENTRY) {
            withUnauthenticatedTokenWithKey(NULLVS_INTEGRATION_ENVIRONMENT_ENTRY) {
                func.invoke()
            }
        }

    suspend fun processFirstPaymentSuccessful(request: String) = withEntryEnvironment {
        logger.info(
            "Received a processFirstPayment",
            "request" to request
        )

        if (checkIfIsFailure(request))
            processFirstPaymentFailure(request)
        else
            processFirstPaymentSuccess(request)

        "[accepted]".toResponse()
    }

    private fun getGlobalItems(composition: List<TotvsFirstPaymentSuccessRequest.Composition>) =
        fromTOTVSToItems(composition.filter { it.totvsId.isNullOrBlank() && it.aliceId.isNullOrBlank() })

    private suspend fun processFirstPaymentSuccess(request: String) {
        val body = gson.fromJson(request, TotvsFirstPaymentSuccessRequest::class.java)

        val referenceDate = body.let { "01${it.referenceMonth}${it.referenceYear}" }
            .fromTotvsFormatDateDMY()

        val dueDate = body.realDueDate.fromTotvsFormatDateDMY()
        val paidAt = body.paidAt.fromTotvsFormatDateDMY()

        val event = NullvsFirstPaymentCreatedEvent(
            payload = NullvsFirstPaymentCreatedEvent.Payload(
                firstPaymentScheduleId = body.invoiceScheduleId.trim(),
                externalId = TotvsInvoiceIndex(
                    prefix = body.prefix,
                    number = body.numberOfTitle,
                    installment = body.installment,
                    type = body.type,
                ).toString(),
                billingAccountablePartyId = RangeUUID.generate(),
                discount = body.firstPaymentDiscount.money,
                dueDate = dueDate,
                referenceDate = referenceDate,
                value = body.balance.money,
                paidAt = paidAt,
                gloablItems = getGlobalItems(body.composition),
                memberDetails = fromTotvsToMemberInvoiceItems(referenceDate, dueDate, body.composition),
                groupCompany = body.companyCode,
                companyContractNumber = body.companyContract,
                companySubContractNumber = body.companySubContract,
            )
        )

        logger.info(
            "Producing event ${event.name} for first payment ${body.invoiceScheduleId}",
            "event" to event,
        )

        kafkaProducerService.produce(event)
    }

    private suspend fun processFirstPaymentFailure(request: String) {

        logger.info(
            "First payment failure",
            "request" to request
        )

        val body = gson.fromJson(request, TotvsFirstPaymentFailureRequest::class.java)

        val event = NullvsFirstPaymentFailedEvent(
            payload = NullvsFirstPaymentFailedEvent.Payload(
                firstPaymentScheduleId = body.invoiceScheduleId.trim(),
                errorMessage = body.message,
            )
        )

        kafkaProducerService.produce(event)
    }

    private fun checkIfIsFailure(request: String) =
        gson.fromJson(request, TotvsFirstPaymentFailureRequest::class.java).message.isNotNullOrBlank()
}
