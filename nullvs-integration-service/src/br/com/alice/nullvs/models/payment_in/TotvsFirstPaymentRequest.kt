package br.com.alice.nullvs.models.payment_in

import com.google.gson.annotations.SerializedName

data class TotvsFirstPaymentFailureRequest(
    @SerializedName("invoiceScheduleId")
    val invoiceScheduleId: String,
    val message: String
)

data class TotvsFirstPaymentSuccessRequest(
    @SerializedName("descontoPrimeiroPagamento")
    val firstPaymentDiscount: Double,
    @SerializedName("invoiceScheduleId")
    val invoiceScheduleId: String,
    @SerializedName("E1_PREFIXO")
    val prefix: String,
    @SerializedName("E1_NUM")
    val numberOfTitle: String,
    @SerializedName("E1_PARCELA")
    val installment: String,
    @SerializedName("E1_TIPO")
    val type: String,
    @SerializedName("E1_CLIENTE")
    val client: String,
    @SerializedName("E1_LOJA")
    val store: String,
    @SerializedName("E1_NOMCLI")
    val clientName: String?,
    @SerializedName("E1_EMISSAO")
    val issuedDate: String?,
    @SerializedName("E1_VENCREA")
    val realDueDate: String,
    @SerializedName("E1_VALOR")
    val value: Double?,
    @SerializedName("E1_BAIXA")
    val paidAt: String,
    @SerializedName("E1_SALDO")
    val balance: Double,
    @SerializedName("E1_VALLIQ")
    val netValue: Double?,
    @SerializedName("COMPOSICAO")
    val composition: List<Composition>,
    @SerializedName("E1_XLOTE")
    val batchIntegration: String?,
    @SerializedName("E1_ANOBASE")
    val referenceYear: String,
    @SerializedName("E1_MESBASE")
    val referenceMonth: String,
    @SerializedName("E1_CODEMP")
    val companyCode: String,
    @SerializedName("E1_CONEMP")
    val companyContract: String,
    @SerializedName("E1_SUBCON")
    val companySubContract: String,
) {
    data class Composition(
        @SerializedName("FILIAL")
        val filial: String?,
        @SerializedName("IDTOTVS")
        val totvsId: String?,
        @SerializedName("IDALICE")
        val aliceId: String?,
        @SerializedName("SEQUENCIAL")
        val sequential: String?,
        @SerializedName("ANO")
        val year: String,
        @SerializedName("MES")
        val month: String,
        @SerializedName("LANCAMENTO")
        val release: String,
        @SerializedName("DESCLAN")
        val releaseDescription: String?,
        @SerializedName("TIPO")
        val type: String,
        @SerializedName("VALOR")
        val value: Double,
        @SerializedName("NOMEUSUARIO")
        val userName: String?,
    )
}
