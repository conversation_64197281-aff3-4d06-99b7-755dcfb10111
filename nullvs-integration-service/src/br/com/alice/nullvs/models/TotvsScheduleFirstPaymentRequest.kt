package br.com.alice.nullvs.models

import com.google.gson.annotations.SerializedName
import java.util.UUID

private const val defaultTotvsRequestType = "movimento"
private const val defaultTotvsRequestEntity = "agendar"

data class TotvsScheduleFirstPaymentRequest(
    val total: Int = 1,
    @SerializedName("idlote")
    val batchId: String,
    @SerializedName("tipo")
    val type: String = defaultTotvsRequestType,
    @SerializedName("entidade")
    val entity: String = defaultTotvsRequestEntity,
    @SerializedName("data")
    val date: String,
    @SerializedName("usuario")
    val user: String = TotvsDefaultUsusario,
    @SerializedName("acao")
    val action: String = TotvsActionClient.INSERT.code,
    val payload: Payload
) {
    data class Payload(
        @SerializedName("ALICE_ID")
        val aliceId: String,
        @SerializedName("DIA")
        val scheduleDay: String,
        @SerializedName("RA")
        val prePayment: PrePayment,
        @SerializedName("ADICIONAIS")
        val additional: List<Additional>,
        @SerializedName("CODEMP")
        val groupCompany: String,
        @SerializedName("CONEMP")
        val contractNumber: String,
        @SerializedName("SUBCON")
        val subContractNumber: String,
    ) {
        data class PrePayment(
            @SerializedName("PREFIXO")
            val prefix: String,
            @SerializedName("NUMERO")
            val number: String,
            @SerializedName("PARCELA")
            val installment: String,
            @SerializedName("TIPO")
            val type: String,
        )

        data class Additional(
            @SerializedName("nivel_adicional")
            val level: Level,
            @SerializedName("cpf_cnpj")
            val cpfCnpj: String,
            @SerializedName("codigo_lancamento")
            val code: String,
            @SerializedName("tipo__valor")
            val valueType: ValueType,
            @SerializedName("valor_lancamento")
            val value: Double,
            @SerializedName("observacao")
            val observation: String,
            @SerializedName("id_add_billing")
            val idAddBilling: UUID,
        ) {
            enum class Level(val description: String) {
                MEMBER("membro"),
                SUBCONTRACT("subcontrato")
            }

            enum class ValueType(val description: String) {
                PERCENTAGE("0"),
                ABSOLUTE("1")
            }
        }
    }
}
