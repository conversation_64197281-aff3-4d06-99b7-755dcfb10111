{"invoiceScheduleId": "0001", "descontoPrimeiroPagamento": 50.0, "E1_FILIAL": "", "E1_PREFIXO": "PLS", "E1_NUM": "0000001", "E1_PARCELA": "01", "E1_TIPO": "DP", "E1_CLIENTE": "000001", "E1_LOJA": "01", "E1_EMISSAO": "26052021", "E1_VENCREA": "30052021", "E1_BAIXA": "30052021", "E1_ANOBASE": "2024", "E1_MESBASE": "11", "E1_VALOR": 4400.0, "E1_SALDO": 4400.0, "E1_NOMCLI": "NOME DO RESPONSAVEL FINANCEIRO 1", "E1_XLOTE": "0000000012", "E1_CONEMP": "000000000002", "E1_SUBCON": "000000001", "E1_CODEMP": "0022", "COMPOSICAO": [{"FILIAL": " ", "IDTOTVS": "00010002000001010", "IDALICE": "8b01691a-b706-49a9-9527-4ff941b37cca", "SEQUENCIAL": "001", "ANO": "2023", "MES": "03", "LANCAMENTO": "101", "DESCLAN": "Produto/Plano (Mensalidade)", "TIPO": "1", "VALOR": 3000.0, "NOMEUSUARIO": "NOME DO PAULO"}, {"FILIAL": " ", "IDTOTVS": "00010002000001010", "IDALICE": "8b01691a-b706-49a9-9527-4ff941b37cca", "SEQUENCIAL": "002", "ANO": "2023", "MES": "03", "LANCAMENTO": "924", "DESCLAN": "DESCONTO MUDANÇA DE PLANO", "TIPO": "2", "VALOR": 400.0, "NOMEUSUARIO": "NOME DO PAULO"}, {"FILIAL": " ", "IDTOTVS": "00010002000001110", "IDALICE": "b8326a64-57f0-49ee-af4d-d9d826c9500c", "SEQUENCIAL": "001", "ANO": "2023", "MES": "03", "LANCAMENTO": "101", "DESCLAN": "Produto/Plano (Mensalidade)", "TIPO": "1", "VALOR": 2000.0, "NOMEUSUARIO": "NOME DO JOÃO"}, {"FILIAL": " ", "IDTOTVS": "00010002000001110", "IDALICE": "b8326a64-57f0-49ee-af4d-d9d826c9500c", "SEQUENCIAL": "002", "ANO": "2023", "MES": "03", "LANCAMENTO": "924", "DESCLAN": "DESCONTO MUDANÇA DE PLANO", "TIPO": "2", "VALOR": 200.0, "NOMEUSUARIO": "NOME DO JOÃO"}]}