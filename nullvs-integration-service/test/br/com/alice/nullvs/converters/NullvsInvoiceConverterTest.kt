package br.com.alice.nullvs.converters

import br.com.alice.business.events.FirstPaymentScheduleCreated
import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.money
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.InvoiceBreakdown
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.ProductType
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.common.fromTotvsFormatDateDMY
import br.com.alice.nullvs.common.totvsFormatDateDMY
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.extractAmountPaidFineInterest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.fromTOTVSToItems
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.fromTotvsToMemberInvoiceItems
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsIntegrationLog
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceGroupGeneratedEvent
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequestResponse
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvs
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsInvoiceRequest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsReceivableIntegrationRequest
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.events.NullvsPaymentGroupGeneratedEvent
import br.com.alice.nullvs.exceptions.PreActivationPaymentExternalIdNullException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsIntegrationBatchResponse
import br.com.alice.nullvs.models.TotvsReceivableIntegrationRequest
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceData
import br.com.alice.nullvs.models.payment.TotvsInvoiceResponse
import br.com.alice.nullvs.models.payment_in.TotvsFirstPaymentSuccessRequest
import br.com.alice.nullvs.models.payment_in.TotvsInvoiceCreationBatch
import br.com.alice.nullvs.models.payment_in.TotvsInvoiceRequest
import com.github.kittinunf.result.success
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertFailsWith

class NullvsInvoiceConverterTest {

    private val meta = Meta(
        eventId = RangeUUID.generate(),
        eventName = "event01",
        internalId = RangeUUID.generate(),
        internalModelName = InternalModelType.MEMBER_INVOICE_GROUP,
        integrationEventName = "nullvs-invoice-request-event",
        externalId = null,
        externalModelName = ExternalModelType.INVOICE,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    companion object {
        @JvmStatic
        fun fromMemberInvoiceToNullvsInvoiceRequestData() = listOf(
            arrayOf(
                ProductType.B2B,
                PaymentMethod.SIMPLE_CREDIT_CARD,
                "NCC",
                "20009",
                "RECEITAS B2B - ALICE",
                "PPG",
                null,
                null,
                null,
                null,
                null
            ),
            arrayOf(
                ProductType.B2B,
                PaymentMethod.BOLETO,
                "RA",
                "20009",
                "RECEITAS B2B - ALICE",
                "PPG",
                null,
                null,
                null,
                null,
                null
            ),
            arrayOf(
                ProductType.B2C,
                PaymentMethod.PIX,
                "NCC",
                "20010",
                "RECEITAS B2C - ALICE",
                "PLC",
                "PLC|00132|01|NCC",
                "00132",
                "01",
                LocalDateTime.of(2022, 2, 2, 10, 0, 0)
            ),
        )

        @JvmStatic
        fun fromMemberInvoiceGroupToNullvsInvoiceRequestData() = listOf(
            arrayOf(
                ProductType.B2B,
                PaymentMethod.SIMPLE_CREDIT_CARD,
                "DP",
                "20009",
                "RECEITAS B2B - ALICE",
                "PLC",
                "PLC|00132|01|DP",
                "00132",
                "01",
                null,
            ),
            arrayOf(
                ProductType.B2B,
                PaymentMethod.BOLETO,
                "DP",
                "20009",
                "RECEITAS B2B - ALICE",
                "PLS",
                "PLS|00132|01|DP",
                "00132",
                "01",
                null,
            ),
            arrayOf(
                ProductType.B2C,
                PaymentMethod.PIX,
                "DP",
                "20010",
                "RECEITAS B2C - ALICE",
                "PLC",
                "PLC|00132|01|DP",
                "00132",
                "01",
                LocalDateTime.of(2022, 2, 2, 10, 0, 0),
            ),
        )

        @JvmStatic
        fun amounts() = listOf(
            arrayOf(
                190.money,
                200.money,
                7.money,
                3.money,
                190.money,
                7.money,
                3.money,
            ),
            arrayOf(
                190.money,
                198.money,
                7.money,
                3.money,
                190.money,
                8.money,
                0.money,
            ),
            arrayOf(
                190.money,
                190.money,
                null,
                null,
                190.money,
                0.money,
                0.money,
            ),
            arrayOf(
                190.money,
                null,
                null,
                null,
                190.money,
                0.money,
                0.money,
            ),
            arrayOf(
                190.money,
                null,
                9.money,
                null,
                190.money,
                0.money,
                0.money,
            ),
        )
    }

    @Nested
    inner class ExtractAmountPaidFineInterest {

        @ParameterizedTest
        @MethodSource("br.com.alice.nullvs.converters.NullvsInvoiceConverterTest#amounts")
        fun `extract from invoicePayment`(
            amount: BigDecimal,
            amountPaid: BigDecimal?,
            fine: BigDecimal?,
            interest: BigDecimal?,
            expectedAmountPaid: BigDecimal,
            expectedFine: BigDecimal,
            expectedInterest: BigDecimal?,
        ) {
            val invoicePayment = TestModelFactory.buildInvoicePayment(
                amount = amount,
            ).copy(
                amountPaid = amountPaid,
                fine = fine,
                interest = interest
            )

            val result = invoicePayment.extractAmountPaidFineInterest()
            assertThat(result).isEqualTo(Triple(expectedAmountPaid, expectedFine, expectedInterest))
        }
    }

    @Nested
    inner class ConvertToNullvsInvoiceRequest {
        @ParameterizedTest(name = "should generate invoices for payment method {0}")
        @MethodSource("br.com.alice.nullvs.converters.NullvsInvoiceConverterTest#fromMemberInvoiceToNullvsInvoiceRequestData")
        fun `#toNullvsInvoiceRequest - converts correctly from MemberInvoice to NullvsInvoiceRequest`(
            productType: ProductType,
            paymentMethod: PaymentMethod,
            expectedType: String,
            expectedNatureCode: String,
            expectedFinancialHistory: String,
            expectedTitlePrefix: String,
            titleCode: String?,
            expectedTitleNumber: String?,
            expectedTitleInstallment: String?,
            paidAt: LocalDateTime?
        ) {
            val memberInvoice = TestModelFactory.buildMemberInvoice(
                dueDate = LocalDateTime.of(2023, 1, 10, 1, 1),
                totalAmount = BigDecimal("1000.0"),
                discount = BigDecimal("10.00"),
                referenceDate = LocalDate.of(2023, 1, 1),
                status = InvoiceStatus.OPEN,
            ).copy(paidAt = paidAt)
            val expected = NullvsInvoiceBatchRequest(
                meta = meta,
                action = NullvsActionType.CREATE,
                invoiceCreationData = NullvsInvoiceData(
                    titlePrefix = expectedTitlePrefix,
                    dueDate = memberInvoice.dueDate.toSaoPauloTimeZone().toLocalDate(),
                    amount = 1000.0,
                    financialHistory = expectedFinancialHistory,
                    type = expectedType,
                    discount = 10.0,
                    status = NullvsInvoiceData.Status.OPEN,
                    clientCode = "client-code",
                    emittedDate = memberInvoice.referenceDate,
                    fine = 0.0,
                    interest = 0.0,
                    natureCode = expectedNatureCode,
                    monetaryCorrection = 0.0,
                    paidAt = paidAt?.toSaoPauloTimeZone()?.toLocalDate(),
                    titleInstallment = expectedTitleInstallment,
                    titleNumber = expectedTitleNumber,
                    memberId = memberInvoice.memberId,
                    memberInvoiceGroupId = memberInvoice.id
                )
            )

            val result = memberInvoice.toNullvsInvoiceRequest(
                meta,
                NullvsActionType.CREATE,
                productType,
                paymentMethod,
                "client-code",
                titleCode
            )

            assertThat(result).isEqualTo(expected)
        }

        @ParameterizedTest(name = "should generate invoices for payment method {0}")
        @MethodSource("br.com.alice.nullvs.converters.NullvsInvoiceConverterTest#fromMemberInvoiceGroupToNullvsInvoiceRequestData")
        fun `#toNullvsInvoiceRequest - converts correctly from MemberInvoiceGroup to NullvsInvoiceRequest`(
            productType: ProductType,
            paymentMethod: PaymentMethod,
            expectedType: String,
            expectedNatureCode: String,
            expectedFinancialHistory: String,
            expectedTitlePrefix: String,
            titleCode: String,
            expectedTitleNumber: String?,
            expectedTitleInstallment: String?,
            paidAt: LocalDateTime?,
        ) {
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
                dueDate = LocalDate.of(2023, 9, 15),
                totalAmount = BigDecimal("1000.0"),
                referenceDate = LocalDate.of(2023, 1, 1),
                status = MemberInvoiceGroupStatus.WAITING_PAYMENT,
                externalId = titleCode,
            )

            val invoicePayment = TestModelFactory.buildInvoicePayment(
                method = paymentMethod,
                approvedAt = paidAt,
                amount = memberInvoiceGroup.totalAmount!!
            )

            val expected = NullvsInvoiceBatchRequest(
                meta = meta,
                action = NullvsActionType.UPDATE_PAYMENT,
                invoiceCreationData = NullvsInvoiceData(
                    titlePrefix = expectedTitlePrefix,
                    dueDate = memberInvoiceGroup.dueDate,
                    amount = 1000.0,
                    financialHistory = expectedFinancialHistory,
                    type = expectedType,
                    discount = 0.0,
                    status = NullvsInvoiceData.Status.OPEN,
                    clientCode = "client-code",
                    emittedDate = memberInvoiceGroup.referenceDate,
                    fine = 0.0,
                    interest = 0.0,
                    natureCode = expectedNatureCode,
                    monetaryCorrection = 0.0,
                    paidAt = paidAt?.toSaoPauloTimeZone()?.toLocalDate(),
                    titleInstallment = expectedTitleInstallment,
                    titleNumber = expectedTitleNumber,
                    externalPaymentId = invoicePayment.id.toString()
                )
            )

            val result = memberInvoiceGroup.toNullvsInvoiceRequest(
                meta,
                NullvsActionType.UPDATE_PAYMENT,
                productType,
                invoicePayment,
                "client-code",
            )

            assertThat(result).isEqualTo(expected)
        }
    }

    @Nested
    inner class TotvsPaymentBatchConvertToNullvsInvoiceGroupGeneratedEvent {
        @Test
        fun `#toNullvsMemberBatchCreationResponse - converts correctly`() {
            val firstMember = TestModelFactory.buildMember()
            val secondMember = TestModelFactory.buildMember()
            val thirdMember = TestModelFactory.buildMember()

            val firstBillingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val secondBillingAccountableParty = TestModelFactory.buildBillingAccountableParty()

            val totvsPaymentBatch = TotvsInvoiceCreationBatch(
                total = 1,
                batchId = "001",
                type = "movimento",
                entity = "titulo",
                date = "********",
                action = "insert",
                user = "TOTVS",
                payload = listOf(
                    TotvsInvoiceCreationBatch.Payload(
                        prefix = "PLS",
                        installment = "01",
                        type = "DP",
                        client = "000001",
                        clientName = "João D'Oliveira",
                        store = "01",
                        issuedDate = "********",
                        value = 1600.0,
                        balance = 1600.0,
                        batchIntegration = "001",
                        netValue = 0.00,
                        realDueDate = "********",
                        numberOfTitle = "000001",
                        paidAt = "29032023",
                        referenceDate = "01052023",
                        composition = listOf(
                            TotvsInvoiceCreationBatch.Payload.Composition(
                                filial = "",
                                totvsId = "0000000001",
                                aliceId = firstMember.id.toString(),
                                sequential = "001",
                                year = "2023",
                                month = "03",
                                release = "101",
                                releaseDescription = "Produto/Plano (Mensalidade)",
                                type = "1",
                                value = 1200.00,
                                userName = "Some user"
                            ),
                            TotvsInvoiceCreationBatch.Payload.Composition(
                                filial = "",
                                totvsId = "0000000001",
                                aliceId = firstMember.id.toString(),
                                sequential = "001",
                                year = "2023",
                                month = "03",
                                release = "924",
                                releaseDescription = "DESCONTO MUDANÇA DE PLANO",
                                type = "2",
                                value = 200.00,
                                userName = "Some user"
                            ),
                            TotvsInvoiceCreationBatch.Payload.Composition(
                                filial = "",
                                totvsId = "0000000002",
                                aliceId = secondMember.id.toString(),
                                sequential = "001",
                                year = "2023",
                                month = "03",
                                release = "101",
                                releaseDescription = "Produto/Plano (Mensalidade)",
                                type = "1",
                                value = 600.00,
                                userName = "Some user"
                            ),
                            TotvsInvoiceCreationBatch.Payload.Composition(
                                filial = "",
                                totvsId = "0000000002",
                                aliceId = secondMember.id.toString(),
                                sequential = "001",
                                year = "2023",
                                month = "03",
                                release = "unknown",
                                releaseDescription = "Um lançamento qualquer",
                                type = "1",
                                value = 50.00,
                                userName = "Some user"
                            ),
                        ),
                    ),

                    TotvsInvoiceCreationBatch.Payload(
                        prefix = "PLS",
                        installment = "01",
                        type = "DP",
                        client = "000002",
                        clientName = "José Pereira",
                        store = "01",
                        issuedDate = "********",
                        value = 400.32,
                        balance = 400.32,
                        batchIntegration = "002",
                        netValue = 0.00,
                        realDueDate = "********",
                        numberOfTitle = "000002",
                        paidAt = "29032023",
                        referenceDate = "01052023",
                        composition = listOf(
                            TotvsInvoiceCreationBatch.Payload.Composition(
                                filial = "",
                                totvsId = "0000000003",
                                aliceId = thirdMember.id.toString(),
                                sequential = "001",
                                year = "2023",
                                month = "03",
                                release = "101",
                                releaseDescription = "Produto/Plano (Mensalidade)",
                                type = "1",
                                value = 400.32,
                                userName = "Some user"
                            ),
                            TotvsInvoiceCreationBatch.Payload.Composition(
                                filial = "",
                                totvsId = "",
                                aliceId = "",
                                sequential = "002",
                                year = "2023",
                                month = "03",
                                release = "929",
                                releaseDescription = "ACRESCIMO AJT OPERACIONAL",
                                type = "1",
                                value = 100.32,
                                userName = ""
                            ),
                        ),
                    ),
                )
            )

            val members =
                mapOf(firstMember.id to firstMember, secondMember.id to secondMember, thirdMember.id to thirdMember)

            val billingAccountableParties =
                mapOf("000001" to firstBillingAccountableParty.id, "000002" to secondBillingAccountableParty.id)

            val firstMemberInvoiceItems = listOf(
                TestModelFactory.buildInvoiceItem(
                    type = InvoiceItemType.PRODUCT_PRICE,
                    notes = "Produto/Plano (Mensalidade)",
                    absoluteValue = BigDecimal("1200.0"),
                    personId = firstMember.personId,
                    referenceDate = LocalDate.of(2023, 3, 1),
                    resolvedValue = BigDecimal("1200.00"),
                ),
                TestModelFactory.buildInvoiceItem(
                    operation = InvoiceItemOperation.DISCOUNT,
                    type = InvoiceItemType.PRODUCT_CHANGE,
                    notes = "DESCONTO MUDANÇA DE PLANO",
                    absoluteValue = BigDecimal("200.0"),
                    personId = firstMember.personId,
                    referenceDate = LocalDate.of(2023, 3, 1),
                    resolvedValue = BigDecimal("-200.00"),
                )
            )

            val secondMemberInvoiceItems = listOf(
                TestModelFactory.buildInvoiceItem(
                    operation = InvoiceItemOperation.CHARGE,
                    type = InvoiceItemType.PRODUCT_PRICE,
                    notes = "Produto/Plano (Mensalidade)",
                    absoluteValue = BigDecimal("600.0"),
                    personId = secondMember.personId,
                    referenceDate = LocalDate.of(2023, 3, 1),
                    resolvedValue = BigDecimal("600.00"),
                ),
                TestModelFactory.buildInvoiceItem(
                    operation = InvoiceItemOperation.CHARGE,
                    type = InvoiceItemType.OTHERS,
                    notes = "Um lançamento qualquer",
                    absoluteValue = BigDecimal("50.0"),
                    personId = secondMember.personId,
                    referenceDate = LocalDate.of(2023, 3, 1),
                    resolvedValue = BigDecimal("50.00"),
                )
            )

            val thirdMemberInvoiceItems = listOf(
                TestModelFactory.buildInvoiceItem(
                    operation = InvoiceItemOperation.CHARGE,
                    type = InvoiceItemType.PRODUCT_PRICE,
                    notes = "Produto/Plano (Mensalidade)",
                    absoluteValue = BigDecimal("400.32"),
                    personId = thirdMember.personId,
                    referenceDate = LocalDate.of(2023, 3, 1),
                    resolvedValue = BigDecimal("400.32"),
                ),

                )

            val globalItems = listOf(
                TestModelFactory.buildInvoiceItem(
                    operation = InvoiceItemOperation.CHARGE,
                    type = InvoiceItemType.OPERATIONAL_ADJUSTMENT,
                    notes = "ACRESCIMO AJT OPERACIONAL",
                    absoluteValue = BigDecimal("100.32"),
                    referenceDate = LocalDate.of(2023, 3, 1),
                    personId = null
                )
            )

            val expectedResult = listOf(
                NullvsPaymentGroupGeneratedEvent(
                    memberInvoices = listOf(
                        MemberInvoice(
                            memberId = firstMember.id,
                            personId = firstMember.personId,
                            totalAmount = BigDecimal("1000.0"),
                            status = InvoiceStatus.OPEN,
                            canceledReason = null,
                            referenceDate = LocalDate.of(2023, 5, 1),
                            dueDate = LocalDateTime.of(2023, 3, 31, 23, 59, 59),
                            paidAt = LocalDateTime.of(2023, 3, 29, 0, 0),
                            invoiceItems = firstMemberInvoiceItems,
                            invoiceBreakdown = InvoiceBreakdown(
                                totalAmount = BigDecimal("1000.00"),
                                invoiceItems = firstMemberInvoiceItems,
                                productPrice = BigDecimal("1200.00"),
                                promoCode = BigDecimal("0.00"),
                                proRation = BigDecimal("0.00"),
                                copay = BigDecimal("0.00"),
                                productChange = BigDecimal("-200.00"),
                                sales = BigDecimal("0.00"),
                                discount = BigDecimal("-200.00"),
                                addition = BigDecimal("1200.00"),
                                promoCodeResult = BigDecimal("0.00"),
                                salesResult = BigDecimal("0.00"),
                                others = BigDecimal("0.00"),
                                operationalAdjustment = BigDecimal("0.00"),
                                retroactiveMonthlyFeeBilling = BigDecimal("0.00"),
                                readjustment = BigDecimal("0.00"),
                            )
                        ),
                        MemberInvoice(
                            memberId = secondMember.id,
                            personId = secondMember.personId,
                            totalAmount = BigDecimal("650.0"),
                            status = InvoiceStatus.OPEN,
                            canceledReason = null,
                            referenceDate = LocalDate.of(2023, 5, 1),
                            dueDate = LocalDateTime.of(2023, 3, 31, 23, 59, 59),
                            paidAt = LocalDateTime.of(2023, 3, 29, 0, 0),
                            invoiceItems = secondMemberInvoiceItems,
                            invoiceBreakdown = InvoiceBreakdown(
                                totalAmount = BigDecimal("650.00"),
                                invoiceItems = secondMemberInvoiceItems,
                                productPrice = BigDecimal("600.00"),
                                promoCode = BigDecimal("0.00"),
                                proRation = BigDecimal("0.00"),
                                copay = BigDecimal("0.00"),
                                productChange = BigDecimal("0.00"),
                                sales = BigDecimal("0.00"),
                                discount = BigDecimal("0.00"),
                                addition = BigDecimal("650.00"),
                                promoCodeResult = BigDecimal("0.00"),
                                salesResult = BigDecimal("0.00"),
                                others = BigDecimal("50.00"),
                                operationalAdjustment = BigDecimal("0.00"),
                                retroactiveMonthlyFeeBilling = BigDecimal("0.00"),
                                readjustment = BigDecimal("0.00"),
                            )
                        ),
                    ),
                    referenceDate = LocalDate.of(2023, 5, 1),
                    dueDate = LocalDate.of(2023, 3, 31),
                    paymentMethod = PaymentMethod.BOLETO,
                    externalId = "PLS|000001|01|DP",
                    billingAccountablePartyId = billingAccountableParties["000001"]!!,
                    globalItems = listOf()
                ),
                NullvsPaymentGroupGeneratedEvent(
                    memberInvoices = listOf(
                        MemberInvoice(
                            memberId = thirdMember.id,
                            personId = thirdMember.personId,
                            totalAmount = BigDecimal("400.32"),
                            status = InvoiceStatus.OPEN,
                            canceledReason = null,
                            referenceDate = LocalDate.of(2023, 5, 1),
                            dueDate = LocalDateTime.of(2023, 3, 31, 23, 59, 59),
                            paidAt = LocalDateTime.of(2023, 3, 29, 0, 0),
                            invoiceItems = thirdMemberInvoiceItems,
                            invoiceBreakdown = InvoiceBreakdown(
                                totalAmount = BigDecimal("400.32"),
                                invoiceItems = thirdMemberInvoiceItems,
                                productPrice = BigDecimal("400.32"),
                                promoCode = BigDecimal("0.00"),
                                proRation = BigDecimal("0.00"),
                                copay = BigDecimal("0.00"),
                                productChange = BigDecimal("0.00"),
                                sales = BigDecimal("0.00"),
                                discount = BigDecimal("0.00"),
                                addition = BigDecimal("400.32"),
                                promoCodeResult = BigDecimal("0.00"),
                                salesResult = BigDecimal("0.00"),
                                others = BigDecimal("0.00"),
                                operationalAdjustment = BigDecimal("0.00"),
                                retroactiveMonthlyFeeBilling = BigDecimal("0.00"),
                                readjustment = BigDecimal("0.00"),
                            )
                        ),
                    ),
                    referenceDate = LocalDate.of(2023, 5, 1),
                    dueDate = LocalDate.of(2023, 3, 31),
                    paymentMethod = PaymentMethod.BOLETO,
                    externalId = "PLS|000002|01|DP",
                    billingAccountablePartyId = billingAccountableParties["000002"]!!,
                    globalItems = globalItems
                )
            )

            val result = totvsPaymentBatch.payload.map {
                it.toNullvsInvoiceGroupGeneratedEvent(
                    members,
                    billingAccountableParties
                )
            }

            assertThat(result)
                .usingRecursiveComparison()
                .ignoringFields(
                    "eventDate",
                    "messageId",
                    "payload.memberInvoices.id",
                    "payload.memberInvoices.createdAt",
                    "payload.memberInvoices.invoiceItems.id",
                    "payload.memberInvoices.invoiceItems.createdAt",
                    "payload.memberInvoices.invoiceItems.updatedAt",
                    "payload.memberInvoices.invoiceBreakdown.invoiceItems.id",
                    "payload.memberInvoices.invoiceBreakdown.invoiceItems.createdAt",
                    "payload.memberInvoices.invoiceBreakdown.invoiceItems.updatedAt",
                    "payload.globalItems.id",
                    "payload.globalItems.createdAt",
                    "payload.globalItems.updatedAt",
                ).isEqualTo(expectedResult)
        }
    }

    @Nested
    inner class TotvsIntegrationBatchResponseToNullvsInvoiceBatchRequestResponse {

        private val meta = Meta(
            eventId = RangeUUID.generate(),
            eventName = "event01",
            internalId = RangeUUID.generate(),
            internalModelName = InternalModelType.MEMBER,
            integrationEventName = "integration01",
            externalId = null,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = LocalDateTime.now().minusDays(1),
            originalTopic = "original01",
        )

        @Test
        fun `#toNullvsInvoiceRequestResponse`() {
            val response = TotvsIntegrationBatchResponse(
                httpStatus = "200",
                batch = "123",
                idSoc = "aa123",
                action = "insercao",
            )
            meta

            val expectedResult = TotvsInvoiceResponse(
                httpStatus = response.httpStatus,
                batch = response.batch,
                idSoc = response.idSoc,
                action = response.action,
            )

            val result = response.toNullvsInvoiceRequestResponse(meta)
            assertThat(result.metadata).isEqualTo(meta)
            assertThat(result.totvsInvoiceResponse).isEqualTo(expectedResult)
        }
    }

    @Nested
    inner class NullvsInvoiceBatchRequestToTotvsInvoiceBatchRequest {

        val eventId: UUID = RangeUUID.generate()
        val internalId: UUID = RangeUUID.generate()

        private val meta = Meta(
            eventId = eventId,
            eventName = "event01",
            internalId = internalId,
            internalModelName = InternalModelType.MEMBER_INVOICE_GROUP,
            integrationEventName = "integration01",
            externalId = null,
            externalModelName = ExternalModelType.INVOICE,
            integratedAt = LocalDateTime.now().minusDays(1),
            originalTopic = "original01",
        )

        private val nullvsInvoiceData = NullvsInvoiceData(
            titlePrefix = "F",
            emittedDate = "2023-01-01".toLocalDate(),
            dueDate = "2023-02-01".toLocalDate(),
            amount = 1000.0,
            status = NullvsInvoiceData.Status.OPEN,
            financialHistory = "Some financial history",
            clientCode = "12345",
            type = "DP",
            discount = 50.0,
            fine = 10.0,
            interest = 5.0,
            monetaryCorrection = 0.0,
            natureCode = "001",
            titleNumber = null,
            titleInstallment = null,
            paidAt = null,
        )

        private val requestPayload = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)

        @Test
        fun `should convert NullvsInvoiceBatchRequest to TotvsInvoiceRequest`() {
            val nullvsInvoiceData = nullvsInvoiceData.copy(
                paidAt = "2023-05-11".toLocalDate(),
                emittedDate = "2023-05-01".toLocalDate(),
                dueDate = "2023-05-20".toLocalDate(),
                memberInvoiceGroupId = UUID.randomUUID(),
                titlePrefix = "PPG",
            )

            val requestPayload = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)

            val result = requestPayload.toTotvsInvoiceRequest()

            val expected = TotvsInvoiceRequest(
                action = "insert",
                date = meta.integratedAt.totvsFormatDateDMY(),
                idSoc = requestPayload.meta.eventId.toString(),
                payload = listOf(
                    TotvsInvoiceRequest.Payload(
                        prefix = "PPG",
                        type = "DP",
                        client = "12345",
                        paidAt = "11052023",
                        issuedDate = "01052023",
                        dueDate = "20052023",
                        financialHistory = "Some financial history",
                        situation = "0",
                        value = 1000.0,
                        interest = 5.0,
                        fine = 10.0,
                        discount = 50.0,
                        monetaryCorrection = 0.0,
                        origin = "001",
                        memberInvoiceGroupId = nullvsInvoiceData.memberInvoiceGroupId!!
                    )
                ),
            )

            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `should throw exception if member invoice id is missing`() {
            val nullvsInvoiceData = nullvsInvoiceData.copy(
                paidAt = "2023-05-11".toLocalDate(),
                emittedDate = "2023-05-01".toLocalDate(),
                dueDate = "2023-05-20".toLocalDate(),
                memberInvoiceGroupId = null
            )
            val requestPayload = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)

            assertThrows<IllegalArgumentException> { requestPayload.toTotvsInvoiceRequest() }
        }

        @Nested
        inner class ToTotvsReceivableIntegrationRequest {
            @Test
            fun `should convert NullvsInvoiceBatchRequest to toTotvsReceivableIntegrationRequest`() {
                val nullvsInvoiceData = nullvsInvoiceData.copy(
                    titleNumber = "titleNumber",
                    titleInstallment = "titleInstallment",
                    paidAt = "2023-01-01".toLocalDate(),
                    externalPaymentId = "123456"
                )

                val requestPayload = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)
                val result = requestPayload.toTotvsReceivableIntegrationRequest()

                val expected = TotvsReceivableIntegrationRequest(
                    batchId = requestPayload.meta.eventId.toString(),
                    date = requestPayload.meta.integratedAt.totvsFormatDateDMY(),
                    action = "update",
                    payload = listOf(
                        TotvsReceivableIntegrationRequest.Payload(
                            prefix = nullvsInvoiceData.titlePrefix,
                            numberOfTitle = nullvsInvoiceData.titleNumber!!,
                            installment = nullvsInvoiceData.titleInstallment!!,
                            type = nullvsInvoiceData.type,
                            client = nullvsInvoiceData.clientCode,
                            paidAt = nullvsInvoiceData.paidAt!!.totvsFormatDateDMY(),
                            value = nullvsInvoiceData.amount,
                            description = "",
                            interest = nullvsInvoiceData.interest,
                            fine = nullvsInvoiceData.fine,
                            discount = nullvsInvoiceData.discount,
                            externalPaymentId = "123456"
                        )
                    ),
                )

                assertThat(result).isEqualTo(expected)
            }

            @Test
            fun `should throw exception when required field is null`() {
                assertFailsWith<NullPointerException> { requestPayload.toTotvsReceivableIntegrationRequest() }
            }

            @Test
            fun `should throw exception if externalPaymentid is missing`() {
                val nullvsInvoiceData = nullvsInvoiceData.copy(
                    titleNumber = "titleNumber",
                    titleInstallment = "titleInstallment",
                    paidAt = "2023-01-01".toLocalDate(),
                    externalPaymentId = null
                )
                val requestPayload = NullvsInvoiceBatchRequest(meta, NullvsActionType.UPDATE_PAYMENT, nullvsInvoiceData)

                assertThrows<IllegalArgumentException> { requestPayload.toTotvsReceivableIntegrationRequest() }
            }
        }

    }

    @Nested
    inner class ConvertToNullvsIntegrationLog {

        private val totvsInvoiceResponse = TotvsInvoiceResponse(
            httpStatus = "200",
            batch = "batch01",
            idSoc = "idSoc01",
            action = "I",
        )

        @Test
        fun `#toNullvsIntegrationLog - converts correctly to NullvsIntegrationLog`() {
            val nullvsInvoiceBatchResponse = NullvsInvoiceBatchResponse(meta, totvsInvoiceResponse)
            val expected = NullvsIntegrationLog(
                eventId = meta.eventId,
                eventName = meta.eventName,
                integrationEventName = meta.integrationEventName,
                internalId = meta.internalId,
                internalModelName = meta.internalModelName,
                externalModelName = meta.externalModelName,
                batchId = totvsInvoiceResponse.batch,
                idSoc = totvsInvoiceResponse.idSoc,
                batchType = BatchType.CREATE,
                payloadSequenceId = 1,
                description = null,
                status = LogStatus.PENDING,
            )

            val result = nullvsInvoiceBatchResponse.toNullvsIntegrationLog().success()

            ResultAssert.assertThat(result)
                .isSuccessWithDataIgnoringGivenFields(expected, "id", "createdAt", "updatedAt")
        }
    }

    @Test
    fun `#fromTOTVSToItems -should convert composition to invoice items`() {
        val composition = TotvsFirstPaymentSuccessRequest.Composition(
            filial = "",
            totvsId = "",
            aliceId = null,
            sequential = "001",
            year = "2023",
            month = "03",
            release = "101",
            releaseDescription = "Produto/Plano (Mensalidade)",
            type = "1",
            value = 1200.00,
            userName = "Some user"
        )

        val expected = listOf(
            NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem(
                referenceDate = "01032023".fromTotvsFormatDateDMY(),
                operation = InvoiceItemOperation.CHARGE,
                type = InvoiceItemType.PRODUCT_PRICE,
                notes = "Produto/Plano (Mensalidade)",
                value = BigDecimal("1200.00"),
            )
        )

        val result = fromTOTVSToItems(listOf(composition))
        assertThat(result).isEqualTo(expected)
    }


    @Test
    fun `#fromTotvsToMemberInvoiceItems - should convert composition to member invoice items`() {
        val memberId = RangeUUID.generate()
        val composition = TotvsFirstPaymentSuccessRequest.Composition(
            filial = "",
            totvsId = "0000",
            aliceId = memberId.toString(),
            sequential = "001",
            year = "2023",
            month = "03",
            release = "101",
            releaseDescription = "Produto/Plano (Mensalidade)",
            type = "1",
            value = 1200.00,
            userName = "Some user"
        )

        val expected = listOf(
            NullvsFirstPaymentCreatedEvent.Payload.MemberInvoiceDetail(
                totalAmount = BigDecimal("1200.00"),
                memberId = memberId,
                referenceDate = LocalDate.of(2023, 3, 1),
                dueDate = LocalDate.of(2023, 3, 31),
                items = listOf(
                    NullvsFirstPaymentCreatedEvent.Payload.InvoiceDetailItem(
                        referenceDate = "01032023".fromTotvsFormatDateDMY(),
                        operation = InvoiceItemOperation.CHARGE,
                        type = InvoiceItemType.PRODUCT_PRICE,
                        notes = "Produto/Plano (Mensalidade)",
                        value = BigDecimal("1200.00"),
                    )
                ),
            )
        )

        val result =
            fromTotvsToMemberInvoiceItems(
                LocalDate.of(2023, 3, 1),
                LocalDate.of(2023, 3, 31),
                listOf(composition)
            )
        assertThat(result).isEqualTo(expected)
    }

    @Nested
    inner class ToTotvsScheduleFirstPaymentRequest {
        private val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        private val companyContract = TestModelFactory.buildCompanyContract(
            groupCompany = "0018",
            externalId = "000001",
        )
        private val company = TestModelFactory.buildCompany(
            contractIds = listOf(companyContract.id),
        )

        private val companySubContract = TestModelFactory.buildCompanySubContract(
            companyId = company.id,
            contractId = companyContract.id,
            externalId = "000001",
        )

        private val subContractInvoiceItem =
            TestModelFactory.buildInvoiceItem(
                personId = null,
                subcontractId = companySubContract.id,
                notes = null,
                percentageValue = BigDecimal("10.0"),
            )

        private val preActivationPayment = TestModelFactory.buildPreActivationPayment(
            companyId = company.id,
            companySubContractId = companySubContract.id,
            externalId = "PLS|00000001|1|RA",
            globalItems = listOf(subContractInvoiceItem),
        )

        private val person = TestModelFactory.buildPerson()
        private val personInvoiceItem = TestModelFactory.buildInvoiceItem(personId = person.id, notes = "OBS")

        private val event = FirstPaymentScheduleCreatedEvent(
            firstPaymentScheduleCreated = FirstPaymentScheduleCreated(
                firstPaymentSchedule = firstPaymentSchedule,
                preActivationPayment = preActivationPayment,
                companySubContract = companySubContract,
                companyContract = companyContract,
                company = company,
                invoiceItemsWithPeople = listOf(personInvoiceItem to person),
            )
        )

        @Test
        fun `#should convert correctly`() {
            val expected = TotvsScheduleFirstPaymentRequest(
                batchId = event.messageId.toString(),
                date = event.eventDate.totvsFormatDateDMY(),
                payload = TotvsScheduleFirstPaymentRequest.Payload(
                    aliceId = firstPaymentSchedule.id.toString(),
                    scheduleDay = firstPaymentSchedule.scheduledDate.dayOfMonth.toString(),
                    prePayment = TotvsScheduleFirstPaymentRequest.Payload.PrePayment(
                        prefix = "PLS",
                        number = "00000001",
                        installment = "1",
                        type = "RA"
                    ),
                    additional = listOf(
                        TotvsScheduleFirstPaymentRequest.Payload.Additional(
                            level = TotvsScheduleFirstPaymentRequest.Payload.Additional.Level.SUBCONTRACT,
                            idAddBilling = subContractInvoiceItem.id,
                            observation = "",
                            cpfCnpj = company.cnpj,
                            code = subContractInvoiceItem.type.toTotvs(subContractInvoiceItem.operation),
                            valueType = TotvsScheduleFirstPaymentRequest.Payload.Additional.ValueType.PERCENTAGE,
                            value = subContractInvoiceItem.value.toDouble(),
                        ),
                        TotvsScheduleFirstPaymentRequest.Payload.Additional(
                            level = TotvsScheduleFirstPaymentRequest.Payload.Additional.Level.MEMBER,
                            idAddBilling = personInvoiceItem.id,
                            observation = personInvoiceItem.notes!!,
                            cpfCnpj = person.nationalId,
                            code = personInvoiceItem.type.toTotvs(personInvoiceItem.operation),
                            valueType = TotvsScheduleFirstPaymentRequest.Payload.Additional.ValueType.ABSOLUTE,
                            value = personInvoiceItem.value.toDouble(),
                        ),
                    ),
                    groupCompany = "0018",
                    contractNumber = "000001",
                    subContractNumber = "000001"
                )
            )

            val result = event.toTotvsScheduleFirstPaymentRequest()
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `#should throw exception when preActivationPayment externalId is null`() {
            val event = event.copy(
                firstPaymentScheduleCreated = event.firstPaymentScheduleCreated.copy(
                    preActivationPayment = event.firstPaymentScheduleCreated.preActivationPayment.copy(
                        externalId = null
                    )
                )
            )

            assertThrows<PreActivationPaymentExternalIdNullException> {
                event.toTotvsScheduleFirstPaymentRequest()
            }
        }
    }
}
