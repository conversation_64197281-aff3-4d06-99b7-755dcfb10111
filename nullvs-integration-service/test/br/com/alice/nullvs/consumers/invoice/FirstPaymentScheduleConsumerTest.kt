package br.com.alice.nullvs.consumers.invoice

import br.com.alice.business.events.FirstPaymentScheduleCreated
import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.nullvs.clients.TotvsInvoiceClient
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.events.NullvsScheduleFirstPaymentResponseEvent
import br.com.alice.nullvs.exceptions.TotvsInvoiceClientPostException
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentResponse
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FirstPaymentScheduleConsumerTest : ConsumerTest() {
    private val totvsInvoiceClient: TotvsInvoiceClient = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val consumer = FirstPaymentScheduleConsumer(totvsInvoiceClient, kafkaProducerService)

    companion object {
        @JvmStatic
        fun exceptions() = listOf(
            TotvsInvoiceClientPostException(""),
        )
    }

    @Nested
    inner class ProcessFirstPaymentScheduled {
        private val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        private val companyContract = TestModelFactory.buildCompanyContract(
            groupCompany = "0018",
            externalId = "000001",
        )
        private val company = TestModelFactory.buildCompany(
            contractIds = listOf(companyContract.id),
        )

        private val companySubContract = TestModelFactory.buildCompanySubContract(
            companyId = company.id,
            contractId = companyContract.id,
            externalId = "000001",
        )
        private val preActivationPayment = TestModelFactory.buildPreActivationPayment(
            companyId = company.id,
            companySubContractId = companySubContract.id,
            externalId = "PLS|00000001|1|RA"
        )

        private val person = TestModelFactory.buildPerson()
        private val invoiceItem = TestModelFactory.buildInvoiceItem(personId = person.id)

        private val event = FirstPaymentScheduleCreatedEvent(
            firstPaymentScheduleCreated = FirstPaymentScheduleCreated(
                firstPaymentSchedule = firstPaymentSchedule,
                preActivationPayment = preActivationPayment,
                companySubContract = companySubContract,
                companyContract = companyContract,
                company = company,
                invoiceItemsWithPeople = listOf(invoiceItem to person)
            )
        )

        @Test
        fun `#should convert and produce the NullvsScheduleFirstPaymentResponseEvent event`() = runBlocking {
            val request = event.toTotvsScheduleFirstPaymentRequest()
            coEvery { totvsInvoiceClient.scheduleFirstPayment(request) } returns TotvsScheduleFirstPaymentResponse(
                invoiceScheduleId = "1111",
                aliceId = firstPaymentSchedule.id.toString() + "   ",
                message = null
            )

            coEvery {
                kafkaProducerService.produce(match<NullvsScheduleFirstPaymentResponseEvent> {
                    it.payload.externalId == "1111"
                            && it.payload.firstPaymentScheduleId == firstPaymentSchedule.id
                            && it.payload.error == null
                })
            } returns mockk()

            val result = consumer.processFirstPaymentScheduled(event)

            assertThat(result).isSuccess()

            coVerifyOnce {
                totvsInvoiceClient.scheduleFirstPayment(request)
                kafkaProducerService.produce(match<NullvsScheduleFirstPaymentResponseEvent> {
                    it.payload.externalId == "1111"
                            && it.payload.firstPaymentScheduleId == firstPaymentSchedule.id
                            && it.payload.error == null
                })
            }

        }

        @ParameterizedTest
        @MethodSource("br.com.alice.nullvs.consumers.invoice.FirstPaymentScheduleConsumerTest#exceptions")
        fun `#should throw the auto retry exception when client fails with some exception`(
            exception: BadRequestException
        ) =
            runBlocking {
                val request = event.toTotvsScheduleFirstPaymentRequest()
                coEvery { totvsInvoiceClient.scheduleFirstPayment(request) } returns exception.failure()

                coEvery {
                    kafkaProducerService.produce(match<NullvsScheduleFirstPaymentResponseEvent> {
                        it.payload.externalId == "1111"
                                && it.payload.firstPaymentScheduleId == firstPaymentSchedule.id
                                && it.payload.error == null
                    })
                } returns mockk()

                val result = consumer.processFirstPaymentScheduled(event)

                assertThat(result).isFailureOfType(AutoRetryableException::class)

                coVerifyOnce {
                    totvsInvoiceClient.scheduleFirstPayment(request)
                }

                coVerifyNone {
                    kafkaProducerService.produce(any())
                }
            }
    }
}
