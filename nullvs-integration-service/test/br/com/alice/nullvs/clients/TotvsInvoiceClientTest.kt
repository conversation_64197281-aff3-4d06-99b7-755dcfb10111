package br.com.alice.nullvs.clients

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.NullvsInvoiceConverter
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequestResponse
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsInvoiceRequest
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toTotvsReceivableIntegrationRequest
import br.com.alice.nullvs.exceptions.TotvsInvoiceClientPostException
import br.com.alice.nullvs.exceptions.TotvsPostBatchDuplicationException
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsIntegrationBatchResponse
import br.com.alice.nullvs.models.TotvsReceivableIntegrationRequest
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentRequest
import br.com.alice.nullvs.models.TotvsScheduleFirstPaymentResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceData
import br.com.alice.nullvs.models.payment.TotvsInvoiceResponse
import br.com.alice.nullvs.models.payment_in.TotvsInvoiceRequest
import br.com.alice.nullvs.models.payment_out.TotvsInvoiceIntegrationRequest
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TotvsInvoiceClientTest {

    @BeforeTest
    fun setup() {
        mockkObject(NullvsInvoiceConverter)
        mockkObject(TotvsIntegrationMetric)
    }

    private val eventId = RangeUUID.generate()
    private val internalId = RangeUUID.generate()

    private val meta = Meta(
        eventId = eventId,
        eventName = "event01",
        internalId = internalId,
        internalModelName = InternalModelType.MEMBER_INVOICE_GROUP,
        integrationEventName = "integration01",
        externalId = null,
        externalModelName = ExternalModelType.INVOICE,
        integratedAt = LocalDateTime.now().minusDays(1),
        originalTopic = "original01",
    )

    val payload = TotvsInvoiceIntegrationRequest.Payload(
        prefix = "ABC",
        installment = "1",
        type = "TYPE",
        client = "CLIENT",
        issuedDate = "2023-01-01",
        realDueDate = "2023-02-01",
        value = 100.0,
        paidAt = "2023-02-01",
        balance = 0.0,
        clientName = "Payment",
        batchIntegration = "01",
        store = "01",
        numberOfTitle = "00000001",
        composition = listOf(
            TotvsInvoiceIntegrationRequest.Payload.Composition(
                filial = "FILIAL",
                totvsId = "TOTVS_ID",
                aliceId = "ALICE_ID",
                sequential = "1",
                year = "2023",
                month = "01",
                release = "RELEASE",
                releaseDescription = "RELEASE_DESCRIPTION",
                type = "TYPE",
                value = 50.0,
                userName = "USER_NAME"
            )
        )
    )

    private val nullvsInvoiceData = NullvsInvoiceData(
        titlePrefix = "F",
        emittedDate = "2023-01-01".toLocalDate(),
        dueDate = "2023-02-01".toLocalDate(),
        amount = 1000.0,
        status = NullvsInvoiceData.Status.OPEN,
        financialHistory = "Some financial history",
        clientCode = "12345",
        type = "RA",
        discount = 50.0,
        fine = 10.0,
        interest = 5.0,
        monetaryCorrection = 0.0,
        natureCode = "001",
        paidAt = null,
        titleInstallment = null,
        titleNumber = null
    )

    private val request = NullvsInvoiceBatchRequest(meta, NullvsActionType.CREATE, nullvsInvoiceData)

    private val response = TotvsIntegrationBatchResponse(
        httpStatus = "200",
        batch = "01",
        idSoc = request.meta.eventId.toString(),
        action = "action",
    )

    private val baseUrl = "https://totvs.com/rest/fwmodel/PLIncAutoBenModel"
    private val secretKey = "12345"

    @Nested
    inner class PostInvoice {

        private val totvsInvoiceIntegrationRequest = TotvsInvoiceIntegrationRequest(
            batchId = "batch_id",
            type = "movimento",
            entity = "titulo",
            date = "2023-02-01",
            action = NullvsActionType.CREATE.description,
            user = "USER",
            payload = listOf(payload)
        )

        private val bodyRequest = TotvsInvoiceRequest(
            idSoc = "id_soc",
            date = "11052023",
            action = "insert",
            payload = listOf(
                TotvsInvoiceRequest.Payload(
                    prefix = "prefix",
                    type = "RA",
                    client = "client",
                    paidAt = "11052023",
                    issuedDate = "01052023",
                    dueDate = "20052023",
                    financialHistory = "",
                    situation = "0",
                    value = 20.0,
                    interest = 0.0,
                    fine = 0.0,
                    discount = 0.0,
                    monetaryCorrection = 0.0,
                    origin = "1",
                    memberInvoiceGroupId = UUID.randomUUID()
                )
            ),
        )

        @Test
        fun `#postInvoice - should post request correctly`() = runBlocking {
            val serialized = gson.toJson(response)
            val httpClient = httpClientMock(
                responseContent = serialized,
                httpMethod = HttpMethod.Post,
                url = baseUrl,
            )
            val totvsCreateInvoiceResponse =
                TotvsInvoiceResponse(response.httpStatus, response.batch, response.idSoc, response.action)
            val nullvsInvoiceCreationBatchResponse = NullvsInvoiceBatchResponse(meta, totvsCreateInvoiceResponse)

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)

            every { request.toTotvsInvoiceRequest() } returns bodyRequest
            every { response.toNullvsInvoiceRequestResponse(any()) } returns nullvsInvoiceCreationBatchResponse

            val actualResponse = client.postInvoice(request)
            assertThat(actualResponse).isSuccessWithData(nullvsInvoiceCreationBatchResponse)

            coVerifyOnce { TotvsIntegrationMetric.countTotvsIntegration(Method.INVOICE, Status.SUCCESS) }
        }

        @Test
        fun `#postInvoice - post request error`() = runBlocking<Unit> {
            val serialized = gson.toJson(response)
            val httpClient = httpClientMock(
                responseContent = serialized,
                httpMethod = HttpMethod.Get,
                url = baseUrl,
            )
            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.postInvoice(request)

            assertThat(actualResponse).isFailureOfType(TotvsInvoiceClientPostException::class)

            coVerifyOnce { TotvsIntegrationMetric.countTotvsIntegration(Method.INVOICE, Status.FAILURE) }
        }

        @Test
        fun `#post request error with already sent batch id`() = runBlocking<Unit> {
            val httpClient = httpClientMock(
                responseContent = "{\"code\":400,\"message\":\"O Lote de ID LOTE [c91c3bfc-c3b6-4b19-934d-73d26b6d1d00] já existe no Protheus a partir do dia 07/07/23 15:10:27\",\"detailedMessage\":\"\"}",
                httpMethod = HttpMethod.Post,
                url = baseUrl,
                statusCode = HttpStatusCode.BadRequest,
            )

            every { request.toTotvsInvoiceRequest() } returns bodyRequest

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.postInvoice(request)

            assertThat(actualResponse).isFailureOfType(TotvsPostBatchDuplicationException::class)

            coVerifyOnce { TotvsIntegrationMetric.countTotvsIntegration(Method.INVOICE, Status.FAILURE) }
        }
    }

    @Nested
    inner class PostInvoicePayment {

        private val bodyRequest = TotvsReceivableIntegrationRequest(
            batchId = "batchId",
            date = "date",
            action = "action",
            payload = listOf(
                TotvsReceivableIntegrationRequest.Payload(
                    prefix = "prefix",
                    numberOfTitle = "numberOfTitle",
                    installment = "installment",
                    type = "type",
                    client = "client",
                    paidAt = "paidAt",
                    value = 20.0,
                    description = "description",
                    interest = null,
                    fine = null,
                    discount = null,
                    externalPaymentId = "123456"
                )
            ),
        )

        @Test
        fun `#should post request correctly`() = runBlocking {
            val serialized = gson.toJson(response)
            val httpClient = httpClientMock(
                responseContent = serialized,
                httpMethod = HttpMethod.Post,
                url = baseUrl,
            )
            val totvsCreateInvoiceResponse =
                TotvsInvoiceResponse(response.httpStatus, response.batch, response.idSoc, response.action)
            val nullvsInvoiceCreationBatchResponse = NullvsInvoiceBatchResponse(meta, totvsCreateInvoiceResponse)

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)

            every { request.toTotvsReceivableIntegrationRequest() } returns bodyRequest
            every { response.toNullvsInvoiceRequestResponse(any()) } returns nullvsInvoiceCreationBatchResponse

            val actualResponse = client.postInvoicePayment(request)
            assertThat(actualResponse).isSuccessWithData(nullvsInvoiceCreationBatchResponse)

            coVerifyOnce { TotvsIntegrationMetric.countTotvsIntegration(Method.PAYMENT, Status.SUCCESS) }
        }

        @Test
        fun `#post request error`() = runBlocking<Unit> {
            val serialized = gson.toJson(response)
            val httpClient = httpClientMock(
                responseContent = serialized,
                httpMethod = HttpMethod.Get,
                url = baseUrl,
            )
            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.postInvoicePayment(request)

            assertThat(actualResponse).isFailureOfType(TotvsInvoiceClientPostException::class)

            coVerifyOnce { TotvsIntegrationMetric.countTotvsIntegration(Method.PAYMENT, Status.FAILURE) }
        }

        @Test
        fun `#post request error with already sent batch id`() = runBlocking<Unit> {
            val httpClient = httpClientMock(
                responseContent = "{\"code\":400,\"message\":\"O Lote de ID LOTE [c91c3bfc-c3b6-4b19-934d-73d26b6d1d00] já existe no Protheus a partir do dia 07/07/23 15:10:27\",\"detailedMessage\":\"\"}",
                httpMethod = HttpMethod.Post,
                url = baseUrl,
                statusCode = HttpStatusCode.BadRequest,
            )

            every { request.toTotvsReceivableIntegrationRequest() } returns bodyRequest

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.postInvoicePayment(request)

            assertThat(actualResponse).isFailureOfType(TotvsPostBatchDuplicationException::class)

            coVerifyOnce { TotvsIntegrationMetric.countTotvsIntegration(Method.PAYMENT, Status.FAILURE) }
        }
    }

    @Nested
    inner class ScheduleInvoicePayment {
        private val request = TotvsScheduleFirstPaymentRequest(
            batchId = "batchId",
            date = "date",
            action = "action",
            payload = TotvsScheduleFirstPaymentRequest.Payload(
                aliceId = "aliceId",
                scheduleDay = "1",
                prePayment = TotvsScheduleFirstPaymentRequest.Payload.PrePayment(
                    prefix = "PAP",
                    number = "00000000",
                    installment = "01",
                    type = "DP"
                ),
                additional = emptyList(),
                groupCompany = "0018",
                contractNumber = "000001",
                subContractNumber = "00001"
            )
        )

        @Test
        fun `#should post request correctly`() = runBlocking {
            val totvsScheduleFirstPaymentResponse =
                TotvsScheduleFirstPaymentResponse("00001", "aliceId", null)

            val serialized = gson.toJson(totvsScheduleFirstPaymentResponse)
            val httpClient = httpClientMock(
                responseContent = serialized,
                httpMethod = HttpMethod.Post,
                url = baseUrl,
            )

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)

            val actualResponse = client.scheduleFirstPayment(request)
            assertThat(actualResponse).isSuccessWithData(totvsScheduleFirstPaymentResponse)
        }

        @Test
        fun `#post request error`() = runBlocking<Unit> {
            val httpClient = httpClientMock(
                responseContent = "{\"message\": \"mensagem informativa do erro\", \"aliceFirstPaymentId\": \"aliceId\"}",
                httpMethod = HttpMethod.Get,
                url = baseUrl,
                statusCode = HttpStatusCode.BadRequest,
            )
            val totvsScheduleFirstPaymentResponse =
                TotvsScheduleFirstPaymentResponse(null, "aliceId", "mensagem informativa do erro")

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.scheduleFirstPayment(request)

            assertThat(actualResponse).isSuccessWithData(totvsScheduleFirstPaymentResponse)
        }

        @Test
        fun `#post request return an error when the schedule already persisted on TOTVS`() = runBlocking<Unit> {
            val httpClient = httpClientMock(
                responseContent = "{\"invoiceScheduleId\": \"00001\", \"message\": \"mensagem informativa do erro\", \"aliceFirstPaymentId\": \"aliceId\"}",
                httpMethod = HttpMethod.Get,
                url = baseUrl,
                statusCode = HttpStatusCode.Conflict,
            )

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.scheduleFirstPayment(request)

            assertThat(actualResponse).isFailureOfType(TotvsPostBatchDuplicationException::class)
        }

        @Test
        fun `#post request error with already sent batch id`() = runBlocking<Unit> {
            val httpClient = httpClientMock(
                responseContent = "{\"code\":400,\"message\":\"O Lote de ID LOTE [c91c3bfc-c3b6-4b19-934d-73d26b6d1d00] já existe no Protheus a partir do dia 07/07/23 15:10:27\",\"detailedMessage\":\"\"}",
                httpMethod = HttpMethod.Post,
                url = baseUrl,
                statusCode = HttpStatusCode.BadRequest,
            )

            val client = TotvsInvoiceClient(baseUrl, secretKey, httpClient)
            val actualResponse = client.scheduleFirstPayment(request)

            assertThat(actualResponse).isFailureOfType(TotvsPostBatchDuplicationException::class)
        }
    }

    private fun httpClientMock(
        responseContent: String = "",
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        url: String,
        httpMethod: HttpMethod,
    ): HttpClient {
        return HttpClient(MockEngine) {
            expectSuccess = true
            install(ContentNegotiation) { simpleGson() }
            engine {
                addHandler { request ->
                    if (request.method == httpMethod && request.url.toString() == url) {
                        respond(
                            responseContent,
                            statusCode,
                        )
                    } else {
                        respond(
                            responseContent,
                            statusCode,
                        )
                    }
                }
            }
        }
    }
}
