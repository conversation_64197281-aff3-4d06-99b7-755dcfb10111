package br.com.alice.nullvs.controllers

import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.extensions.money
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.nullvs.events.NullvsFirstPaymentCreatedEvent
import br.com.alice.nullvs.events.NullvsFirstPaymentFailedEvent
import br.com.alice.nullvs.models.payment_in.TotvsFirstPaymentFailureRequest
import br.com.alice.nullvs.models.payment_in.TotvsFirstPaymentSuccessRequest
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.test.BeforeTest
import kotlin.test.Test

class FirstPaymentControllerTest : ControllerTestHelper() {
    private val kafkaProducerService: KafkaProducerService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            FirstPaymentController(kafkaProducerService)
        }
    }

    private val authorizationHeader = mapOf("Authorization" to "Basic YWxpY2U6YWxpY2U=")

    @Test
    fun `#should process the first payment successful`() = runBlocking {
        val requestString = File("testResources/first-payment/TotvsFirstPaymentSuccessfully.json")
            .bufferedReader()
            .readText()

        coEvery {
            kafkaProducerService.produce(match<NullvsFirstPaymentCreatedEvent> {
                it.payload.firstPaymentScheduleId == "0001"
                        && it.payload.externalId == "PLS|0000001|01|DP"
                        && it.payload.discount == 50.money
            })
        } returns mockk()

        val request = gson.fromJson<TotvsFirstPaymentSuccessRequest>(requestString)

        internalAuthentication {
            post("/entry/first-payment", body = request, headers = authorizationHeader) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            kafkaProducerService.produce(match<NullvsFirstPaymentCreatedEvent> {
                it.payload.firstPaymentScheduleId == "0001"
                        && it.payload.externalId == "PLS|0000001|01|DP"
                        && it.payload.discount == 50.money
            })
        }

        coVerifyNone { kafkaProducerService.produce(match<NullvsFirstPaymentFailedEvent> { it.name == NullvsFirstPaymentFailedEvent.name }) }
    }

    @Test
    fun `#should process the first payment failure`() = runBlocking {
        val requestString = File("testResources/first-payment/TotvsFirstPaymentFailure.json")
            .bufferedReader()
            .readText()

        coEvery {
            kafkaProducerService.produce(match<NullvsFirstPaymentFailedEvent> {
                it.payload.firstPaymentScheduleId == "00001"
                        && it.payload.errorMessage == "Erro no processamento"
            })
        } returns mockk()

        val request = gson.fromJson<TotvsFirstPaymentFailureRequest>(requestString)

        internalAuthentication {
            post("/entry/first-payment", body = request, headers = authorizationHeader) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            kafkaProducerService.produce(match<NullvsFirstPaymentFailedEvent> {
                it.payload.firstPaymentScheduleId == "00001"
                        && it.payload.errorMessage == "Erro no processamento"
            })
        }

        coVerifyNone { kafkaProducerService.produce(match<NullvsFirstPaymentCreatedEvent> { it.name == NullvsFirstPaymentCreatedEvent.name }) }
    }
}
