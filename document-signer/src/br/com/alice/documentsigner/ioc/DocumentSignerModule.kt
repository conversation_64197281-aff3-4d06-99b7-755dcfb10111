package br.com.alice.documentsigner.ioc

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.documentsigner.clients.CessClient
import br.com.alice.documentsigner.services.DocumentPrinterService
import br.com.alice.documentsigner.services.SignaturePdfService
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import org.koin.dsl.module

val DocumentSignerModule = module(createdAtStart = true) {

    val client = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 15_000)

    single { FileVaultStorage(client) }

    single { CessClient() }
    single { SignaturePdfService(get()) }
    single { DocumentPrinterService(get(), get(), get(), get(), get()) }

}.plus(listOf(StaffDomainClientModule, PersonDomainClientModule))
