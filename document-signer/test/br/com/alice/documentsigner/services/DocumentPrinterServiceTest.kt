package br.com.alice.documentsigner.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role.HEALTHCARE_TEAM_NURSE
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.storage.AliceFile
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.TestRequest
import br.com.alice.data.layer.models.copy
import br.com.alice.data.layer.models.withStaff
import br.com.alice.documentsigner.clients.CertifyDocumentRequest
import br.com.alice.documentsigner.clients.DocumentRequest
import br.com.alice.documentsigner.clients.DocumentType
import br.com.alice.documentsigner.clients.DocumentType.APPOINTMENT
import br.com.alice.documentsigner.clients.DocumentType.PRESCRIPTION
import br.com.alice.documentsigner.clients.DocumentType.TEST_REQUEST
import br.com.alice.documentsigner.clients.SignatureSettings
import br.com.alice.documentsigner.utils.PDFPrinter
import br.com.alice.documentsigner.utils.RoutineDocumentsUtil
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.time.ZoneId
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class DocumentPrinterServiceTest {

    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val personService: PersonService = mockk()
    private val signatureService: SignaturePdfService = mockk()
    private val fileVaultStorage: FileVaultStorage = mockk()
    private val staffService: StaffService = mockk()

    private val healthPlanPrinterService = DocumentPrinterService(
        healthProfessionalService,
        personService,
        signatureService,
        fileVaultStorage,
        staffService
    )

    private val token = "token"
    private val person = TestModelFactory.buildPerson()
    private val staff = TestModelFactory.buildStaff()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(staffId = staff.id)
    private val id = RangeUUID.generate()
    private val prescription = TestModelFactory.buildHealthPlanTaskPrescription(
        personId = person.id,
        staffId = staff.id,
    ).copy(releasedAt = LocalDateTime.now()).specialize<Prescription>()
    private val referral = TestModelFactory.buildHealthPlanTaskReferral(
        personId = person.id,
    ).copy(releasedAt = LocalDateTime.now()).specialize<Referral>()
    private val fileContent = ByteArray(1)
    private val settings = SignatureSettings(
        contact = "",
        location = "test",
        reason = "test",
        extraInfo = emptyList()
    )
    private val documentData = "data"
    private val request = CertifyDocumentRequest(
        signatureSettings = listOf(settings),
        documents = listOf(
            DocumentRequest(
                id = id.toString(),
                originalFileName = "$id.pdf",
                data = documentData
            )
        )
    )

    private val testRequest = TestModelFactory.buildHealthPlanTaskTestRequest(
        personId = person.id,
    ).copy(releasedAt = LocalDateTime.now()).specialize<TestRequest>()

    @BeforeTest
    fun init() {
        mockkObject(PDFPrinter)
        mockkObject(RoutineDocumentsUtil)
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        healthProfessionalService,
        personService,
        signatureService,
        fileVaultStorage,
        PDFPrinter,
        RoutineDocumentsUtil
    )

    @Test
    fun `#printPrescriptionsPdf should call PDFPrint if have a token`() = runBlocking {
        val date = LocalDateTime.now(ZoneId.of("America/Sao_Paulo"))

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now(ZoneId.of("America/Sao_Paulo")) } returns date

            val prescriptions = listOf(prescription)
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.success()
            coEvery {
                PDFPrinter.generatePrescriptions(
                    prescriptions,
                    date,
                    healthProfessional,
                    person
                )
            } returns fileContent

            coEvery {
                signatureService.createDefaultSignatureSettings(
                    healthProfessional.council,
                    PRESCRIPTION,
                    "Simples",
                    id
                )
            } returns settings
            coEvery { signatureService.transformContent(fileContent) } returns documentData
            coEvery { signatureService.signDocument(request, token) } returns fileContent.success()

            val result = healthPlanPrinterService.printPrescriptionsPdf(
                prescriptions = prescriptions,
                staffId = staff.id,
                id = id,
                token = token
            )
            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { PDFPrinter.generatePrescriptions(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.createDefaultSignatureSettings(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.transformContent(any()) }
            coVerifyOnce { signatureService.signDocument(any(), any()) }
        }
    }

    @Test
    fun `#printPrescriptionsPdf should call RoutineDocumentsUtil if it doesn't have a token`() = runBlocking {
        val prescriptions = listOf(prescription)
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.success()

        coEvery {
            RoutineDocumentsUtil.generatePrescriptions(prescriptions, healthProfessional, person)
        } returns fileContent

        val result = healthPlanPrinterService.printPrescriptionsPdf(
            prescriptions = prescriptions,
            staffId = staff.id,
            id = id,
            token = null
        )
        assertThat(result).isSuccessWithData(fileContent)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { RoutineDocumentsUtil.generatePrescriptions(any(), any(), any()) }
    }

    @Test
    fun `#printPrescriptionsPdf should return error if staff is not a health professional`() = runBlocking {
        val prescriptions = listOf(prescription)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id) } returns NotFoundException().failure()

        val result = healthPlanPrinterService.printPrescriptionsPdf(
            prescriptions = prescriptions,
            staffId = staff.id,
            id = id,
            token = token
        )
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
    }

    @Test
    fun `#printSpecialPrescriptionsPdf should call PDFPrint if have a token`() = runBlocking {
        val healthProfessional = healthProfessional.withStaff(staff)
        val date = LocalDateTime.now(ZoneId.of("America/Sao_Paulo"))

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now(ZoneId.of("America/Sao_Paulo")) } returns date
            coEvery { personService.get(person.id) } returns person.success()
            coEvery {
                healthProfessionalService.findByStaffId(
                    staff.id,
                    HealthProfessionalService.FindOptions(withStaff = true)
                )
            } returns healthProfessional.success()

            coEvery {
                PDFPrinter.generateSpecialPrescription(
                    prescription,
                    date,
                    healthProfessional,
                    person
                )
            } returns fileContent

            coEvery {
                signatureService.createDefaultSignatureSettings(
                    healthProfessional.council,
                    PRESCRIPTION,
                    "Especial",
                    id
                )
            } returns settings
            coEvery { signatureService.transformContent(fileContent) } returns documentData
            coEvery { signatureService.signDocument(request, token) } returns fileContent.success()

            val result = healthPlanPrinterService.printSpecialPrescriptionsPdf(
                prescription = prescription,
                physicianId = staff.id,
                id = id,
                token = token
            )
            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
            coVerifyOnce { PDFPrinter.generateSpecialPrescription(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.createDefaultSignatureSettings(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.transformContent(any()) }
            coVerifyOnce { signatureService.signDocument(any(), any()) }
        }
    }

    @Test
    fun `#printSpecialPrescriptionsPdf should call RoutineDocumentsUtil if it doesn't have a token`() = runBlocking {
        val healthProfessional = healthProfessional.withStaff(staff)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            healthProfessionalService.findByStaffId(
                staff.id,
                HealthProfessionalService.FindOptions(withStaff = true)
            )
        } returns healthProfessional.success()

        coEvery {
            RoutineDocumentsUtil.generateSpecialPrescription(prescription, healthProfessional, person)
        } returns fileContent

        val result = healthPlanPrinterService.printSpecialPrescriptionsPdf(
            prescription = prescription,
            physicianId = staff.id,
            id = id,
            token = null
        )
        assertThat(result).isSuccessWithData(fileContent)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
        coVerifyOnce { RoutineDocumentsUtil.generateSpecialPrescription(any(), any(), any()) }
    }

    @Test
    fun `#printSpecialPrescriptionsPdf should return error if staff is not a physician`() = runBlocking {
        val healthProfessional = healthProfessional.copy(
            staff = staff.copy(role = HEALTHCARE_TEAM_NURSE)
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            healthProfessionalService.findByStaffId(
                staff.id,
                HealthProfessionalService.FindOptions(withStaff = true)
            )
        } returns healthProfessional.success()

        val result = healthPlanPrinterService.printSpecialPrescriptionsPdf(
            prescription = prescription,
            physicianId = staff.id,
            id = id,
            token = token
        )
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `#printTestRequestPdf should call RoutineDocumentsUtil and return file content`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            healthProfessionalService.findByStaffId(staff.id, HealthProfessionalService.FindOptions(withStaff = true))
        } returns healthProfessional.success()

        coEvery {
            RoutineDocumentsUtil.generateExamRequest(
                testRequest.releasedAt!!,
                listOf(testRequest.formattedTitle()),
                healthProfessional,
                person
            )
        } returns fileContent

        val result = healthPlanPrinterService.printTestRequestPdf(
            testRequests = listOf(testRequest),
            staffId = staff.id,
        )
        assertThat(result).isSuccessWithData(fileContent)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
        coVerifyOnce { RoutineDocumentsUtil.generateExamRequest(any(), any(), any(), any()) }
    }

    @Test
    fun `#printTestRequestPdf sets createdAt as now if test request was not released yet`() = runBlocking {
        val now = LocalDateTime.of(2021, 6, 7, 16, 0)

        mockkStatic(LocalDateTime::class) {
            val unreleasedTestRequest = testRequest.copy(releasedAt = null).specialize<TestRequest>()

            coEvery { LocalDateTime.now() } returns now
            coEvery { personService.get(person.id) } returns person.success()

            coEvery {
                healthProfessionalService.findByStaffId(
                    staffId = staff.id,
                    findOptions = HealthProfessionalService.FindOptions(withStaff = true)
                )
            } returns healthProfessional.success()

            coEvery {
                RoutineDocumentsUtil.generateExamRequest(
                    createdAt = now,
                    testRequests = listOf(unreleasedTestRequest.formattedTitle()),
                    healthProfessional = healthProfessional,
                    person = person,
                )
            } returns fileContent

            val result = healthPlanPrinterService.printTestRequestPdf(
                testRequests = listOf(unreleasedTestRequest),
                staffId = staff.id,
            )
            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
            coVerifyOnce { RoutineDocumentsUtil.generateExamRequest(any(), any(), any(), any()) }
        }
    }

    @Test
    fun `#printTestRequestPdf should return error if staff is not a health professional`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            healthProfessionalService.findByStaffId(staff.id, HealthProfessionalService.FindOptions(withStaff = true))
        } returns NotFoundException().failure()

        val result = healthPlanPrinterService.printTestRequestPdf(
            testRequests = listOf(testRequest),
            staffId = staff.id,
        )
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyNone { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `#printSignedTestRequestPDF calls PDFPrint`() = runBlocking {
        val now = LocalDateTime.now()

        mockkStatic(LocalDateTime::class) {
            coEvery { LocalDateTime.now() } returns now
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.success()
            coEvery {
                PDFPrinter.generateSignedExamRequest(
                    now,
                    listOf(testRequest),
                    healthProfessional,
                    person
                )
            } returns fileContent
            coEvery {
                signatureService.createDefaultSignatureSettings(
                    healthProfessional.council,
                    TEST_REQUEST,
                    "",
                    id
                )
            } returns settings
            coEvery { signatureService.transformContent(fileContent) } returns documentData
            coEvery { signatureService.signDocument(request, token) } returns fileContent.success()

            val result = healthPlanPrinterService.printSignedTestRequestPdf(
                testRequests = listOf(testRequest),
                physicianId = staff.id,
                id = id,
                token = token,
            )

            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
            coVerifyOnce { PDFPrinter.generateSignedExamRequest(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.createDefaultSignatureSettings(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.transformContent(any()) }
            coVerifyOnce { signatureService.signDocument(any(), any()) }
        }
    }

    @Test
    fun `#printSignedTestRequestPDF returns an error if physicianId is not a health professional`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            healthProfessionalService.findByStaffId(staff.id)
        } returns NotFoundException().failure()

        val result = healthPlanPrinterService.printSignedTestRequestPdf(
            testRequests = listOf(testRequest),
            staff.id,
            id = id,
            token = token,
        )

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
        coVerify { PDFPrinter wasNot called }
        coVerify { signatureService wasNot called }
    }

    @Test
    fun `#printExcuseNotes should call PDFPrint if have a token`() = runBlocking {
        val appointment = TestModelFactory.buildAppointment(personId = person.id, staffId = staff.id)

        val request = CertifyDocumentRequest(
            signatureSettings = listOf(settings),
            documents = listOf(
                DocumentRequest(
                    id = appointment.id.toString(),
                    originalFileName = "${appointment.id}.pdf",
                    data = documentData
                )
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.success()

        coEvery {
            PDFPrinter.generateExcuseNotes(
                any(),
                healthProfessional,
                person,
                any()
            )
        } returns fileContent

        coEvery {
            signatureService.createDefaultSignatureSettings(
                healthProfessional.council,
                APPOINTMENT,
                "Atestado médico",
                appointment.id
            )
        } returns settings
        coEvery { signatureService.transformContent(fileContent) } returns documentData
        coEvery { signatureService.signDocument(request, any()) } returns fileContent.success()

        val result = healthPlanPrinterService.printExcuseNotes(
            appointment = appointment,
            staffId = staff.id,
            token = "",
            documentToken = token
        )
        assertThat(result).isSuccessWithData(fileContent)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { PDFPrinter.generateExcuseNotes(any(), any(), any(), any()) }
        coVerifyOnce { signatureService.createDefaultSignatureSettings(any(), any(), any(), any()) }
        coVerifyOnce { signatureService.transformContent(any()) }
        coVerifyOnce { signatureService.signDocument(any(), any()) }
    }

    @Test
    fun `#saveFile save the file into file vault and return the attachment`() = runBlocking<Unit> {
        val genericId = RangeUUID.generate()
        val expected = Attachment(
            id = RangeUUID.generate(),
            type = "pdf",
            fileName = "test.pdf"
        )
        val vaultResponse = AliceFile(
            id = expected.id,
            fileName = "$genericId.pdf",
            type = expected.type,
            url = "url"
        )

        mockkObject(RangeUUID) {
            every { RangeUUID.generate() } returns genericId
            coEvery {
                fileVaultStorage.store(
                    personId = person.id,
                    domain = "ehr",
                    namespace = "test",
                    multipartRequest = match {
                        it.fileContent?.readBytes().contentEquals(fileContent) &&
                                it.file?.path == vaultResponse.fileName &&
                                it.parameters.isEmpty()
                    }
                )
            } returns vaultResponse.success()

            val result = healthPlanPrinterService.saveFile(
                id = prescription.id,
                personId = person.id,
                doc = fileContent,
                namespace = "test",
                fileName = "test.pdf"
            )
            assertThat(result).isEqualTo(expected)

            coVerifyOnce { fileVaultStorage.store(any(), any(), any(), any()) }
        }
    }

    @Test
    fun `#printReferralPdf should call PDFPrint if have a token`() = runBlocking {
        val date = LocalDateTime.now(ZoneId.of("America/Sao_Paulo"))

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now(ZoneId.of("America/Sao_Paulo")) } returns date

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.success()
            coEvery {
                PDFPrinter.generateSignedReferral(
                    referral,
                    date,
                    healthProfessional,
                    person
                )
            } returns fileContent

            coEvery {
                signatureService.createDefaultSignatureSettings(
                    healthProfessional.council,
                    DocumentType.REFERRAL,
                    "",
                    id
                )
            } returns settings
            coEvery { signatureService.transformContent(fileContent) } returns documentData
            coEvery { signatureService.signDocument(request, token) } returns fileContent.success()

            val result = healthPlanPrinterService.printReferralPdf(
                referral = referral,
                staffId = staff.id,
                id = id,
                token = token
            )
            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { PDFPrinter.generateSignedReferral(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.createDefaultSignatureSettings(any(), any(), any(), any()) }
            coVerifyOnce { signatureService.transformContent(any()) }
            coVerifyOnce { signatureService.signDocument(any(), any()) }
        }
    }

    @Test
    fun `#printReferralPdf should call RoutineDocumentsUtil if it doesn't have a token`() = runBlocking {

        val date = LocalDateTime.now(ZoneId.of("America/Sao_Paulo"))

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now(ZoneId.of("America/Sao_Paulo")) } returns date

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.success()
            coEvery { staffService.get(staff.id) } returns staff.success()

            coEvery {
                RoutineDocumentsUtil.generateReferral(referral, date, staff, person)
            } returns fileContent

            val result = healthPlanPrinterService.printReferralPdf(
                referral = referral,
                staffId = staff.id,
                id = id,
                token = null
            )
            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { RoutineDocumentsUtil.generateReferral(any(), any(), any(), any()) }
            coVerifyOnce { staffService.get(any()) }
        }
    }

    @Test
    fun `#printReferralPdf should call RoutineDocumentsUtil if it doesn't health professional`() = runBlocking {

        val date = LocalDateTime.now(ZoneId.of("America/Sao_Paulo"))

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now(ZoneId.of("America/Sao_Paulo")) } returns date

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { healthProfessionalService.findByStaffId(staff.id) } returns NotFoundException("Cant find health professional").failure()
            coEvery { staffService.get(staff.id) } returns staff.success()

            coEvery {
                RoutineDocumentsUtil.generateReferral(referral, date, staff, person)
            } returns fileContent

            val result = healthPlanPrinterService.printReferralPdf(
                referral = referral,
                staffId = staff.id,
                id = id,
                token = null
            )
            assertThat(result).isSuccessWithData(fileContent)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { RoutineDocumentsUtil.generateReferral(any(), any(), any(), any()) }
            coVerifyOnce { staffService.get(any()) }
        }
    }
}
