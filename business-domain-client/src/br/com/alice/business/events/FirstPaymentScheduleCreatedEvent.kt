package br.com.alice.business.events

import br.com.alice.business.SERVICE_NAME
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FirstPaymentSchedule
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PreActivationPayment

data class FirstPaymentScheduleCreatedEvent(val firstPaymentScheduleCreated: FirstPaymentScheduleCreated) :
    NotificationEvent<FirstPaymentScheduleCreatedEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(firstPaymentScheduleCreated)
    ) {
    companion object {
        const val name = "first-payment-schedule-created"
    }

    data class Payload(
        val firstPaymentScheduleCreated: FirstPaymentScheduleCreated
    )
}

data class FirstPaymentScheduleCreated(
    val firstPaymentSchedule: FirstPaymentSchedule,
    val companySubContract: CompanySubContract,
    val company: Company,
    val preActivationPayment: PreActivationPayment,
    val companyContract: CompanyContract,
    val invoiceItemsWithPeople: List<Pair<InvoiceItem, Person>>,
)
