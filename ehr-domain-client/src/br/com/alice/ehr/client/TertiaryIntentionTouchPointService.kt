package br.com.alice.ehr.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.ehr.model.TertiaryIntentionTouchPointFilter
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface TertiaryIntentionTouchPointService : Service {
    override val namespace: String get() = "ehr"
    override val serviceName: String get() = "tertiary_intention_touch_point"

    suspend fun get(id: UUID): Result<TertiaryIntentionTouchPoint, Throwable>
    suspend fun getByIdAndPersonId(
        id: UUID,
        personId: PersonId
    ): Result<TertiaryIntentionTouchPoint, Throwable>

    suspend fun countAllInCurrent(filters: TertiaryIntentionTouchPointFilter): Result<Int, Throwable>
    suspend fun findByPersonIdAndType(
        personId: PersonId,
        type: TertiaryIntentionType
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable>

    suspend fun findByPersonIdAndBetweenDates(
        personId: PersonId,
        initialDate: LocalDateTime,
        finalDate: LocalDateTime
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable>

    suspend fun create(model: TertiaryIntentionTouchPoint): Result<TertiaryIntentionTouchPoint, Throwable>
    suspend fun update(
        model: TertiaryIntentionTouchPoint,
        publishEvent: Boolean = true
    ): Result<TertiaryIntentionTouchPoint, Throwable>

    suspend fun findAllInCurrentAttendance(
        filters: TertiaryIntentionTouchPointFilter,
        range: IntRange
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable>

    suspend fun findByHealthEventId(healthEventId: UUID): Result<TertiaryIntentionTouchPoint, Throwable>
    suspend fun findByTotvsGuia(totvsGuiaId: UUID): Result<TertiaryIntentionTouchPoint, Throwable>
    suspend fun findTertiaryIntentionByPeriod(
        personId: PersonId,
        providerUnit: UUID,
        type: TertiaryIntentionType,
        dateInit: LocalDateTime,
        dateLimit: LocalDateTime
    ): Result<TertiaryIntentionTouchPoint, Throwable>

    suspend fun findTertiaryIntentionByPeriodAndTypes(
        personId: PersonId,
        types: List<TertiaryIntentionType>,
        dateInit: LocalDateTime,
        dateLimit: LocalDateTime
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable>

    suspend fun findByObjectiveCodes(
        offset: Int,
        limit: Int
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable>
}
