package br.com.alice.hr.core.model

import java.util.UUID

data class BeneficiaryBatchTransport(
    val uploadId: UUID,
    val items: List<BeneficiaryBatchItemTransport>,
)

data class BeneficiaryBatchItemTransport(
    val index: Int,
    val nationalId: String? = null,
    val fullName: String? = null,
    val cnpj: String? = null,
    val insuranceCnpj: String? = null,
    val subContractTitle: String? = null,
    val sex: String? = null,
    val dateOfBirth: String? = null,
    val mothersName: String? = null,
    val email: String? = null,
    val phoneNumber: String? = null,
    val addressPostalCode: String? = null,
    val addressStreet: String? = null,
    val addressCity: String? = null,
    val addressState: String? = null,
    val addressNeighborhood: String? = null,
    val addressNumber: String? = null,
    val addressComplement: String? = null,
    val productTitle: String? = null,
    val activatedAt: String? = null,
    val beneficiaryContractType: String? = null,
    val hiredAt: String? = null,
    val parentNationalId: String? = null,
    val parentBeneficiaryRelationType: String? = null,
    val relationExceeds30Days: String? = null,
    val ownership: String? = null,
)

data class BeneficiaryBatchValidation(
    val success: List<Int> = emptyList(),
    val errors: List<BeneficiaryBatchValidationError> = emptyList(),
)

data class BeneficiaryBatchValidationError(
    val index: Int,
    val error: List<BeneficiaryBatchValidationErrorItem>,
)

data class BeneficiaryBatchValidationErrorItem(
    val field: String,
    val message: String,
)
