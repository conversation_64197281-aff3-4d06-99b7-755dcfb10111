package br.com.alice.hr.core.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.SERVICE_NAME
import java.util.UUID

class HrCreateBeneficiaryBatchEvent(
    uploadId: UUID,
    companyId: UUID,
    beneficiaryItem: BeneficiaryBatchItemTransport
): NotificationEvent<HrCreateBeneficiaryBatchEvent.Payload>(
    name = NAME,
    producer = SERVICE_NAME,
    payload = Payload(
        uploadId = uploadId,
        companyId = companyId,
        nationalId = beneficiaryItem.nationalId,
        fullName = beneficiaryItem.fullName,
        cnpj = beneficiaryItem.cnpj,
        subContractTitle = beneficiaryItem.subContractTitle,
        sex = beneficiaryItem.sex,
        dateOfBirth = beneficiaryItem.dateOfBirth,
        mothersName = beneficiaryItem.mothersName,
        email = beneficiaryItem.email,
        phoneNumber = beneficiaryItem.phoneNumber,
        addressPostalCode = beneficiaryItem.addressPostalCode,
        addressNumber = beneficiaryItem.addressNumber,
        addressComplement = beneficiaryItem.addressComplement,
        productTitle = beneficiaryItem.productTitle,
        activatedAt = beneficiaryItem.activatedAt,
        beneficiaryContractType = beneficiaryItem.beneficiaryContractType,
        hiredAt = beneficiaryItem.hiredAt,
        parentNationalId = beneficiaryItem.parentNationalId,
        parentBeneficiaryRelationType = beneficiaryItem.parentBeneficiaryRelationType,
        relationExceeds30Days = beneficiaryItem.relationExceeds30Days,
        ownership = beneficiaryItem.ownership
    )
) {
    companion object {
        const val NAME = "HR-CREATE-BENEFICIARY-BATCH"
    }

    data class Payload(
        val uploadId: UUID,
        val companyId: UUID,
        val nationalId: String? = null,
        val fullName: String? = null,
        val cnpj: String? = null,
        val subContractTitle: String? = null,
        val sex: String? = null,
        val dateOfBirth: String? = null,
        val mothersName: String? = null,
        val email: String? = null,
        val phoneNumber: String? = null,
        val addressPostalCode: String? = null,
        val addressNumber: String? = null,
        val addressComplement: String? = null,
        val productTitle: String? = null,
        val activatedAt: String? = null,
        val beneficiaryContractType: String? = null,
        val hiredAt: String? = null,
        val parentNationalId: String? = null,
        val parentBeneficiaryRelationType: String? = null,
        val relationExceeds30Days: String? = null,
        val ownership: String? = null,
    )
}
