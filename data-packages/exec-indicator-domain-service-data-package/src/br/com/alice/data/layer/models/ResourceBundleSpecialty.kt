package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.util.UUID

data class ResourceBundleSpecialty(
    val id: UUID = RangeUUID.generate(),
    val healthSpecialistResourceBundleId: UUID,
    val medicalSpecialtyId: UUID,
    val status: Status,
    val pricingStatus: PricingStatus,
    val appointmentRecommendationLevel: AppointmentRecommendationLevel = AppointmentRecommendationLevel.NONE,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val version: Int = 0,
    var updatedBy: UpdatedBy? = null,
) {
    fun isActive() = status == Status.ACTIVE

    fun activate(): ResourceBundleSpecialty {
        return this.copy(
            status = Status.ACTIVE,
        )
    }

    fun inactivate(): ResourceBundleSpecialty {
        return this.copy(
            status = Status.INACTIVE,
            pricingStatus = PricingStatus.NOT_PRICED
        )
    }
}

enum class AppointmentRecommendationLevel {
    NONE,
    RECOMMENDED,
    DEFAULT
}

enum class PricingStatus {
    PRICED,
    NOT_PRICED
}
