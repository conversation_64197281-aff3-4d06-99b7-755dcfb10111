package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.SkipReadAuthz
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Geography
import br.com.alice.common.models.GeographyReference
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.serialization.JsonSerializable
import kotlinx.serialization.Transient
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@SkipReadAuthz
data class ConsolidatedAccreditedNetwork(
    override val id: UUID = RangeUUID.generate(),
    val additionalInfo: AdditionalInfo,
    val bundleIds: List<UUID>,
    val referencedId: UUID,
    val type: ConsolidatedAccreditedNetworkType,
    val addressId: UUID?,
    val providerId: UUID?,
    val tiers: List<SpecialistTier>?,
    val specialtyIds: List<UUID>,
    val subSpecialtyIds: List<UUID>,
    val brand: Brand = Brand.ALICE_DUQUESA,
    val deAccreditationDate: LocalDate? = null,
    val appointmentTypes: List<ConsolidatedAccreditedNetworkAppointmentType> = emptyList(),
    override val latitude: String? = null,
    override val longitude: String? = null,
    override val geoLocation: Geography? = null,
    val hasHospitalHealthTeam: Boolean = false,
    val flagship: Boolean = false,
    /** computed filter **/
    @Transient
    val distance: Double? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, GeographyReference {

    override fun sanitize(): ConsolidatedAccreditedNetwork {
        return copy(
            geoLocation = GeographyReference.getGeoLocation(latitude = latitude, longitude = longitude)
        )
    }
}

enum class ConsolidatedAccreditedNetworkAppointmentType(val description: String) {
    PRESENTIAL("Presencial"), REMOTE("Vídeo")
}

data class AdditionalInfo(
    val name: String,
    val imageUrl: String?,
    val address: StructuredAddress?,
    val gender: Gender? = null,
    val scheduleAvailabilityDays: Int? = null,
) : JsonSerializable

enum class ConsolidatedAccreditedNetworkType(val title: String) {
    HOSPITAL("Hospital"),
    HOSPITAL_CHILDREN("Hospital Infantil"),
    LABORATORY("Laboratório"),
    ALICE_HOUSE("Casa Alice"),
    EMERGENCY_UNITY("Pronto Socorro"),
    EMERGENCY_UNITY_CHILDREN("Pronto Socorro Infantil"),
    MATERNITY("Maternidade"),
    CLINICAL("Clínica"),
    CLINICAL_COMMUNITY("Clínica"),
    CASSI_SPECIALIST("Especialista Cassi"),
    HEALTH_PROFESSIONAL("Profissional de Saúde (Pitaya)"),
    PARTNER_HEALTH_PROFESSIONAL("Profissional de Saúde Parceiro"),
    SPECIALIST_HEALTH_PROFESSIONAL("Especialista"),
    VACCINE("Clínicas de vacinação");

    companion object {
        private fun getAliceSpecialistTypes(): List<ConsolidatedAccreditedNetworkType> = listOf(
            CLINICAL_COMMUNITY,
            SPECIALIST_HEALTH_PROFESSIONAL,
            CASSI_SPECIALIST,
            HEALTH_PROFESSIONAL,
            PARTNER_HEALTH_PROFESSIONAL,
        )

        private fun getDuquesaSpecialistTypes(): List<ConsolidatedAccreditedNetworkType> =
            getAliceSpecialistTypes() + CLINICAL

        fun getSpecialistTypes(brand: Brand): List<ConsolidatedAccreditedNetworkType> =
            if (brand == Brand.ALICE) getAliceSpecialistTypes() else getDuquesaSpecialistTypes()

        fun getSurgerySiteTypes(): List<ConsolidatedAccreditedNetworkType> = listOf(
            HOSPITAL,
            HOSPITAL_CHILDREN,
            MATERNITY
        )

    }
}
