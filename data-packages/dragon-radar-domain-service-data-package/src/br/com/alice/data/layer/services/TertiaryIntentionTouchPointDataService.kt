package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationResponsible
import br.com.alice.data.layer.models.TertiaryIntentionSurgeryStatus
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.data.layer.services.TertiaryIntentionTouchPointDataService.FieldOptions
import br.com.alice.data.layer.services.TertiaryIntentionTouchPointDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface TertiaryIntentionTouchPointDataService :
    Service,
    Finder<FieldOptions, OrderingOptions, TertiaryIntentionTouchPoint>,
    Adder<TertiaryIntentionTouchPoint>,
    Updater<TertiaryIntentionTouchPoint>,
    UpdaterList<TertiaryIntentionTouchPoint>,
    Getter<TertiaryIntentionTouchPoint>,
    Counter<FieldOptions, OrderingOptions, TertiaryIntentionTouchPoint> {

    override val namespace: String get() = "ehr"
    override val serviceName: String get() = "tertiary_intention_touch_point"

    class IdField : Field.UUIDField(TertiaryIntentionTouchPoint::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonIdField : Field.TableIdField(TertiaryIntentionTouchPoint::personId)
    class Type : Field.TextField(TertiaryIntentionTouchPoint::type) {
        fun eq(value: TertiaryIntentionType) = Predicate.eq(this, value)
        fun inList(value: List<TertiaryIntentionType>) = Predicate.inList(this, value)
    }

    class CreatedAt : Field.DateTimeField(TertiaryIntentionTouchPoint::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class StartedAt : Field.DateTimeField(TertiaryIntentionTouchPoint::startedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class EndedAt : Field.DateTimeField(TertiaryIntentionTouchPoint::endedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class SurgeryStatus : Field.TextField(TertiaryIntentionTouchPoint::surgeryStatus) {
        fun eq(value: TertiaryIntentionSurgeryStatus) = Predicate.eq(this, value)
        fun inList(value: List<TertiaryIntentionSurgeryStatus>) = Predicate.inList(this, value)
    }

    class HealthEventId : Field.UUIDField(TertiaryIntentionTouchPoint::healthEventId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class TotvsGuiaId : Field.UUIDField(TertiaryIntentionTouchPoint::totvsGuiaId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ProviderUnitId : Field.UUIDField(TertiaryIntentionTouchPoint::providerUnitId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class HospitalizationProcedures : Field.JsonbField(TertiaryIntentionTouchPoint::hospitalizationProcedures) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun isNotEmpty() = Predicate.isNotEmptyList(this)
    }

    class HospitalizationSpecialty : Field.TextField(TertiaryIntentionTouchPoint::hospitalizationSpecialty) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class HospitalizationResponsible : Field.TextField(TertiaryIntentionTouchPoint::hospitalizationResponsible) {
        fun eq(value: TertiaryIntentionHospitalizationResponsible) = Predicate.eq(this, value)
    }

    class ObjectiveCodes : Field.JsonbField(TertiaryIntentionTouchPoint::objectiveCodes) {
        fun isNotEmpty() = Predicate.isNotEmptyList(this)
    }

    class NewBornWeight : Field.BigDecimalField(TertiaryIntentionTouchPoint::newBornWeight) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class SurgeonSpecialistId : Field.UUIDField(TertiaryIntentionTouchPoint::surgeonSpecialistId) {
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val id = IdField()
        val type = Type()
        val healthEventId = HealthEventId()
        val totvsGuiaId = TotvsGuiaId()
        val providerUnitId = ProviderUnitId()
        val surgeryStatus = SurgeryStatus()
        val startedAt = StartedAt()
        val endedAt = EndedAt()
        val hospitalizationProcedures = HospitalizationProcedures()
        val hospitalizationSpecialty = HospitalizationSpecialty()
        val hospitalizationResponsible = HospitalizationResponsible()
        val objectiveCodes = ObjectiveCodes()
        val newBornWeight = NewBornWeight()
        val surgeonSpecialistId = SurgeonSpecialistId()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
        val startedAt = StartedAt()
        val hospitalizationResponsible = HospitalizationResponsible()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<TertiaryIntentionTouchPoint, Throwable>
    override suspend fun add(model: TertiaryIntentionTouchPoint): Result<TertiaryIntentionTouchPoint, Throwable>
    override suspend fun update(model: TertiaryIntentionTouchPoint): Result<TertiaryIntentionTouchPoint, Throwable>
    override suspend fun updateList(
        models: List<TertiaryIntentionTouchPoint>,
        returnOnFailure: Boolean
    ): Result<List<TertiaryIntentionTouchPoint>, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<TertiaryIntentionTouchPoint>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
