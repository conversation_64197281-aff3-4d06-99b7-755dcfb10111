package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.AppointmentStatus.DRAFT
import br.com.alice.data.layer.services.UpdateRequest
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class Appointment(
    val staffId: UUID,
    @Expose
    override val personId: PersonId,
    val description: String? = null,
    val guidance: String? = null,
    val subjectiveCodes: List<Disease> = emptyList(),
    val objectiveCodes: List<Disease> = emptyList(),
    val objective: String? = null,
    val subjective: String? = null,
    val components: List<AppointmentComponent>? = emptyList(),
    val plan: String? = null,
    val excuseNotes: List<ExcuseNote> = emptyList(),
    @Expose
    val type: AppointmentType,
    val serviceScriptId: UUID? = null,
    val completedAt: LocalDateTime? = null,
    val ownerStaffIds: Set<UUID> = setOf(),
    val startedAt: LocalDateTime? = null,
    val endedAt: LocalDateTime? = null,
    val attachments: List<Attachment> = emptyList(),
    val specialty: AppointmentSpecialty? = null,
    val specialist: AppointmentSpecialist? = null,
    val referencedLinks: List<ReferencedLink> = emptyList(),
    val status: AppointmentStatus = DRAFT,
    val discardedType: AppointmentDiscardedType? = null,
    val discardedReason: String? = null,
    val finishType: AppointmentFinishType? = null,
    val clinicalEvaluation: String? = null,
    val treatedBy: TreatedBy? = null,
    val outcome: Outcome? = null,
    val channelId: String? = null,
    val caseRecordDetails: List<CaseRecordDetails>? = emptyList(),
    val draftGroupStaffIds: List<UUID>? = emptyList(),
    val event: AppointmentEventDetail? = null,
    val name: String? = null,
    val templateType: TemplateType? = null,
    val staffRole: Role? = null,
    val emptyEventReason: String? = null,
    val protocolNavigationHistory: List<ProtocolNavigation>? = emptyList(),
    val externalFiles: List<ExternalFile>? = emptyList(),
    val contractualRisks: List<AppointmentContractualRisk>? = emptyList(),
    val medicalDischarge: Boolean? = null,
    val primaryAttentionRequest: String? = null,
    val appointmentDate: LocalDate? = null,
    val anesthetist: Anesthetist? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null
) : Model, PersonReference, UpdatedByReference, HealthInformation, UpdateRequest {

    // These objects could have null description although the property being non-nullable because they were deserialized
    @Suppress("SENSELESS_COMPARISON")
    override fun sanitize() = copy(
        excuseNotes = excuseNotes.filter { it.description != null && it.description.isNotBlank() }
            .map { it.copy(description = it.description.trim()) },
        subjectiveCodes = subjectiveCodes.filter { it.value.isNotBlank() }
            .map { it.copy(value = it.value.trim(), description = null) },
        objectiveCodes = objectiveCodes.filter { it.value.isNotBlank() }
            .map { it.copy(value = it.value.trim(), description = null) },
        primaryAttentionRequest = primaryAttentionRequest?.trim().nullIfBlank(),
    )

    @Transient
    val content get() = (description ?: subjective).orEmpty()

    @Transient
    val completed get() = completedAt != null

    @Transient
    val alreadyExistsSubjectiveCodes
        get() = createdAt > subjectiveCodesStartDate

    @Transient
    val isImmersion
        get() = type == AppointmentType.IMMERSION

    @Transient
    val isFollowUpVisit
        get() = type == AppointmentType.FOLLOW_UP_VISIT

    @Transient
    val isAssistanceCare
        get() = type == AppointmentType.ASSISTANCE_CARE

    @Transient
    val isHealthPlanCare
        get() = type == AppointmentType.HEALTH_PLAN_CARE

    @Transient
    val isStatementOfHealth
        get() = type == AppointmentType.STATEMENT_OF_HEALTH

    @Transient
    val isAnnotation
        get() = type == AppointmentType.ANNOTATION

    @Transient
    val isAnnotationHealthCommunity
        get() = type == AppointmentType.ANNOTATION_HEALTH_COMMUNITY

    @Transient
    val isDefault
        get() = type == AppointmentType.DEFAULT

    @Transient
    val isOnSite
        get() = type == AppointmentType.ON_SITE

    fun isFinished() = this.status == AppointmentStatus.FINISHED

    fun getConsolidatedInfo() =
        """
            ${type.name};
            ${description.orEmpty()};
            ${subjective.orEmpty()};
            ${objective.orEmpty()};
            ${plan.orEmpty()};
            ${clinicalEvaluation.orEmpty()};
            ${caseRecordDetails.orEmpty()};
            ${staffRole?.name.orEmpty()}
        """.trimIndent()

    companion object {
        // Before this date both subjective and objective codes using CID and CIAP were on AssistanceCare.clinicalEvaluationCodes
        private val subjectiveCodesStartDate = LocalDateTime.of(2020, 9, 1, 14, 0)
    }

    data class ReferencedLink(
        val id: UUID,
        val model: ReferenceLinkModel
    ) : JsonSerializable

    enum class ReferenceLinkModel {
        SCHEDULE,
        HEALTH_PLAN_TASK,
    }

    fun getStaffIds() = ownerStaffIds
        .plus(staffId)
        .plus(draftGroupStaffIds ?: emptyList())
        .plus(anesthetist?.staffId)
        .filterNotNull()
        .distinct()

}

enum class AppointmentStatus {
    DRAFT,
    FINISHED,
    DISCARDED;

    fun toTimeline(discardedReason: AppointmentDiscardedType?): TimelineStatus = when (this) {
        FINISHED -> TimelineStatus.FINISHED
        DISCARDED -> getReason(discardedReason)
        else -> throw IllegalArgumentException("invalid status $this")
    }

    private fun getReason(discardedReason: AppointmentDiscardedType?): TimelineStatus =
        if (discardedReason == AppointmentDiscardedType.NO_SHOW) TimelineStatus.DISCARDED_NO_SHOW
        else throw IllegalArgumentException("invalid reason $discardedReason")
}

enum class AppointmentFinishType {
    AUTOMATIC,
    MANUAL
}

enum class AppointmentDiscardedType {
    WRONG_APPOINTMENT_TYPE,
    OTHERS,
    NO_SHOW,
    LATE_CANCEL,
}

data class ExcuseNote(
    val description: String,
    val id: String? = null,
    val attachmentId: String? = null,
    val status: String? = null,
    val token: String? = null,
)

data class AppointmentSpecialty(
    val name: String,
    val id: UUID
) : JsonSerializable

data class AppointmentSpecialist(
    val name: String,
    val id: UUID
) : JsonSerializable

data class RelatedStaff(
    val name: String? = null,
    val id: UUID,
    val fullName: String? = null
) : JsonSerializable

data class AppointmentComponent(
    val componentType: AppointmentComponentType,
    val order: Int
) : JsonSerializable

enum class AppointmentComponentType {
    SUBJECTIVE,
    OBJECTIVE,
    EVALUATION,
    PLAN,
    EXCUSE_NOTES,
    PROCEDURES,
    FREE_TEXT,
    TREATED_BY,
    EMPTY_EVENT_REASON,
    CONTRACTUAL_RISK_CALCULATOR,
    PROCEDURE_EXECUTED,
    COUNTER_REFERRAL_FREE_TEXT,
}

data class AppointmentEventDetail(
    val name: String,
    val referenceModelId: String,
    val referenceModel: AppointmentEventReferenceModel
) : JsonSerializable

enum class TemplateType(val description: String) {
    SOAP("Consulta"),
    FREE_TEXT("Consulta"),
    COUNTER_REFERRAL_FREE_TEXT("Consulta");
}

data class ProtocolNavigation(
    val rootNodeId: UUID? = null,
    val protocolName: String? = null,
    val nodeId: UUID,
    val question: String,
    val answer: String? = null,
    val date: LocalDateTime,
    val type: String? = null,
    val index: Int,
    val additionalInformation: AdditionalInformation? = null
) : JsonSerializable

data class AdditionalInformation(
    val symptomsQuestionAnswers: List<String>
) : JsonSerializable

fun Appointment.areFilledAllCaseRecord() =
    this.caseRecordDetails.isNotNullOrEmpty() && this.caseRecordDetails!!.all { it.caseId != null }

data class ExternalFile(
    val id: UUID,
    val name: String? = null,
    val store: Boolean = false,
    val type: ExternalFileType,
    val origin: ExternalFileOrigin
) : JsonSerializable

enum class ExternalFileType {
    IMAGE,
    VIDEO,
    AUDIO,
    DOCUMENT
}

enum class ExternalFileOrigin {
    CHANNELS
}

data class AppointmentContractualRisk(
    val caseId: UUID,
    val description: HealthConditionDescription,
    val baseRiskRating: Int,
    val factor: Int,
    val finalRiskRating: Int,
    val reason: String
)

data class HealthConditionDescription(
    val id: UUID,
    val type: Disease.Type,
    val value: String,
    val description: String? = null
)

data class Anesthetist(
    val type: AnesthetistType,
    val staffId: UUID?,
) : JsonSerializable

enum class AnesthetistType(val description: String) {
    IN_HOUSE("Da casa (hospital)"),
    TAKAOKA("Takaoka"),
    SMA("SMA"),
    INDIVIDUAL("Anestesista individual"),
    NOT_APPLICABLE("Não se aplica")
}

