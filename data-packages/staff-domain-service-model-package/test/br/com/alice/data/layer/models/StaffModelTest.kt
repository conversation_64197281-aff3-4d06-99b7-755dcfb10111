package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.models.Gender
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class StaffModelTest {

    private val staff = buildStaff()

    companion object {
        @JvmStatic
        fun validNationalIds(): Stream<String?> = Stream.of(
            "11144477735",
            "111.444.777-35",
            null
        )

        data class InvalidCpfTestCase(val cpf: String, val expectedMessage: String)

        @JvmStatic
        fun invalidNationalIds(): Stream<InvalidCpfTestCase> = Stream.of(
            InvalidCpfTestCase("12345678901", "CPF '12345678901' é inválido"),
            InvalidCpfTestCase("123.456.789-01", "CPF '123.456.789-01' é inválido")
        )
    }

    @Test
    fun `#allRoles gets only role`() {
        val staff = staff.copy(role = Role.MANAGER_PHYSICIAN)
        assertThat(staff.allRoles()).isEqualTo(listOf(Role.MANAGER_PHYSICIAN))
    }

    @Test
    fun `#sanitize values`() {
        val staff = staff.copy(
            email = " <EMAIL> ", firstName = " José ", lastName = " Pereira ",
            nationalId = " 012.345.678-90 ", profileImageUrl = "  "
        )

        val sanitizedStaff = staff.sanitize()
        assertThat(sanitizedStaff.email).isEqualTo("<EMAIL>")
        assertThat(sanitizedStaff.firstName).isEqualTo("José")
        assertThat(sanitizedStaff.lastName).isEqualTo("Pereira")
        assertThat(sanitizedStaff.nationalId).isEqualTo("01234567890")
        assertThat(sanitizedStaff.profileImageUrl).isNull()
    }

    @Test
    fun `#treatment`() {
        val maleStaff = staff
        val femaleStaff = staff.copy(gender = Gender.FEMALE)

        assertThat(maleStaff.treatment).isEqualTo("Dr.")
        assertThat(femaleStaff.treatment).isEqualTo("Dra.")
    }

    @Test
    fun `#isAllowedToPublishPrescriptions`() {
        val staff1 = staff.copy(role = Role.MANAGER_PHYSICIAN)
        assertThat(staff1.isAllowedToPublishPrescriptions()).isTrue

        val staff2 = staff
        assertThat(staff2.isAllowedToPublishPrescriptions()).isTrue

        val staff3 = staff.copy(role = Role.NAVIGATOR)
        assertThat(staff3.isAllowedToPublishPrescriptions()).isFalse

        val staff4 = staff.copy(role = Role.CHIEF_NAVIGATOR)
        assertThat(staff4.isAllowedToPublishPrescriptions()).isFalse
    }

    @Test
    fun `#isAllowedToGetChat`() {
        val staff1 = staff.copy(role = Role.NAVIGATOR)
        assertThat(staff1.isAllowedToGetChat()).isTrue

        val staff2 = staff.copy(role = Role.NAVIGATOR)
        assertThat(staff2.isAllowedToGetChat()).isTrue

        val staff3 = staff.copy(role = Role.CHIEF_NAVIGATOR)
        assertThat(staff3.isAllowedToGetChat()).isTrue
    }

    @ParameterizedTest(name = "should return success for valid nationalId: {0}")
    @MethodSource("validNationalIds")
    fun `#validate should return success when nationalId is valid CPF`(value: String?) = runBlocking {
        val staff = buildStaff(role = Role.HEALTH_OPS, nationalId = value)
        assertDoesNotThrow {
            staff.validate()
        }
    }

    @ParameterizedTest(name = "should return error for invalid nationalId: {0}")
    @MethodSource("invalidNationalIds")
    fun `#validate should return error when nationalId is invalid CPF`(value: InvalidCpfTestCase) = runBlocking {
        val staff = buildStaff(role = Role.HEALTH_OPS, nationalId = value.cpf)
        val exception = assertThrows <InvalidArgumentException> {
            staff.validate()
        }
        assertEquals(value.expectedMessage, exception.message)
    }

    private fun buildStaff(
        firstName: String = "José",
        lastName: String = "Silva",
        email: String = "<EMAIL>",
        gender: Gender = Gender.MALE,
        id: UUID = RangeUUID.generate(),
        role: Role = Role.MANAGER_PHYSICIAN,
        nationalId: String? = "10201857081",
        type: StaffType = StaffType.PITAYA,
        profileImageUrl: String? = null,
    ) = StaffModel(
        id = id,
        email = email,
        firstName = firstName,
        lastName = lastName,
        gender = gender,
        role = role,
        nationalId = nationalId,
        type = type,
        profileImageUrl = profileImageUrl,
    )

}
