package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.money
import br.com.alice.common.logging.logger
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberInvoiceGroup(
    val id: UUID = RangeUUID.generate(),
    val externalId: String?,
    val memberInvoiceIds: List<UUID> = emptyList(),
    val billingAccountablePartyId: UUID,
    val referenceDate: LocalDate,
    val dueDate: LocalDate,
    val status: MemberInvoiceGroupStatus,
    val type: MemberInvoiceType?,
    val totalAmount: BigDecimal?,
    val globalItems: List<InvoiceItem>? = null,
    val companyId: UUID? = null,
    val companySubcontractId: UUID? = null,
    val quantityMemberInvoices: Int? = null,
    val invoiceLiquidationIds: List<UUID>? = null,
    val preActivationPaymentId: UUID? = null,
    val discount: BigDecimal = BigDecimal.ZERO,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    val hasPositiveTotalAmount = totalAmount?.let { it >= BigDecimal.ZERO } ?: false

    fun markAsPaid() = this.copy(status = MemberInvoiceGroupStatus.PAID)

    fun changeReferenceDate(paidAt: LocalDateTime? = null) =
        if (type?.isFirstPayment() == true && paidAt != null) {
            val referenceDate = referenceDate.withYear(paidAt.year).withMonth(paidAt.monthValue)

            logger.info(
                "Should change the reference date",
                "current_reference_date" to this.referenceDate,
                "new_reference_date" to referenceDate,
            )

            copy(
                referenceDate = referenceDate,
                dueDate = paidAt.toLocalDate()
            )
        } else {
            logger.info("Should not change the reference date")
            this
        }

    fun markAsPartiallyPaid() = this.copy(status = MemberInvoiceGroupStatus.PARTIALLY_PAID)

    fun markAsProcessed() = this.copy(status = MemberInvoiceGroupStatus.PROCESSED)

    fun markAsPaidByLiquidation(invoiceLiquidationIds: List<UUID>) = this.copy(
        invoiceLiquidationIds = invoiceLiquidationIds,
        status = MemberInvoiceGroupStatus.CANCELED_BY_LIQUIDATION
    )

    val alreadyPaid = status == MemberInvoiceGroupStatus.PAID

    val isProcessed = status == MemberInvoiceGroupStatus.PROCESSED

    val isPaidByLiquidation = status == MemberInvoiceGroupStatus.CANCELED_BY_LIQUIDATION

    fun calculateTotalAmount(invoices: List<MemberInvoice>) =
        this.copy(
            totalAmount = globalItems?.let { invoiceItems ->
                invoices.sumOf { it.totalAmount.money }.let { totalAmount ->
                    totalAmount.plus(invoiceItems.sumOf {
                        it.resolvedValue?.money ?: it.resolveValue(totalAmount)
                    })
                }

            } ?: invoices.sumOf { it.totalAmount.money }
        )
}

enum class MemberInvoiceGroupStatus(val description: String) {
    PROCESSING("Member invoice group criada"),
    WAITING_PAYMENT("Aguardando pagamento"),
    PROCESSED("Invoices e pagamentos gerados"),
    PAID("Member Invoice Group pago"),
    PARTIALLY_PAID("Member Invoice Group parcialmente pago"),
    CANCELED("Member invoice group cancelado"),
    CANCELED_BY_LIQUIDATION("Baixa por liquidação")
}

enum class MemberInvoiceType(val description: String) {
    FIRST_PAYMENT("Primeiro Pagamento"),
    REGULAR_PAYMENT("Pagamento da mensalidade"),
    B2B_REGULAR_PAYMENT("Pagamento da mensalidade B2B"),
    OVERDUE_PAYMENT("Pagamento atrasado"),
    B2B_FIRST_PAYMENT("Primeiro pagamento B2B"),
    B2B_PRE_ACTIVATION_PAYMENT("Pré-pagamento de ativação B2B"),
    B2C_PRE_ACTIVATION_PAYMENT("Pré-pagamento de ativação");

    fun isFirstPayment() = when (this) {
        FIRST_PAYMENT, B2B_FIRST_PAYMENT -> true
        else -> false
    }

    fun isB2B() = when (this) {
        B2B_FIRST_PAYMENT, B2B_REGULAR_PAYMENT -> true
        else -> false
    }

    fun toPaymentReason() = when (this) {
        FIRST_PAYMENT -> PaymentReason.FIRST_PAYMENT
        REGULAR_PAYMENT -> PaymentReason.REGULAR_PAYMENT
        OVERDUE_PAYMENT -> PaymentReason.OVERDUE_PAYMENT
        B2B_REGULAR_PAYMENT -> PaymentReason.B2B_REGULAR_PAYMENT
        B2B_FIRST_PAYMENT -> PaymentReason.B2B_FIRST_PAYMENT
        B2B_PRE_ACTIVATION_PAYMENT -> PaymentReason.B2B_PRE_ACTIVATION_PAYMENT
        B2C_PRE_ACTIVATION_PAYMENT -> PaymentReason.B2C_PRE_ACTIVATION_PAYMENT
    }
}

enum class MemberInvoicePriceType(val description: String) {
    FULL("Valor cheio"),
    PRO_RATA("Valor Pro rata"),
}
