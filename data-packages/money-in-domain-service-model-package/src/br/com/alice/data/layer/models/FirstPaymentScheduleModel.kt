package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class FirstPaymentScheduleModel(
    override val id: UUID = RangeUUID.generate(),
    val preActivationPaymentId: UUID,
    val companyId: UUID,
    val companySubcontractId: UUID,
    val memberInvoiceGroupId: UUID? = null,
    val statusHistory: List<FirstPaymentScheduleStatusHistoryEntryModel> = emptyList(),
    val status: FirstPaymentScheduleStatus = FirstPaymentScheduleStatus.PENDING,
    val error: String? = null,
    val externalId: String? = null,
    val scheduledDate: LocalDate,
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null
) : Model, UpdatedByReference

data class FirstPaymentScheduleStatusHistoryEntryModel(
    val status: FirstPaymentScheduleStatus,
    val createdAt: String
) : JsonSerializable
