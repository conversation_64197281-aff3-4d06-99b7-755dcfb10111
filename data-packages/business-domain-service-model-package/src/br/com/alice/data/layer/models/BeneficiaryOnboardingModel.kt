package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.SkipReadAuthz
import br.com.alice.common.core.exceptions.BadRequestException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

@SkipReadAuthz
data class BeneficiaryOnboardingModel(
    override val id: UUID = RangeUUID.generate(),
    val beneficiaryId: UUID,
    val flowType: BeneficiaryOnboardingFlowType,
    val initialProductId: UUID,
    val acceptedDataProcessingTermsAt: LocalDateTime? = null,
    val version: Int = 0,
    @Transient
    val phases: List<BeneficiaryOnboardingPhaseModel> = emptyList()
) : Model {

    @Transient
    val currentPhase: BeneficiaryOnboardingPhaseModel?
        get() = phases.maxByOrNull { it.transactedAt }

    @Transient
    val allPhases: List<BeneficiaryOnboardingPhaseType>
        get() = OnboardingFlowFactoryModel.get(flowType).allPhases()

    fun nextPhase(): BeneficiaryOnboardingPhaseModel = currentPhase?.let {
        it.newPhase(it.getNextPhase())
    } ?: throw InvalidBeneficiaryOnboardingNextPhaseException()

    fun getPreviousPhasesFromInitial() = sequence {
        val onboardingFactory = OnboardingFlowFactoryModel.get(flowType)
        currentPhase?.let {
            var nextPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
            while (nextPhase != it.phase) {
                yield(nextPhase)
                nextPhase = onboardingFactory.nextPhase(nextPhase)
            }
        } ?: throw BeneficiaryWithoutPhaseException()
    }

    fun toPhase(
        targetPhase: BeneficiaryOnboardingPhaseType,
        transactedAt: LocalDateTime,
        maxSteps: Int = 1
    ): Result<BeneficiaryOnboardingPhaseModel, Throwable> =
        currentPhase?.let { currentPhase ->
            return if (maxSteps > 0)
                toForwardPhase(currentPhase, targetPhase, transactedAt, maxSteps)
            else
                toBackwardPhase(currentPhase, targetPhase, transactedAt, maxSteps)
        } ?: InvalidBeneficiaryOnboardingNextPhaseException().failure()

    private fun toForwardPhase(
        currentPhase: BeneficiaryOnboardingPhaseModel,
        targetPhase: BeneficiaryOnboardingPhaseType,
        transactedAt: LocalDateTime,
        maxSteps: Int
    ): Result<BeneficiaryOnboardingPhaseModel, Throwable> {
        (1..maxSteps).fold(currentPhase) { phase: BeneficiaryOnboardingPhaseModel, _: Int ->
            val nextPhaseType = phase.getNextPhase()
            val nextPhase = nextPhaseType.toPhase(phase.beneficiaryOnboardingId, transactedAt)

            when {
                nextPhaseType == targetPhase -> return nextPhase.success()
                currentPhase.phase >= targetPhase -> return BeneficiaryAlreadyOnCorrectPhaseException(
                    nextPhaseType,
                    targetPhase
                ).failure()

                else -> nextPhase
            }
        }

        return InvalidBeneficiaryOnboardingToPhaseException(maxSteps, targetPhase).failure()
    }

    private fun toBackwardPhase(
        currentPhase: BeneficiaryOnboardingPhaseModel,
        targetPhase: BeneficiaryOnboardingPhaseType,
        transactedAt: LocalDateTime,
        maxSteps: Int
    ): Result<BeneficiaryOnboardingPhaseModel, Throwable> {
        (maxSteps..-1).fold(currentPhase) { phase: BeneficiaryOnboardingPhaseModel, _: Int ->
            val previousPhaseType = phase.getPreviousPhase()
            val previousPhase = previousPhaseType.toPhase(phase.beneficiaryOnboardingId, transactedAt)

            when {
                previousPhaseType == targetPhase -> return previousPhase.success()
                currentPhase.phase <= targetPhase -> return BeneficiaryAlreadyOnCorrectPhaseException(
                    previousPhaseType,
                    targetPhase
                ).failure()

                else -> previousPhase
            }
        }

        return InvalidBeneficiaryOnboardingToPhaseException(maxSteps, targetPhase).failure()
    }

    private fun BeneficiaryOnboardingPhaseModel.getNextPhase() = OnboardingFlowFactoryModel.get(flowType).nextPhase(this.phase)
    private fun BeneficiaryOnboardingPhaseModel.getPreviousPhase() =
        OnboardingFlowFactoryModel.get(flowType).previousPhase(this.phase)
}

fun BeneficiaryOnboardingPhaseType.toPhase(beneficiaryOnboardingId: UUID, transactedAt: LocalDateTime = LocalDateTime.now()) =
    BeneficiaryOnboardingPhaseModel(
        beneficiaryOnboardingId = beneficiaryOnboardingId,
        phase = this,
        transactedAt = transactedAt
    )

fun BeneficiaryOnboardingPhaseModel.stepsForPhase(
    flowType: BeneficiaryOnboardingFlowType,
    target: BeneficiaryOnboardingPhaseType
) = OnboardingFlowFactoryModel.get(flowType).stepsForPhase(phase, target)

fun BeneficiaryOnboardingModel.withPhases(phases: List<BeneficiaryOnboardingPhaseModel>): BeneficiaryOnboardingModel =
    copy(phases = phases)

class InvalidBeneficiaryOnboardingNextPhaseException(
    message: String = "BeneficiaryOnboarding has no current phase",
    code: String = "invalid_next_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class BeneficiaryWithoutPhaseException(
    message: String = "BeneficiaryOnboarding has no current phase",
    code: String = "invalid_current_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class InvalidBeneficiaryOnboardingToPhaseException(
    message: String,
    code: String = "invalid_to_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(maxSteps: Int, requestedPhase: BeneficiaryOnboardingPhaseType) : this(
        message = "BeneficiaryOnboarding next phases with max steps [$maxSteps] does not reach the requested phase [$requestedPhase]"
    )
}

class BeneficiaryAlreadyOnCorrectPhaseException(
    message: String,
    code: String = "already_on_correct_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(nextPhase: BeneficiaryOnboardingPhaseType, requestedPhase: BeneficiaryOnboardingPhaseType) : this(
        message = "BeneficiaryOnboarding already on correct phase: [$nextPhase]. Requested phase: [$requestedPhase]"
    )
}
