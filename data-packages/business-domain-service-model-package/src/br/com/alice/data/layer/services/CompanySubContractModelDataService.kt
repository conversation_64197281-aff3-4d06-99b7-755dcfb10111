package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.CompanySubContractModel
import br.com.alice.data.layer.models.CompanySubContractStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CompanySubContractModelDataService : Service,
    Finder<CompanySubContractModelDataService.FieldOptions, CompanySubContractModelDataService.OrderingOptions ,CompanySubContractModel>,
    Adder<CompanySubContractModel>,
    Getter<CompanySubContractModel>,
    Updater<CompanySubContractModel>,
    Deleter<CompanySubContractModel>
{

    override val namespace: String
        get() = "business"
    override val serviceName: String
        get() = "company_sub_contract"

    class IdField : Field.UUIDField(CompanySubContractModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CompanyIdField : Field.UUIDField(CompanySubContractModel::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)

        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ContractIdField : Field.UUIDField(CompanySubContractModel::contractId) {
        fun eq(value: UUID) = Predicate.eq(this, value)

        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ExternalIdField : Field.TextField(CompanySubContractModel::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)

        fun isNotNull() = Predicate.isNotNull(this)
    }

    class StatusField : Field.TextField(CompanySubContractModel::status) {
        fun inList(value: List<CompanySubContractStatus>) = Predicate.inList(this, value)
    }

    class TitleField : Field.TextField(CompanySubContractModel::title) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class BillingAccountablePartyIdField : Field.UUIDField(CompanySubContractModel::billingAccountablePartyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val externalId = ExternalIdField()
        val title = TitleField()
        val companyId = CompanyIdField()
        val contractId = ContractIdField()
        val billingAccountablePartyId = BillingAccountablePartyIdField()
        val status = StatusField()
    }

    class OrderingOptions {
        val id = IdField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: CompanySubContractModel): Result<CompanySubContractModel, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<CompanySubContractModel>, Throwable>

    override suspend fun get(id: UUID): Result<CompanySubContractModel, Throwable>

    override suspend fun update(model: CompanySubContractModel): Result<CompanySubContractModel, Throwable>

    override suspend fun delete(model: CompanySubContractModel): Result<Boolean, Throwable>

}
