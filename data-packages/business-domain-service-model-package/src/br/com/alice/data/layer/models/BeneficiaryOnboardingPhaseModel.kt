package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.SkipReadAuthz
import java.time.LocalDateTime
import java.util.UUID

@SkipReadAuthz
data class BeneficiaryOnboardingPhaseModel(
    override val id: UUID = RangeUUID.generate(),
    val beneficiaryOnboardingId: UUID,
    val phase: BeneficiaryOnboardingPhaseType,
    val transactedAt: LocalDateTime = LocalDateTime.now(),
    val finishedAt: LocalDateTime? = null,
    val version: Int = 0,
) : Model {

    fun newPhase(phase: BeneficiaryOnboardingPhaseType, transactedAt: LocalDateTime = LocalDateTime.now()) =
        BeneficiaryOnboardingPhaseModel(
            beneficiaryOnboardingId = this.beneficiaryOnboardingId,
            phase = phase,
            transactedAt = transactedAt
        )
}
