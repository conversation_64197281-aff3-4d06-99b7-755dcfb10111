package br.com.alice.provider.consumers

import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory.buildStaff
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.event.AnesthetistCreatedEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.Test

class AnesthetistConsumerTest : ConsumerTest() {
    private val providerService: ProviderService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val consumer = AnesthetistConsumer(providerUnitService, providerService)

    private val staff = buildStaff(
        role = Role.ANESTHETIST,
    )
    private val nowMock = LocalDateTime.now()

    val provider = Provider(
        id = "${staff.fullName}${staff.id}".toSafeUUID(),
        name = staff.fullName,
        type = ProviderType.MEDICAL_COMPANY,
        imageUrl = staff.profileImageUrl,
        createdAt = nowMock,
        updatedAt = nowMock
    )

    val providerUnit = ProviderUnit(
        id = provider.id,
        type = ProviderUnit.Type.MEDICAL_COMPANY,
        name = staff.fullName,
        providerId = provider.id,
        showOnApp = false,
        clinicalStaffIds = listOf(staff.id),
        createdAt = nowMock,
        updatedAt = nowMock
    )

    @Test
    fun `should create provider and provider unit for anesthetist`() = mockLocalDateTime(nowMock) {
        val event = AnesthetistCreatedEvent(staff)

        coEvery {
            providerService.add(provider)
        } returns provider.success()

        coEvery {
            providerUnitService.add(providerUnit)
        } returns providerUnit.success()

        val result = consumer.associateProviderAddProviderUnitToAnesthetist(event)
        assertThat(result).isSuccessWithData(providerUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerUnitService.add(any()) }
        coVerifyNone { providerService.get(any()) }
    }

    @Test
    fun `should handle duplicate provider creation`() = mockLocalDateTime(nowMock) {
        val event = AnesthetistCreatedEvent(staff)

        coEvery {
            providerService.add(provider)
        } returns DuplicatedItemException().failure()

        coEvery {
            providerService.get(provider.id)
        } returns provider.success()

        coEvery {
            providerUnitService.add(providerUnit)
        } returns providerUnit.success()

        val result = consumer.associateProviderAddProviderUnitToAnesthetist(event)
        assertThat(result).isSuccessWithData(providerUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerService.get(any()) }
        coVerifyOnce { providerUnitService.add(any()) }
    }
}
