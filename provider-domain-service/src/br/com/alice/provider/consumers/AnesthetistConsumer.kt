package br.com.alice.provider.consumers

import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.event.AnesthetistCreatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap

class AnesthetistConsumer(
    private val providerUnitService: ProviderUnitService,
    private val providerService: ProviderService
) : Consumer() {

    suspend fun associateProviderAddProviderUnitToAnesthetist(event: AnesthetistCreatedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val staff = event.payload.staff
            val providerId = "${staff.fullName}${staff.id}".toSafeUUID()
            providerService.add(
                Provider(
                    id = providerId,
                    name = staff.fullName,
                    type = ProviderType.MEDICAL_COMPANY,
                    imageUrl = staff.profileImageUrl,
                )
            ).coFoldDuplicated {
                providerService.get(providerId)
            }.flatMap {
                providerUnitService.add(
                    ProviderUnit(
                        id = providerId,
                        type = ProviderUnit.Type.MEDICAL_COMPANY,
                        showOnApp = false,
                        name = staff.fullName,
                        providerId = it.id,
                        clinicalStaffIds = listOf(staff.id)
                    )
                )
            }
        }

}
